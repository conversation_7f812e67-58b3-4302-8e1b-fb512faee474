button{
    cursor: pointer;
}

.title {
    font-family: "Noto Serif TC", serif;
    font-weight: bold;
    margin-bottom: 1rem;
}

.left-customize {
    padding-bottom: 40px;
}

.left-customize .row {
    row-gap: 3.5rem;
}

.left-customize .number {
    font-size: 16px;
    color: var(--main-color);
}

.left-customize .btns {
    display: flex;
    column-gap: 0.5rem;
}

.left-customize a,
.right-inner-select a {
    border: none;
    padding: 0.5rem;
    color: var(--white);
    border-radius: 5px;
    display: flex;
    align-items: center;
    column-gap: 0.3rem;
}

.left-customize .select,
.right-inner-select .select {
    background-color: var(--link);
}

.left-customize .clear {
    background-color: var(--main-color);
}

.left-customize .img-container {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.25);
}

.left-customize .img-preview {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width:100%;
    padding-bottom: 100%;
    position:relative;
}

.draggable-box {
    position: absolute;
    /* background-color: rgba(255, 255, 255, 0.5); */
    /* border: 1px solid #000; */
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    touch-action: none;
    /* background-size: contain; */
    /* background-repeat: no-repeat; */
    /* background-position: center; */
}

.draggable-box span{
    font-size:18px;
}

.right-inner-select {
    background-color: #efefef;
    margin-bottom: 48px;
}

@media (min-width: 992px) {
    .right-inner-select {
        position: absolute;
        top: 0;
        right: 0;
        width: 400px;
        height: 100%;
        margin-bottom: 0;
        display:flex;
        flex-direction: column;
    }

    .right-inner-select .inner-list{
        flex-grow:1;
        overflow-y:scroll;
    }

    .left-customize {
        width: calc(100% - 400px);
        padding-bottom: 80px;
    }
}

.right-inner-select .title {
    padding: 15px;
    border-bottom: 1px solid var(--link);
}

.right-inner-select .inner-list {
    padding: 15px;
}

.right-inner-select .number {
    color: var(--link);
    font-size: 16px;
}

.right-inner-select .remove {
    background-color: var(--white);
    color: var(--main-color);
}

.right-inner-select .remove i {
    color: #cccccc;
}

.right-inner-select .inner-list .item:not(:last-child) {
    margin-bottom: 1rem;
}

.right-inner-select .layer {
    display: flex;
    align-items: center;
    column-gap: 0.5rem;
}

.right-inner-select .inner-list .item .layer:not(:last-child) {
    margin-bottom: 1rem;
}

.right-inner-select .confirm-btn-container{
    padding: 15px;
    display:flex;
    column-gap:1rem;
}

.right-inner-select .confirm-btn{
  border-radius:5px;
  background-color:var(--sub-color);
  text-align: center;
  padding:10px;
  width:100%;
  display:block;
}

.boxModal .category:not(:last-child){
    margin-bottom:1rem;
}

.boxModal .row {
    row-gap: 1rem;
}

.boxModal .item-title{
    color:var(--white);
    background-color: var(--link);
    padding:5px;
    margin-top: 1rem;
    margin-bottom:1rem;
}

.boxModal .row .item {
    display: block;
    position: relative;
}

.boxModal#contentSelectModal .item > div{
    border:1px solid #eaeaea;
    padding:5px;
}

.boxModal .row .item h5{
    font-size:20px;
    margin-bottom:0.5rem;
    margin-top: 0.8rem;
}

.boxModal .row .item > div:hover, .boxModal .row input:checked + div{
    transform: scale(1.05);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.25);
    cursor: pointer;
    border:none;
}

.boxModal .row .img {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    padding-bottom: 100%;
}

.boxModal .row input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

button.submitBtn {
    border: none;
}

#contentSelectModal .modal-header, #contentSelectModal .modal-footer{
    flex-shrink: 0;
}

#contentSelectModal .modal-dialog{
    display:flex;
}

#contentSelectModal .modal-content{
    overflow:hidden;
    max-height: calc(100vh - 1rem);
}

#contentSelectModal .modal-body{
    overflow-y: auto;
}

#contentSelectModal .modal-footer{
    justify-content: center;
    border-top:none;
}

@media (min-width: 576px) {
    #contentSelectModal .modal-dialog{
        max-height: calc(100% - 3.5rem);
    }
    #contentSelectModal .modal-content{
        max-height: calc(100vh - 3.5rem);
    }
}