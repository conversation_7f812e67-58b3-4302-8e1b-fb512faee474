* {
  /*     -webkit-touch-callout: none;
-webkit-user-select: none;
-khtml-user-select: none;
-moz-user-select: none;
-ms-user-select: none;
user-select: none; */
}

input,
textarea {
  -webkit-user-select: auto;
}

[type='text']:focus,
[type='password']:focus,
[type='date']:focus,
[type='datetime']:focus,
[type='datetime-local']:focus,
[type='month']:focus,
[type='week']:focus,
[type='email']:focus,
[type='number']:focus,
[type='search']:focus,
[type='tel']:focus,
[type='time']:focus,
[type='url']:focus,
[type='color']:focus,
textarea:focus,
.form-control:focus {
  border    : none;
  box-shadow: none;
}



img {
  width    : fit-content;
  max-width: 100%;
  max-height: 100%;
  height   : auto;
}

p,
h1,
h2,
h3,
h4,
h5,
h6,
i {
  padding: 0px;
  margin : 0px;
}

body {
  overflow-x: hidden;
  overflow-y: scroll;
  font-size : 14px;
}

.modal {
  -webkit-user-select: none;
  -moz-user-select   : none;
  -o-user-select     : none;
  user-select        : none;
}

.cursor-pointer {
  cursor: pointer !important;
}
.cursor-defult {
  cursor: defult !important;
}
.overflow-x-scroll{
  overflow-x: scroll;
}

.cursor-initial {
  cursor: initial !important;
}

.mb-5pct {
  margin-bottom: 5%;
}

.mb-5rem {
  margin-bottom: 5rem;
}

/* 禁止點擊 */
.disabled {
  pointer-events: none;
}

/* 淡 */
.level-C-1 {
  color: #fff2e9 !important;
}

.level-BC-1 {
  background-color: #fff2e9 !important;
}

.level-C-2 {
  color: #ff822e !important;
}

.level-BC-2 {
  background-color: #ff822e !important;
}

.level-C-3 {
  color: #ff6d00 !important;
}

.level-BC-3 {
  background-color: #ff6d00 !important;
}

.level-C-4 {
  color: #e26202 !important;
}

.level-BC-4 {
  background-color: #e26202 !important;
}

.level-C-5 {
  color: #ff4300 !important;
}

.level-BC-5 {
  background-color: #ff4300 !important;
}

/* independent color */


/* independent color */
.container-fluid.limit-container {
  /* max-width: 1920px; */
  width: 100%;
  padding  : 0px;
}

@media (min-width: 1440px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1680px) {
  .container {
    max-width: 1440px;
  }
}


/* #phoneMenu strat */
#phoneMenu.page {
  overflow-x: hidden;
  position  : relative;
}

#phoneMenu .wrapper {
  position: relative;
  top:0;
  -webkit-transition: ease 0.5s;
  -moz-transition   : ease 0.5s;
  -o-transition     : ease 0.5s;
  transition        : ease 0.5s;
}

#phoneMenu .wrapper.pushed {
  position: fixed;

}
#phoneMenu .wrapper.pushed:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 70px;
  z-index: 1000;
  background-color: rgb(39, 39, 39, .5);
}

#phoneMenu .menuTrigger {
  display: flex;
  align-items: center;
}

@media (min-width: 992px) {
  #phoneMenu .menuTrigger {
    display: none;
  }
}

@media (max-width: 991px) {
  #phoneMenu nav.panel:not(.proInfoPanel).isOpen {
    transform: translateX(-100%);
    transition: transform 0.3s linear;
  }
}

#phoneMenu .closeSubPanel,
#phoneMenu .closePanel,
#phoneMenu .openSubPanel {
  cursor: pointer;
}

/* nav.panel {
  display: none;
} */

@media (max-width: 991px) {
  nav.panel {
    display: block;
  }
}
@media (min-width: 992px) {
  #phoneMenu nav.panel.isOpen {
    transform: translateX(30rem);
  }
}
.wrapper {
  width: 100%;
}

/* #phoneMenu end */

/* bannerOwlCarouselRow start */

.bannerOwlCarouselRow .item a {
  display            : block;
  width              : 100%;
  background-repeat  : no-repeat;
  background-size    : contain;
  background-position: center;
}

.bannerOwlCarouselRow .item h2 {
  position        : absolute;
  top             : 0px;
  left            : 0px;
  background-color: rgba(51, 51, 51, 0.6);
  color           : #fff;
  padding         : .5rem 1.2rem;
  font-size       : 1.25rem;
}

.bannerOwlCarouselRow {
  position: relative;
}

.suggestProductBox .owl-dots{
  bottom  : -40px;
}

.suggestProductBox .owl-item {
  padding: 0 15px;
}
 .owl-dots {
  position: absolute;
  bottom  : 10px;
  left:50%;
  transform: translateX(-50%);
  margin  : 0px;
}

.owl-theme .owl-nav.disabled+.owl-dots {
  margin: 0px;
}

.owl-theme .owl-dots .owl-dot span {
  border-radius: 50%;
  margin       : 0px 6px;
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
  background: var(--link);
}

/* bannerOwlCarouselRow end */
/* productListRow start */

@media (min-width: 577px) {
  .bannerOwlCarouselRow .item h2 {
    font-size: 2.25rem;
  }

}

@media (max-width: 320px) {

  .productListRow i.dpNone {
    display: none;
  }
}

.productListRow .contentBox .item>a {
  display        : block;
  width          : 100%;
  text-decoration: none;
}



 /* .productListRow .owl-carousel .owl-nav {
  display: none;
}

@media (min-width: 577px) {
  .productListRow .owl-carousel:hover .owl-nav {
    display: block;
  }
}  */





.productListRow .owl-item .textBox {
  margin-bottom: 1rem;
}

.productListRow .owl-item .textBox a{
  font-family: "Noto Serif TC", serif;
  font-size:20px;
  font-weight:bold;
  color:var(--main-color);
}

.productListRow .owl-item .textBox>div {
  display    : flex;
  align-items: flex-end;
  /* justify-content: center; */
}

.productListRow .owl-item .textBox span.more {
  font-size       : 1.25rem;
  color           : #fff;
  width           : 25px;
  height          : 25px;
  display         : inline-flex;
  align-items     : center;
  justify-content : center;
  background-color: #333333;
}

.productListRow .owl-item .textBox span.more a {
  text-decoration: none;
  display        : block;
  color          : #fff;
}

@media (max-width: 576px) {
  .productListRow .contentBox .item .textBox h3 {
    margin-bottom: .5rem;
  }

  .productListRow span.originalPrice {
    display: block;
    height : 18px;
  }
}


/* productListRow end */


/* productBranchRow start */


.productBranchRow .no-touch .productBranch-tab-nav {
  padding-bottom: 10px;
}

.productBranchRow .productBranch-tab-nav li {
  color      : #777777;
  white-space: nowrap;
  cursor     : pointer;
  height     : 100%;
}

.productBranchRow .productBranch-viewport {
  overflow-x: scroll;
  width     : 100%;
}

.productBranch-viewport::-webkit-scrollbar {
  display: none;
}

.productBranchNav .productBranch-viewport {
  -ms-overflow-style: none;
}

.productBranch-viewport {
  overflow-x                : auto;
  white-space               : nowrap;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style        : -ms-autohiding-scrollbar;
}

.productBranchRow h2.chText {
  font-size  : 2rem;
  color      : #ff7300;
  font-weight: 500;
  display    : inline-block;
  position   : relative;
  word-wrap  : break-word;
  word-break : break-all;
}

.productBranchRow h2.chText a {
  font-size      : 2rem;
  color          : #ff7300;
  font-weight    : 500;
  display        : inline-block;
  position       : relative;
  word-wrap      : break-word;
  word-break     : break-all;
  text-decoration: none;
}

.productBranchRow h2.chText:before,
.productBranchRow h2.chText:after {
  content   : '';
  position  : absolute;
  top       : 50%;
  background: #ff7300;
  width     : 60px;
  height    : 4px;
}

.productBranchRow h2.chText:before {
  left: -75px;
}

.productBranchRow h2.chText:after {
  right: -75px;
}

@media (max-width: 767px) {

  .productBranchRow h2.chText:before,
  .productBranchRow h2.chText:after {
    width : 35px;
    height: 3px;
  }

  .productBranchRow h2.chText:before {
    left: -50px;
  }

  .productBranchRow h2.chText:after {
    right: -50px;
  }
}

.productBranchRow p.subtitle {
  font-size    : 1.125rem;
  color        : #777;
  text-align   : center;
  margin-bottom: 12px;
}

.productBranchRow .productBranch-tab-nav li a {
  text-decoration: none;
  display        : block;
  width          : 100%;
  height         : 100%;
  padding        : 0px 10px;
  display        : flex;
  align-items    : center;
}

@media screen and (max-width: 991px) {
  .productBranchRow .productBranch-tab-nav li a {
    padding: 0px .75rem;
  }
}

.productBranchRow .productBox a.mainProImg {
  display        : block;
  width          : 100%;
  height         : 100%;
  text-decoration: none;
}

.productBranchRow .productBox a.mainProImg>div {
  width              : 100%;
  padding-bottom     : 100%;
  background-repeat  : no-repeat;
  background-size    : contain;
  background-position: center;
  background-color   : #fff;
  position           : relative;
}

.productBranchRow .productBox a.mainProImg span.amount {
  position        : absolute;
  bottom          : 12px;
  left            : -12px;
  background-color: rgba(51, 51, 51, .8);
  padding         : .5rem .75rem;
  color           : #fff;
}

.productBranchRow .productBox a.secondaryProImg {
  display        : block;
  width          : 100%;
  height         : 100%;
  padding-bottom : 15%;
  position       : relative;
  text-decoration: none;
}

.productBranchRow .productBox a.secondaryProImg>div {
  width                  : 100%;
  padding-bottom         : 85%;
  background-repeat      : no-repeat;
  -webkit-background-size: contain;
  -moz-background-size   : contain;
  -o-background-size     : contain;
  background-size        : contain;
  background-position    : center;
  background-color       : #fff;
}

.productBranchRow .productBox a.secondaryProImg span.amount {
  position  : absolute;
  bottom    : 0px;
  width     : 100%;
  text-align: center;
  font-size : 1rem;
  color     : #333;
}

/* productBranchRow end */
/* footer start*/
footer {
  /* overflow        : hidden; */
  background-color: var(--title);
  padding         : 2rem 0px ;
  color           : #ffffff;
  word-wrap       : break-word;
  word-break      : break-all;
  position:relative;
}
/* footer::before{
  content:"";
  position:absolute;
  background-image:url('/public/static/index/img/footer-top.png');
  background-repeat:no-repeat;
  background-size:contain;
  background-position:center;
  width:90px;
  height:70px;
  top:-35px;
  left:50%;
  transform: translateX(-50%);
  z-index:1;
} */
 footer p,
footer a {
  font-size      : 1rem;
  line-height    : 1.55rem;
  color : #ffffff;
  text-decoration: none;
}

footer h4 {
  font-size    : 1.15rem;
  margin-bottom: 12px;
  font-family: "Noto Serif TC", serif;
  color:var(--link);
}

footer img {
  width : auto;
  height: auto;
}

footer>div.pdSpacing {
  padding: 0px 12px;
}

footer div.mbSpacing {
  margin-bottom: 12px;
}

footer .row {
  row-gap:1.2rem;
}

footer .col-md-3 {
  padding-right: 12px;
  padding-left : 12px;
}
@media screen and (min-width: 992px) {
    footer{
      padding : 5rem 0px 1rem;
    }
}
/* footer end*/

/* product Brand All start////////////////////////////////////////////////////*/


/* ///////////////////////// */

.productPublic .cardBox ul.navList {
  margin: 0px;
  padding-left:12px;
  border-bottom:1px solid var(--title);
}


/* -------------- */
.innerPageBox .item>a {
  display        : block;
  width          : 100%;
  text-decoration: none;
}

.innerPageBox .owl-carousel.proImgCarousel {
  position: relative;
  overflow: hidden;
}

.innerPageBox .owl-nav {
  position: absolute;
  width   : 100%;
  top     : 50%;
  bottom  : 0px;
  height  : 0px;
  margin  : 0px;
}

.innerPageBox .owl-carousel.proImgCarousel .owl-nav button.owl-next {
  position       : absolute;
  right          : -30px;
  top            : -30px;
  font-size      : 2rem;
  margin         : 0px;
  width          : 60px;
  height         : 60px;
  background     : #333;
  border-radius  : 100%;
  color          : #fff;
  display        : flex;
  align-items    : center;
  justify-content: center;
}

.innerPageBox .owl-carousel.proImgCarousel .owl-nav button.owl-next .icon-right {
  position : relative;
  left     : -13px;
  font-size: 22px;
}

.innerPageBox .owl-carousel.proImgCarousel .owl-nav button.owl-prev {
  position       : absolute;
  left           : -30px;
  top            : -30px;
  font-size      : 2rem;
  margin         : 0px;
  width          : 60px;
  height         : 60px;
  background     : #333;
  border-radius  : 100%;
  color          : #fff;
  display        : flex;
  align-items    : center;
  justify-content: center;
}

.innerPageBox .owl-carousel.proImgCarousel .owl-nav button.owl-prev .icon-left {
  position : relative;
  right    : -13px;
  font-size: 22px;
}

.innerPageBox .owl-carousel.proImgCarousel .owl-nav {
  display: none;
}

@media (min-width: 577px) {
  .innerPageBox .owl-carousel.proImgCarousel:hover .owl-nav {
    display: block;
  }
}

.innerPageBox .item .textBox h3 {
  font-size    : 1.375rem;
  color        : var(--main-color);
  text-overflow: ellipsis;
  overflow:hidden;
  display:-webkit-box;
  -webkit-line-clamp:2;
  -webkit-box-orient:vertical;
  margin-bottom: .75rem;
}

@media (max-width: 576px) {
  .innerPageBox .owl-carousel.proImgCarousel.owl-drag .owl-item {
    padding: 6px;
  }
}

.innerPageBox .owl-item .textBox {
  margin-bottom: 1rem;
}

.innerPageBox .owl-item .textBox span.more {
  font-size       : 1.25rem;
  color           : #fff;
  width           : 25px;
  height          : 25px;
  display         : inline-flex;
  align-items     : center;
  justify-content : center;
  background-color: #333333;
}

.innerPageBox .owl-item .textBox a.more {
  font-size       : 1.25rem;
  color           : #fff;
  width           : 25px;
  height          : 25px;
  display         : inline-flex;
  align-items     : center;
  justify-content : center;
  background-color: #333333;
}

/* .innerPageBox span.originalPrice {
display: block;
height: 18px;
margin-bottom: 5px;
} */


/* -------------- */
.popularProBranch div.row.mgSpacing {
  margin-right: 6px;
  margin-left : 6px;
}

.popularProBranch .recommendBox {
  padding-left : 2px;
  padding-right: 2px;
  margin-bottom: 12px;
}

.popularProBranch .recommendBox div.img {
  display            : block;
  width              : 100%;
  padding-bottom     : 33.3%;
  background-repeat  : no-repeat;
  background-size    : contain;
  background-position: center;
  margin-bottom      : 6px;
  background-color   : #777777;
}

.popularProBranch .recommendBox>div {
  padding: 0px 12px;
}

.popularProBranch .recommendBox>div p {
  font-size    : 1rem;
  color        : #777;
  margin-bottom: 6px;
}


/* product Brand All end////////////////////////////////////////////////////*/
/* product Brand List end////////////////////////////////////////////////////*/
.row-pro {
  margin-right: -6px;
  margin-left : -6px;
}

.col-12-pro,
.col-12-pro,
.col-1200-4-pro,
.col-1440-3-pro {
  padding-left : 6px;
  padding-right: 6px;
}

@media (min-width: 0px) {
  .col-12-pro {
    -ms-flex : 0 0 100%;
    flex     : 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 411px) {
  .col-414-6-pro {
    -ms-flex : 0 0 50%;
    flex     : 0 0 50%;
    max-width: 50%;
  }
}

@media (min-width: 1200px) {
  .col-1200-4-pro {
    -ms-flex : 0 0 33.333333%;
    flex     : 0 0 33.333333%;
    max-width: 33.333333%;
  }
}

@media (min-width: 1440px) {
  .col-1440-3-pro {
    -ms-flex : 0 0 25%;
    flex     : 0 0 25%;
    max-width: 25%;
  }
}

/* product Brand List end////////////////////////////////////////////////////*/
/* product intro start */


.innerPageBox .productIntroBox .owl-carousel.owl-drag .owl-item {
  background-color: initial;
  padding         : 0px;
}

.productIntroBox.arrow .owl-carousel:hover .owl-nav {
  display: none;
}

/* //////////////////////////////// */
div.cs-select {
  position             : relative;
  z-index              : 100;
  display              : inline-block;
  width                : 100%;
  /* max-width         : 500px; */
  -webkit-user-select  : none;
  -moz-user-select     : none;
  -ms-user-select      : none;
  user-select          : none;
  text-align           : left;
  vertical-align       : middle;
  background           : #fff;
  -webkit-touch-callout: none;
  -khtml-user-select   : none;
}

div.cs-select:focus {
  outline: none;
  /* For better accessibility add a style for this in your skin */
}

.cs-select select {
  display: none;
}

.cs-select span {
  position     : relative;
  display      : block;
  overflow     : hidden;
  padding      : .75rem;
  cursor       : pointer;
  white-space  : nowrap;
  text-overflow: ellipsis;
}

/* Placeholder and selected option */
.cs-select>span {
  padding-right: 3em;
}

.cs-select>span::after,
.cs-select .cs-selected span::after {
  position               : absolute;
  top                    : 50%;
  -webkit-transform      : translateY(-50%);
  transform              : translateY(-50%);
  speak                  : none;
  -webkit-font-smoothing : antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.cs-select>span::after {
  right  : 1em;
  content: '\25BE';
}

.cs-select .cs-selected span::after {
  margin-left: 1em;
  content    : '\2713';
}

.cs-select.cs-active>span::after {
  -webkit-transform: translateY(-50%) rotate(180deg);
  transform        : translateY(-50%) rotate(180deg);
}

div.cs-active {
  z-index: 200;
}

/* Options */
.cs-select .cs-options {
  position  : absolute;
  visibility: hidden;
  overflow  : hidden;
  width     : 100%;
  background: #fff;
}

.cs-select.cs-active .cs-options {
  visibility: visible;
}

.cs-select ul {
  width     : 100%;
  margin    : 0;
  padding   : 0;
  list-style: none;
}

.cs-select ul span {
  padding: .75rem;
}

.cs-select ul li.cs-focus span {
  background-color: #ddd;
}

/* Optgroup and optgroup label */
.cs-select li.cs-optgroup ul {
  padding-left: 1em;
}

.cs-select li.cs-optgroup>span {
  cursor: default;
}

div.cs-skin-elastic {
  font-size     : 1rem;
  /* font-weight: 700; */
  color         : #777;
  background    : transparent;
  border        : 1px solid #ededed;
}

@media screen and (max-width: 30em) {
  div.cs-skin-elastic {
    font-size: 1em;
  }
}

.cs-skin-elastic>span {
  z-index         : 100;
  background-color: #fff;
}

.cs-skin-elastic .cs-options {
  visibility    : visible;
  overflow      : visible;
  pointer-events: none;
  opacity       : 1;
  background    : transparent;
}

.cs-skin-elastic.cs-active .cs-options {
  pointer-events: auto;
}

.cs-skin-elastic .cs-options>ul::before {
  position                : absolute;
  top                     : 0;
  left                    : 0;
  width                   : 100%;
  height                  : 100%;
  content                 : '';
  -webkit-transition      : -webkit-transform .3s;
  transition              : transform .3s;
  -webkit-transform       : scale3d(1, 0, 1);
  transform               : scale3d(1, 0, 1);
  -webkit-transform-origin: 50% 0;
  transform-origin        : 50% 0;
  background              : #fff;
}

.cs-skin-elastic.cs-active .cs-options>ul::before {
  -webkit-transition: none;
  transition        : none;
  -webkit-transform : scale3d(1, 1, 1);
  transform         : scale3d(1, 1, 1);
  -webkit-animation : expand .6s ease-out;
  animation         : expand .6s ease-out;
}

.cs-skin-elastic .cs-options ul li {
  -webkit-transition: opacity .15s, -webkit-transform .15s;
  transition        : opacity .15s, transform .15s;
  -webkit-transform : translate3d(0, -25px, 0);
  transform         : translate3d(0, -25px, 0);
  opacity           : 0;
}

.cs-skin-elastic.cs-active .cs-options ul li {
  -webkit-transition: none;
  transition        : none;
  -webkit-transform : translate3d(0, 0, 0);
  transform         : translate3d(0, 0, 0);
  -webkit-animation : bounce .6s ease-out;
  animation         : bounce .6s ease-out;
  opacity           : 1;
  background-color  : #ededed;
  color             : #777;
}

.cs-skin-elastic .cs-options span {
  background-repeat  : no-repeat;
  background-position: 1.5em 50%;
  background-size    : 2em auto;
}

.cs-skin-elastic .cs-options span:hover,
.cs-skin-elastic .cs-options li.cs-focus span,
.cs-skin-elastic .cs-options .cs-selected span {
  color: #333;
}

.cs-skin-elastic .cs-options .cs-selected span::after {
  content: '';
}

@-webkit-keyframes expand {
  0% {
    -webkit-transform: scale3d(1, 0, 1);
  }

  25% {
    -webkit-transform: scale3d(1, 1.2, 1);
  }

  50% {
    -webkit-transform: scale3d(1, .85, 1);
  }

  75% {
    -webkit-transform: scale3d(1, 1.05, 1);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
  }
}

@keyframes expand {
  0% {
    -webkit-transform: scale3d(1, 0, 1);
    transform        : scale3d(1, 0, 1);
  }

  25% {
    -webkit-transform: scale3d(1, 1.2, 1);
    transform        : scale3d(1, 1.2, 1);
  }

  50% {
    -webkit-transform: scale3d(1, .85, 1);
    transform        : scale3d(1, .85, 1);
  }

  75% {
    -webkit-transform: scale3d(1, 1.05, 1);
    transform        : scale3d(1, 1.05, 1);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform        : scale3d(1, 1, 1);
  }
}

@-webkit-keyframes bounce {
  0% {
    -webkit-transform: translate3d(0, -25px, 0);
    opacity          : 0;
  }

  25% {
    -webkit-transform: translate3d(0, 10px, 0);
  }

  50% {
    -webkit-transform: translate3d(0, -6px, 0);
  }

  75% {
    -webkit-transform: translate3d(0, 2px, 0);
  }

  100% {
    -webkit-transform: translate3d(0, 0, 0);
    opacity          : 1;
  }
}

@keyframes bounce {
  0% {
    -webkit-transform: translate3d(0, -25px, 0);
    transform        : translate3d(0, -25px, 0);
    opacity          : 0;
  }

  25% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform        : translate3d(0, 10px, 0);
  }

  50% {
    -webkit-transform: translate3d(0, -6px, 0);
    transform        : translate3d(0, -6px, 0);
  }

  75% {
    -webkit-transform: translate3d(0, 2px, 0);
    transform        : translate3d(0, 2px, 0);
  }

  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform        : translate3d(0, 0, 0);
    opacity          : 1;
  }
}

.selectBox {
  margin-bottom: 1rem;
}

/* //////////////////////////////// */
/* ///////// select end   ///////// */
/* //////////////////////////////// */
.spinnerBox .spinner * {
  text-align: center;
}

.spinnerBox .spinner input {
  pointer-events  : none;
  background-color: white !important;
}

.spinnerBox .spinner input::-webkit-outer-spin-button,
.spinnerBox .spinner input::-webkit-inner-spin-button {
  margin            : 0;
  -webkit-appearance: none;
}

.spinnerBox .input-group {
  width: initial;
}

.spinnerBox button {
  border-radius  : 0px;
  display        : flex;
  align-items    : center;
  justify-content: center;
}



/* product intro end */



/* about  end*/
/* ///////////////////////////////////////// */
/* ///////////////////////////////////////// */



/* memberInforBox start*/
#itemBox.memberInforBox.examinee_box {flex-flow: column nowrap;}

@media screen and (max-width : 767px) {
  #itemBox.memberInforBox {
    flex-flow: column nowrap;
    padding  : 0px 1.25rem;
  }

  .memberInforBox #leftBox {
    display: block;
    flex   : 1 1 auto;
  }

  .membeMwnu {
    display: none;
  }
}

/* .memberInforBox .memberNumberBox {
background-color: #ededed;
padding: .75rem 1rem;
display: flex;
align-items: center;
margin-bottom: .75rem;
}
.memberInforBox .memberNumberBox .photo {
width: 60px;
height: 60px;
min-width: 60px;
background-color: #fff;
display: inline-block;
border-radius: 100%;
display: flex;
align-items: center;
justify-content: center;
margin-right: .75rem;
}
.memberInforBox .memberNumberBox .photo i {
font-size: 32px;
color: #333333;
}
.memberInforBox .memberNumberBox h3 {
font-size: 1.125rem;
color: #333333;
}
.memberInforBox .memberNumberBox p.memberName {
font-size: 1.125rem;
color: #777777;
} */

#processModel .modal-content {
  border-radius: 0px;
}

.shoppingCartBox a.send_btn {
  background-color: #777;
  padding         : .5rem 1.25rem;
  display         : inline-block;
  text-align      : center;
  color           : #fff;
}

.shoppingCartBox a.send_btn:hover {
  background-color: #333;
  color           : #fff;
}

@media (min-width: 992px) {
  .modal-dialog {
    max-width: 670px;
  }
  #regiModal .modal-dialog {
    max-width: 80%;
  }
}

#chooseOfferModel .modal-content {
  border-radius: 0px;
}

/* memberInforBox end*/
/* /////////////// */
/* orderTable start */

.orderTable.table {
  border-collapse: separate;
  padding-bottom: 1rem;
  border: none;
  /* border-bottom         :1px solid #eaeaea; */
}
.orderTable.table .num{padding: 3px;}
.shoppingCartBox .selected_total{font-size: 1.5rem;font-weight: 600;}
.orderDetailsBox form:not(:last-child) .orderTable.table{
  border-bottom: 1px solid #eaeaea;
}



.orderTable.table td,
.orderTable.table th {
  padding: .5rem;
  vertical-align: middle;
}
.orderTable.table td.price{font-size: 1.125rem;}
.orderTable.table td.offerPrice{ color: #aaa; text-decoration: line-through;}
.orderTable.table-bordered td,
.orderTable.table-bordered th {
  border: none;
}

.orderTable.table thead th {
  border: none;
}

/* .orderTable.table-striped tbody tr:nth-of-type(odd) {
  background-color: inherit;
} */

/* orderTable end */
.orderDetailsBox .smallProImg,
.orderDetailsBox.shoppingCartBox .smallProImg {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-repeat  : no-repeat;
  background-size    : contain;
  background-position: center;
  width              : 100px;
  height             : 100px;
  min-width          : 100px;
  min-height         : 100px;
  border             : 1px solid #e5e5e5;
}

@media (max-width: 1280px) {
  .couponBox .transferBox {
    margin-top: .5rem;
  }

  .couponBox .transferBox input {
    background: #ededed;
  }

  .couponBox .transferBox input {
    background: #ededed;
  }
}

.paginationBox{
  margin: 2.5rem 0;
}

/* /////////////////////////////////////////////// */
/* registration Model strat */
.smallMOdel .modal-content {
  border-radius: 0px;
}

@media (min-width: 992px) {
  .smallMOdel .modal-dialog {
    max-width: 576px;
  }
}

.lightModel .custom-file-label::after {
  content: "瀏覽";
}

.memberContentBox.registeredBox {
  background: #ededed;
  padding   : 3rem;
}

/* registration Model end */
/* shoppingCart start*/
.shoppingCartBox :focus {
  outline: none;
}

.shoppingCartBox .orderTable.orderTable a.deleteBtn {
  font-size      : 1.25rem;
  color          : #333;
  text-decoration: none;
}

.examinee_table{
  width: max-content;
  min-width: 100%;
  max-width: unset;
  border-collapse: inherit;
  border-spacing: 0px;
  word-break: break-all;
}
/* shoppingCart end*/

/* social-bar start */
  .social-bar {
    position        : fixed;
    right           : 0;
    top             : 50%;
    width: 40px;
    height: 200px;
    z-index         : 100;
    transform       : translateY(-50%);
  }
  .check-menu .hb {
    margin-bottom   : 0;
    display         : block;
    cursor          : pointer;
    background-color: var(--sub-color);
    color           : #fff;
    text-align      : center;
    width: 20px;
    height: 50px;
    display: flex;
    align-items: center;
    padding: 6px;
    border-radius: 5px 0 0 5px;
    position: absolute;
    right: 0;
    font-size       : 1rem;
  }
  .fixed-menu {
    opacity   : 0;
    position  : absolute;
    right     : -100%;
    visibility: hidden;
    overflow  : hidden;
    cursor    : pointer;
    transition: all 0.2s ease-in; 
  }

  #menu-link.active~.fixed-menu,
  #menu-link:checked~.fixed-menu {
    opacity   : 1;
    width     : 50px;
    left      : 5px;
    margin-bottom: 0;
    visibility: visible;
  }
  #menu-link:checked~ .check-menu .hb{right: 35px; transition: all 1s;}

  #menu-link {
    display: none;
  }

  .social-bar__item {
    padding                   : 6px 20px 6px 6px;
    color                     : white;
    font-size                 : 1.5rem;
    text-align                : center;
    transition-duration       : 100ms;
    transition-timing-function: ease-in-out;
  }

  .social-bar__fb {
    background-color: #3b5999;
  }
  .social-bar__line {
    background-color: #00c200;
  }
  .social-bar__mailbox {
    background-color: #ff4300;
  }
  .social-bar__phone {
    background-color: #ff7300;
  }
  .social-bar__twitter {
    background-color: #55acee;
  }
  .social-bar__linkedin {
    background-color: #0077B5;
  }
  .social-bar__instagram {
    background-color: #e4405f;
  }
  .social-bar__printerest {
    background-color: #bd081c;
  }
  @media (min-width: 992px) {
   .check-menu .hb{display: none;}
   .fixed-menu{opacity: 1;visibility: visible; right: -15px;}
  }
/* social-bar end */

/* POP廣告 start */
/* 側邊廣告 start */
.adSideBox {
  position: fixed;
  top: 50%;
  left: 50%;
 
  
  cursor: pointer;
  transform: translate(-50% , -50%);
}
.adSidebg {
  
  background: rgba(0, 0, 0, 0.212);
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0; z-index:5000;
 
}

.adSideBox:hover {
  color: #ffffff;
}

.adSideBox a.adSide img {
  width: auto;
  height: 70vh;
  min-height: 400px;
}

.adSideBox a.closeAdSide {
  position: absolute;
  top: 25px;
  transform: translateY(-50%);
  right: 0;
  display: block;
  background-color: #333333;
  color: #fff;
  writing-mode: vertical-rl;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.adSideBox img {
  max-height: 70vh;
  width: auto;
}

.adSideBox img.closed {
  display: none;
  
}
.adSideBox .closeAdSide svg {
  fill: #fff;
  width: 25px;
  height: 25px;
}


 
/* POP廣告 end */

/* //////////////////////////////////////////////////// */
.paginationBox .boxCenter {
  text-align: center;
}

.paginationBox ul.pagination {
  margin : 0px;
}

.paginationBox ul.pagination li {
  padding: 0.1rem;
  color          : var(--main-color);
  /* border : 1px solid #ededed; */
}

.paginationBox ul.pagination li a {
  text-decoration: none;
  color          : var(--main-color);
  font-size      : 1rem;
}

.paginationBox ul.pagination li.active {
  color    : var(--link);
  font-size: 1rem;
}

#qaModel .question .qaIcon,
#qaModel .answer .qaIcon {
  display        : flex;
  align-items    : center;
  justify-content: center;
}

#qaModel .question .qaIcon span.num,
#qaModel .answer .qaIcon span.num {
  color      : #fff;
  font-family: 'Open Sans';
  font-weight: 500;
  font-size  : 12px;
}

#qaModel .question .qaIcon span.num span.letter,
#qaModel .answer .qaIcon span.num span.letter {
  color       : #fff;
  font-family : 'Open Sans';
  font-weight : 700;
  display     : inline-block;
  margin-right: 3px;
  font-size   : 1.25rem;
}

ul.strongholdNav li a {
  color          : #000;
  text-decoration: none;
}

ul.strongholdNav li.active a {
  color: #ff6d00;
}

#leftBox .icon-member.mr {
  margin-right: .75rem;
}

/* //////////////////////////////////////////////////// */
#carousel01 .owl-dots {
  display: none;
}

#tab5 .table thead {
  background-color: #fff;
}

#tab5 .table td,
.table th {
  padding       : .5rem;
  vertical-align: initial;
  border        : none;
}

#proIntroTab iframe {
  width: 100%
}

/* //////////////////////////////////////////////////// */


/* ///////////////////////////////////////// */
.strongholdBox ul.strongholdNav {
  margin-bottom: 1rem;
}

.strongholdBox ul.strongholdNav a {
  padding  : .5rem 1.25rem;
  font-size: 1.15rem;
  color    : #777;
}

.couponBox .couponList.proItem div.textBox h3 {
  margin-bottom: .25rem;
}

.couponBox .couponList.proItem span.time {
  font-size    : 12px;
  color        : #777;
  display      : inline-block;
  margin-bottom: .25rem;
}

.couponBox .couponList.proItem div.textBox>div {
  align-items: inherit;
}

.couponBox .thirdPartyLinkBox ul li {
  display     : inline-block;
  margin-right: .5rem;
}

.couponBox .thirdPartyLinkBox ul li a {
  text-decoration: none;
  color          : #777;
  font-size      : 1.75rem;
}

.couponBox div.numBox input {
  border-radius: 0px;
}

.couponBox .returnBox a.returnBtn {
  text-decoration : none;
  color           : #777;
  display         : inline-block;
  background-color: #ededed;
  color           : #333333;
  padding         : .5rem 1.25rem;
}

.couponBox .returnBox a.returnBtn:hover {
  background-color: #333;
  color           : #fff;
}


@media (max-width: 991px) {

  a.goTop {
    bottom: 6.8rem;
  }

  .productIntroBox .bottomBox .mbSpacing {
    margin-bottom: 0px;
  }
}

#phoneMenu nav.panel ul li ul .prevSubPanel,
#phoneMenu nav.panel ul li ul .prevSubPanel:hover {
  color: #676767;
}