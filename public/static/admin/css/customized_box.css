.row.selected-items{
    row-gap:2rem;
}

.row.selected-items .item{
    padding:18px;
    border:1px solid #d9d9d9;
    border-radius: 5px;
    display:flex;
    flex-direction:column;
    align-items:center;
    row-gap:0.8rem;
}

.row.selected-items .item .img{
    width:100%;
    background-repeat:no-repeat;
    background-size:contain;
    background-position:center;
    padding-bottom:100%;
}

button{
    border:none;
    cursor:pointer;
}

button:focus{
    outline:none;
}

.boxModal .modal-header{
    position:relative;
  display:flex;
  justify-content: center; 
}

.boxModal h5{
    color:#000000;
    font-weight:bold; 
}

.boxModal.new h5{
    background-color:rgba(0,0,0,.03);
    padding:0.5rem;
}

.boxModal .close{
    position:absolute;
    right:15px;
    opacity:1;
    font-weight:bold; 
}

.boxModal .card-header{
    padding:0;
}

.boxModal h2{
    padding:0.5rem;
}

.boxModal .modal-footer{
    justify-content:center;
}

.boxModal .accordion-btn{
    background-color:transparent;
    position:relative;
    padding:0;
    display:flex;
    justify-content: space-between;
}

.boxModal .accordion-btn::after{
    content: "\F282";
    font-family:"bootstrap-icons";
    transition: transform .2s ease-in-out;
}

.boxModal .accordion-btn:not(.collapsed)::after {
    transform: rotate(-180deg);
}

.custom-file-label::after {
    content:"瀏覽";
} 

.custom-file-input:focus~.custom-file-label{
    border-color:#ced4da;
    box-shadow:none;
}

.modal-left{
    min-height:300px;
    display:flex;
    justify-content: center;
    align-items:center;
    border:1px solid #ced4da;
}


.modal-left .img-preview{
    width:100%;
    padding-bottom:100%;
    background-repeat:no-repeat;
    background-size:contain; 
    background-position:center;
    position:relative;
}


.img-preview .draggable-box{
    cursor: move;
    user-select: none;
    touch-action: none;
    position: absolute;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid #000;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}


.img-preview .box span{
    font-size:18px;
}


.added-item:not(:last-child){
    margin-bottom:1rem;
}
.added-item .box{
    border:1px solid #ced4da;
    padding:0.5rem;
    display:flex;
    flex-wrap:wrap;
    column-gap:0.5rem;
    border-radius:5px;
}

.added-item .box button{
    border-radius:5px;
    border:1px solid #ced4da;
}

.added-item .box .remove{
    background-color:transparent;
}

.item-row{
    display:flex;
    flex-wrap:wrap;
    align-items:center;
    column-gap:0.5rem;
    margin-bottom:2rem;
}

.item-row .item{
    padding:0.2rem 0.5rem;
    border-radius:5px;
    border:1px solid #ced4da;
    display:flex;
    align-items:center;
    column-gap:5px;
}

.layout_img_file{
    color:#ffffff;
    background-color:#000000;
    border-radius:5px;
    padding:5px 8px;
}

.layout_img_file input{
    display:none;
}