Options -Indexes

## 強制跳轉https 
RewriteCond %{HTTPS} off
#本機不強制
RewriteCond %{HTTP_HOST} !^localhost(:[0-9]+)?$
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Hide a specific file
<Files .env>
  Order allow,deny
  <PERSON><PERSON> from all
</Files>

<IfModule mod_rewrite.c>
	RewriteEngine on
	
	RewriteCond %{REQUEST_URI} !^/public/
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule ^(.*)$ /public/$1
	RewriteRule ^(/)?$ /public/index.php [L]
</IfModule>