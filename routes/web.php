<?php

use Illuminate\Support\Facades\Route;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

if (!defined('__APP__')) {
    define('__APP__', 'App\Http\Controllers');
}

Route::get('/', __APP__ . '\home\IndexController@index');
Route::prefix('index/index')->group(function () {
    Route::get('/index', __APP__ . '\home\IndexController@index');
    Route::post('/subscripe', __APP__ . '\home\IndexController@subscripe');
});
Route::prefix('index/ajax')->group(function () {
    Route::get('/offer_nav_menu', __APP__ . '\home\Ajax@offerNavMenu');
    Route::post('/product_menu', __APP__ . '\home\Ajax@productMenu');
    Route::post('/get_prod_aside_menu', __APP__ . '\home\Ajax@getProdAsideMenu');
    Route::get('/newslink', __APP__ . '\home\Ajax@newslink');
    Route::post('/chpwd', __APP__ . '\home\Ajax@chpwd');
    Route::post('/ckaccount', __APP__ . '\home\Ajax@ckaccount');
    Route::post('/prod_all_qa', __APP__ . '\home\Ajax@prodAllQa');
    Route::post('/prod_qa_create', __APP__ . '\home\Ajax@prodQaCreate');
    Route::get('/close_ad_side', __APP__ . '\home\Ajax@closeAdSide');
    Route::post('/love_record', __APP__ . '\home\Ajax@love_record');
    Route::post('/store_record', __APP__ . '\home\Ajax@store_record');
    Route::get('/icon', __APP__ . '\home\Ajax@icon');
    Route::post('/base64_ajax', __APP__ . '\home\Ajax@base64_ajax');
});
Route::prefix('index/product')->group(function () {
    Route::get('/product', __APP__ . '\home\Product@product');
    Route::get('/typeinfo', __APP__ . '\home\Product@typeinfo');
    Route::get('/distributor', __APP__ . '\home\Product@distributor');
    Route::get('/activity', __APP__ . '\home\Product@activity');
    Route::get('/search', __APP__ . '\home\Product@search');
    Route::get('/coupon', __APP__ . '\home\Product@coupon');
    Route::get('/couponinfo', __APP__ . '\home\Product@couponinfo');
    Route::get('/productinfo_one', __APP__ . '\home\Product@productinfo_one');
    Route::get('/productinfo', __APP__ . '\home\Product@productinfo');
    Route::post('/get_free_coupon', __APP__ . '\home\Product@getFreeCoupon');
    Route::get('/cart', __APP__ . '\home\Product@cart');
    Route::post('/get_reg_prod', __APP__ . '\home\Product@get_reg_prod');
    Route::post('/reg', __APP__ . '\home\Product@reg');
    Route::post('/aj_get_productinfo_and_price', __APP__ . '\home\Product@aj_get_productinfo_and_price');
    Route::get('/customizebox', __APP__ . '\home\Product@customize_box');
});
Route::prefix('index/examination')->group(function () {
    Route::get('/examination', __APP__ . '\home\Examination@examination');
    Route::get('/examinee_list', __APP__ . '\home\Examination@examinee_list');
    Route::post('/examinee_list_table', __APP__ . '\home\Examination@examinee_list_table');
    Route::post('/examinee_panel', __APP__ . '\home\Examination@examinee_panel');
    Route::get('/tracking_registration', __APP__ . '\home\Examination@tracking_registration');
    Route::post('/tracking_registration_view', __APP__ . '\home\Examination@tracking_registration_view');
    Route::post('/examinee_save', __APP__ . '\home\Examination@examinee_save');
    Route::post('/examinee_delete', __APP__ . '\home\Examination@examinee_delete');
    Route::get('/get_fields_by_prod_id', __APP__ . '\home\Examination@get_fields_by_prod_id');
    Route::get('/send_act_remind_mail', __APP__ . '\home\Examination@send_act_remind_mail');
    // Route::get('/personal_show', __APP__.'\home\Examination@personal_show');
});
Route::prefix('index/about')->group(function () {
    Route::get('/about_contact', __APP__ . '\home\About@about_contact');
    Route::post('/do_contact', __APP__ . '\home\About@doContact');
    Route::get('/test_mail', __APP__ . '\home\About@test_mail');
    Route::get('/about_map', __APP__ . '\home\About@about_map');
    Route::get('/about_story', __APP__ . '\home\About@about_story');
});
Route::prefix('index/news')->group(function () {
    Route::get('/news', __APP__ . '\home\News@news');
    Route::get('/news_c', __APP__ . '\home\News@news_c');
});
Route::prefix('index/qa')->group(function () {
    Route::get('/qa', __APP__ . '\home\Qa@qa');
});
Route::prefix('index/login')->group(function () {
    Route::get('/login', __APP__ . '\home\Login@login');
    Route::post('/pagelogin', __APP__ . '\home\Login@pagelogin');
    Route::get('/logout', __APP__ . '\home\Login@logout');
    Route::get('/signup', __APP__ . '\home\Login@signup');
    Route::post('/dosignup', __APP__ . '\home\Login@dosignup');
    Route::post('/order_create_account', __APP__ . '\home\Login@order_create_account');
    Route::get('/signcheck', __APP__ . '\home\Login@signcheck');
    Route::post('/forgot_form', __APP__ . '\home\Login@forgot_form');
    Route::get('/check_forgot', __APP__ . '\home\Login@check_forgot');
    Route::post('/change_forgot', __APP__ . '\home\Login@change_forgot');
    Route::get('/privacy_rule', __APP__ . '\home\Login@privacy_rule');
    Route::get('/g_access_token', __APP__ . '\home\Login@g_access_token');
    Route::post('/g_login', __APP__ . '\home\Login@g_login');
    Route::get('/g_check', __APP__ . '\home\Login@g_check');
    Route::post('/fb_login', __APP__ . '\home\Login@fb_login');
    Route::post('/fb_open', __APP__ . '\home\Login@fb_open');
    Route::post('/line_login', __APP__ . '\home\Login@line_login');
    Route::post('/line_open', __APP__ . '\home\Login@line_open');
    Route::post('/cancel_line_binding', __APP__ . '\home\Login@cancel_line_binding');
    Route::post('/cancel_google_binding', __APP__ . '\home\Login@cancel_google_binding');
    Route::post('/account_open', __APP__ . '\home\Login@account_open');
    Route::post('/town_ajax', __APP__ . '\home\Login@town_ajax');
    Route::post('/zip_ajax', __APP__ . '\home\Login@zip_ajax');
    Route::post('/city_town_ajax', __APP__ . '\home\Login@city_town_ajax');
});
Route::prefix('index/activity')->group(function () {
    Route::get('/activity', __APP__ . '\home\Activity@activity');
});
Route::prefix('index/experience')->group(function () {
    Route::get('/experience', __APP__ . '\home\Experience@experience');
    Route::get('/experience_c', __APP__ . '\home\Experience@experience_c');
});
Route::prefix('index/member')->group(function () {
    Route::get('/member', __APP__ . '\home\Member@member');
    Route::post('/chpwd', __APP__ . '\home\Member@chpwd');
    Route::post('/chdata', __APP__ . '\home\Member@chdata');
    Route::get('/reg_product', __APP__ . '\home\Member@reg_product');
    Route::get('/product_store', __APP__ . '\home\Member@product_store');
    Route::get('/share_content', __APP__ . '\home\Member@share_content');
    Route::post('/update_share_content', __APP__ . '\home\Member@update_share_content');
    Route::get('/down_line', __APP__ . '\home\Member@down_line');
});
Route::prefix('index/kol')->group(function () {
    Route::post('/pagelogin', __APP__ . '\home\Kol@pagelogin');
    Route::get('/logout', __APP__ . '\home\Kol@logout');
    Route::get('/kol_data', __APP__ . '\home\Kol@kol_data');
    Route::get('/sale_record', __APP__ . '\home\Kol@sale_record');
    Route::get('/selling_product', __APP__ . '\home\Kol@selling_product');
    Route::get('/sale_list', __APP__ . '\home\Kol@sale_list');
    Route::get('/sale_detail', __APP__ . '\home\Kol@sale_detail');
    Route::post('/get_sale_period', __APP__ . '\home\Kol@get_sale_period');
    Route::post('/get_sale_record', __APP__ . '\home\Kol@get_sale_record');
    Route::get('/get_selling_product', __APP__ . '\home\Kol@get_selling_product');
});
Route::prefix('index/cart')->group(function () {
    Route::get('/buy_askprice', __APP__ . '\home\Cart@buy_askprice');
    Route::get('/choose_shop', __APP__ . '\home\Cart@choose_shop');
    Route::post('/do_choose_shop', __APP__ . '\home\Cart@do_choose_shop');
    Route::get('/cart_one', __APP__ . '\home\Cart@cart_one');
    Route::post('/cart_one', __APP__ . '\home\Cart@cart_one');
    Route::get('/cart', __APP__ . '\home\Cart@cart');
    Route::post('/cart', __APP__ . '\home\Cart@cart');
    Route::post('/ajax_cart_data', __APP__ . '\home\Cart@ajax_cart_data');
    Route::get('/select_place', __APP__ . '\home\Cart@selectPlace');
    Route::post('/buy', __APP__ . '\home\Cart@buy');
    Route::post('/redirect3_next', __APP__ . '\home\Cart@redirect3Next');
    Route::post('/redirect2_next', __APP__ . '\home\Cart@redirect2Next');
    Route::post('/cart_ctrl', __APP__ . '\home\Cart@cartCtrl');
    Route::post('/add_customize_box_to_cart', __APP__ . '\home\Cart@add_customize_box_to_cart');
    // check_Invoice_Infos
    Route::GET('/invoice_lovecode_check', __APP__ . '\home\Cart@invoice_lovecode_check');
    Route::GET('/invoice_carrier_check', __APP__ . '\home\Cart@invoice_carrier_check');
    Route::GET('/get_current_gift', __APP__ . '\home\Cart@get_current_gift');
});
Route::prefix('index/coupondirect')->group(function () {
    Route::post('/get_discount', __APP__ . '\home\Coupondirect@get_discount');
});
Route::prefix('index/distribution')->group(function () {
    Route::get('/distribution', __APP__ . '\home\Distribution@distribution');
});
Route::prefix('index/consumption')->group(function () {
    Route::get('/create_pay', __APP__ . '\home\Consumption@create_pay');
    Route::post('/do_create_pay', __APP__ . '\home\Consumption@do_create_pay');
    Route::get('/pay_success', __APP__ . '\home\Consumption@pay_success');
    Route::get('/scratch_history', __APP__ . '\home\Consumption@scratch_history');
    Route::get('/scratch', __APP__ . '\home\Consumption@scratch');
    Route::post('/draw_result_save', __APP__ . '\home\Consumption@draw_result_save');
    Route::get('/exchange', __APP__ . '\home\Consumption@exchange');
    Route::post('/get_exchange_gift', __APP__ . '\home\Consumption@get_exchange_gift');
});
Route::prefix('index/coupon')->group(function () {
    Route::get('/coupon', __APP__ . '\home\Coupon@coupon');
    Route::get('/record', __APP__ . '\home\Coupon@record');
    Route::post('/description', __APP__ . '\home\Coupon@description');
    Route::post('/get_coupon_by_number', __APP__ . '\home\Coupon@getCouponByNumber');
    Route::post('/transfor_coupon', __APP__ . '\home\Coupon@transforCoupon');
});
Route::prefix('index/points')->group(function () {
    Route::get('/set_point_expire', __APP__ . '\home\Points@set_point_expire');
    Route::get('/points', __APP__ . '\home\Points@points');
});
Route::prefix('index/buyform')->group(function () {
    Route::get('/buyform', __APP__ . '\home\Buyform@buyform');
});
Route::prefix('index/askprice')->group(function () {
    Route::post('/do_contact', __APP__ . '\home\Askprice@doContact');
    Route::post('/do_ask_again', __APP__ . '\home\Askprice@do_ask_again');
    Route::get('/askprice', __APP__ . '\home\Askprice@askprice');
    Route::get('/get_list', __APP__ . '\home\Askprice@getList');
    Route::get('/get_one', __APP__ . '\home\Askprice@getOne');
});
Route::prefix('index/orderform')->group(function () {
    Route::get('/orderform', __APP__ . '\home\Orderform@orderform');
    Route::get('/history', __APP__ . '\home\Orderform@history');
    Route::get('/orderform_success', __APP__ . '\home\Orderform@orderform_success');
    Route::get('/orderform_c', __APP__ . '\home\Orderform@orderform_c');
    Route::post('/set_report_number', __APP__ . '\home\Orderform@setReportNumber');
    Route::post('/cancel', __APP__ . '\home\Orderform@cancel');
    Route::get('/tracking', __APP__ . '\home\Orderform@tracking');
    Route::post('/order_tracking', __APP__ . '\home\Orderform@orderTracking');
    Route::get('/ajax_logistic_status', __APP__ . '\home\Orderform@ajax_logistic_status');
});
Route::prefix('index/findorder')->group(function () {
    Route::get('/findorder', __APP__ . '\home\Findorder@findorder');
    Route::post('/do_findorder', __APP__ . '\home\Findorder@doFindorder');
});
Route::prefix('index/linelogin')->group(function () {
    Route::get('/call_back', __APP__ . '\home\Linelogin@callBack');
    Route::get('/open', __APP__ . '\home\Linelogin@open');
    Route::get('/consumption_exchange', __APP__ . '\home\Linelogin@consumption_exchange');
    Route::get('/consumption_scratch_history', __APP__ . '\home\Linelogin@consumption_scratch_history');
});
Route::prefix('index/ecreturn')->group(function () {
    Route::post('/returnurl/{id}', __APP__ . '\home\Ecreturn@returnurl');
    Route::post('/returnurl_atm/{id}', __APP__ . '\home\Ecreturn@returnurl_atm');
    Route::post('/returnurl_atm_payment_info/{id}', __APP__ . '\home\Ecreturn@returnurl_atm_payment_info');
});
Route::prefix('index/tspg')->group(function () {
    Route::post('/returnurl/{id}', __APP__ . '\home\Tspg@returnurl');
    Route::get('/check_order', __APP__ . '\home\Tspg@check_order');
});
Route::prefix('index/iine_pay')->group(function () {
    Route::get('/returnurl/{id}/{total}/{order_number}', __APP__ . '\home\LinePay@returnurl');
    Route::get('/check_order', __APP__ . '\home\LinePay@check_order');
});

Route::prefix('ajax/ecpaylogistic')->group(function () {
    Route::get('/create_transport_paper/{id}', __APP__ . '\ajax\Ecpaylogistic@createTransportPaper');
    Route::get('/create_transport_paper/{id}/{home_type}', __APP__ . '\ajax\Ecpaylogistic@createTransportPaper');
    Route::post('/update_status/{id}', __APP__ . '\ajax\Ecpaylogistic@updateStatus');
});
Route::prefix('ajax/hctlogistic')->group(function () {
    Route::get('/create_transport_paper/{id}', __APP__ . '\ajax\Hctlogistic@createTransportPaper');
    Route::get('/check_transport_paper/{id}', __APP__ . '\ajax\Hctlogistic@checkTransportPaper');
    Route::get('/logistic_print_page/{id}', __APP__ . '\ajax\Hctlogistic@logisticPrintPage');
});
Route::prefix('ajax/tcatlogistic')->group(function () {
    Route::get('/create_transport_paper/{id}', __APP__ . '\ajax\Tcatlogistic@createTransportPaper');
    Route::get('/check_transport_paper/{id}', __APP__ . '\ajax\Tcatlogistic@checkTransportPaper');
    Route::get('/logistic_print_page/{id}', __APP__ . '\ajax\Tcatlogistic@logisticPrintPage');
    Route::get('/obt_status', __APP__ . '\ajax\Tcatlogistic@obtStatus');
});
Route::prefix('ajax/invoice_create')->group(function () {
    Route::get('/invoice', __APP__ . '\ajax\InvoiceCreate@invoice');
    Route::get('/print', __APP__ . '\ajax\InvoiceCreate@print');
    Route::post('/create_one', __APP__ . '\ajax\InvoiceCreate@create_one');
});
Route::prefix('ajax/points')->group(function () {
    Route::get('/check_expire', __APP__ . '\ajax\Points@check_expire');
});
Route::prefix('ajax/vip_type')->group(function () {
    Route::get('/auto_set_type', __APP__ . '\ajax\VipType@auto_set_type');
});
Route::prefix('ajax/stats')->group(function () {
    Route::get('/calculate', __APP__ . '\ajax\Stats@calculate');
});
Route::prefix('ajax/holiday')->group(function () {
    Route::get('/get_holiday', __APP__ . '\ajax\Holiday@get_holiday');
});

//-----------------distribution----------------------
Route::group(['prefix' => 'distAdmin', 'module' => 'distAdmin'], function () {
    Route::get('/', function () {
        return redirect('/distAdmin/login');
    });
    Route::get('/login', __APP__ . '\distribution\Login@index');
});

//-----------------admin----------------------
Route::group(['prefix' => 'shop', 'module' => 'shop'], function () {
    Route::get('/login/index', function () {
        return redirect('/admin/login/index');
    });

    Route::get('/stats/index', __APP__ . '\shop\Stats@index');
    Route::post('/stats/get_data', __APP__ . '\shop\Stats@get_data');

    Route::get('/store/index', __APP__ . '\shop\Store@index');
    Route::get('/store/input', __APP__ . '\shop\Store@input');
    Route::get('/store/final', __APP__ . '\shop\Store@final');
    Route::post('/store/search_memeber', __APP__ . '\shop\Store@search_memeber');
    Route::post('/store/check', __APP__ . '\shop\Store@check');
});
Route::group(['prefix' => 'admin', 'module' => 'admin'], function () {
    Route::get('/', function () {
        return redirect('/admin/index/index');
    });

    Route::get('/about/index', __APP__ . '\admin\About@index');
    Route::post('/about/update', __APP__ . '\admin\About@update');

    Route::get('/act/index', __APP__ . '\admin\Act@index');
    Route::post('/act/get_act_list', __APP__ . '\admin\Act@getActList');
    Route::post('/act/change_online', __APP__ . '\admin\Act@changeOnline');
    Route::post('/act/del_act', __APP__ . '\admin\Act@delAct');
    Route::post('/act/do_create', __APP__ . '\admin\Act@doCreate');
    Route::get('/act/edit', __APP__ . '\admin\Act@edit');
    Route::post('/act/update', __APP__ . '\admin\Act@update');
    Route::get('/act/get_count', __APP__ . '\admin\Act@getCount');
    Route::post('/act/get_act_prod', __APP__ . '\admin\Act@getActProd');
    Route::post('/act/insert_act', __APP__ . '\admin\Act@insertAct');
    Route::post('/act/del_act_prod', __APP__ . '\admin\Act@delActProd');

    Route::get('/activity/index', __APP__ . '\admin\Activity@index');
    Route::post('/activity/do_create', __APP__ . '\admin\Activity@doCreate');
    Route::post('/activity/update', __APP__ . '\admin\Activity@update');
    Route::get('/activity/delete', __APP__ . '\admin\Activity@delete');
    Route::post('/activity/multi_delete', __APP__ . '\admin\Activity@multiDelete');
    Route::post('/activity/cell_ctrl', __APP__ . '\admin\Activity@cellCtrl');

    Route::get('/addprice/index', __APP__ . '\admin\Addprice@index');
    Route::get('/addprice/edit', __APP__ . '\admin\Addprice@edit');
    Route::post('/addprice/do_create', __APP__ . '\admin\Addprice@doCreate');
    Route::post('/addprice/do_update', __APP__ . '\admin\Addprice@doUpdate');
    Route::post('/addprice/change_online', __APP__ . '\admin\Addprice@changeOnline');
    Route::post('/addprice/updatelimit_num', __APP__ . '\admin\Addprice@updatelimitNum');
    Route::post('/addprice/get_list', __APP__ . '\admin\Addprice@getList');
    Route::post('/addprice/do_del', __APP__ . '\admin\Addprice@doDel');
    Route::post('/addprice/get_detail', __APP__ . '\admin\Addprice@getDetail');
    Route::post('/addprice/insert_act_prod', __APP__ . '\admin\Addprice@insertActProd');
    Route::post('/addprice/del_act_prod', __APP__ . '\admin\Addprice@delActProd');
    Route::post('/addprice/get_addable_prod', __APP__ . '\admin\Addprice@getAddableProd');

    Route::get('/admin/edit', __APP__ . '\admin\Admin@edit');
    Route::get('/admin/point_set', __APP__ . '\admin\Admin@point_set');
    Route::post('/admin/point_set_update', __APP__ . '\admin\Admin@point_set_update');
    Route::get('/admin/maxlifetime_set', __APP__ . '\admin\Admin@maxlifetime_set');
    Route::post('/admin/maxlifetime_update', __APP__ . '\admin\Admin@maxlifetime_update');
    Route::post('/admin/add', __APP__ . '\admin\Admin@add');
    Route::get('/admin/del', __APP__ . '\admin\Admin@del');
    Route::post('/admin/update', __APP__ . '\admin\Admin@update');
    Route::post('/admin/current_change', __APP__ . '\admin\Admin@current_change');
    Route::post('/admin/update_purview', __APP__ . '\admin\Admin@update_purview');
    Route::post('/admin/email_update', __APP__ . '\admin\Admin@emailUpdate');
    Route::post('/admin/save_email_remind', __APP__ . '\admin\Admin@save_email_remind');
    Route::get('/admin/admin_info', __APP__ . '\admin\Admin@admin_info');
    Route::post('/admin/admin_info_update', __APP__ . '\admin\Admin@admin_info_update');
    Route::get('/admin/system_email', __APP__ . '\admin\Admin@system_email');
    Route::post('/admin/system_email_update', __APP__ . '\admin\Admin@system_email_update');

    Route::get('/all/index', __APP__ . '\admin\All@index');
    Route::get('/all/print', __APP__ . '\admin\All@print');
    Route::get('/all/search_product_ajax', __APP__ . '\admin\All@search_product_ajax');
    Route::get('/all/init_data_ajax', __APP__ . '\admin\All@init_data_ajax');

    Route::get('/askprice/index', __APP__ . '\admin\Askprice@index');
    Route::post('/askprice/get_list', __APP__ . '\admin\Askprice@getList');
    Route::post('/askprice/get_one', __APP__ . '\admin\Askprice@getOne');
    Route::get('/askprice/delete', __APP__ . '\admin\Askprice@delete');
    Route::post('/askprice/multi_delete', __APP__ . '\admin\Askprice@multiDelete');
    Route::post('/askprice/status', __APP__ . '\admin\Askprice@status');
    Route::get('/askprice/get_count', __APP__ . '\admin\Askprice@getCount');

    Route::get('/banner/index', __APP__ . '\admin\Banner@index');
    Route::post('/banner/cell_ctrl', __APP__ . '\admin\Banner@cellCtrl');
    Route::post('/banner/update', __APP__ . '\admin\Banner@update');

    Route::get('/consent/index', __APP__ . '\admin\Consent@index');
    Route::post('/consent/cell_ctrl', __APP__ . '\admin\Consent@cellCtrl');

    Route::get('/consumption/exchange', __APP__ . '\admin\Consumption@exchange');
    Route::post('/consumption/exchange_save', __APP__ . '\admin\Consumption@exchange_save');
    Route::post('/consumption/exchange_delete', __APP__ . '\admin\Consumption@exchange_delete');
    Route::get('/consumption/exchange_list', __APP__ . '\admin\Consumption@exchange_list');
    Route::get('/consumption/draw', __APP__ . '\admin\Consumption@draw');
    Route::post('/consumption/draw_save', __APP__ . '\admin\Consumption@draw_save');
    Route::post('/consumption/draw_delete', __APP__ . '\admin\Consumption@draw_delete');
    Route::post('/consumption/draw_limit_save', __APP__ . '\admin\Consumption@draw_limit_save');
    Route::get('/consumption/draw_list', __APP__ . '\admin\Consumption@draw_list');
    Route::get('/consumption/pay_list', __APP__ . '\admin\Consumption@pay_list');
    Route::post('/consumption/pay_list_save', __APP__ . '\admin\Consumption@pay_list_save');
    Route::post('/consumption/pay_list_delete', __APP__ . '\admin\Consumption@pay_list_delete');

    Route::get('/contact/index', __APP__ . '\admin\Contact@index');
    Route::post('/contact/contact_log_record', __APP__ . '\admin\Contact@contact_log_record');
    Route::get('/contact/delete', __APP__ . '\admin\Contact@delete');
    Route::post('/contact/multi_delete', __APP__ . '\admin\Contact@multiDelete');
    Route::post('/contact/status', __APP__ . '\admin\Contact@status');
    Route::post('/contact/type', __APP__ . '\admin\Contact@type');
    Route::get('/contact/get_count', __APP__ . '\admin\Contact@getCount');

    Route::get('/coupon/index', __APP__ . '\admin\Coupon@index');
    Route::get('/coupon/create', __APP__ . '\admin\Coupon@create');
    Route::post('/coupon/do_create', __APP__ . '\admin\Coupon@doCreate');
    Route::get('/coupon/show', __APP__ . '\admin\Coupon@show');
    Route::get('/coupon/dump_used_excel', __APP__ . '\admin\Coupon@dumpUsedExcel');
    Route::get('/coupon/dump_pool_excel', __APP__ . '\admin\Coupon@dumpPoolExcel');
    Route::get('/coupon/delete', __APP__ . '\admin\Coupon@delete');
    Route::post('/coupon/multi_delete', __APP__ . '\admin\Coupon@multiDelete');
    Route::post('/coupon/cell_ctrl', __APP__ . '\admin\Coupon@cellCtrl');

    Route::get('/coupondirect/index', __APP__ . '\admin\Coupondirect@index');
    Route::post('/coupondirect/get_act_list', __APP__ . '\admin\Coupondirect@getActList');
    Route::post('/coupondirect/change_online', __APP__ . '\admin\Coupondirect@changeOnline');
    Route::post('/coupondirect/del_act', __APP__ . '\admin\Coupondirect@delAct');
    Route::post('/coupondirect/do_create', __APP__ . '\admin\Coupondirect@doCreate');
    Route::get('/coupondirect/edit', __APP__ . '\admin\Coupondirect@edit');
    Route::post('/coupondirect/update', __APP__ . '\admin\Coupondirect@update');
    Route::get('/coupondirect/get_count', __APP__ . '\admin\Coupondirect@getCount');
    Route::post('/coupondirect/get_act_prod', __APP__ . '\admin\Coupondirect@getActProd');
    Route::post('/coupondirect/insert_act', __APP__ . '\admin\Coupondirect@insertAct');
    Route::post('/coupondirect/del_act_prod', __APP__ . '\admin\Coupondirect@delActProd');
    Route::post('/coupondirect/get_coupon_direct_prod', __APP__ . '\admin\Coupondirect@getCouponDirectProd');

    Route::get('/discount/index', __APP__ . '\admin\Discount@index');
    Route::post('/discount/get_act_list', __APP__ . '\admin\Discount@getActList');
    Route::post('/discount/change_online', __APP__ . '\admin\Discount@changeOnline');
    Route::post('/discount/del_act', __APP__ . '\admin\Discount@delAct');
    Route::post('/discount/do_create', __APP__ . '\admin\Discount@doCreate');
    Route::get('/discount/edit', __APP__ . '\admin\Discount@edit');
    Route::post('/discount/update', __APP__ . '\admin\Discount@update');
    Route::get('/discount/get_count', __APP__ . '\admin\Discount@getCount');
    Route::post('/discount/get_act_prod', __APP__ . '\admin\Discount@getActProd');
    Route::post('/discount/insert_act', __APP__ . '\admin\Discount@insertAct');
    Route::post('/discount/del_act_prod', __APP__ . '\admin\Discount@delActProd');

    Route::get('/disset/index', __APP__ . '\admin\Disset@index');
    Route::post('/disset/edit', __APP__ . '\admin\Disset@edit');

    Route::get('/examination/examinee_list', __APP__ . '\admin\Examination@examinee_list');
    Route::post('/examination/examinee_panel', __APP__ . '\admin\Examination@examinee_panel');
    Route::post('/examination/examinee_save', __APP__ . '\admin\Examination@examinee_save');
    Route::get('/examination/excel', __APP__ . '\admin\Examination@excel');
    Route::post('/examination/select_pre_examinee', __APP__ . '\admin\Examination@select_pre_examinee');
    Route::get('/examination/close_type', __APP__ . '\admin\Examination@close_type');
    Route::get('/examination/multiCancel', __APP__ . '\admin\Examination@multiCancel');
    Route::post('/examination/update_examinee', __APP__ . '\admin\Examination@update_examinee');
    Route::get('/examination/start_roll_call', __APP__ . '\admin\Examination@start_roll_call');
    Route::get('/examination/qrcode_roll_call', __APP__ . '\admin\Examination@qrcode_roll_call');
    Route::post('/examination/get_fields_by_prod_id', __APP__ . '\admin\Examination@get_fields_by_prod_id');

    Route::get('/excel/index', __APP__ . '\admin\Excel@index');
    Route::get('/excel/reply', __APP__ . '\admin\Excel@reply');
    Route::post('/excel/import', __APP__ . '\admin\Excel@Import');
    Route::post('/excel/update', __APP__ . '\admin\Excel@update');
    Route::get('/excel/delete', __APP__ . '\admin\Excel@delete');
    Route::post('/excel/multi_delete', __APP__ . '\admin\Excel@multiDelete');
    Route::post('/excel/multi_delete_or', __APP__ . '\admin\Excel@multiDelete_or');

    Route::get('/experience/index', __APP__ . '\admin\Experience@index');
    Route::post('/experience/do_create', __APP__ . '\admin\Experience@doCreate');
    Route::post('/experience/update', __APP__ . '\admin\Experience@update');
    Route::get('/experience/delete', __APP__ . '\admin\Experience@delete');
    Route::get('/experience/multi_delete', __APP__ . '\admin\Experience@multiDelete');
    Route::post('/experience/cell_ctrl', __APP__ . '\admin\Experience@cellCtrl');

    Route::get('/fields/fields_set', __APP__ . '\admin\Fields@fields_set');
    Route::get('/fields/get_fields', __APP__ . '\admin\Fields@get_fields');
    Route::post('/fields/fields_set_save', __APP__ . '\admin\Fields@fields_set_save');
    Route::post('/fields/fields_set_delete', __APP__ . '\admin\Fields@fields_set_delete');
    Route::get('/fields/comments_set', __APP__ . '\admin\Fields@comments_set');
    Route::post('/fields/comments_set_save', __APP__ . '\admin\Fields@comments_set_save');
    Route::post('/fields/comments_set_delete', __APP__ . '\admin\Fields@comments_set_delete');

    Route::get('/findorder/findorder', __APP__ . '\admin\Findorder@findorder');
    Route::get('/findorder/delete', __APP__ . '\admin\Findorder@delete');
    Route::post('/findorder/multi_delete', __APP__ . '\admin\Findorder@multiDelete');
    Route::post('/findorder/status', __APP__ . '\admin\Findorder@status');
    Route::get('/findorder/get_count', __APP__ . '\admin\Findorder@getCount');

    Route::get('/index/index', __APP__ . '\admin\Index@index');
    Route::post('/index/set_slide_show', __APP__ . '\admin\Index@setSlideShow');
    Route::post('/index/set_pic_with_link', __APP__ . '\admin\Index@setPicWithLink');
    Route::post('/index/setthirteenth', __APP__ . '\admin\Index@setthirteenth');
    Route::post('/index/block_ctrl', __APP__ . '\admin\Index@blockCtrl');
    Route::post('/index/set_social_link', __APP__ . '\admin\Index@setSocialLink');
    Route::post('/index/set_product', __APP__ . '\admin\Index@setProduct');
    Route::post('/index/set_text', __APP__ . '\admin\Index@setText');
    Route::post('/index/set_link', __APP__ . '\admin\Index@setLink');
    Route::post('/index/set_time_range', __APP__ . '\admin\Index@setTimeRange');
    Route::post('/index/set_product_online', __APP__ . '\admin\Index@setProductOnline');
    Route::post('/index/set_product_order', __APP__ . '\admin\Index@setProductOrder');
    Route::get('/index/spe_price_product', __APP__ . '\admin\Index@spePriceProduct');
    Route::post('/index/update_spe_price', __APP__ . '\admin\Index@updateSpePrice');

    Route::get('/indexad/index', __APP__ . '\admin\Indexad@index');
    Route::post('/indexad/do_create', __APP__ . '\admin\Indexad@doCreate');
    Route::post('/indexad/update', __APP__ . '\admin\Indexad@update');
    Route::get('/indexad/delete', __APP__ . '\admin\Indexad@delete');
    Route::post('/indexad/multi_delete', __APP__ . '\admin\Indexad@multiDelete');
    Route::post('/indexad/cell_ctrl', __APP__ . '\admin\Indexad@cellCtrl');

    Route::get('/kol/index', __APP__ . '\admin\Kol@index');
    Route::get('/kol/delete', __APP__ . '\admin\Kol@delete');
    Route::post('/kol/multi_delete', __APP__ . '\admin\Kol@multiDelete');
    Route::post('/kol/do_create', __APP__ . '\admin\Kol@doCreate');
    Route::post('/kol/update', __APP__ . '\admin\Kol@update');
    Route::post('/kol/create_period', __APP__ . '\admin\Kol@create_period');
    Route::get('/kol/salelist', __APP__ . '\admin\Kol@salelist');
    Route::get('/kol/sale_detail', __APP__ . '\admin\Kol@sale_detail');
    Route::post('/kol/confirm_period', __APP__ . '\admin\Kol@confirm_period');

    Route::get('/layertree/tree', __APP__ . '\admin\Layertree@tree');
    Route::get('/layertree/get_distributors', __APP__ . '\admin\Layertree@get_distributors');
    Route::get('/layertree/get_product_tree', __APP__ . '\admin\Layertree@get_product_tree');

    Route::get('/limit/index', __APP__ . '\admin\Limit@index');
    Route::get('/limit/get_count', __APP__ . '\admin\Limit@getCount');
    Route::post('limit/change_type_num', __APP__ . '\admin\Limit@change_type_num');

    Route::get('/login/index', __APP__ . '\admin\Login@index');
    Route::post('/login/login_action', __APP__ . '\admin\Login@loginAction');
    Route::get('/login/logout', __APP__ . '\admin\Login@logout');

    Route::get('/lovecode/index', __APP__ . '\admin\LoveCode@index');
    Route::get('/lovecode/delete', __APP__ . '\admin\LoveCode@delete');
    Route::post('/lovecode/multi_delete', __APP__ . '\admin\LoveCode@multiDelete');
    Route::post('/lovecode/do_create', __APP__ . '\admin\LoveCode@doCreate');
    Route::post('/lovecode/update', __APP__ . '\admin\LoveCode@update');
    Route::post('/lovecode/cell_ctrl', __APP__ . '\admin\LoveCode@cellCtrl');

    Route::get('/news/index', __APP__ . '\admin\News@index');
    Route::post('/news/do_create', __APP__ . '\admin\News@doCreate');
    Route::post('/news/update', __APP__ . '\admin\News@update');
    Route::get('/news/delete', __APP__ . '\admin\News@delete');
    Route::post('/news/multi_delete', __APP__ . '\admin\News@multiDelete');
    Route::post('/news/cell_ctrl', __APP__ . '\admin\News@cellCtrl');

    Route::get('/payfee/index', __APP__ . '\admin\Payfee@index');
    // Route::get('/payfee/delete', __APP__.'\admin\Payfee@delete');
    // Route::post('/payfee/multi_delete', __APP__.'\admin\Payfee@multiDelete');
    // Route::post('/payfee/do_create', __APP__.'\admin\Payfee@doCreate');
    Route::post('/payfee/update', __APP__ . '\admin\Payfee@update');
    Route::post('/payfee/cell_ctrl', __APP__ . '\admin\Payfee@cellCtrl');

    Route::get('/boxcontent/cate', __APP__ . '\admin\BoxContent@cate');  // 禮盒內容物分類管理列表
    Route::get('/boxcontent/content', __APP__ . '\admin\BoxContent@content');  // 禮盒內容物管理列表
    Route::post('/boxcontent/create_and_update', __APP__ . '\admin\BoxContent@create_and_update');  // 建立/編輯禮盒內容物分類
    Route::post('/boxcontent/change_order', __APP__ . '\admin\BoxContent@change_order');  // 設定禮盒內容物分類排序
    Route::post('/boxcontent/delete_one', __APP__ . '\admin\BoxContent@delete_one');  // 刪除禮盒內容物分類
    Route::post('/boxcontent/get_all_productinfo', __APP__ . '\admin\BoxContent@get_all_productinfo');  // 取得所有品項
    Route::post('/boxcontent/get_content', __APP__ . '\admin\BoxContent@get_content');  // 取得禮盒內容物分類中的內容物
    Route::post('/boxcontent/add_to_content', __APP__ . '\admin\BoxContent@add_to_content');  // 添加內容物至禮盒內容物分類
    Route::post('/boxcontent/remove_content', __APP__ . '\admin\BoxContent@remove_content');  // 刪除禮盒內容物分類中的內容物

    Route::get('/boxlayout/index', __APP__ . '\admin\BoxLayout@index');  // 版型分類管理列表
    Route::get('/boxlayout/edit', __APP__ . '\admin\BoxLayout@edit_layout');  // 版型管理列表
    Route::post('/boxlayout/check_box_content_cate', __APP__ . '\admin\BoxLayout@check_box_content_cate');  // 計算禮盒內容物分類數量
    Route::post('/boxlayout/create_and_update', __APP__ . '\admin\BoxLayout@create_and_update');  // 建立/編輯版型
    Route::post('/boxlayout/change_online', __APP__ . '\admin\BoxLayout@change_online');  // 設定版型分類上/下架
    Route::post('/boxlayout/change_order', __APP__ . '\admin\BoxLayout@change_order');  // 設定版型分類排序
    Route::post('/boxlayout/delete_one', __APP__ . '\admin\BoxLayout@delete_one');  // 刪除版型分類
    Route::post('/boxlayout/multi_operate', __APP__ . '\admin\BoxLayout@multi_operate');  // 批次操作版型分類(上/下架、刪除)
    Route::post('/boxlayout/get_layout', __APP__ . '\admin\BoxLayout@get_layout');  // 取得版型分類中的版型
    Route::post('/boxlayout/get_modal_block_items', __APP__ . '\admin\BoxLayout@get_modal_block_items');  // 取得所有的禮盒內容物分類
    Route::post('/boxlayout/store', __APP__ . '\admin\BoxLayout@store');  // 儲存版型
    Route::post('/boxlayout/delete_layout', __APP__ . '\admin\BoxLayout@delete_layout');  // 刪除版型

    Route::get('/position/index', __APP__ . '\admin\Position@index');
    Route::post('/position/edit', __APP__ . '\admin\Position@edit');

    Route::get('/pricesearch/index', __APP__ . '\admin\Pricesearch@index');
    Route::post('/pricesearch/do_create', __APP__ . '\admin\Pricesearch@doCreate');
    Route::post('/pricesearch/update', __APP__ . '\admin\Pricesearch@update');
    Route::get('/pricesearch/delete', __APP__ . '\admin\Pricesearch@delete');
    Route::post('/pricesearch/multi_delete', __APP__ . '\admin\Pricesearch@multiDelete');
    Route::post('/pricesearch/cell_ctrl', __APP__ . '\admin\Pricesearch@cellCtrl');

    Route::get('/prodesc/index', __APP__ . '\admin\Prodesc@index');
    Route::post('/prodesc/edit', __APP__ . '\admin\Prodesc@edit');

    Route::get('/product/index', __APP__ . '\admin\Product@index');
    Route::post('/product/update', __APP__ . '\admin\Product@update');
    Route::post('/product/cell_ctrl', __APP__ . '\admin\Product@cellCtrl');
    Route::get('/product/delete', __APP__ . '\admin\Product@delete');
    Route::post('/product/create', __APP__ . '\admin\Product@create');
    Route::post('/product/get_list', __APP__ . '\admin\Product@getList');
    Route::post('/product/get_cate', __APP__ . '\admin\Product@getCate');
    Route::post('/product/get_cate2', __APP__ . '\admin\Product@getCate2');
    Route::post('/product/get_cate_prod', __APP__ . '\admin\Product@getCateProd');
    Route::post('/product/import', __APP__ . '\admin\Product@import');

    Route::get('/productinfo/show_position_portion', __APP__ . '\admin\Productinfo@show_position_portion');
    Route::post('/productinfo/input_isbn', __APP__ . '\admin\Productinfo@input_isbn');
    Route::post('/productinfo/show_array', __APP__ . '\admin\Productinfo@show_array');
    Route::get('/productinfo/allcreate', __APP__ . '\admin\Productinfo@allcreate');
    Route::get('/productinfo/edit', __APP__ . '\admin\Productinfo@edit');
    Route::post('/productinfo/update', __APP__ . '\admin\Productinfo@update');
    Route::post('/productinfo/cell_ctrl', __APP__ . '\admin\Productinfo@cellCtrl');
    Route::get('/productinfo/delete', __APP__ . '\admin\Productinfo@delete');
    Route::post('/productinfo/multi_delete', __APP__ . '\admin\Productinfo@multiDelete');
    Route::get('/productinfo/copy', __APP__ . '\admin\Productinfo@copy');
    Route::post('/productinfo/add_to_index_adv', __APP__ . '\admin\Productinfo@add_to_index_adv');
    Route::post('/productinfo/remove_to_index_adv', __APP__ . '\admin\Productinfo@remove_to_index_adv');
    Route::post('/productinfo/position_select', __APP__ . '\admin\Productinfo@position_select');
    Route::post('/productinfo/position_portion', __APP__ . '\admin\Productinfo@position_portion');
    Route::post('/productinfo/cell_ctrl_from_default', __APP__ . '\admin\Productinfo@cellCtrlFromDefault');
    Route::post('/productinfo/cell_get_from_default', __APP__ . '\admin\Productinfo@cellGetFromDefault');
    Route::get('/productinfo/product_example', __APP__ . '\admin\Productinfo@product_example');
    Route::get('/productinfo/price_example', __APP__ . '\admin\Productinfo@price_example');
    Route::get('/productinfo/edit_fields', __APP__ . '\admin\Productinfo@edit_fields');
    Route::get('/productinfo/get_prod_fields', __APP__ . '\admin\Productinfo@get_prod_fields');
    Route::post('/productinfo/fields_set_save', __APP__ . '\admin\Productinfo@fields_set_save');
    Route::post('/productinfo/fields_set_delete', __APP__ . '\admin\Productinfo@fields_set_delete');

    Route::get('/productqa/index', __APP__ . '\admin\Productqa@index');
    Route::get('/productqa/delete', __APP__ . '\admin\Productqa@delete');
    Route::post('/productqa/multi_delete', __APP__ . '\admin\Productqa@multiDelete');
    Route::post('/productqa/update', __APP__ . '\admin\Productqa@update');

    Route::get('/qa/index', __APP__ . '\admin\Qa@index');
    Route::get('/qa/delete', __APP__ . '\admin\Qa@delete');
    Route::post('/qa/update', __APP__ . '\admin\Qa@update');
    Route::post('/qa/multi_delete', __APP__ . '\admin\Qa@multiDelete');
    Route::post('/qa/cell_ctrl', __APP__ . '\admin\Qa@cellCtrl');
    Route::post('/qa/do_create', __APP__ . '\admin\Qa@doCreate');

    Route::get('/sell/index', __APP__ . '\admin\Sell@index');
    Route::post('/sell/search', __APP__ . '\admin\Sell@search');
    Route::post('/sell/update', __APP__ . '\admin\Sell@update');
    Route::post('/sell/edit', __APP__ . '\admin\Sell@edit');
    Route::post('/sell/search_bar_mul', __APP__ . '\admin\Sell@search_bar_mul');
    Route::post('/sell/search_bar', __APP__ . '\admin\Sell@search_bar');
    Route::post('/sell/search_user', __APP__ . '\admin\Sell@search_user');

    Route::get('/seo/edit', __APP__ . '\admin\Seo@edit');
    Route::get('/seo/edit_social', __APP__ . '\admin\Seo@edit_social');
    Route::get('/seo/edit_advance', __APP__ . '\admin\Seo@edit_advance');
    Route::post('/seo/update', __APP__ . '\admin\Seo@update');
    Route::post('/seo/update_social', __APP__ . '\admin\Seo@update_social');
    Route::post('/seo/update_advance', __APP__ . '\admin\Seo@update_advance');

    Route::get('/shippingfee/index', __APP__ . '\admin\Shippingfee@index');
    Route::get('/shippingfee/delete', __APP__ . '\admin\Shippingfee@delete');
    Route::post('/shippingfee/multi_delete', __APP__ . '\admin\Shippingfee@multiDelete');
    Route::post('/shippingfee/do_create', __APP__ . '\admin\Shippingfee@doCreate');
    Route::post('/shippingfee/update', __APP__ . '\admin\Shippingfee@update');
    Route::post('/shippingfee/cell_ctrl', __APP__ . '\admin\Shippingfee@cellCtrl');
    Route::post('/shippingfee/get_exclude_weekday', __APP__ . '\admin\Shippingfee@get_exclude_weekday');
    Route::post('/shippingfee/update_exclude_weekday', __APP__ . '\admin\Shippingfee@update_exclude_weekday');
    Route::post('/shippingfee/get_exclude_dates', __APP__ . '\admin\Shippingfee@get_exclude_dates');
    Route::post('/shippingfee/add_exclude_date', __APP__ . '\admin\Shippingfee@add_exclude_date');
    Route::post('/shippingfee/remove_exclude_date', __APP__ . '\admin\Shippingfee@remove_exclude_date');

    Route::get('/shippingfeetag/index', __APP__ . '\admin\Shippingfeetag@index');
    Route::post('/shippingfeetag/do_create', __APP__ . '\admin\Shippingfeetag@doCreate');
    Route::post('/shippingfeetag/update', __APP__ . '\admin\Shippingfeetag@update');
    Route::post('/shippingfeetag/cell_ctrl', __APP__ . '\admin\Shippingfeetag@cellCtrl');
    Route::get('/shippingfeetag/delete', __APP__ . '\admin\Shippingfeetag@delete');
    Route::post('/shippingfeetag/multiDelete', __APP__ . '\admin\Shippingfeetag@multiDelete');

    Route::get('/stronghold/index', __APP__ . '\admin\Stronghold@index');
    Route::post('/stronghold/do_create', __APP__ . '\admin\Stronghold@doCreate');
    Route::post('/stronghold/update', __APP__ . '\admin\Stronghold@update');
    Route::get('/stronghold/delete', __APP__ . '\admin\Stronghold@delete');
    Route::post('/stronghold/multi_delete', __APP__ . '\admin\Stronghold@multiDelete');
    Route::post('/stronghold/cell_ctrl', __APP__ . '\admin\Stronghold@cellCtrl');
    Route::get('/stronghold/dodelete', __APP__ . '\admin\Stronghold@dodelete');
    Route::post('/stronghold/create', __APP__ . '\admin\Stronghold@create');
    Route::post('/stronghold/updatetitle', __APP__ . '\admin\Stronghold@updatetitle');

    Route::get('/system/index', __APP__ . '\admin\System@index');
    Route::post('/system/update', __APP__ . '\admin\System@update');

    Route::get('/tag/tag', __APP__ . '\admin\Tag@tag');
    Route::post('/tag/update_tag', __APP__ . '\admin\Tag@update_tag');

    Route::get('/typeinfo/delete', __APP__ . '\admin\Typeinfo@delete');
    Route::post('/typeinfo/create', __APP__ . '\admin\Typeinfo@create');
    Route::post('/typeinfo/update', __APP__ . '\admin\Typeinfo@update');
    Route::post('/typeinfo/cell_ctrl', __APP__ . '\admin\Typeinfo@cellCtrl');

    Route::get('/giftwithpurchase/index', __APP__ . '\admin\Giftwithpurchase@index');
    Route::post('/giftwithpurchase/get_items', __APP__ . '\admin\Giftwithpurchase@get_items');
    Route::post('/giftwithpurchase/update_item', __APP__ . '\admin\Giftwithpurchase@update_item');
    Route::post('/giftwithpurchase/add_item', __APP__ . '\admin\Giftwithpurchase@add_item');
    Route::post('/giftwithpurchase/delete_item', __APP__ . '\admin\Giftwithpurchase@delete_item');

    Route::get('/giftwithpurchase/edit', __APP__ . '\admin\Giftwithpurchase@edit');
    Route::post('/giftwithpurchase/update_item_basic', __APP__ . '\admin\Giftwithpurchase@update_item_basic');
    Route::post('/giftwithpurchase/get_gift_with_purchase', __APP__ . '\admin\Giftwithpurchase@get_gift_with_purchase');
    Route::post('/giftwithpurchase/update_rule_item', __APP__ . '\admin\Giftwithpurchase@update_rule_item');
    Route::post('/giftwithpurchase/delete_rule_item', __APP__ . '\admin\Giftwithpurchase@delete_rule_item');
    Route::post('/giftwithpurchase/update_content_item', __APP__ . '\admin\Giftwithpurchase@update_content_item');
    Route::post('/giftwithpurchase/delete_content_item', __APP__ . '\admin\Giftwithpurchase@delete_content_item');
    Route::post('/giftwithpurchase/get_branches', __APP__ . '\admin\Giftwithpurchase@get_branches');
    Route::post('/giftwithpurchase/save_content_num', __APP__ . '\admin\Giftwithpurchase@save_content_num');
});

Route::group(['prefix' => 'order', 'module' => 'order'], function () {
    Route::get('/admin/member_discount', __APP__ . '\order\Admin@member_discount');
    Route::post('/admin/add_vip_type', __APP__ . '\order\Admin@add_vip_type');
    Route::post('/admin/update_vip_type', __APP__ . '\order\Admin@update_vip_type');
    Route::get('/admin/del_vip_type', __APP__ . '\order\Admin@del_vip_type');

    Route::get('/index/index', __APP__ . '\order\Index@index');
    Route::get('/index/ajax_search', __APP__ . '\order\Index@ajax_search');
    Route::get('/index/edit', __APP__ . '\order\Index@edit');
    Route::get('/index/delete', __APP__ . '\order\Index@delete');
    Route::post('/index/multi_delete', __APP__ . '\order\Index@multiDelete');
    Route::post('/index/multi_update', __APP__ . '\order\Index@multiUpdate');
    Route::get('/index/member_excel', __APP__ . '\order\Index@member_excel');
    Route::post('/index/import', __APP__ . '\order\Index@import');
    Route::post('/index/gift', __APP__ . '\order\Index@gift');
    Route::post('/index/add_member', __APP__ . '\order\Index@addMember');
    Route::post('/index/change_password', __APP__ . '\order\Index@changePassword');
    Route::post('/index/update_member', __APP__ . '\order\Index@updateMember');

    Route::get('/login/index', __APP__ . '\order\Login@index');

    Route::get('/member/product_view', __APP__ . '\order\Member@product_view');
    Route::post('/member/get_act_list', __APP__ . '\order\Member@getActList');
    Route::post('/member/change_online', __APP__ . '\order\Member@changeOnline');
    Route::post('/member/del_act', __APP__ . '\order\Member@delAct');
    Route::post('/member/do_create', __APP__ . '\order\Member@doCreate');
    Route::get('/member/product_view_edit', __APP__ . '\order\Member@product_view_edit');
    Route::post('/member/update', __APP__ . '\order\Member@update');
    Route::post('/member/get_act_prod', __APP__ . '\order\Member@getActProd');
    Route::post('/member/get_cate_prod', __APP__ . '\order\Member@getCateProd');
    Route::post('/member/insert_act', __APP__ . '\order\Member@insertAct');
    Route::post('/member/del_act_prod', __APP__ . '\order\Member@delActProd');

    Route::get('/notification/send_notification', __APP__ . '\order\Notification@send_notification');

    Route::get('/order_ctrl/index', __APP__ . '\order\OrderCtrl@index');
    Route::get('/order_ctrl/trash', __APP__ . '\order\OrderCtrl@trash');
    Route::get('/order_ctrl/excel', __APP__ . '\order\OrderCtrl@excel');
    Route::get('/order_ctrl/group_excel', __APP__ . '\order\OrderCtrl@group_excel');
    Route::get('/order_ctrl/get_orderforms', __APP__ . '\order\OrderCtrl@get_orderforms');
    Route::get('/order_ctrl/check_stock', __APP__ . '\order\OrderCtrl@check_stock');
    Route::get('/order_ctrl/show', __APP__ . '\order\OrderCtrl@show');
    Route::post('/order_ctrl/check_report', __APP__ . '\order\OrderCtrl@checkReport');
    Route::post('/order_ctrl/set_receipts_state', __APP__ . '\order\OrderCtrl@setReceiptsState');
    Route::post('/order_ctrl/set_transport_state', __APP__ . '\order\OrderCtrl@setTransportState');
    Route::post('/order_ctrl/set_p_s', __APP__ . '\order\OrderCtrl@setPS');
    Route::post('/order_ctrl/change_status2_cancel', __APP__ . '\order\OrderCtrl@changeStatus2Cancel');
    Route::post('/order_ctrl/delete', __APP__ . '\order\OrderCtrl@delete');
    Route::post('/order_ctrl/change_status2_restore', __APP__ . '\order\OrderCtrl@changeStatus2Restore');
    Route::post('/order_ctrl/multi_cancel', __APP__ . '\order\OrderCtrl@multiCancel');
    Route::post('/order_ctrl/multi_picked', __APP__ . '\order\OrderCtrl@multiPicked');
    Route::post('/order_ctrl/multi_next', __APP__ . '\order\OrderCtrl@multiNext');
    Route::post('/order_ctrl/multi_delete', __APP__ . '\order\OrderCtrl@multiDelete');
    Route::get('/order_ctrl/move_to_pick', __APP__ . '\order\OrderCtrl@move_to_pick');
    Route::get('/order_ctrl/ajax_logistic_status', __APP__ . '\order\OrderCtrl@ajax_logistic_status');
    Route::post('/order_ctrl/get_position', __APP__ . '\order\OrderCtrl@get_position');
    Route::post('/order_ctrl/release_position', __APP__ . '\order\OrderCtrl@release_position');
    Route::post('/order_ctrl/update', __APP__ . '\order\OrderCtrl@update');
    Route::post('/order_ctrl/move_to_picked', __APP__ . '\order\OrderCtrl@move_to_picked');
    Route::get('/order_ctrl/print_order', __APP__ . '\order\OrderCtrl@print_order');
    Route::post('/order_ctrl/change_transport_date', __APP__ . '\order\OrderCtrl@change_transport_date');

    Route::get('/pick/index', __APP__ . '\order\Pick@index');
    Route::get('/pick/history', __APP__ . '\order\Pick@history');
    Route::get('/pick/get_history', __APP__ . '\order\Pick@get_history');

    Route::get('/point/points_record', __APP__ . '\order\Point@points_record');
    Route::post('/point/get_point_data', __APP__ . '\order\Point@get_point_data');
});
