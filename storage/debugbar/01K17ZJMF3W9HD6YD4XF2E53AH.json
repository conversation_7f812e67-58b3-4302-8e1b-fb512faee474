{"__meta": {"id": "01K17ZJMF3W9HD6YD4XF2E53AH", "datetime": "2025-07-28 15:39:56", "utime": **********.259356, "method": "GET", "uri": "/", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 22, "start": **********.218844, "end": **********.259395, "duration": 2.040550947189331, "duration_str": "2.04s", "measures": [{"label": "Booting", "start": **********.218844, "relative_start": 0, "end": **********.509743, "relative_end": **********.509743, "duration": 0.*****************, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.509799, "relative_start": 0.****************, "end": **********.2594, "relative_end": 5.0067901611328125e-06, "duration": 1.***************, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.522432, "relative_start": 0.****************, "end": **********.52476, "relative_end": **********.52476, "duration": 0.0023279190063476562, "duration_str": "2.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home.index.index", "start": **********.067705, "relative_start": 0.****************, "end": **********.067705, "relative_end": **********.067705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.mainTpl", "start": **********.529102, "relative_start": 1.***************, "end": **********.529102, "relative_end": **********.529102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.910687, "relative_start": 1.***************, "end": **********.910687, "relative_end": **********.910687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.913341, "relative_start": 1.6944971084594727, "end": **********.913341, "relative_end": **********.913341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.034532, "relative_start": 1.815688133239746, "end": **********.034532, "relative_end": **********.034532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.040754, "relative_start": 1.8219101428985596, "end": **********.040754, "relative_end": **********.040754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.043093, "relative_start": 1.824249029159546, "end": **********.043093, "relative_end": **********.043093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.044315, "relative_start": 1.8254711627960205, "end": **********.044315, "relative_end": **********.044315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.newsLink", "start": **********.045603, "relative_start": 1.8267590999603271, "end": **********.045603, "relative_end": **********.045603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.073918, "relative_start": 1.855074167251587, "end": **********.073918, "relative_end": **********.073918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.074583, "relative_start": 1.8557391166687012, "end": **********.074583, "relative_end": **********.074583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.075852, "relative_start": 1.8570079803466797, "end": **********.075852, "relative_end": **********.075852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.076992, "relative_start": 1.8581480979919434, "end": **********.076992, "relative_end": **********.076992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.077977, "relative_start": 1.859133005142212, "end": **********.077977, "relative_end": **********.077977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.079134, "relative_start": 1.8602900505065918, "end": **********.079134, "relative_end": **********.079134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.other_nav", "start": **********.082633, "relative_start": 1.8637890815734863, "end": **********.082633, "relative_end": **********.082633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.243208, "relative_start": 2.0243639945983887, "end": **********.243208, "relative_end": **********.243208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.243911, "relative_start": 2.025067090988159, "end": **********.243911, "relative_end": **********.243911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.product.select_type_vue", "start": **********.251418, "relative_start": 2.03257417678833, "end": **********.251418, "relative_end": **********.251418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 6536104, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.16", "PHP Version": "8.3.14", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8888", "Timezone": "Asia/Taipei", "Locale": "A"}}, "views": {"count": 19, "nb_templates": 19, "templates": [{"name": "home.index.index", "param_count": null, "params": [], "start": **********.067574, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/index.blade.phphome.index.index", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Findex%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "home.Public.mainTpl", "param_count": null, "params": [], "start": **********.529053, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/mainTpl.blade.phphome.Public.mainTpl", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FmainTpl.blade.php&line=1", "ajax": false, "filename": "mainTpl.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.910645, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.913263, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.03447, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.040689, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.043035, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.044248, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.newsLink", "param_count": null, "params": [], "start": **********.045543, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/newsLink.blade.phphome.Public.newsLink", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FnewsLink.blade.php&line=1", "ajax": false, "filename": "newsLink.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.073857, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.074527, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.075789, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.07693, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.077916, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.079079, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.other_nav", "param_count": null, "params": [], "start": **********.082574, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/other_nav.blade.phphome.Public.other_nav", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fother_nav.blade.php&line=1", "ajax": false, "filename": "other_nav.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.243165, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.243877, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.product.select_type_vue", "param_count": null, "params": [], "start": **********.251373, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/select_type_vue.blade.phphome.product.select_type_vue", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Fproduct%2Fselect_type_vue.blade.php&line=1", "ajax": false, "filename": "select_type_vue.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.34966, "accumulated_duration_str": "350ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}], "start": **********.526458, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:197", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=197", "ajax": false, "filename": "HelperService.php", "line": "197"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0, "width_percent": 0.895}, {"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, {"index": 14, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.534239, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:210", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=210", "ajax": false, "filename": "HelperService.php", "line": "210"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0.895, "width_percent": 0.732}, {"sql": "select * from `backstage_menu` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.540918, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:213", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=213", "ajax": false, "filename": "HelperService.php", "line": "213"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 1.627, "width_percent": 0.729}, {"sql": "select * from `backstage_menu_second` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.5478911, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:215", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=215", "ajax": false, "filename": "HelperService.php", "line": "215"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 2.357, "width_percent": 1.03}, {"sql": "select * from `frontend_menu_name` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.5673468, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:65", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=65", "ajax": false, "filename": "GlobalController.php", "line": "65"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 3.386, "width_percent": 1.07}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.576952, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:76", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=76", "ajax": false, "filename": "GlobalController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 4.456, "width_percent": 1.098}, {"sql": "select * from `admin_info` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.588002, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:80", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=80", "ajax": false, "filename": "GlobalController.php", "line": "80"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 5.554, "width_percent": 0.704}, {"sql": "select * from `index_online` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.5951571, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:66", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=66", "ajax": false, "filename": "PublicController.php", "line": "66"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 6.258, "width_percent": 0.735}, {"sql": "select * from `index_excel` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.603485, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:69", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=69", "ajax": false, "filename": "PublicController.php", "line": "69"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 6.993, "width_percent": 0.892}, {"sql": "select * from `seo`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.610584, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:73", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=73", "ajax": false, "filename": "PublicController.php", "line": "73"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 7.885, "width_percent": 0.867}, {"sql": "select `id` from `stronghold` order by order_id asc, id desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.618558, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:76", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=76", "ajax": false, "filename": "PublicController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 8.751, "width_percent": 0.721}, {"sql": "select `title`, `id`, `recommend`, `pic_icon` as `pic` from `product` where `online` = 1 and `distributor_id` = 0 and `show_on_nav` = 1 order by order_id asc, id asc", "type": "query", "params": [], "bindings": [1, 0, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.626257, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:108", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=108", "ajax": false, "filename": "PublicController.php", "line": "108"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 9.472, "width_percent": 0.844}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='15' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.634363, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 10.316, "width_percent": 0.815}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='17' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.643281, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 11.131, "width_percent": 0.752}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='20' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.6509662, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 11.883, "width_percent": 0.832}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='1' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.659865, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 12.715, "width_percent": 0.766}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='3' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.668296, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 13.482, "width_percent": 1.193}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='10' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.678917, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 14.674, "width_percent": 1.419}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='12' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.6885822, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 16.093, "width_percent": 1.433}, {"sql": "select exists(select * from `visitor_logs` where `ip` = '::1' and `product_id` = 0 and date(`created_at`) = '2025-07-28') as `exists`", "type": "query", "params": [], "bindings": ["::1", 0, "2025-07-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, {"index": 10, "namespace": null, "name": "app/Http/Middleware/CountVisitors.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Middleware/CountVisitors.php", "line": 14}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}], "start": **********.699602, "duration": 0.*****************, "duration_str": "216ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:308", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=308", "ajax": false, "filename": "PublicController.php", "line": "308"}, "connection": "<PERSON><PERSON><PERSON><PERSON><PERSON>_shop_admin", "explain": null, "start_percent": 17.526, "width_percent": 61.777}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.9218612, "duration": 0.0087, "duration_str": "8.7ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:20", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=20", "ajax": false, "filename": "IndexController.php", "line": "20"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 79.303, "width_percent": 2.488}, {"sql": "select `id`, `title`, `pic_icon`, `index_adv01_pic`, `index_adv01_link`, `index_adv02_pic`, `index_adv02_link`, `index_adv03_pic`, `index_adv03_link`, `index_adv04_pic`, `index_adv04_link`, `index_adv05_pic`, `index_adv05_link`, `index_adv06_pic`, `index_adv06_link`, `index_adv07_pic`, `index_adv07_link` from `product` where (`ad_online` = 1 and `online` = 1) order by order_id", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.937977, "duration": 0.00888, "duration_str": "8.88ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 81.791, "width_percent": 2.54}, {"sql": "select `title`, `pic`, `link` from `slideshow` where `online` = 1 order by `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.952073, "duration": 0.00697, "duration_str": "6.97ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:61", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=61", "ajax": false, "filename": "IndexController.php", "line": "61"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 84.33, "width_percent": 1.993}, {"sql": "select * from `index_ad` where `online` = 1 order by `orders` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.9644802, "duration": 0.0205, "duration_str": "20.5ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:64", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=64", "ajax": false, "filename": "IndexController.php", "line": "64"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 86.324, "width_percent": 5.863}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `expiring_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 67}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.9904, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 92.187, "width_percent": 1.244}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `hot_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 70}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.0012271, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 93.431, "width_percent": 1.09}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `recommend_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.011998, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 94.52, "width_percent": 1.161}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `spe_price_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by orders asc, table.id desc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.023307, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 95.682, "width_percent": 0.952}, {"sql": "select `data3` from `index_excel` where `id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.0321162, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:79", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=79", "ajax": false, "filename": "IndexController.php", "line": "79"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 96.634, "width_percent": 0.889}, {"sql": "select `data2`, `data3` from `index_excel` where `id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.040623, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:83", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=83", "ajax": false, "filename": "IndexController.php", "line": "83"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 97.523, "width_percent": 0.655}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 109}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.047465, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 98.178, "width_percent": 0.958}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '2' limit 1", "type": "query", "params": [], "bindings": [1, "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.056987, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 99.136, "width_percent": 0.864}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8888", "action_name": null, "controller_action": "App\\Http\\Controllers\\home\\IndexController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\home\\IndexController@index<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/home/<USER>/a>", "middleware": "web", "duration": "2.16s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1539938046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1539938046\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1629080519 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1629080519\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1260339105 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8888</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260339105\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-56254889 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-56254889\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-7847023 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 07:39:55 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7847023\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2108251953 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IxpqWyuI97n48gfYOGU2s0orYPu3mTd4MyLClQEm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108251953\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8888", "controller_action": "App\\Http\\Controllers\\home\\IndexController@index"}, "badge": null}}