{"__meta": {"id": "01K17ZVAY1S570DK476ESND3XC", "datetime": "2025-07-28 15:44:41", "utime": **********.410501, "method": "GET", "uri": "/", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 22, "start": **********.462307, "end": **********.410574, "duration": 1.9482669830322266, "duration_str": "1.95s", "measures": [{"label": "Booting", "start": **********.462307, "relative_start": 0, "end": **********.741894, "relative_end": **********.741894, "duration": 0.****************, "duration_str": "280ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.741934, "relative_start": 0.*****************, "end": **********.410583, "relative_end": 9.059906005859375e-06, "duration": 1.****************, "duration_str": "1.67s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.753724, "relative_start": 0.*****************, "end": **********.756963, "relative_end": **********.756963, "duration": 0.0032389163970947266, "duration_str": "3.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home.index.index", "start": **********.174678, "relative_start": 0.****************, "end": **********.174678, "relative_end": **********.174678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.mainTpl", "start": **********.56712, "relative_start": 1.****************, "end": **********.56712, "relative_end": **********.56712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.082803, "relative_start": 1.****************, "end": **********.082803, "relative_end": **********.082803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.087099, "relative_start": 1.6247920989990234, "end": **********.087099, "relative_end": **********.087099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.194963, "relative_start": 1.7326560020446777, "end": **********.194963, "relative_end": **********.194963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.199204, "relative_start": 1.7368969917297363, "end": **********.199204, "relative_end": **********.199204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.21563, "relative_start": 1.7533230781555176, "end": **********.21563, "relative_end": **********.21563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.21644, "relative_start": 1.7541329860687256, "end": **********.21644, "relative_end": **********.21644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.newsLink", "start": **********.217501, "relative_start": 1.7551939487457275, "end": **********.217501, "relative_end": **********.217501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.240824, "relative_start": 1.7785170078277588, "end": **********.240824, "relative_end": **********.240824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.241399, "relative_start": 1.7790920734405518, "end": **********.241399, "relative_end": **********.241399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.242741, "relative_start": 1.7804341316223145, "end": **********.242741, "relative_end": **********.242741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.243485, "relative_start": 1.7811779975891113, "end": **********.243485, "relative_end": **********.243485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.244011, "relative_start": 1.7817039489746094, "end": **********.244011, "relative_end": **********.244011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.244643, "relative_start": 1.7823359966278076, "end": **********.244643, "relative_end": **********.244643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.other_nav", "start": **********.247162, "relative_start": 1.7848551273345947, "end": **********.247162, "relative_end": **********.247162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.387243, "relative_start": 1.924936056137085, "end": **********.387243, "relative_end": **********.387243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.388277, "relative_start": 1.9259700775146484, "end": **********.388277, "relative_end": **********.388277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.product.select_type_vue", "start": **********.394335, "relative_start": 1.93202805519104, "end": **********.394335, "relative_end": **********.394335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 6536104, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.16", "PHP Version": "8.3.14", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8888", "Timezone": "Asia/Taipei", "Locale": "A"}}, "views": {"count": 19, "nb_templates": 19, "templates": [{"name": "home.index.index", "param_count": null, "params": [], "start": **********.17461, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/index.blade.phphome.index.index", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Findex%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "home.Public.mainTpl", "param_count": null, "params": [], "start": **********.567078, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/mainTpl.blade.phphome.Public.mainTpl", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FmainTpl.blade.php&line=1", "ajax": false, "filename": "mainTpl.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.082739, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.087033, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.194875, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.199144, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.215593, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.216387, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.newsLink", "param_count": null, "params": [], "start": **********.217443, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/newsLink.blade.phphome.Public.newsLink", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FnewsLink.blade.php&line=1", "ajax": false, "filename": "newsLink.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.240778, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.241318, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.242678, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.243449, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.243978, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.244612, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.other_nav", "param_count": null, "params": [], "start": **********.247093, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/other_nav.blade.phphome.Public.other_nav", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fother_nav.blade.php&line=1", "ajax": false, "filename": "other_nav.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.38719, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.388218, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.product.select_type_vue", "param_count": null, "params": [], "start": **********.394298, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/select_type_vue.blade.phphome.product.select_type_vue", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Fproduct%2Fselect_type_vue.blade.php&line=1", "ajax": false, "filename": "select_type_vue.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.23342999999999997, "accumulated_duration_str": "233ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}], "start": **********.758908, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:197", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=197", "ajax": false, "filename": "HelperService.php", "line": "197"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0, "width_percent": 0.942}, {"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, {"index": 14, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.765831, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:210", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=210", "ajax": false, "filename": "HelperService.php", "line": "210"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0.942, "width_percent": 1.041}, {"sql": "select * from `backstage_menu` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.772544, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:213", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=213", "ajax": false, "filename": "HelperService.php", "line": "213"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 1.983, "width_percent": 1.002}, {"sql": "select * from `backstage_menu_second` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.7793171, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:215", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=215", "ajax": false, "filename": "HelperService.php", "line": "215"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 2.986, "width_percent": 1.242}, {"sql": "select * from `frontend_menu_name` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.798861, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:65", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=65", "ajax": false, "filename": "GlobalController.php", "line": "65"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 4.228, "width_percent": 0.814}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.805591, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:76", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=76", "ajax": false, "filename": "GlobalController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 5.042, "width_percent": 0.891}, {"sql": "select * from `admin_info` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.8146348, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:80", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=80", "ajax": false, "filename": "GlobalController.php", "line": "80"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 5.933, "width_percent": 1.011}, {"sql": "select * from `index_online` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.8253431, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:66", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=66", "ajax": false, "filename": "PublicController.php", "line": "66"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 6.944, "width_percent": 1.092}, {"sql": "select * from `index_excel` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.832679, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:69", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=69", "ajax": false, "filename": "PublicController.php", "line": "69"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 8.037, "width_percent": 1.118}, {"sql": "select * from `seo`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.839688, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:73", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=73", "ajax": false, "filename": "PublicController.php", "line": "73"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 9.155, "width_percent": 1.024}, {"sql": "select `id` from `stronghold` order by order_id asc, id desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.8469782, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:76", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=76", "ajax": false, "filename": "PublicController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 10.179, "width_percent": 1.178}, {"sql": "select `title`, `id`, `recommend`, `pic_icon` as `pic` from `product` where `online` = 1 and `distributor_id` = 0 and `show_on_nav` = 1 order by order_id asc, id asc", "type": "query", "params": [], "bindings": [1, 0, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.856282, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:108", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=108", "ajax": false, "filename": "PublicController.php", "line": "108"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 11.357, "width_percent": 1.015}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='15' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.864359, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 12.372, "width_percent": 1.384}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='17' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.872249, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 13.756, "width_percent": 0.994}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='20' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.878362, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 14.75, "width_percent": 1.384}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='1' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.886178, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 16.133, "width_percent": 1.028}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='3' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.89291, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 17.161, "width_percent": 2.176}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='10' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.903085, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 19.338, "width_percent": 1.302}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='12' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.912083, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 20.64, "width_percent": 1.971}, {"sql": "select exists(select * from `visitor_logs` where `ip` = '::1' and `product_id` = 0 and date(`created_at`) = '2025-07-28') as `exists`", "type": "query", "params": [], "bindings": ["::1", 0, "2025-07-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, {"index": 10, "namespace": null, "name": "app/Http/Middleware/CountVisitors.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Middleware/CountVisitors.php", "line": 14}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}], "start": **********.927694, "duration": 0.15206, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:308", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=308", "ajax": false, "filename": "PublicController.php", "line": "308"}, "connection": "<PERSON><PERSON><PERSON><PERSON><PERSON>_shop_admin", "explain": null, "start_percent": 22.611, "width_percent": 65.142}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.0850308, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:20", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=20", "ajax": false, "filename": "IndexController.php", "line": "20"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 87.752, "width_percent": 1.388}, {"sql": "select `id`, `title`, `pic_icon`, `index_adv01_pic`, `index_adv01_link`, `index_adv02_pic`, `index_adv02_link`, `index_adv03_pic`, `index_adv03_link`, `index_adv04_pic`, `index_adv04_link`, `index_adv05_pic`, `index_adv05_link`, `index_adv06_pic`, `index_adv06_link`, `index_adv07_pic`, `index_adv07_link` from `product` where (`ad_online` = 1 and `online` = 1) order by order_id", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.094281, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 89.14, "width_percent": 1.054}, {"sql": "select `title`, `pic`, `link` from `slideshow` where `online` = 1 order by `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.101175, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:61", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=61", "ajax": false, "filename": "IndexController.php", "line": "61"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 90.194, "width_percent": 0.904}, {"sql": "select * from `index_ad` where `online` = 1 order by `orders` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.1077259, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:64", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=64", "ajax": false, "filename": "IndexController.php", "line": "64"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 91.098, "width_percent": 0.925}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `expiring_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 67}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.116635, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 92.023, "width_percent": 1.17}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `hot_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 70}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.125771, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 93.193, "width_percent": 0.964}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `recommend_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.134378, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 94.157, "width_percent": 1.011}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `spe_price_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by orders asc, table.id desc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.142153, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 95.168, "width_percent": 0.947}, {"sql": "select `data3` from `index_excel` where `id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.148073, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:79", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=79", "ajax": false, "filename": "IndexController.php", "line": "79"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 96.114, "width_percent": 0.934}, {"sql": "select `data2`, `data3` from `index_excel` where `id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.154264, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:83", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=83", "ajax": false, "filename": "IndexController.php", "line": "83"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 97.048, "width_percent": 0.955}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 109}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.1602142, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 98.004, "width_percent": 1.024}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '2' limit 1", "type": "query", "params": [], "bindings": [1, "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.167318, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 99.028, "width_percent": 0.972}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8888", "action_name": null, "controller_action": "App\\Http\\Controllers\\home\\IndexController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\home\\IndexController@index<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/home/<USER>/a>", "middleware": "web", "duration": "2.1s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1080873175 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1080873175\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-229610265 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-229610265\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1803887344 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8888</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803887344\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-759765043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-759765043\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2045322141 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 07:44:40 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045322141\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1863240075 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JLuFWBIqC0ioEvpFZJFIHK3focmPImiwGVN4r3Ea</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863240075\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8888", "controller_action": "App\\Http\\Controllers\\home\\IndexController@index"}, "badge": null}}