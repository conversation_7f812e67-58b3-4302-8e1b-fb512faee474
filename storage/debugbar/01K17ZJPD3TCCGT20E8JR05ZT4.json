{"__meta": {"id": "01K17ZJPD3TCCGT20E8JR05ZT4", "datetime": "2025-07-28 15:39:58", "utime": **********.243919, "method": "GET", "uri": "/", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 22, "start": **********.475713, "end": **********.243957, "duration": 1.7682440280914307, "duration_str": "1.77s", "measures": [{"label": "Booting", "start": **********.475713, "relative_start": 0, "end": **********.739787, "relative_end": **********.739787, "duration": 0.*****************, "duration_str": "264ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.739824, "relative_start": 0.****************, "end": **********.243962, "relative_end": 5.0067901611328125e-06, "duration": 1.****************, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.74911, "relative_start": 0.*****************, "end": **********.751295, "relative_end": **********.751295, "duration": 0.0021851062774658203, "duration_str": "2.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home.index.index", "start": **********.237331, "relative_start": 0.**************, "end": **********.237331, "relative_end": **********.237331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.mainTpl", "start": **********.634828, "relative_start": 1.****************, "end": **********.634828, "relative_end": **********.634828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.954457, "relative_start": 1.****************, "end": **********.954457, "relative_end": **********.954457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.958166, "relative_start": 1.4824528694152832, "end": **********.958166, "relative_end": **********.958166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.036128, "relative_start": 1.5604150295257568, "end": **********.036128, "relative_end": **********.036128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.038102, "relative_start": 1.5623888969421387, "end": **********.038102, "relative_end": **********.038102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.039603, "relative_start": 1.563889980316162, "end": **********.039603, "relative_end": **********.039603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.040323, "relative_start": 1.5646100044250488, "end": **********.040323, "relative_end": **********.040323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.newsLink", "start": **********.040874, "relative_start": 1.5651609897613525, "end": **********.040874, "relative_end": **********.040874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.060277, "relative_start": 1.584563970565796, "end": **********.060277, "relative_end": **********.060277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.060981, "relative_start": 1.5852680206298828, "end": **********.060981, "relative_end": **********.060981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.062409, "relative_start": 1.586695909500122, "end": **********.062409, "relative_end": **********.062409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.063309, "relative_start": 1.5875959396362305, "end": **********.063309, "relative_end": **********.063309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.063859, "relative_start": 1.5881459712982178, "end": **********.063859, "relative_end": **********.063859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.064894, "relative_start": 1.5891809463500977, "end": **********.064894, "relative_end": **********.064894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.other_nav", "start": **********.067116, "relative_start": 1.5914030075073242, "end": **********.067116, "relative_end": **********.067116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.231191, "relative_start": 1.7554779052734375, "end": **********.231191, "relative_end": **********.231191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.231931, "relative_start": 1.7562179565429688, "end": **********.231931, "relative_end": **********.231931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.product.select_type_vue", "start": **********.237353, "relative_start": 1.7616400718688965, "end": **********.237353, "relative_end": **********.237353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 6536104, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.16", "PHP Version": "8.3.14", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8888", "Timezone": "Asia/Taipei", "Locale": "A"}}, "views": {"count": 19, "nb_templates": 19, "templates": [{"name": "home.index.index", "param_count": null, "params": [], "start": **********.237283, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/index.blade.phphome.index.index", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Findex%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "home.Public.mainTpl", "param_count": null, "params": [], "start": **********.634736, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/mainTpl.blade.phphome.Public.mainTpl", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FmainTpl.blade.php&line=1", "ajax": false, "filename": "mainTpl.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.954399, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.958102, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.036067, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.038061, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.039563, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.040288, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.newsLink", "param_count": null, "params": [], "start": **********.040842, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/newsLink.blade.phphome.Public.newsLink", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FnewsLink.blade.php&line=1", "ajax": false, "filename": "newsLink.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.060212, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.06092, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.062358, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.063272, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.063822, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.064854, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.other_nav", "param_count": null, "params": [], "start": **********.067049, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/other_nav.blade.phphome.Public.other_nav", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fother_nav.blade.php&line=1", "ajax": false, "filename": "other_nav.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.231153, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.231895, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.product.select_type_vue", "param_count": null, "params": [], "start": **********.237298, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/select_type_vue.blade.phphome.product.select_type_vue", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Fproduct%2Fselect_type_vue.blade.php&line=1", "ajax": false, "filename": "select_type_vue.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.27995999999999993, "accumulated_duration_str": "280ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}], "start": **********.753179, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:197", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=197", "ajax": false, "filename": "HelperService.php", "line": "197"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0, "width_percent": 0.861}, {"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, {"index": 14, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.759317, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:210", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=210", "ajax": false, "filename": "HelperService.php", "line": "210"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0.861, "width_percent": 0.907}, {"sql": "select * from `backstage_menu` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.766374, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:213", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=213", "ajax": false, "filename": "HelperService.php", "line": "213"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 1.768, "width_percent": 0.911}, {"sql": "select * from `backstage_menu_second` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.773554, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:215", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=215", "ajax": false, "filename": "HelperService.php", "line": "215"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 2.679, "width_percent": 1.293}, {"sql": "select * from `frontend_menu_name` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.793042, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:65", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=65", "ajax": false, "filename": "GlobalController.php", "line": "65"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 3.972, "width_percent": 0.879}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.799721, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:76", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=76", "ajax": false, "filename": "GlobalController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 4.851, "width_percent": 1.032}, {"sql": "select * from `admin_info` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.807188, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:80", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=80", "ajax": false, "filename": "GlobalController.php", "line": "80"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 5.883, "width_percent": 0.918}, {"sql": "select * from `index_online` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.8147058, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:66", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=66", "ajax": false, "filename": "PublicController.php", "line": "66"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 6.801, "width_percent": 0.847}, {"sql": "select * from `index_excel` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.8209581, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:69", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=69", "ajax": false, "filename": "PublicController.php", "line": "69"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 7.648, "width_percent": 0.982}, {"sql": "select * from `seo`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.827876, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:73", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=73", "ajax": false, "filename": "PublicController.php", "line": "73"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 8.63, "width_percent": 0.939}, {"sql": "select `id` from `stronghold` order by order_id asc, id desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.835422, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:76", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=76", "ajax": false, "filename": "PublicController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 9.569, "width_percent": 1.582}, {"sql": "select `title`, `id`, `recommend`, `pic_icon` as `pic` from `product` where `online` = 1 and `distributor_id` = 0 and `show_on_nav` = 1 order by order_id asc, id asc", "type": "query", "params": [], "bindings": [1, 0, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.84546, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:108", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=108", "ajax": false, "filename": "PublicController.php", "line": "108"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 11.152, "width_percent": 1.325}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='15' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.854403, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 12.477, "width_percent": 1.164}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='17' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.86232, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 13.641, "width_percent": 0.997}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='20' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.871056, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 14.638, "width_percent": 1.618}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='1' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.880882, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 16.256, "width_percent": 1.122}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='3' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.888775, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 17.377, "width_percent": 0.814}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='10' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.896728, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 18.192, "width_percent": 0.85}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='12' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.9043171, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 19.042, "width_percent": 1.493}, {"sql": "select exists(select * from `visitor_logs` where `ip` = '::1' and `product_id` = 0 and date(`created_at`) = '2025-07-28') as `exists`", "type": "query", "params": [], "bindings": ["::1", 0, "2025-07-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, {"index": 10, "namespace": null, "name": "app/Http/Middleware/CountVisitors.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Middleware/CountVisitors.php", "line": 14}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}], "start": **********.917729, "duration": 0.17241, "duration_str": "172ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:308", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=308", "ajax": false, "filename": "PublicController.php", "line": "308"}, "connection": "<PERSON><PERSON><PERSON><PERSON><PERSON>_shop_admin", "explain": null, "start_percent": 20.535, "width_percent": 61.584}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.098852, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:20", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=20", "ajax": false, "filename": "IndexController.php", "line": "20"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 82.119, "width_percent": 1.282}, {"sql": "select `id`, `title`, `pic_icon`, `index_adv01_pic`, `index_adv01_link`, `index_adv02_pic`, `index_adv02_link`, `index_adv03_pic`, `index_adv03_link`, `index_adv04_pic`, `index_adv04_link`, `index_adv05_pic`, `index_adv05_link`, `index_adv06_pic`, `index_adv06_link`, `index_adv07_pic`, `index_adv07_link` from `product` where (`ad_online` = 1 and `online` = 1) order by order_id", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.10988, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 83.401, "width_percent": 1.239}, {"sql": "select `title`, `pic`, `link` from `slideshow` where `online` = 1 order by `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.121762, "duration": 0.012119999999999999, "duration_str": "12.12ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:61", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=61", "ajax": false, "filename": "IndexController.php", "line": "61"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 84.641, "width_percent": 4.329}, {"sql": "select * from `index_ad` where `online` = 1 order by `orders` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.140159, "duration": 0.00667, "duration_str": "6.67ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:64", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=64", "ajax": false, "filename": "IndexController.php", "line": "64"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 88.97, "width_percent": 2.382}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `expiring_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 67}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.154372, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 91.352, "width_percent": 0.993}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `hot_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 70}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.164365, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 92.345, "width_percent": 0.989}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `recommend_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.1745229, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 93.335, "width_percent": 1.032}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `spe_price_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by orders asc, table.id desc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.18399, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 94.367, "width_percent": 0.989}, {"sql": "select `data3` from `index_excel` where `id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.190652, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:79", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=79", "ajax": false, "filename": "IndexController.php", "line": "79"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 95.356, "width_percent": 0.904}, {"sql": "select `data2`, `data3` from `index_excel` where `id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.197665, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:83", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=83", "ajax": false, "filename": "IndexController.php", "line": "83"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 96.26, "width_percent": 1.418}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 109}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.221016, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 97.678, "width_percent": 0.822}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '2' limit 1", "type": "query", "params": [], "bindings": [1, "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.227435, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 98.5, "width_percent": 1.5}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8888", "action_name": null, "controller_action": "App\\Http\\Controllers\\home\\IndexController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\home\\IndexController@index<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/home/<USER>/a>", "middleware": "web", "duration": "1.9s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-232304517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-232304517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1043050092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1043050092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1713446265 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8888</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713446265\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-863509363 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-863509363\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-132641120 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 07:39:57 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132641120\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-827200639 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">v4JrqZCAkdhsKwhZC9aC1sZu11Qla4A1eXNObmEd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827200639\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8888", "controller_action": "App\\Http\\Controllers\\home\\IndexController@index"}, "badge": null}}