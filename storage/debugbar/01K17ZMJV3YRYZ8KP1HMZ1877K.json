{"__meta": {"id": "01K17ZMJV3YRYZ8KP1HMZ1877K", "datetime": "2025-07-28 15:41:00", "utime": **********.131521, "method": "GET", "uri": "/", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 22, "start": **********.330878, "end": **********.13157, "duration": 1.800692081451416, "duration_str": "1.8s", "measures": [{"label": "Booting", "start": **********.330878, "relative_start": 0, "end": **********.603403, "relative_end": **********.603403, "duration": 0.****************, "duration_str": "273ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.603444, "relative_start": 0.****************, "end": **********.131576, "relative_end": 5.9604644775390625e-06, "duration": 1.****************, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.611595, "relative_start": 0.****************, "end": **********.613592, "relative_end": **********.613592, "duration": 0.0019969940185546875, "duration_str": "2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home.index.index", "start": **********.913072, "relative_start": 0.****************, "end": **********.913072, "relative_end": **********.913072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.mainTpl", "start": **********.328789, "relative_start": 0.****************, "end": **********.328789, "relative_end": **********.328789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.794737, "relative_start": 1.****************, "end": **********.794737, "relative_end": **********.794737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.79851, "relative_start": 1.4676320552825928, "end": **********.79851, "relative_end": **********.79851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.890018, "relative_start": 1.5591399669647217, "end": **********.890018, "relative_end": **********.890018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.891847, "relative_start": 1.5609688758850098, "end": **********.891847, "relative_end": **********.891847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.893686, "relative_start": 1.5628080368041992, "end": **********.893686, "relative_end": **********.893686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.894763, "relative_start": 1.563884973526001, "end": **********.894763, "relative_end": **********.894763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.newsLink", "start": **********.895377, "relative_start": 1.5644989013671875, "end": **********.895377, "relative_end": **********.895377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.917489, "relative_start": 1.586611032485962, "end": **********.917489, "relative_end": **********.917489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.91884, "relative_start": 1.5879619121551514, "end": **********.91884, "relative_end": **********.91884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.920328, "relative_start": 1.5894498825073242, "end": **********.920328, "relative_end": **********.920328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.921587, "relative_start": 1.5907089710235596, "end": **********.921587, "relative_end": **********.921587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.922579, "relative_start": 1.5917010307312012, "end": **********.922579, "relative_end": **********.922579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.923772, "relative_start": 1.5928940773010254, "end": **********.923772, "relative_end": **********.923772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.other_nav", "start": **********.927838, "relative_start": 1.5969600677490234, "end": **********.927838, "relative_end": **********.927838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.112866, "relative_start": 1.7819879055023193, "end": **********.112866, "relative_end": **********.112866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.113576, "relative_start": 1.7826979160308838, "end": **********.113576, "relative_end": **********.113576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.product.select_type_vue", "start": **********.120736, "relative_start": 1.7898578643798828, "end": **********.120736, "relative_end": **********.120736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 6536104, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.16", "PHP Version": "8.3.14", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8888", "Timezone": "Asia/Taipei", "Locale": "A"}}, "views": {"count": 19, "nb_templates": 19, "templates": [{"name": "home.index.index", "param_count": null, "params": [], "start": **********.912998, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/index.blade.phphome.index.index", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Findex%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "home.Public.mainTpl", "param_count": null, "params": [], "start": **********.328729, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/mainTpl.blade.phphome.Public.mainTpl", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FmainTpl.blade.php&line=1", "ajax": false, "filename": "mainTpl.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.794696, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.79846, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.889977, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.891807, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.893649, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.894724, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.newsLink", "param_count": null, "params": [], "start": **********.895341, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/newsLink.blade.phphome.Public.newsLink", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FnewsLink.blade.php&line=1", "ajax": false, "filename": "newsLink.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.917334, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.918778, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.920267, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.921525, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.922518, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.923711, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.other_nav", "param_count": null, "params": [], "start": **********.927773, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/other_nav.blade.phphome.Public.other_nav", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fother_nav.blade.php&line=1", "ajax": false, "filename": "other_nav.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.112827, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.113543, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.product.select_type_vue", "param_count": null, "params": [], "start": **********.120548, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/select_type_vue.blade.phphome.product.select_type_vue", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Fproduct%2Fselect_type_vue.blade.php&line=1", "ajax": false, "filename": "select_type_vue.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.16293999999999997, "accumulated_duration_str": "163ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}], "start": **********.615099, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:197", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=197", "ajax": false, "filename": "HelperService.php", "line": "197"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0, "width_percent": 1.332}, {"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, {"index": 14, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.620477, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:210", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=210", "ajax": false, "filename": "HelperService.php", "line": "210"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 1.332, "width_percent": 1.264}, {"sql": "select * from `backstage_menu` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.6270661, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:213", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=213", "ajax": false, "filename": "HelperService.php", "line": "213"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 2.596, "width_percent": 1.381}, {"sql": "select * from `backstage_menu_second` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.6322849, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:215", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=215", "ajax": false, "filename": "HelperService.php", "line": "215"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 3.977, "width_percent": 1.731}, {"sql": "select * from `frontend_menu_name` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.64543, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:65", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=65", "ajax": false, "filename": "GlobalController.php", "line": "65"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 5.708, "width_percent": 1.387}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.65148, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:76", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=76", "ajax": false, "filename": "GlobalController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 7.095, "width_percent": 1.725}, {"sql": "select * from `admin_info` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.6587539, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:80", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=80", "ajax": false, "filename": "GlobalController.php", "line": "80"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 8.819, "width_percent": 1.129}, {"sql": "select * from `index_online` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.664514, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:66", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=66", "ajax": false, "filename": "PublicController.php", "line": "66"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 9.948, "width_percent": 1.633}, {"sql": "select * from `index_excel` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.67044, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:69", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=69", "ajax": false, "filename": "PublicController.php", "line": "69"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 11.581, "width_percent": 1.59}, {"sql": "select * from `seo`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.6759481, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:73", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=73", "ajax": false, "filename": "PublicController.php", "line": "73"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 13.17, "width_percent": 1.596}, {"sql": "select `id` from `stronghold` order by order_id asc, id desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.681291, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:76", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=76", "ajax": false, "filename": "PublicController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 14.766, "width_percent": 1.184}, {"sql": "select `title`, `id`, `recommend`, `pic_icon` as `pic` from `product` where `online` = 1 and `distributor_id` = 0 and `show_on_nav` = 1 order by order_id asc, id asc", "type": "query", "params": [], "bindings": [1, 0, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.687556, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:108", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=108", "ajax": false, "filename": "PublicController.php", "line": "108"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 15.951, "width_percent": 1.399}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='15' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.693959, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 17.35, "width_percent": 1.663}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='17' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.700128, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 19.013, "width_percent": 1.252}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='20' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.70624, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 20.265, "width_percent": 1.497}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='1' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.712025, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 21.763, "width_percent": 1.277}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='3' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.717439, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 23.039, "width_percent": 1.442}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='10' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.723355, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 24.481, "width_percent": 1.59}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='12' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.730157, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 26.071, "width_percent": 1.534}, {"sql": "select exists(select * from `visitor_logs` where `ip` = '::1' and `product_id` = 0 and date(`created_at`) = '2025-07-28') as `exists`", "type": "query", "params": [], "bindings": ["::1", 0, "2025-07-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, {"index": 10, "namespace": null, "name": "app/Http/Middleware/CountVisitors.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Middleware/CountVisitors.php", "line": 14}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}], "start": **********.740053, "duration": 0.08704, "duration_str": "87.04ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:308", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=308", "ajax": false, "filename": "PublicController.php", "line": "308"}, "connection": "<PERSON><PERSON><PERSON><PERSON><PERSON>_shop_admin", "explain": null, "start_percent": 27.605, "width_percent": 53.418}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.831474, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:20", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=20", "ajax": false, "filename": "IndexController.php", "line": "20"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 81.024, "width_percent": 1.424}, {"sql": "select `id`, `title`, `pic_icon`, `index_adv01_pic`, `index_adv01_link`, `index_adv02_pic`, `index_adv02_link`, `index_adv03_pic`, `index_adv03_link`, `index_adv04_pic`, `index_adv04_link`, `index_adv05_pic`, `index_adv05_link`, `index_adv06_pic`, `index_adv06_link`, `index_adv07_pic`, `index_adv07_link` from `product` where (`ad_online` = 1 and `online` = 1) order by order_id", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.8379061, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 82.448, "width_percent": 1.675}, {"sql": "select `title`, `pic`, `link` from `slideshow` where `online` = 1 order by `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.84363, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:61", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=61", "ajax": false, "filename": "IndexController.php", "line": "61"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 84.123, "width_percent": 1.27}, {"sql": "select * from `index_ad` where `online` = 1 order by `orders` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.848579, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:64", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=64", "ajax": false, "filename": "IndexController.php", "line": "64"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 85.393, "width_percent": 1.412}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `expiring_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 67}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.8558571, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 86.805, "width_percent": 1.491}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `hot_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 70}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.863066, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 88.296, "width_percent": 1.436}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `recommend_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.869452, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 89.732, "width_percent": 1.743}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `spe_price_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by orders asc, table.id desc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.877062, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 91.475, "width_percent": 2.038}, {"sql": "select `data3` from `index_excel` where `id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.883488, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:79", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=79", "ajax": false, "filename": "IndexController.php", "line": "79"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 93.513, "width_percent": 1.393}, {"sql": "select `data2`, `data3` from `index_excel` where `id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.8903608, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:83", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=83", "ajax": false, "filename": "IndexController.php", "line": "83"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 94.906, "width_percent": 1.436}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 109}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.896599, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 96.342, "width_percent": 1.927}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '2' limit 1", "type": "query", "params": [], "bindings": [1, "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.905012, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 98.269, "width_percent": 1.731}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8888", "action_name": null, "controller_action": "App\\Http\\Controllers\\home\\IndexController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\home\\IndexController@index<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/home/<USER>/a>", "middleware": "web", "duration": "1.98s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-884929365 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-884929365\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1250212204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1250212204\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-235616415 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8888</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235616415\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1970619534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1970619534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-53559355 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 07:40:58 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53559355\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-228991618 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DSy7UlR1lO9b8FrGNPCyy35h6EWKhVKmXOBD859W</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228991618\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8888", "controller_action": "App\\Http\\Controllers\\home\\IndexController@index"}, "badge": null}}