{"__meta": {"id": "01K17ZK08RAMXFZWHTWVJYRP7Z", "datetime": "2025-07-28 15:40:08", "utime": **********.34466, "method": "GET", "uri": "/", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 22, "start": **********.24752, "end": **********.344727, "duration": 2.0972070693969727, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": **********.24752, "relative_start": 0, "end": **********.757166, "relative_end": **********.757166, "duration": 0.***************, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.757229, "relative_start": 0.****************, "end": **********.344736, "relative_end": 9.059906005859375e-06, "duration": 1.****************, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.775669, "relative_start": 0.****************, "end": **********.784918, "relative_end": **********.784918, "duration": 0.009248971939086914, "duration_str": "9.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home.index.index", "start": **********.219617, "relative_start": 0.****************, "end": **********.219617, "relative_end": **********.219617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.mainTpl", "start": **********.634919, "relative_start": 1.****************, "end": **********.634919, "relative_end": **********.634919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.986955, "relative_start": 1.****************, "end": **********.986955, "relative_end": **********.986955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.990919, "relative_start": 1.7433991432189941, "end": **********.990919, "relative_end": **********.990919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.095169, "relative_start": 1.847649097442627, "end": **********.095169, "relative_end": **********.095169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.097551, "relative_start": 1.8500311374664307, "end": **********.097551, "relative_end": **********.097551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.101895, "relative_start": 1.8543751239776611, "end": **********.101895, "relative_end": **********.101895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.102954, "relative_start": 1.8554339408874512, "end": **********.102954, "relative_end": **********.102954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.newsLink", "start": **********.103721, "relative_start": 1.856200933456421, "end": **********.103721, "relative_end": **********.103721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.125554, "relative_start": 1.8780341148376465, "end": **********.125554, "relative_end": **********.125554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.nav_addition_btn", "start": **********.126204, "relative_start": 1.8786840438842773, "end": **********.126204, "relative_end": **********.126204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.127454, "relative_start": 1.8799340724945068, "end": **********.127454, "relative_end": **********.127454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_orderform", "start": **********.128508, "relative_start": 1.8809881210327148, "end": **********.128508, "relative_end": **********.128508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.129447, "relative_start": 1.8819270133972168, "end": **********.129447, "relative_end": **********.129447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.131423, "relative_start": 1.8839030265808105, "end": **********.131423, "relative_end": **********.131423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.other_nav", "start": **********.135833, "relative_start": 1.8883130550384521, "end": **********.135833, "relative_end": **********.135833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_cancel", "start": **********.319221, "relative_start": 2.0717010498046875, "end": **********.319221, "relative_end": **********.319221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.Public.svg_search", "start": **********.320588, "relative_start": 2.073068141937256, "end": **********.320588, "relative_end": **********.320588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: home.product.select_type_vue", "start": **********.332504, "relative_start": 2.084984064102173, "end": **********.332504, "relative_end": **********.332504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 6536104, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.16", "PHP Version": "8.3.14", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8888", "Timezone": "Asia/Taipei", "Locale": "A"}}, "views": {"count": 19, "nb_templates": 19, "templates": [{"name": "home.index.index", "param_count": null, "params": [], "start": **********.219572, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/index.blade.phphome.index.index", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Findex%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "home.Public.mainTpl", "param_count": null, "params": [], "start": **********.634856, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/mainTpl.blade.phphome.Public.mainTpl", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FmainTpl.blade.php&line=1", "ajax": false, "filename": "mainTpl.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.986893, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.990853, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.095131, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.097447, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.101672, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.102915, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.newsLink", "param_count": null, "params": [], "start": **********.103653, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/newsLink.blade.phphome.Public.newsLink", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2FnewsLink.blade.php&line=1", "ajax": false, "filename": "newsLink.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.125489, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.nav_addition_btn", "param_count": null, "params": [], "start": **********.126147, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/nav_addition_btn.blade.phphome.Public.nav_addition_btn", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fnav_addition_btn.blade.php&line=1", "ajax": false, "filename": "nav_addition_btn.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.127401, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_orderform", "param_count": null, "params": [], "start": **********.128448, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_orderform.blade.phphome.Public.svg_orderform", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_orderform.blade.php&line=1", "ajax": false, "filename": "svg_orderform.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.129389, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.131175, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.Public.other_nav", "param_count": null, "params": [], "start": **********.135772, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/other_nav.blade.phphome.Public.other_nav", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fother_nav.blade.php&line=1", "ajax": false, "filename": "other_nav.blade.php", "line": "?"}}, {"name": "home.Public.svg_cancel", "param_count": null, "params": [], "start": **********.31916, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_cancel.blade.phphome.Public.svg_cancel", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_cancel.blade.php&line=1", "ajax": false, "filename": "svg_cancel.blade.php", "line": "?"}}, {"name": "home.Public.svg_search", "param_count": null, "params": [], "start": **********.320525, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/svg_search.blade.phphome.Public.svg_search", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2FPublic%2Fsvg_search.blade.php&line=1", "ajax": false, "filename": "svg_search.blade.php", "line": "?"}}, {"name": "home.product.select_type_vue", "param_count": null, "params": [], "start": **********.332346, "type": "blade", "hash": "blade/Users/<USER>/project/photonic/nakamurafumi/resources/views/home/<USER>/select_type_vue.blade.phphome.product.select_type_vue", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fresources%2Fviews%2Fhome%2Fproduct%2Fselect_type_vue.blade.php&line=1", "ajax": false, "filename": "select_type_vue.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.23446999999999998, "accumulated_duration_str": "234ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}], "start": **********.789001, "duration": 0.00297, "duration_str": "2.97ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:197", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=197", "ajax": false, "filename": "HelperService.php", "line": "197"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 0, "width_percent": 1.267}, {"sql": "select `purview` from `admin` where `permission` = 'current' limit 1", "type": "query", "params": [], "bindings": ["current"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, {"index": 14, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.798667, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:210", "source": {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=210", "ajax": false, "filename": "HelperService.php", "line": "210"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 1.267, "width_percent": 1.343}, {"sql": "select * from `backstage_menu` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.809593, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:213", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=213", "ajax": false, "filename": "HelperService.php", "line": "213"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 2.61, "width_percent": 1.561}, {"sql": "select * from `backstage_menu_second` order by `sort` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, {"index": 13, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 200}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 27}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}], "start": **********.820649, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "HelperService.php:215", "source": {"index": 12, "namespace": null, "name": "app/Services/pattern/HelperService.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Services/pattern/HelperService.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FServices%2Fpattern%2FHelperService.php&line=215", "ajax": false, "filename": "HelperService.php", "line": "215"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 4.171, "width_percent": 1.744}, {"sql": "select * from `frontend_menu_name` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.846479, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:65", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=65", "ajax": false, "filename": "GlobalController.php", "line": "65"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 5.915, "width_percent": 1.685}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.855069, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:76", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=76", "ajax": false, "filename": "GlobalController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 7.6, "width_percent": 1.369}, {"sql": "select * from `admin_info` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.8620672, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "GlobalController.php:80", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FGlobalController.php&line=80", "ajax": false, "filename": "GlobalController.php", "line": "80"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 8.969, "width_percent": 1.164}, {"sql": "select * from `index_online` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.8705199, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:66", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=66", "ajax": false, "filename": "PublicController.php", "line": "66"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 10.133, "width_percent": 1.152}, {"sql": "select * from `index_excel` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.878639, "duration": 0.00618, "duration_str": "6.18ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:69", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=69", "ajax": false, "filename": "PublicController.php", "line": "69"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 11.285, "width_percent": 2.636}, {"sql": "select * from `seo`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.8913329, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:73", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=73", "ajax": false, "filename": "PublicController.php", "line": "73"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 13.921, "width_percent": 1.134}, {"sql": "select `id` from `stronghold` order by order_id asc, id desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 706}], "start": **********.898144, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:76", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=76", "ajax": false, "filename": "PublicController.php", "line": "76"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 15.055, "width_percent": 1.723}, {"sql": "select `title`, `id`, `recommend`, `pic_icon` as `pic` from `product` where `online` = 1 and `distributor_id` = 0 and `show_on_nav` = 1 order by order_id asc, id asc", "type": "query", "params": [], "bindings": [1, 0, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.907597, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:108", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=108", "ajax": false, "filename": "PublicController.php", "line": "108"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 16.778, "width_percent": 1.697}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='15' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.917175, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 18.476, "width_percent": 1.181}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='17' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.924113, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 19.657, "width_percent": 1.147}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='20' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.930785, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 20.804, "width_percent": 1.587}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='1' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.940876, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 22.391, "width_percent": 1.437}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='3' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.949074, "duration": 0.0059299999999999995, "duration_str": "5.93ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 23.828, "width_percent": 2.529}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='10' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.959873, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 26.357, "width_percent": 0.844}, {"sql": "select `title`, `id`, `pic` from `typeinfo` where parent_id='12' AND branch_id = 0\nAND (\nend <= 0\nOR\n(start < ********** AND end > **********)\n) and `online` = 1 and `distributor_id` = 0 order by order_id", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 929}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 770}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 856}], "start": **********.96889, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:163", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=163", "ajax": false, "filename": "PublicController.php", "line": "163"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 27.202, "width_percent": 1.228}, {"sql": "select exists(select * from `visitor_logs` where `ip` = '::1' and `product_id` = 0 and date(`created_at`) = '2025-07-28') as `exists`", "type": "query", "params": [], "bindings": ["::1", 0, "2025-07-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, {"index": 10, "namespace": null, "name": "app/Http/Middleware/CountVisitors.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Middleware/CountVisitors.php", "line": 14}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 180}], "start": **********.97853, "duration": 0.13324, "duration_str": "133ms", "memory": 0, "memory_str": null, "filename": "PublicController.php:308", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 308}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FPublicController.php&line=308", "ajax": false, "filename": "PublicController.php", "line": "308"}, "connection": "<PERSON><PERSON><PERSON><PERSON><PERSON>_shop_admin", "explain": null, "start_percent": 28.43, "width_percent": 56.826}, {"sql": "select * from `frontend_data_name` where `show_type` = 'tag' order by `id` asc", "type": "query", "params": [], "bindings": ["tag"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.118403, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:20", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=20", "ajax": false, "filename": "IndexController.php", "line": "20"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 85.256, "width_percent": 1.032}, {"sql": "select `id`, `title`, `pic_icon`, `index_adv01_pic`, `index_adv01_link`, `index_adv02_pic`, `index_adv02_link`, `index_adv03_pic`, `index_adv03_link`, `index_adv04_pic`, `index_adv04_link`, `index_adv05_pic`, `index_adv05_link`, `index_adv06_pic`, `index_adv06_link`, `index_adv07_pic`, `index_adv07_link` from `product` where (`ad_online` = 1 and `online` = 1) order by order_id", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.1262848, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 86.288, "width_percent": 0.78}, {"sql": "select `title`, `pic`, `link` from `slideshow` where `online` = 1 order by `id` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.1319618, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:61", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=61", "ajax": false, "filename": "IndexController.php", "line": "61"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 87.069, "width_percent": 1.906}, {"sql": "select * from `index_ad` where `online` = 1 order by `orders` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.1408432, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:64", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=64", "ajax": false, "filename": "IndexController.php", "line": "64"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 88.975, "width_percent": 1.331}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `expiring_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 67}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.150113, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 90.306, "width_percent": 1.557}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `hot_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 70}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.16054, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 91.862, "width_percent": 1.105}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `recommend_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by `table`.`id` asc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 73}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.1712132, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 92.967, "width_percent": 1.322}, {"sql": "select `productinfo`.`title`, `productinfo`.`id`, `productinfo`.`pic`, `productinfo`.`has_price`, `productinfo`.`is_registrable` from `spe_price_product` as `table` inner join `productinfo` on `productinfo`.`id` = `table`.`product_id` where `productinfo`.`online` = 1 and `table`.`product_id` <> 0 order by orders asc, table.id desc", "type": "query", "params": [], "bindings": [1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 76}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.1818192, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:152", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=152", "ajax": false, "filename": "IndexController.php", "line": "152"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 94.289, "width_percent": 1.616}, {"sql": "select `data3` from `index_excel` where `id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.188777, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:79", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=79", "ajax": false, "filename": "IndexController.php", "line": "79"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 95.906, "width_percent": 1.143}, {"sql": "select `data2`, `data3` from `index_excel` where `id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "start": **********.1948051, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:83", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=83", "ajax": false, "filename": "IndexController.php", "line": "83"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 97.049, "width_percent": 1.028}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 109}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.203125, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 98.077, "width_percent": 0.913}, {"sql": "select `title`, `content`, `id`, `pic`, `has_price` from `productinfo` where `online` = 1 and `id` = '2' limit 1", "type": "query", "params": [], "bindings": [1, "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "/Users/<USER>/project/photonic/nakamurafumi/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "start": **********.210568, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:120", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/home/<USER>", "file": "/Users/<USER>/project/photonic/nakamurafumi/app/Http/Controllers/home/<USER>", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=120", "ajax": false, "filename": "IndexController.php", "line": "120"}, "connection": "nakam<PERSON><PERSON>mi_shop", "explain": null, "start_percent": 98.989, "width_percent": 1.011}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8888", "action_name": null, "controller_action": "App\\Http\\Controllers\\home\\IndexController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\home\\IndexController@index<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Flilun%2Fproject%2Fphotonic%2Fnakamurafumi%2Fapp%2FHttp%2FControllers%2Fhome%2FIndexController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/home/<USER>/a>", "middleware": "web", "duration": "2.27s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-997574167 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-997574167\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-600313939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-600313939\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-983155222 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8888</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983155222\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-322748227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-322748227\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1018813762 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 28 Jul 2025 07:40:07 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018813762\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-605659542 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IepNUzQiVptdrjDPlGW2dt6CS2Tv19j6uKCjc0AX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605659542\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8888", "controller_action": "App\\Http\\Controllers\\home\\IndexController@index"}, "badge": null}}