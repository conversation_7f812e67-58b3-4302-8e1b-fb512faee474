<?php

// +----------------------------------------------------------------------
// 黑貓宅急便物流參數
// +----------------------------------------------------------------------

$default_customer_id = '1265635401';
$default_customer_token = 'abcd1234';

$tcat = [
    'customer_id' => env('tcat.customer_id', $default_customer_id),
    'shippable' => ['宅配', '黑貓宅配', '黑貓宅配(貨到付款)', '冷藏宅配', '冷凍宅配'],
];

if (env('tcat.is_test')) {
    $tcat['service_url'] = 'https://egs.suda.com.tw:8443/api/Egs/';
    $tcat['customer_token'] = env('tcat.customer_test_token', $default_customer_token);
} else {
    $tcat['service_url'] = 'https://api.suda.com.tw/api/Egs/';
    $tcat['customer_token'] = env('tcat.customer_token', $default_customer_token);
}

return $tcat;
