<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\Mailsend;
use App\Services\CommonService;

class HelperService
{
    public static function getSeoData($config_db = '')
    {
        $seo = Cache::remember("seo_{$config_db}_1", 600, function () use ($config_db) {
            return DB::connection($config_db)->table('seo')->find(1);
        });

        return CommonService::objectToArray($seo);
    }

    public static function getMailData($config_db = '', $email_type = '')
    {
        $seo = self::getSeoData($config_db);
        $system_email = Cache::remember("system_email_1", 600, function () {
            return DB::table('system_email')->where('id', 1)->find(1);
        });
        $system_email = CommonService::objectToArray($system_email);

        if (substr($email_type, 0, 6) == 'admin_') {
            $return_email = Cache::remember("admin_email_{$email_type}", 600, function () use ($email_type) {
                $query = DB::table('admin')->whereNotNull('email');
                if ($email_type) {
                    $query = $query->join('admin_email_remind AS aer', 'aer.admin_id', 'admin.id')
                        ->where('aer.email_type', $email_type);
                }
                return $query->pluck('email');
            });
            $return_email = CommonService::objectToArray($return_email);
        } else {
            $return_email = [];
        }

        return [
            'mailHost' => config('extra.mail.host'),
            'mailUsername' => config('extra.mail.username'),
            'mailPassword' => config('extra.mail.password'),
            'mailSubject' => $seo['title'] . ' ' . Lang::get('系統信箱'),
            'mailFrom' => config('extra.mail.username'),
            'mailFromName' => $seo['title'],
            'system_email' => $system_email,
            'return_email' => $return_email,
        ];
    }

    public static function Mail_Send($Body = '', $email_type = 'admin_', $client_email = '', $subject = NULL, $config_db = '')
    {
        try {
            $globalMailData = self::getMailData($config_db, $email_type);

            $subject = Cache::remember("seo_title_{$config_db}_1", 600, function () use ($config_db) {
                return DB::connection($config_db)->table('seo')->where('id', 1)->value('title');
            }) . ' ' . Lang::get('系統信箱') . ' ' . $subject;

            $type_check = substr($email_type, 0, 6);

            if ($type_check == 'client') {
                Mail::to($client_email)->send(new Mailsend($subject, $Body));
            } else if ($type_check == 'admin_') {
                if (empty($globalMailData['return_email'])) {
                    throw new \Exception('無信箱可寄送');
                }

                Mail::to($globalMailData['return_email'])->send(new Mailsend($subject, $Body));
            }
        } catch (\Exception $e) {
            // 若要顯示錯誤，則取消註解以下：
            // dd($e->getMessage());

            return false;
        }

        return true;
    }

    /*發送請求*/
    public static function http_request($url, $data = null, $headers = null)
    {
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);

        if (empty($data) == false) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            curl_setopt($curl, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
            ]);
        }

        if ($headers) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }

        $output = curl_exec($curl);

        curl_close($curl);

        return $output;
    }

    // 文字星號隱藏
    public static function hidestr($string, $start = 0, $length = 0, $re = '*')
    {
        if (empty($string)) {
            return false;
        }

        $strarr = array();
        $mb_strlen = mb_strlen($string);

        // 循环把字符串变为数组
        while ($mb_strlen) {
            $strarr[] = mb_substr($string, 0, 1, 'utf8');
            $string = mb_substr($string, 1, $mb_strlen, 'utf8');
            $mb_strlen = mb_strlen($string);
        }

        $strlen = count($strarr);
        $begin = $start >= 0 ? $start : ($strlen - abs($start));
        $end = $last = $strlen - 1;

        if ($length > 0) {
            $end  = $begin + $length - 1;
        } elseif ($length < 0) {
            $end -= abs($length);
        }

        for ($i = $begin; $i <= $end; $i++) {
            $strarr[$i] = $re;
        }

        if ($begin >= $last || $end > $last) {
            return false;
        }

        return implode('', $strarr);
    }

    /*回傳並命名系統控制功能起停用狀態*/
    public static function get_excel_function()
    {
        $excel = Cache::remember("excel_main_db_all", 600, function () {
            return DB::connection('main_db')->table('excel')->orderBy('id')->get();
        });
        $excel = CommonService::objectToArray($excel);

        $excel_function = [];
        $excel_function['control_auto_logout'] = $excel[4]['value1'];           /* 自動登出 */

        $excel_function['control_pre_buy'] = $excel[0]['value1'];                         /* 商品可否設定超額購買 */
        $excel_function['control_card_pay'] = $excel[6]['value1'];                         /* 商品可否設定刷卡 */
        $excel_function['control_product_paying'] = $excel[20]['value1'];     /* 商品可否設定付款方法 */
        $excel_function['control_register'] = $excel[13]['value1'];                     /* 是否啟用報名功能 */
        $excel_function['control_product_shipping'] = $excel[14]['value1'];      /* 商品可否設定運法 */
        $excel_function['control_time_limit_prod'] = $excel[8]['value1'];          /* 首頁是否顯示限時搶購 */
        $excel_function['control_index_edm'] = $excel[10]['value1'];                    /* 首頁是否顯示EDM */
        $excel_function['control_sepc_price'] = $excel[9]['value1'];                    /* 是否使用特價商品(不限數量標籤) */
        $excel_function['control_copy_product'] = $excel[11]['value1'];       /* 後台複製商品 */

        $excel_function['control_point_duration'] = $excel[1]['value1'];        /* 點數到期週期(年) */
        $excel_function['control_point_duration_date'] = $excel[1]['value2']; /* 點數到期日期 */

        $excel_function['thirdpart_money'] = $excel[15]['value1'];                        /* 是否啟用第三方金流 */
        $excel_function['thirdpart_logistic'] = $excel[16]['value1'];                    /* 是否啟用第三方物流 */
        $excel_function['thirdpart_invoice'] = $excel[17]['value1'];              /* 是否啟用第三方發票 */

        $excel_function['control_FirstBuyDiscount'] = $excel[18]['value1'];   /* 首購優惠 */
        $excel_function['control_VipDiscount'] = $excel[19]['value1'];        /* VIP等級 */
        $excel_function['control_platform'] = $excel[21]['value1'];           /* 是否平台化 */
        $excel_function['control_img_quantity'] = $excel[2]['value1'];        /* 商品照片數量 */
        $excel_function['control_prod_type_layer'] = $excel[3]['value1'];     /* 商品階層品項 */
        $excel_function['control_upload_film'] = $excel[5]['value1'];         /* 商品圖是否可以放影片 */
        $excel_function['control_prod_edm'] = $excel[7]['value1'];            /* 商品是否顯示EDM */
        $excel_function['control_social_share'] = $excel[12]['value1'];       /* 商品是否可單一商品社群分享 */
        $excel_function['control_down_line'] = $excel[22]['value1'];          /* 是否使用招募會員 */

        $excel_function['invoice_style_text'] = [                             /* 發票開立方式選項 */
            '1' => '個人實體紙本發票',
            '2' => '個人電子郵件寄送發票',
            '3' => '個人共通性載具',
            '4' => '公司戶發票',
            '5' => '捐贈',
        ];

        return $excel_function;
    }

    /*依管理者帳號回傳有使用的功能*/
    public static function get_frontend_user_use_function()
    {
        $user_function_setting_current = Cache::remember("admin_purview_current", 600, function () {
            return DB::table('admin')->select('purview')->where('permission', 'current')->first()->purview;
        });
        $user_function_setting_current = json_decode($user_function_setting_current, true);

        return self::arrange_use_function($user_function_setting_current);
    }

    public static function arrange_use_function($user_function_setting, $admin_type = '', $permission = 'no')
    {
        $close_function = [];
        $close_desk = [];
        $close_desk_admin = [];

        /*總可用功能(current)*/
        $current_Block = Cache::remember("admin_purview_current", 600, function () {
            return DB::table('admin')->select('purview')->where('permission', 'current')->first()->purview;
        });
        $current_Block = json_decode($current_Block, true);

        $first_list = Cache::remember("backstage_menu_all", 600, function () {
            return DB::table('backstage_menu')->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
        });
        $first_list = CommonService::objectToArray($first_list);
        $second_list = Cache::remember("backstage_menu_second_all", 600, function () {
            return DB::table('backstage_menu_second')->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
        });
        $second_list = CommonService::objectToArray($second_list);

        $show_list = [];

        foreach ($first_list as $k => $v) {
            $show_list[$v['id']]['id'] = $v['id'];
            $show_list[$v['id']]['name'] = $v['name'];
            $show_list[$v['id']]['title'] = $v['title'];
        }

        foreach ($second_list as $k => $v) {
            $v['front_desk'] = $v['front_desk'] ?? '';
            $v['url_show'] = $v['url_show'] ?? '';

            if (empty($show_list[$v['backstage_menu_id']]['sub'])) {
                $show_list[$v['backstage_menu_id']]['sub'] = [];
            }

            if ($admin_type == 'distribution' && $v['target'] == '_parent') { /*供應商登入替換網址*/
                $v['url_show'] = str_replace('/admin/', '/' . $admin_type . '/', $v['url']);
                $v['url_show'] = str_replace('/order/', '/order' . $admin_type . '/', $v['url_show']);
                $v['url_show'] = url($v['url_show']);
            } else {
                $v['url_show'] = $v['url'];
                $v['url_show'] = url($v['url']);
            }

            $ck = 0;

            if (!empty($user_function_setting[$v['backstage_menu_id']])) {
                foreach ($user_function_setting[$v['backstage_menu_id']] as $bk => $bv) {
                    if ($v['id'] == $bv) {
                        $close_function[$v['name']] = 1; //紀錄被關閉的功能
                        $close_desk[strtolower($v['front_desk'])] = 1;
                        $close_desk_admin[strtolower($v['url_show'])] = 1;
                        $ck = 1;
                        // break;
                    }
                }
            }

            $need_hide_functions = false;

            if ($admin_type != 'admin') {
                $need_hide_functions = true;
            } else if ($permission == 'no') { /*一般管理者*/
                $need_hide_functions = true;
            }

            if ($need_hide_functions) {
                if (!empty($current_Block[$v['backstage_menu_id']])) { /*一般管理者*/
                    foreach ($current_Block[$v['backstage_menu_id']] as $bk => $bv) {
                        if ($v['id'] == $bv) {
                            $close_function[$v['name']] = 1; //紀錄被關閉的功能
                            $close_desk[strtolower($v['front_desk'])] = 1;
                            $close_desk_admin[strtolower($v['url_show'])] = 1;
                            $ck = 1;
                        }
                    }
                }
            }

            if ($ck == 0) {
                array_push($show_list[$v['backstage_menu_id']]['sub'], $v);
            }
        }

        foreach ($show_list as $k => $v) {
            if (count($v['sub']) == 0) {
                unset($show_list[$k]);
            }
        }

        $result['show_list'] = $show_list;

        $show_list_group = [];

        foreach ($show_list as $key => $value) {
            if (!isset($show_list_group[$value['title']])) {
                $show_list_group[$value['title']] = ['title' => $value['title'], 'show_list' => []];
            }
            array_push($show_list_group[$value['title']]['show_list'], $value);
        }

        $result['show_list_group'] = $show_list_group;
        $result['close_function'] = $close_function;
        $result['close_desk'] = $close_desk;
        $result['close_desk_admin'] = $close_desk_admin;

        return $result;
    }

    public static function zip32_api($addr)
    {
        preg_match('/^(\d)+/', $addr, $zip); /*找出字串最前方的數字*/

        if ($zip) {
            return $zip[0]; /*回傳自己填寫的郵遞區號*/
        } else {
            preg_match('/(?!\d).*$/', $addr, $words); /*找出去除字串最前方的數字後的字串*/

            if ($words) {
                $addr = $words[0];
            }
        }

        $url = "https://zip5.5432.tw/zip5json.py?adrs={$addr}";

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        $output = json_decode(curl_exec($curl));
        curl_close($curl);

        return $output->zipcode;
    }
}
