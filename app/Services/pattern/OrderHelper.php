<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\Proposal as DiscountProposal;
use App\Services\pattern\recursiveCorrdination\discountRC\MemberFactory as DiscountMemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\ActCalculate;
use App\Services\pattern\simpleFactory\discountFactory\DiscountFactory;
use App\Http\Controllers\admin\Payfee;
use App\Http\Controllers\admin\CartMethod;

class OrderHelper
{
    /*取得對應商品前台的config db設定*/
    public static function get_shop_db_config($order_number)
    {
        return substr($order_number, 0, 1) . '_sub';
    }

    /*將商品加入購物車*/
    public static function cartCtrl(string $cart_key, int $num = 0, string $cmd = 'increase', string $cart_session = 'cart_all', int $user_id = 0)
    {
        if (!empty(config('control.close_function_current')['訂單管理'])) {
            return [
                'code' => 0,
                'msg' => Lang::get('操作失敗'),
                'num' => $num,
            ];
        }

        /*輸入參數調整*/
        $num = $num ? $num : 0;
        $cmd = $cmd ? $cmd : 'increase';
        $cart_session = $cart_session ? $cart_session : 'cart_all';

        $Proposal = self::get_Proposal_GetCartData($cart_session, $user_id);
        $cart = $Proposal->getCartArray();

        [$type_id_ori, $key_type] = Proposal::get_prod_key_type($cart_key);
        $cart_key = $type_id_ori . '_' . $key_type; /*強制所有cart_key帶上key_type*/

        $code = 1;

        if ($cmd != 'delete') { /*刪除以外的操作才需檢查*/
            if (substr($key_type, 0, 3) == 'kol') { // 有掛某網紅
                $productinfo_type = DB::table('productinfo_type')->find($type_id_ori); // 找出該品項
                $productinfo_type = CommonService::objectToArray($productinfo_type);
                $productinfo = DB::table('productinfo')->find($productinfo_type['product_id']); // 找出該商品
                $prodcut = CommonService::objectToArray($productinfo);
                $kol_id = str_replace('kol', '', $key_type); // 取出網紅ID
                // 找出該商品的與該網紅關使用中的最新紀錄
                $kol_productinfo = DB::table('kol_productinfo')->whereRaw('productinfo_id =' . $productinfo_type['product_id'] . ' AND is_using=1 AND kol_id=' . $kol_id)->orderBy('id', 'desc')->get();
                $kol_productinfo = CommonService::objectToArray($kol_productinfo);

                if (!empty($kol_productinfo)) { // 檢查商品是該網紅代銷中
                    // 該網紅代銷中，接續檢查網紅是否已到開賣日
                    $kol = DB::table('kol_period')->whereRaw('date_start <="' . date('Y-m-d') . '" AND date_end >="' . date('Y-m-d') . '"')
                        ->where('kol_id', $kol_id)
                        ->first(); // 找出該網紅
                    $kol = CommonService::objectToArray($kol);

                    if (!$kol) { // 檢查網紅起賣日是否大於現在
                        // 未開賣
                        $key_type = 'normal';
                        $cart_key = $type_id_ori . '_normal';
                    }
                } else {
                    // 非該網紅代銷中
                    $key_type = 'normal';
                    $cart_key = $type_id_ori . '_normal';
                }
            }

            // 依加入購物車的品項區分檢查方式
            switch ($key_type) {
                case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
                case substr($key_type, 0, 3) == 'kol':
                case 'add':
                case substr($key_type, 0, 8) == 'askprice':
                case 'gift':
                    /*檢查目標商品可加入購物車*/
                    $result = self::check_status_can_add_cart($num, $type_id_ori, $cmd, $key_type);

                    if ($result['code'] == 0) {
                        $code = 0;
                        $msg = $result['msg'];

                        break;
                    }

                    $productinfo_type = DB::table('productinfo_type')->find($type_id_ori); // 找出該品項
                    $productinfo_type = CommonService::objectToArray($productinfo_type);
                    $productinfo = DB::table('productinfo')->find($productinfo_type['product_id']); // 找出該商品
                    $productinfo = CommonService::objectToArray($productinfo);
                    /*如果目標購物車是cart，則要檢查賣家是否統一*/
                    $distributor_id = $cart_session == 'cart' ? $productinfo['distributor_id'] : '';

                    /*按種類整理購物車商品，統計數量*/
                    $store_num = []; // 有加入購物車的商品的庫存量(結果為扣除cart_key商品外的購買量)
                    $addprice_num = []; // 加購商品累積數量

                    foreach ($cart as $cart_key2 => $cart_v) { /*對每個購物車內的商品處理，計算庫存及加購商品累積數量*/
                        [$cart_key_id, $cart_key_type] = Proposal::get_prod_key_type($cart_key2);

                        if (!in_array($cart_key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*屬於 productinfo商品*/
                            /*檢查目購物車內商品可加入購物車*/
                            $result = self::check_status_can_add_cart($num, $cart_key_id, $cmd, $cart_key_type, $distributor_id);

                            if ($result['code'] == 0) {
                                $code = 0;
                                $msg = $result['msg'];

                                break;
                            } else {
                                if (!isset($store_num[$cart_key_id])) {
                                    $store_num[$cart_key_id] = $result['target_p'];
                                }
                            }

                            if ($cart_key2 != $cart_key) { /*操作的商品非本次要加入購物車的商品*/
                                $store_num[$cart_key_id]['num'] -= $cart_v; /*從記錄的庫存中扣除該數量*/
                            }

                            if (substr($cart_key_type, 0, 3) == 'add') { /*統計購物車裡各品項加價購商品的數量*/
                                if (!isset($addprice_num[$cart_key_id])) {
                                    $addprice_num[$cart_key_id] = $cart_v;
                                } else {
                                    $addprice_num[$cart_key_id] += $cart_v;
                                }
                            }
                        }
                    }

                    if ($key_type == 'add') { /*檢查加價購商品數量上限*/
                        $addable_addprice = Proposal::get_addprice_products_by_cart();
                        $num_limit = false;
                        $addable_addprice_num = isset($addable_addprice['type' . $type_id_ori]) ? $addable_addprice['type' . $type_id_ori]['adp_p_num'] : 0; /*找出加入購物車的品項的可加購量*/

                        if (empty($addprice_num[$type_id_ori])) { // 購物車內無此商品
                            if ($addable_addprice_num < $num) { // 可加購量小於 本次加入的量
                                $num_limit = true;
                            }
                        } else if ($cmd == 'increase') { // 用加的方法
                            if ($addable_addprice_num < $addprice_num[$type_id_ori] + $num) { // 可加購量小於 購物車累積量 + 本次加入的量
                                $num_limit = true;
                            }
                        } else if ($cmd == 'assign') { // 用指派數量的方法
                            if ($addable_addprice_num < $addprice_num[$type_id_ori] - $cart[$cart_key] + $num) { // 可加購量小於 購物車累積的量 - 某品項的量 + 某品項指派的量
                                $num_limit = true;
                            }
                        }

                        if ($num_limit) {
                            $code = 0;
                            $msg = Lang::get('超出加價購數量上限') . '：' . $productinfo['title'];

                            break;
                        }
                    }

                    /*統計購買量*/
                    $buy_num = $num; /*品項單一類類型購買數量(區分一般、網紅推薦、加價購...)*/
                    $storeNum = isset($store_num[$type_id_ori]) ? $store_num[$type_id_ori]['num'] : DB::table('productinfo_type')->find($type_id_ori)->num;

                    // 購物車內無此商品
                    if (!isset($store_num[$type_id_ori])) {
                    } else if ($cmd == 'increase') { // 用加的方法
                        if (isset($cart[$cart_key])) {
                            $buy_num = $cart[$cart_key] + $num;
                        } // 購買數量是 購物車量 + 本次加入的量
                    } else if ($cmd == 'assign') { // 用指派數量的方法
                    }

                    if (substr($key_type, 0, 8) == 'askprice') { /*檢查詢價商品數量是否相同*/
                        $askprice_id = str_replace('askprice', '', $key_type);
                        $AskpriceHelper = new AskpriceHelper();
                        $result = $AskpriceHelper->getOne_by_main_id($askprice_id, 'a.user_id ="' . $user_id . '"');
                        $main = $result['main'];

                        if (!$main) {
                            $code = 0;
                            $msg = Lang::get('資料有誤');

                            break;
                        }

                        $main_record = $result['current'];

                        if (!$main_record) {
                            $code = 0;
                            $msg = Lang::get('資料有誤');

                            break;
                        }

                        if ($main_record['status'] != 1) {
                            $code = 0;
                            $msg = Lang::get('此詢價尚未被回覆');

                            break;
                        }

                        if ($main_record['agree'] != 1) {
                            $code = 0;
                            $msg = Lang::get('此詢價未被同意');

                            break;
                        }

                        if ($main_record['expired_date']) {
                            if (strtotime($main_record['expired_date'] . ' +1Day') < time()) {
                                $code = 0;
                                $msg = Lang::get('已過購買期限');

                                break;
                            }
                        }

                        if ($main_record['bought'] == 1) {
                            $code = 0;
                            $msg = Lang::get('已完成此詢價的購買');

                            break;
                        }

                        if ($main_record['num'] != $buy_num) {
                            $code = 0;
                            $msg = Lang::get('不可修改詢價購買的數量');

                            break;
                        }
                    }

                    /*檢查購物車商品庫存*/
                    if (empty(config('control.close_function_current')['庫存警示'])) { /*檢查此商品的庫存*/
                        $num_limit = false;

                        if ($storeNum < $buy_num) { // 庫存若小於購買數量
                            $num_limit = true;
                        }

                        if ($num_limit) {
                            if (config('control.control_pre_buy') == 0 || $productinfo['pre_buy'] == 0) {
                                $productinfo_and_productinfo_type = str_replace('\\"', '"', $productinfo['title']);

                                if ($productinfo_type['title']) {
                                    $productinfo_and_productinfo_type .= ' - ' . $productinfo_type['title'];
                                }

                                $code = 0;
                                $msg = Lang::get('庫存不足') . '：' . $productinfo_and_productinfo_type;

                                break;
                            } else {
                                if (self::buy_cur_and_pre_at_same_time($cart_key, $buy_num, $user_id)) { /*檢查同一品項的類型商品是否同時買現貨及超額購買*/
                                    $productinfo_and_productinfo_type = $productinfo['title'];

                                    if ($productinfo_type['title']) {
                                        $productinfo_and_productinfo_type .= ' - ' . $productinfo_type['title'];
                                    }

                                    $code = 0;
                                    $msg = Lang::get('無法同時購買現貨及預購') . '：' . $productinfo_and_productinfo_type;

                                    break;
                                }

                                if ($productinfo['pre_buy_limit'] > 0) {
                                    $ori_pre_buy_num = 0;
                                    $pre_buy_product = DB::connection('main_db')->table('orderform_product AS op')
                                        ->select('op.*')
                                        ->join('orderform AS o', 'o.id', 'op.orderform_id')
                                        ->whereRaw('o.status="New"')
                                        ->where('op.pre_buy', 1)
                                        ->where('op.type_id', $type_id_ori)
                                        ->get();
                                    $pre_buy_product = CommonService::objectToArray($pre_buy_product);

                                    foreach ($pre_buy_product as $product_v) {
                                        $ori_pre_buy_num += $product_v['pre_buy_num'] ?? 0;
                                    }

                                    $pre_buy_rest = $productinfo['pre_buy_limit'] - $ori_pre_buy_num;

                                    if ($pre_buy_rest - $buy_num < 0) {
                                        $code = 0;
                                        $msg = Lang::get('超過超額購買總上限') . ':' . $productinfo['title'] . '(' . Lang::get('剩餘') . '：' . $pre_buy_rest . ')';

                                        break;
                                    }
                                }
                            }
                        }
                    }

                    break;
                case 'coupon': // 優惠券商品
                    $respData = ProductHelpler::checkCouponNum($type_id_ori, $user_id, $num, $cmd);

                    if ($respData['code'] == 0) {
                        $code = 0;
                        $msg = $respData['msg'];

                        break;
                    }

                    break;
                default:
                    break;
            }
        }

        if ($code == 0) {
            return [
                'code' => $code,
                'msg' => $msg,
                'num' => $num,
            ];
        }

        // 操作(加減)購物車
        try {
            self::set_cart_session($cart_session, $cmd, $cart_key, $num, $user_id);
            /*沒有啟用平台 且 操作刪除*/

            if (config('control.control_platform') != 1 && $cmd == 'delete') {
                self::set_cart_session('cart_all', $cmd, $cart_key, $num, $user_id);
            }

            $Proposal = self::get_Proposal_GetCartData($cart_session, $user_id);

            return [
                'code' => 1,
                'msg' => sizeof($Proposal->getCartArray()),
                'num' => $num,
            ];
        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'num' => $num,
            ];
        }
    }

    /*檢查是否可有同時購買現貨及超額購買*/
    public static function buy_cur_and_pre_at_same_time($cart_key, $buy_num, $user_id)
    {
        $type_id = explode('_', $cart_key)[0];
        $stock = DB::table('productinfo_type')->find($type_id);
        $stock = CommonService::objectToArray($stock);

        /*找無品項*/
        if (!$stock) {
            return true;
        }

        /*庫存已小於零，不可能同時買現貨及超額購買*/
        if ($stock['num'] <= 0) {
            return false;
        }

        $same_type_product_num = [];    /*同品項的各類型商品在購物車中的數量(區分一般、網紅、加價購...)*/
        $Proposal = self::get_Proposal_GetCartData('cart', $user_id);
        $cart = $Proposal->getCartArray();

        foreach ($cart as $key => $value) {
            if ($key == $cart_key) {  /*如果是操作中的品項*/
                $num = $buy_num;    /*依修正數量*/
            } else if (preg_match('/' . $type_id . '_/', $key)) {
                $num = $value;      /*依購物車紀錄*/
            } else {
                continue;           /*略過不同品項的商品*/
            }

            array_push($same_type_product_num, $num);
        }

        $elements = CommonService::elements_able_to_sum_to_target($same_type_product_num, $stock['num']); /*是否能剛好組成等於庫存數量*/

        return count($elements) == 0; /*如果回傳陣列長度為0，表示組不出剛好能等於庫存的量，代表同時購買現貨及超額購買*/
    }

    /*建立訂單*/
    public static function createOrder($OrderData, $buy_method = 'online', $userId = 0)
    {
        $OrderData['become_member'] = $OrderData['become_member'] ?? '0';

        $MemberInstance = new MemberInstance($userId);
        $member_data = $MemberInstance->get_user_data("ori", ['a.id' => $userId]);

        //get cart data
        $Proposal = self::get_Proposal_GetCartData('cart_all', $userId);

        $cartData = [];

        foreach ($Proposal->getCartArray() as $key => $value) {
            $singleData = Proposal::get_singleData($key, $buy_method); /* 取得商品資料 */
            $singleData['num'] = $value;
            array_push($cartData, $singleData);
        }
        //----

        //get discount data
        $DiscountProposal = DiscountProposal::withTeamMembersAndRequire(
            ['Total', 'CouponCheck', 'PointCheck', 'ActCalculate', 'MemberDiscount'],
            [
                'user_id' => $userId,
                'cartData' => $cartData,
            ]
        );
        $DiscountProposal = DiscountMemberFactory::createNextMember($DiscountProposal);
        //----

        //create products data
        $need_shipfee = false;  // 是否需運費
        $products = [];
        $buy_coupon = [];       // 購買優惠券(成單後派發優惠券用)
        $freeDiscountSum = 0;   // 立馬省總優惠金額
        $pre_buy_num_group = []; // 依照品項回傳購物車中超額購買的商品數量組合

        foreach ($cartData as $singleData) {
            if (in_array($singleData['key_type'], Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*不屬於 productinfo商品*/
                continue; /*略過可超額購買數量統計*/
            }

            $pre_buy_num_group[$singleData['type_id_ori']] = self::get_pre_buy_num_group($singleData['type_id'], $singleData['num'], $userId); /*取得超額購買數組*/
        }

        foreach ($cartData as $singleData) {
            if (!in_array($singleData['key_type'], Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*屬於 productinfo商品*/
                $need_shipfee = true; // 需計算運費
            }

            $url2 = $singleData['info_pic1'];
            $product_name = $singleData['type_title'] ? $singleData['info_title'] . '-' . $singleData['type_title'] : $singleData['info_title'];
            $product = [
                'name' => $product_name,                                                // 商品名
                'price' => $singleData['countPrice'],                                   // 單價
                'num' => $singleData['num'],                                            // 數量
                'total' => $singleData['countPrice'] * $singleData['num'],              // 小計
                'url2' => request()->server('HTTP_HOST') . '/public/static/index' . $url2,  // 圖片網址
                'type_id' => $singleData['type_id_ori'],                                // 商品品項id
                'info_id' => $singleData['type_product_id'],                            // 商品id
                'key_type' => $singleData['key_type'],                                  // 商品種類(ex:一般商品、優惠券商品...)
            ];

            // 根據商品商品種類產生對應資料
            switch ($singleData['key_type']) {
                case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
                case substr($singleData['key_type'], 0, 3) == 'kol':
                case 'add':
                case substr($singleData['key_type'], 0, 8) == 'askprice':
                    // 因為立馬省改動過單價，這邊要記錄回來真實的單價
                    if ($singleData['key_type'] == 'normal' || substr($singleData['key_type'], 0, 3) == 'kol') { /*一般商品 或 網紅商品 需檢查立馬省*/
                        $productinfo_type = [];
                        $productinfo_type = DB::table('productinfo_type')->find($singleData['type_id_ori']);
                        $productinfo_type = CommonService::objectToArray($productinfo_type);

                        if ($productinfo_type) {
                            $origin_price = $productinfo_type['count'];

                            $product['price'] = $origin_price;
                            $product['total'] = $origin_price * $singleData['num'];
                        }

                        // 計算立馬省總優惠金額
                        $act_type_sql = ActCalculate::arrange_search_sql($act_type = 2);
                        $actProductInList = DB::table('act_product')
                            ->select('act.*')
                            ->join('act', 'act.id', '=', 'act_product.act_id')
                            ->whereRaw('act_product.prod_id = "' . $productinfo_type['product_id'] . '"')
                            ->whereRaw($act_type_sql)
                            ->get();
                        $actProductInList = CommonService::objectToArray($actProductInList);
                        if (count($actProductInList) > 0 && $singleData['key_type'] != 'add') { /*有套用利馬省 且 非加價購商品*/
                            if ($actProductInList[0]['discount1'] > $origin_price) { /*立馬省折扣大於商品金額*/
                                $freeDiscountSum += $singleData["num"] * $origin_price;
                            } else {
                                $freeDiscountSum += $singleData["num"] * $actProductInList[0]['discount1'];
                            }
                        }
                    }

                    $up = DB::table('productinfo')->find($singleData['type_product_id']);
                    $up = CommonService::objectToArray($up);
                    $position =  DB::table('productinfo_type as pt')->select('pos.name')
                        ->join('position as pos', 'pt.position', '=', 'pos.id')
                        ->where('pt.id', $singleData['type_id_ori'])->first()->name;
                    $product['Author'] = $up['Author'];
                    $product['house'] = $up['house'];
                    $product['house_date'] = $up['house_date'];
                    $product['ISBN'] = $up['ISBN'];
                    $product['position'] = $position;
                    $product['url'] = request()->server('HTTP_HOST') . '/admin/productinfo/edit?id=' . $singleData['type_product_id'];
                    $product['info_id'] = $singleData['type_product_id'];
                    $product['is_registrable'] = $singleData['is_registrable'];
                    $product['deal_position'] = $buy_method == 'online' ? "0" : "1"; // 線上購物需核銷庫存
                    $product['pre_buy'] = 0;
                    $product['pre_buy_num'] = 0;
                    $in_array_index = array_search($singleData['num'], $pre_buy_num_group[$singleData['type_id_ori']] ?? []);

                    if ($in_array_index !== false) { /*是組成超額購買的數量*/
                        $product['pre_buy'] = 1;
                        $product['pre_buy_num'] = $singleData['num'];
                        array_splice($pre_buy_num_group[$singleData['type_id_ori']], $in_array_index, 1);
                    }

                    break;
                case 'coupon': // 優惠券商品
                    $product['Author'] = '';
                    $product['house'] = '';
                    $product['ISBN'] = '';
                    $product['position'] = '無';
                    $product['url'] = request()->server('HTTP_HOST') . '/Coupon/show?id=' . $singleData['type_product_id'];
                    $product['info_id'] = "0";
                    $product['is_registrable'] = "0";
                    $product['deal_position'] = "1"; // 不需銷庫存
                    $product['pre_buy'] = 0;
                    $product['pre_buy_num'] = 0;
                    $buy_coupon[$singleData['type_product_id']] = $singleData['num'];

                    break;
            }

            array_push($products, $product);
        }

        if (count($products) == 0) {
            throw new \Exception(Lang::get('購物車內無商品'));
        }

        // 滿額贈設定
        if (empty($OrderData['gift']) == false) {
            $gifts = json_decode($OrderData['gift'], true);

            foreach ($gifts as $key => $gift) {
                $db_result = DB::table('productinfo_type')
                    ->select([
                        'productinfo.title as info_title',
                        'productinfo.id as info_id',
                        'productinfo.pic',
                        'productinfo.Author',
                        'productinfo.house',
                        'productinfo.house_date',
                        'productinfo.ISBN',
                        'productinfo.is_registrable',
                        'productinfo_type.title as type_title',
                        'productinfo_type.num',
                        'productinfo_type.pic_index',
                    ])
                    ->join('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
                    ->where('productinfo_type.id', $gift['id'])
                    ->where('productinfo_type.online', 1)
                    ->first();
                $db_result = CommonService::objectToArray($db_result);

                if (empty($db_result)) {
                    throw new \Exception('查無贈品，請聯繫客服人員');
                }

                if (intval($db_result['num'] < intval($gift['num']))) {
                    throw new \Exception('贈品數量不足，請聯繫客服人員');
                }

                self::cartCtrl($gift['id'], $gift['num'], 'gift', 'cart');
                self::cartCtrl($gift['id'], $gift['num'], 'gift');

                $product = [
                    'name' => $db_result['type_title'] ? $db_result['info_title'] . '-' . $db_result['type_title'] : $db_result['info_title'],
                    'price' => 0,
                    'num' => $gift['num'],
                    'total' => 0,
                    'url2' => request()->server('HTTP_HOST') . '/public/static/index' . json_decode($db_result['pic'], true)[$db_result['pic_index'] - 1],
                    'type_id' => strval($gift['id']),
                    'info_id' =>  intval($db_result['info_id']),
                    'key_type' => 'gift',
                    'Author' => $db_result['Author'],
                    'house' =>  $db_result['house'],
                    'house_date' =>  $db_result['house_date'],
                    'ISBN' =>  $db_result['ISBN'],
                    'position' => DB::table('productinfo_type')->join('position', 'productinfo_type.position', '=', 'position.id')->where('productinfo_type.id', $gift['id'])->value('position.name'),
                    'url' => request()->server('HTTP_HOST') . '/admin/productinfo/edit?id=' . $db_result['info_id'],
                    'is_registrable' => $db_result['is_registrable'],
                    'deal_position' => $buy_method == 'online' ? '0' : '1', // 線上購物需核銷庫存
                    'pre_buy' => 0,
                    'pre_buy_num' => 0,
                ];

                array_push($products, $product);
            }
        }
        //----

        // 處理運費
        $shipping = self::get_shipping_method($cartData, $OrderData['send_way']);

        if ($OrderData['send_way'] == Lang::get('到店取貨') || !$need_shipfee) { /*商品銷售、到店取貨、不需運費 使用*/
            array_push($products, [
                'name' => '運費',
                'price' => 0,
                'num' => 1,
                'total' => 0
            ]);
            $shipping[0]['price'] = 0;
        } else { // 有需運送的商品，且配送方式非到店取貨
            if (!$shipping) {
                throw new \Exception(Lang::get('無此運送方式'));
            }

            $OrderData['send_way'] = $shipping[0]['name'];

            if ($DiscountProposal->projectData['Total'] > $shipping[0]['free_rule'] && $shipping[0]['free_rule'] > 0) { // 如果購物金額超過免運條件
                $shipping[0]['price'] = 0; // 設定運費為0元
            }

            array_push($products, [
                'name' => $shipping[0]['name'],
                'price' => $shipping[0]['price'],
                'num' => 1,
                'total' => $shipping[0]['price']
            ]);
        }
        //----

        //create add_point data
        $add_point = 0;

        if (empty(config('control.close_function_current')['點數設定'])) {
            if ($OrderData['pay_way'] != 4) {
                if ($OrderData['discount'] == 'none_discount') {
                    $point_rate = (float)DB::table('points_setting')->find(3)->value;
                    $add_point = floor(($DiscountProposal->projectData['Total'] - $DiscountProposal->projectData['acts']['sumNoneGetPoint']) / $point_rate);
                }
            }
        }
        //----

        //產生優惠說明 & 計算購物總金額
        if (!isset($OrderData['discount'])) {
            throw new \Exception(Lang::get('發生錯誤，請再試一次'));
        }

        $discountData = explode("_", $OrderData['discount']);

        if (count($discountData) < 2) {
            throw new \Exception(Lang::get('發生錯誤，請再試一次'));
        }

        if ($discountData[0] == 'points') {
            $discountData[1] = $OrderData['point'];
        }

        if ($discountData[0] == 'acts') { // 活動優惠
            $discountFinal = [];

            foreach ($DiscountProposal->projectData['acts']['actCart'] as $acKey => $acValue) {
                $actDiscount = $acValue['calculated']['discount'];

                if (($acValue['type'] == 1) | ($acValue['type'] == 3)) {
                    $discountFinal[] = [
                        'type' => Lang::get('活動'),
                        'name' => $acValue['name'],
                        'count' => Lang::get('打') . $actDiscount . Lang::get('折'),
                        'product' => $acValue['prod']
                    ];
                } else {
                    $discountFinal[] = [
                        'type' => Lang::get('活動'),
                        'name' => $acValue['name'],
                        'count' => Lang::get('扣') . config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . $actDiscount,
                        'product' => $acValue['prod']
                    ];
                }
            }

            $discountFinalData['discount'] = json_encode($discountFinal, JSON_UNESCAPED_UNICODE);
            $discountFinalData['total'] = $DiscountProposal->projectData['acts']['sum'];
        } else if ($discountData[0] == "firstbuy") { // 會員首購優惠
            $discountFinalData['discount'] = urldecode(json_encode([
                [
                    'type' => urlencode(Lang::get('會員首購優惠')),
                    'name' => urlencode($DiscountProposal->projectData['firstBuyDiscount']['vip_name']),
                    'count' => urlencode(Lang::get('扣') . config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . $DiscountProposal->projectData['firstBuyDiscount']['discount']),
                ]
            ]));
            $discountFinalData['total'] = $DiscountProposal->projectData['Total'] - $DiscountProposal->projectData['firstBuyDiscount']['discount'];
        } else if ($discountData[0] == "vipdiscount") { // VIP會員優惠
            $discountFinalData['discount'] = urldecode(json_encode([
                [
                    'type' => urlencode(Lang::get('會員等級優惠')),
                    'name' => urlencode($DiscountProposal->projectData['vipDiscount']['vip_name']),
                    'count' => urlencode(Lang::get('扣') . config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . $DiscountProposal->projectData['vipDiscount']['discount']),
                ]
            ]));
            $discountFinalData['total'] = $DiscountProposal->projectData['Total'] - $DiscountProposal->projectData['vipDiscount']['discount'];
        } else { // 其他優惠(ex:紅利、優惠券、直接輸入型優惠券、無優惠)
            $discountObject = DiscountFactory::createDiscount(
                $discountData,
                $DiscountProposal->projectData['Total']
            );
            $discountFinalData = $discountObject->getDiscountAndTotal($OrderData);

            $discountFinalData['total'] = $discountFinalData['total'] < 0 ? 0 : $discountFinalData['total'];
        }
        //----

        //---- count free(auto) discount
        $freeDiscount = $buy_method == 'online' ? $freeDiscountSum : 0;
        // dump($freeDiscount);
        //----------------

        $discountFinalData['total'] = $discountFinalData['total'] < 0 ? 0 : $discountFinalData['total'];
        $discountFinalData['total'] += $shipping[0]['price'];

        if ($userId) {
            $role = 'member';
        } else {
            $role = 'guest';
        }

        $receipts_state = isset($OrderData['receipts_state']) ? $OrderData['receipts_state'] : '0';
        $transport_state = isset($OrderData['transport_state']) ? $OrderData['transport_state'] : '0';
        $order_status = isset($OrderData['status']) ? $OrderData['status'] : 'New';

        $insertData = [
            'distributor_id' => isset($OrderData['distributor_id']) ? $OrderData['distributor_id'] : 0,
            'user_id' => $userId,
            'order_number' => self::create_order_number(),
            'create_time' => time(),
            'over_time' => self::get_eff_dateline(),
            'payment' => htmlspecialchars(trim($OrderData['pay_way'])),
            'transport' => htmlspecialchars(trim($OrderData['send_way'])),
            'transport_location_name' => htmlspecialchars(trim($OrderData['transport_location_name'])),
            'transport_location_phone' => htmlspecialchars(trim($OrderData['transport_location_phone'])),
            'transport_location_tele' => '',
            'transport_location_textarea' => '',
            'transport_email' => htmlspecialchars(trim($OrderData['transport_email'])),
            'product' => json_encode($products, JSON_UNESCAPED_UNICODE),
            'add_point' => $add_point,
            'total' => round($discountFinalData['total']),
            'discount' => $discountFinalData['discount'],
            'freediscount' => $freeDiscount,
            'receipts_state' => $receipts_state,
            'transport_state' => $transport_state,
            'need_arrival_date' => $OrderData['need_arrival_date'],
            'arrival_date' => $OrderData['need_arrival_date'] == '1' ? $OrderData['arrival_date'] : null,
            'uniform_numbers' => htmlspecialchars(trim($OrderData['uniform_numbers'])),
            'company_title' => htmlspecialchars(trim($OrderData['company_title'])),
            'ps' => htmlspecialchars(trim($OrderData['ps'])),
            'layout_preview' => empty(session()->get('customize_box')) ? null : json_encode(array_column(session()->get('customize_box'), 'preview_path'), JSON_UNESCAPED_UNICODE),
            'status' => $order_status,
            'InvoiceStyle' => intval($OrderData['invoice_style']),

            /*綠界物流資料*/
            'MerchantTradeNo' => $OrderData['MerchantTradeNo'] ?? '',
            'LogisticsSubType' => $OrderData['LogisticsSubType'] ?? '',
            'CVSStoreID' => $OrderData['CVSStoreID'] ?? '',
            'CVSStoreName' => $OrderData['CVSStoreName'] ?? '',
            'CVSAddress' => $OrderData['CVSAddress'] ?? '',
            'CVSTelephone' => $OrderData['CVSTelephone'] ?? '',
            'CVSOutSide' => $OrderData['CVSOutSide'] ?? '',
            'ExtraData' => $OrderData['ExtraData'] ?? '',
        ];

        if ($insertData['total'] == 0) {
            $insertData['receipts_state'] = 1;
        } else if ($insertData['payment'] == 2 && $insertData['total'] == 0 && $insertData['total'] <= 15) {
            if ($discountData[0] == 'points') {
                $input['msg'] = '回補' . $DiscountProposal->projectData['Total'] . '點(訂單錯誤)';
                $input['points'] = $DiscountProposal->projectData['Total'];
                $input['user_id'] = $userId;
                $input['belongs_time'] = time();
                $input['msg_time'] = date('Y-m-d H:i:s');
                Db::connection('main_db')->table('points_record')->insert($input);
                Db::connection('main_db')->table('account')->where('id', $userId)->increment('point', $DiscountProposal->projectData['Total']);
            }

            throw new \Exception(Lang::get('匯款訂單金額需大於15元'));
        }

        // 物流資訊
        if (empty($OrderData['addrC']) == false) {
            $insertData['transport_location'] = $OrderData['addrC'];
        } else {
            if (empty($OrderData['transport_location']) == false) {
                $insertData['transport_location'] = $OrderData['transport_location'];
            } else {
                $insertData['transport_location'] = '無';
            }
        }

        /* 發票設定 */
        switch ($insertData['InvoiceStyle']) {
            default:
                $insertData['Print'] = 1;

                break;
            case 3:
                $insertData['CarrierType'] = trim($OrderData['CarrierType']);
                $insertData['CarrierNum'] = trim($OrderData['CarrierNum']);
                $insertData['Print'] = 0;

                break;
            case 4:
                $insertData['uniform_numbers'] = trim($OrderData['uniform_numbers']);
                $insertData['company_title'] = trim($OrderData['company_title']);
                $insertData['Print'] = 1;

                break;
            case 5:
                $insertData['LoveCode'] = trim($OrderData['LoveCode']);
                $insertData['Print'] = 0;

                break;
        }

        //資料庫新增訂單
        $id = DB::connection('main_db')->table('orderform')->insertGetId($insertData);

        /*把商品插入 orderform_product 中*/
        $product_keys = array_keys($products[0] ?? []);
        $products_all = [];

        foreach ($products as $key => $value) {
            foreach ($product_keys  as $product_key) {
                $products_all[$key][$product_key] = $value[$product_key] ?? null;
            }

            $products_all[$key]['orderform_id'] = $id;
        }
        // dump($products_all);exit;
        DB::connection('main_db')->table('orderform_product')->insert($products_all);


        /*更新會員資料(建立會員訂單來源)*/
        $OrderData['become_member'] = $OrderData['become_member'] ?? '0';

        if ($OrderData['become_member'] == '1') {
            $become_member = $MemberInstance->get_user_data($addr_change = "ori", ['a.' . MemberInstance::$account_column => $insertData[MemberInstance::$account_column_order]]);

            if ($become_member) {
                if ($become_member['from_order'] == null) {
                    $MemberInstance->change_user_id($become_member['id']);
                    $MemberInstance->update_user_data([
                        'from_order' => $id,
                    ]);
                    Db::connection('main_db')->table('orderform')->where('id', $id)->update(['user_id' => $become_member['id']]);
                }

                if ($become_member['pwd'] == md5('1234')) {
                    session()->put('user', $member_data);
                }
            }
        }

        //建立直接輸入型優惠券使用紀錄
        if ($discountData[0] == 'directcoupon') {
            $discount = json_decode($insertData['discount'])[0];
            $discount->count = (int)mb_substr($discount->count, 2, -1);

            DB::table('coupon_direct_record')->insertGetId([
                'user_id'   => $userId,
                'coupon_id' => $discount->coupon_id,
                'order_id'  => $id,
                'datetime'  => $insertData['create_time'],
                'total'     => $insertData['total'],
                'discount'  => $discount->count,
            ]);
        }

        //更新購買優惠券
        if ($buy_coupon) {
            foreach ($buy_coupon as $key => $value) {
                DB::table('coupon_pool')
                    ->whereNull('owner')
                    ->whereNull('login_time')
                    ->whereNull('use_time')
                    ->where('coupon_id', $key)
                    ->limit($value)
                    ->update(['owner' => $userId]);
            }
        }

        if (empty(config('control.close_function_current')['消費抽抽樂'])) {
            // 處理抽抽樂優惠
            $MemberInstance = new MemberInstance($userId);
            $MemberInstance->set_lucky_draw($insertData['total'], $pay_record_id = 0, $id);
        }

        /*寄送提醒*/
        $globalMailData = HelperService::getMailData();
        $new_order_letter = Lang::get('訂單成功信消費者');
        $new_order_letter = str_replace("{globalMailData_mailFromName}", $globalMailData['mailFromName'], $new_order_letter);

        $res_goods = [];

        foreach ($products as $key => $value) {
            if (isset($value['key_type'])) {
                $res_good = $value['name'] . "*" . $value['num'];
                array_push($res_goods, $res_good);
            }
        }

        $res_goods = implode(', ', $res_goods);

        $buyer_name = $insertData['transport_location_name'];

        if (isset($member_data['name'])) {
            if ($member_data['name']) {
                $buyer_name = $member_data['name'];
            }
        }

        $payment_name = Payfee::get_payment_name($insertData['payment']);

        $mailBody = "
        <html>
            <head></head>
            <body>
                <div>
                    " . $new_order_letter . "
                    " . Lang::get('訂單編號') . "：" . $insertData['order_number'] . "<br>
                    " . Lang::get('訂單時間') . "：" . date('Y/m/d H:i', $insertData['create_time']) . "<br>
                    " . Lang::get('訂購商品') . "：" . $res_goods . "<br>
                    " . Lang::get('訂單金額') . "：" . $insertData['total'] . "<br>
                    " . Lang::get('購買人') . "：" . $buyer_name . "<br>
                    " . Lang::get('收件人') . "：" . $insertData['transport_location_name'] . "<br>
                    " . Lang::get('出貨地址') . "：" . $insertData['transport_location'] . "<br>
                    " . Lang::get('電子信箱') . "：" . $insertData['transport_email'] . "<br>
                    " . Lang::get('行動電話') . "：" . $insertData['transport_location_phone'] . "<br>
                    " . Lang::get('聯絡電話') . "：" . $insertData['transport_location_tele'] . "<br>
                    " . Lang::get('付款方式') . "：" . $payment_name . "<br>
                    " . Lang::get('備註') . "：" . $insertData['transport_location_textarea'] . "<br>
                </div>
                <div>
                " . $globalMailData['system_email']['order_complete'] . "
                </div>
                <div style='color:red;'>
                    ≡ " . Lang::get('此信件為系統自動發送，請勿直接回覆') . "(" . $id . ") ≡
                </div>
            </body>
        </html>";
        HelperService::Mail_Send($mailBody, 'client', $insertData['transport_email'], Lang::get('訂單建立成功'));

        $new_order_letter_admin = Lang::get('menu.訂單成功信管理者');
        $mailBody = "
        <html>
            <head></head>
            <body>
                <div>
                    " . $new_order_letter_admin . "
                    " . Lang::get('訂單編號') . "：" . $insertData['order_number'] . "<br>
                    " . Lang::get('訂單時間') . "：" . date('Y/m/d H:i', $insertData['create_time']) . "<br>
                    " . Lang::get('訂購商品') . "：" . $res_goods . "<br>
                    " . Lang::get('訂單金額') . "：" . $insertData['total'] . "<br>
                    " . Lang::get('購買人') . "：" . $buyer_name . "<br>
                    " . Lang::get('收件人') . "：" . $insertData['transport_location_name'] . "<br>
                    " . Lang::get('出貨地址') . "：" . $insertData['transport_location'] . "<br>
                    " . Lang::get('電子信箱') . "：" . $insertData['transport_email'] . "<br>
                    " . Lang::get('行動電話') . "：" . $insertData['transport_location_phone'] . "<br>
                    " . Lang::get('聯絡電話') . "：" . $insertData['transport_location_tele'] . "<br>
                    " . Lang::get('付款方式') . "：" . $payment_name . "<br>
                    " . Lang::get('備註') . "：" . $insertData['transport_location_textarea'] . "<br>
                </div>
                <div style='color:red;'>
                    ≡ " . Lang::get('此信件為系統自動發送，請勿直接回覆') . "(" . $id . ") ≡
                </div>
            </body>
        </html>";

        $distributor_id = $insertData['distributor_id'];

        if ($distributor_id == 0) {
            HelperService::Mail_Send($mailBody, 'admin_order_new', '', Lang::get('新訂單提醒'));
        } else {
            $MemberInstance = new MemberInstance($distributor_id);
            $user_data = $MemberInstance->get_user_data_distributor();

            if ($user_data) {
                HelperService::Mail_Send($mailBody, 'client', $user_data['email'], Lang::get('新訂單提醒'));
            }
        }

        // 清空購物車
        self::clearCart($userId);

        return [
            'id' => $id,
            'total' => $discountFinalData['total'],
            'order_number' => $insertData['order_number'],
            'role' => $role,
            'product' => $products,
            'pay_way' => trim($OrderData['pay_way']),
            'email' => trim($OrderData['transport_email']),
            'become_member'  => $become_member ?? null,
        ];
    }

    /*取得購物車內的商品*/
    public static function get_Proposal_GetCartData($cart_session, $user_id = 0)
    {
        $Proposal = Proposal::withTeamMembersAndRequire(
            ['GetCartData'],
            [
                'cart_session' => $cart_session,
                'user_id' => $user_id,
            ]
        );
        $Proposal = MemberFactory::createNextMember($Proposal);

        return $Proposal;
    }
    /*取得指定訂單的商品*/
    public static function get_orderform_products(array $orderform_ids)
    {
        $orderform_products = Db::connection('main_db')->table('orderform_product')->whereIn('orderform_id', $orderform_ids)->get();
        $orderform_products = CommonService::objectToArray($orderform_products);

        return $orderform_products;
    }

    /*取得超額購買數組*/
    public static function get_pre_buy_num_group($cart_key, $buy_num, $userId)
    {
        $pre_buy_num_group = [];

        if (config('control.control_pre_buy')) { /*有啟用超額購買功能*/
            $type_id = explode('_', $cart_key)[0];
            $stock = DB::table('productinfo_type')->find($type_id);
            $stock = CommonService::objectToArray($stock);

            $same_type_product_num = [];    /*同品項的各類型商品在購物車中的數量(區分一般、網紅、加價購...)*/
            $Proposal = self::get_Proposal_GetCartData('cart', $userId);
            $cart = $Proposal->getCartArray();

            foreach ($cart as $key => $value) {
                if ($key == $cart_key) {  /*如果是操作中的品項*/
                    $num = $buy_num;    /*依修正數量*/
                } else if (preg_match('/' . $type_id . '_/', $key)) {
                    $num = $value;      /*依購物車紀錄*/
                } else {
                    continue;           /*略過不同品項的商品*/
                }

                array_push($same_type_product_num, $num);
            }

            if (array_sum($same_type_product_num) > $stock['num']) {
                $elements = CommonService::elements_able_to_sum_to_target($same_type_product_num, $stock['num']); /*是否能剛好組成等於庫存數量*/

                foreach ($same_type_product_num as $key => $value) {
                    $in_array_index = array_search($value, $elements);

                    if ($in_array_index !== false) { /*是組成超額購買的數量*/
                        array_splice($elements, $in_array_index, 1);
                    } else {
                        array_push($pre_buy_num_group, $value);
                    }
                }
            }
        }

        return $pre_buy_num_group;
    }

    /*取得運送方法*/
    public static function get_shipping_method($cartData, $shipping_id = 0)
    {
        /*取得運送方法*/
        //--- 刪去法: 可運運法為所有運法的交集
        if (config('control.control_product_shipping') == 1) { /* 有啟用商品關聯運法 */
            foreach ($cartData as $key => $value) {
                if ($value['shipping_type'] == "") { // 無勾選運法
                    if (!isset($shipping_type)) { // 還未設定過可運運法
                        $shipping_type = [];
                        $shipping_all = DB::table('shipping_fee')->orderByRaw('order_id asc, id desc')->get();
                        $shipping_all = CommonService::objectToArray($shipping_all);
                        array_walk($shipping_all, function ($item) use (&$shipping_type) {
                            array_push($shipping_type, $item['id']);
                        });
                    }
                } else { // 有勾選運法
                    if (!isset($shipping_type)) { // 還未設定過可運運法
                        $shipping_type = explode(',', $value['shipping_type']);
                    } else {
                        $shipping_type = array_intersect($shipping_type, explode(',', $value['shipping_type']));
                    }
                }
            }

            $shipping_where = isset($shipping_type) ? array_filter($shipping_type, function ($v) {
                return $v != null;
            }) : [];
            $shipping_where = $shipping_where ? "id in (" . join(',', $shipping_where) . ")" : 'id=-1';
        } else {
            $shipping_where = '1=1';
        }

        $shipping_fee = DB::table('shipping_fee')->whereRaw($shipping_where);

        if ($shipping_id) {
            $shipping_fee = $shipping_fee->where('id', $shipping_id);
        }

        $shipping_fee = $shipping_fee->orderByRaw('order_id asc, id desc')->get();
        $shipping_fee = CommonService::objectToArray($shipping_fee);

        foreach ($shipping_fee as $key => $value) {
            // $value['name'] = str_replace('\\', '\\\\', $value['name']);

            /*合併供應商設定資料*/
            if (config('control.control_platform') == 1) {
                $distributor_id = $cartData[0]['distributor_id'];
            } else {
                $distributor_id = 0;
            }

            $admin_type = $distributor_id == 0 ? 'admin' : 'distribution';
            $shipping_fee[$key] = CartMethod::merge_distributor_setting($value, 'shipping_fee', $distributor_id);

            if (!isset($shipping_fee[$key]['shipping_fee_id'])) {
                $shipping_fee[$key]['shipping_fee_id'] = $value['id'];
            }
        }

        $shipping_fee = array_filter($shipping_fee, function ($v) {
            return $v['online'] == 1;
        });

        /*調整運費*/
        if (empty(config('control.close_function_current')['運費標籤管理'])) { /*如果有使用運費標籤管理功能*/
            $calculated_shipping_fee = 0;

            foreach ($cartData as $key => $value) {
                if ($value['key_type'] == 'add') {
                    continue;
                } /*加價購商品不計算運費*/

                $shipping_fee_tag = DB::table('shipping_fee_tag')->whereRaw('id="' . $value['shipping_fee_tag'] . '"')->first();
                $shipping_fee_tag = CommonService::objectToArray($shipping_fee_tag);

                if ($shipping_fee_tag) { /*有找到套用的運費標籤*/
                    $calculated_shipping_fee += ($shipping_fee_tag['price'] * $value['num']); /*累計運費*/
                }
            }

            /*修改所有運法的金額為計算出來的運費*/
            foreach ($shipping_fee as $key => $value) {
                $shipping_fee[$key]['price'] = $calculated_shipping_fee;
            }
        }

        return $shipping_fee;
    }

    /*生成訂單編號*/
    public static function create_order_number()
    {
        $count = DB::connection('main_db')->table('orderform')->whereRaw('order_number like "' . config('extra.shop.subDeparment') . 'O' . date('Ymd') . '%"')->orderBy('id', 'desc')->first();
        $count = $count ? intval(substr($count->order_number, -3)) + 1 : 1;

        if ($count < 10) {
            $count = '00' . $count;
        } else if ($count < 100) {
            $count = '0' . $count;
        }

        return date('Ymd') . CommonService::randomkeys(4);
        // return date('Ymd') . $count;  // 客戶要求訂單編號只要12碼
        // return config('extra.shop.subDeparment') . 'O' . date('Ymd') . CommonService::randomkeys(5) . $count;
    }

    /*取得紅利點數到期時間*/
    public static function get_eff_dateline()
    {
        $time = time();
        $Eff = config('control.control_point_duration');            //Sql給予期限
        $Eff_data = config('control.control_point_duration_date');  //Sql給予期限  12-31

        return (date("Y", $time) + $Eff) . '-' . $Eff_data . ' 23:59:59'; // 取得年份
    }

    /*清除購物車內紀錄(減少庫存)*/
    public static function clearCart($user_id)
    {
        $Proposal = self::get_Proposal_GetCartData('cart_all', $user_id);
        $cart_all = $Proposal->getCartArray();

        $Proposal = self::get_Proposal_GetCartData('cart', $user_id);
        $cart = $Proposal->getCartArray();

        foreach ($cart as $cart_key => $num) {
            [$type_id_ori, $key_type] = Proposal::get_prod_key_type($cart_key);

            switch ($key_type) {
                case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
                case substr($key_type, 0, 3) == 'kol':
                case 'add':
                case substr($key_type, 0, 8) == 'askprice':
                    /*設定詢價為已購買*/
                    if (substr($key_type, 0, 8) == 'askprice') {
                        $askprice_id = str_replace('askprice', '', $key_type);
                        $AskpriceHelper = new AskpriceHelper();
                        $result = $AskpriceHelper->getOne_by_main_id($askprice_id, 'a.user_id ="' . $user_id . '"');
                        $main = $result['main'];

                        if (!$main) {
                            break;
                        }

                        $main_record = $result['current'];

                        if (!$main_record) {
                            break;
                        }

                        DB::table('askprice_record')->where('id', $main_record['id'])->update(['bought' => 1]);
                    }

                    /*只扣線上可購買數量*/
                    DB::table('productinfo_type')->where('id', $type_id_ori)->decrement('num', $num);

                    break;
                case 'coupon': // 優惠券商品
                default:
                    break;
            }

            /*刪除加入購物車紀錄*/
            unset($cart_all[$cart_key]);
        }

        session()->put('cart_all', json_encode($cart_all));
        session()->forget('cart');
        session()->forget('customize_box');
    }

    /*依項檢查商品是否可以操作購物車*/
    public static function check_status_can_add_cart($num_input, $type_id = 0, $cmd = "", $prod_type = 'coupon', $distributor_id = '')
    { /*數量, 品項id、 操作方式、 商品類型、 欲加入購物車的賣家id*/
        $target_p = null;

        if (!in_array($prod_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*屬於 productinfo商品*/
            $target_p = DB::table('productinfo_type as pt')->join('productinfo as p', 'pt.product_id', '=', 'p.id')
                // ->where('p.online in (0,1)')
                ->where('pt.closed', 0)
                ->select(
                    'pt.num',
                    'pt.title',
                    'pt.online',
                    'pt.start_time',
                    'pt.end_time',
                    'pt.closed',
                    'p.title as pro_name',
                    'p.card_pay',
                    'p.online as p_online',
                    'p.distributor_id',
                    'p.final_array'
                )
                ->where('pt.id', $type_id)
                ->first();
            $target_p = CommonService::objectToArray($target_p);

            if (!$target_p) {
                return [
                    'code' => 0,
                    'msg' => Lang::get('資料有誤'),
                    'num' => $num_input,
                ];
            }

            if ($cmd != 'delete') { /*刪除以外的操作才需檢查*/
                /*需檢查賣家 且 賣家不相同*/
                if ($distributor_id != '' && $distributor_id != $target_p['distributor_id']) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('選擇的商品包含不同賣家，無法同時結帳') . '：' . $target_p['pro_name'],
                        'num' => $num_input,
                    ];
                }

                // 檢查商品是否上架
                $respData = ProductHelpler::check_product_and_infotype_close($target_p['final_array']);

                if ($target_p['p_online'] == 2 || $respData->close) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('已下架') . $target_p['pro_name'],
                        'num' => $num_input,
                    ];
                }

                // 檢查品項是否被假刪除
                if ($target_p['online'] == 0) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('已被刪除') . $target_p['pro_name'] . '的品項:' . $target_p['title'],
                        'num' => $num_input,
                    ];
                }

                // 檢查品項是否被取消
                if ($target_p['closed'] == 1) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('已被取消的品項') . $target_p['pro_name'] . '的品項:' . $target_p['title'],
                        'num' => $num_input,
                    ];
                }

                // 檢查品項是否在顯示期限內
                if (($target_p['start_time'] != "" && $target_p['start_time'] > date("Y-m-d")) || ($target_p['end_time'] != "" && $target_p['end_time'] < date("Y-m-d"))) {
                    return [
                        'code' => 0,
                        'msg' => Lang::get('不在顯示期限內') . $target_p['pro_name'],
                        'num' => $num_input,
                    ];
                }
            }
        }

        return [
            'code' => 1,
            'target_p' => $target_p,
            'msg' => 'OK',
            'num' => $num_input,
        ];
    }

    public static function set_cart_session($cart_session, $cmd, $cart_key, $num, $user_id = 0)
    {
        $Proposal = Proposal::withTeamMembersAndRequire(
            [
                'GetCartData',
                'Assign',
                'Increase',
                'Delete',
                'Decrease'
            ],
            [
                'cart_session' => $cart_session,
                'cmd' => $cmd,
                'user_id' => $user_id,
                'id' => $cart_key,
                'num' => $num
            ]
        );
        $Proposal = MemberFactory::createNextMember($Proposal);
        session()->put($cart_session, $Proposal->projectData['data']);
    }


    /*檢查庫存*/
    public static function check_stock()
    {
        $orderforms_check_stock = [];
        $productinfo_type_num = [];
        $order_product = DB::connection('main_db')->table('orderform_product AS op')
            ->selectRaw('op.*, o.order_number')
            ->join('orderform AS o', 'o.id', 'op.orderform_id')
            ->whereRaw('o.status="New"')
            ->whereRaw('o.stock_status=0')
            ->whereRaw('op.info_id IS NOT NULL AND op.type_id IS NOT NULL AND op.key_type IS NOT NULL')
            ->orderBy('op.id', 'asc')
            ->get();
        $order_product = CommonService::objectToArray($order_product);

        foreach ($order_product as $product) {
            if (!isset($orderforms_check_stock[$product['orderform_id']])) {
                $orderforms_check_stock[$product['orderform_id']] = true;
            }

            if (in_array($product['key_type'], Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*非「商品」，免檢查庫存*/
                continue;
            } else if ($product['pre_buy'] == 0) { /*非超額購買，能成立訂單必定有庫存*/
                continue;
            } else { /*是「商品」且有超額購買，要檢查「實體庫存」是否夠*/
                if (!isset($productinfo_type_num[$product['type_id']])) {
                    $arr_db = OrderHelper::get_shop_db_config($product['order_number']);
                    $r = DB::connection($arr_db)->table('productinfo_type')
                        ->select('num')
                        ->whereRaw("id='" . $product['type_id'] . "' AND product_id='" . $product['info_id'] . "'")
                        ->first();
                    $r = CommonService::objectToArray($r);
                    $productinfo_type_num[$product['type_id']] = $r['num'] ?? -999999;
                }

                /*只要線上可購買數量小於0就視為庫存不足*/
                if ($productinfo_type_num[$product['type_id']] < 0) {
                    $orderforms_check_stock[$product['orderform_id']] = false;
                }
            }
        }

        foreach ($orderforms_check_stock as $orderform_id => $has_stock) {
            if ($has_stock) {
                DB::connection('main_db')->table('orderform')->where('id', $orderform_id)->update(['stock_status' => 1]);
                // } else {
                //     DB::connection('main_db')->table('orderform')->where('id', $orderform_id)->update(['stock_status' => 0]);
            }
        }
    }
}
