<?php
namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\DBtool\DBFileConnecter;
// use App\Services\DBtool\DBTextConnecter;

class MemberInstance
{   
  private $user_id;
  static private $tableName = 'account';
  static private $orderTableName = 'orderform';
  static public $account_column = 'email'; /*帳號欄位 email、phone*/
  static public $account_column_order = 'transport_email'; /*訂單帳號欄位(用於訂單建立會員資料) transport_email、transport_location_phone*/
  private $DBFileConnecter;

  public function __construct($user_id=0) {       
    $this->user_id = $user_id;
    // $this->DBTextConnecter = DBTextConnecter::withTableName('account', 'main_db');
    $this->DBFileConnecter = DBFileConnecter::withTableName('account', 'main_db');
  }
  /*取得會員資料庫連線*/
  static public function main_db(){
    return DB::connection('main_db');
  }
  /*更換當前會員id*/
  public function change_user_id($user_id){
    $this->user_id = $user_id;
  }

  /*取得會員資料*/
  public function get_user_data($addr_change="combine", $cond=null){
    // dd($cond);
    $userData = self::main_db()->table(self::$tableName . ' as a')
                              ->select('a.*', 'vip.id as vip_id', 'vip.vip_name')
                              ->leftjoin('vip_type as vip', 'a.vip_type', 'vip.id');
    if($cond){ /*有傳入篩選條件*/
      $userData->where($cond); /*依篩選條件搜尋*/
    }else{
      $userData = $userData->where('a.id', $this->user_id); /*依物件user_id搜尋*/
    }

    $userData = $userData->first();
    $userData = CommonService::objectToArray($userData);

    if( $userData ){ /*額外為顯示處理資料*/
      /*生日轉換為日期*/
      $userData['birthday'] = $userData['birthday'] ? date('Y-m-d', $userData['birthday']) : "";

      /*處理地址*/
      if($addr_change=="combine"){ /*合併顯示地址*/
        try{
          if($userData['home']){
            $addcods = explode('|||',$userData['home']);

            $city = DB::connection(config('A_sub'))->table('city')->whereRaw("AutoNo = ".$addcods[0])->get();
            $city = CommonService::objectToArray($city);
            $town = DB::connection(config('A_sub'))->table('town')->whereRaw("AutoNo = ".$addcods[1])->get();
            $town = CommonService::objectToArray($town);
            $post = $addcods[2];
            $otheradd = $addcods[3];

            $userData['home'] = $post.' '.$city[0]['Name'].$town[0]['Name'].$otheradd;
          }
        }catch (\Exception $e){
          $addcods = explode('|||',$userData['home']);
          $userData['home'] = count($addcods)>1 ? ' ' : $userData['home'];
        }
      }
      else if($addr_change=="split"){ /*獨立成縣市、區、郵遞區號*/
        $userData['F_I_CNo']='';
        $userData['F_I_TNo']='';
        $userData['F_S_NH_Zip']='';
        $userData['F_S_NH_Address']='';
        $userData['F_I_CNo_Name']='';
        $userData['F_I_TNo_Name']='';

        if($TryStrpos=strpos($userData['home'],"|||")){
          $ex = explode("|||",$userData['home']);
          $userData['F_I_CNo']=$ex[0];
          $userData['F_I_TNo']=$ex[1];
          $userData['F_S_NH_Zip']=$ex[2];
          $userData['F_S_NH_Address']=$ex[3];

          if(!empty($userData['F_I_CNo']))
            $userData['F_I_CNo_Name'] = DB::table('city')->whereRaw(" AutoNo = '".$userData['F_I_CNo']."'")->select('Name')->first()->Name;

          if(!empty($userData['F_I_TNo']))
            $userData['F_I_TNo_Name'] = DB::table('town')->whereRaw(" AutoNo = '".$userData['F_I_TNo']."'")->select('Name')->first()->Name;
        }else{
          $userData['F_S_NH_Address'] = $userData['home'];
        }
      }
    }
    return $userData;
  }
  /*取得會員資料(判斷是否為供應商)*/
  public function get_user_data_distributor($addr_change="combine", $cond=null){
    $userData = $this->get_user_data($addr_change, $cond);
    // dd($userData);
    if($userData){
      if($userData['user_type']==1){
        if(!$userData['shop_name']){
          $userData['shop_name'] = $userData['name'];
        }
        return $userData;
      }
    }
    return null;
  }
  /*修改會員資料*/
  public function update_user_data($updateData, $cond=[], $change_format=true){
    $returnData = ['code'=>0, 'msg'=>"", 'data'=>[]];
    if( empty($updateData) ){ $returnData['msg'] = Lang::get('資料不完整'); }
    unset($updateData['_token']);
    if( isset($updateData[self::$account_column]) ){ /*要修改帳號*/
      /*檢查是否有與「其他」帳號重複*/
      $adminData = $this->get_user_data($addr_change="ori", [
                  ['a.'.self::$account_column, '=', $updateData[self::$account_column]],
                  ['a.id', '<>', $this->user_id]
                ]);
      if($adminData){
        $returnData['msg'] = Lang::get('帳號已經存在');
        return $returnData;
      }
    }

    if($change_format){ /*調整帳號資料*/
      $returnData = self::arrange_data_to_db_format($updateData);
      if($returnData['code']==0){ /*調整資料時有錯誤訊息*/
        return $returnData;
      }else{
        $updateData = $returnData['data'];
      }
    }
    unset($updateData['id']);

    /*篩選要修改的對象*/
    $userData = self::main_db()->table(self::$tableName.' as a');
    if($cond){ /*有傳入篩選條件*/
      $userData = $userData->where($cond); /*依篩選條件搜尋*/
    }else{
    $userData = $userData->where('a.id', $this->user_id); /*依物件user_id搜尋*/
    }

    /*處理檔案上傳*/
    $userData2 = clone $userData;
    $target_data = $userData2->first();
    $target_data = CommonService::objectToArray($target_data);
    if($target_data){
      $user_id = $target_data['id'];
      $file_company = request()->file('file_company');
      // dump($file_company);
      if($file_company){
        $file_company = $this->DBFileConnecter->fixedFileUp($file_company, 'user_file_company'.CommonService::geraHash(10).'_'.$user_id);
        if($file_company){ $updateData['file_company'] = $file_company; }
      }
      $file_person = request()->file('file_person');
      // dump($file_person);
      if($file_person){
        $file_person = $this->DBFileConnecter->fixedFileUp($file_person, 'user_file_person'.CommonService::geraHash(10).'_'.$user_id);
        if($file_person){ $updateData['file_person'] = $file_person; }
      }
    }

    /*修改資料*/
    // dd($updateData);
    if(isset($updateData['_token'])){
      $remove['_token']=$updateData['_token'];
      $updateData = array_diff_key($updateData,$remove);
    }
    if(!empty($updateData)){
      $result = $userData->update($updateData);

      if($result){
        $returnData['code'] = 1;
        $returnData['msg'] = Lang::get('操作成功');
        $returnData['data'] = $this->get_user_data($addr_change="combine", $cond);
      }else{
        $returnData['msg'] == Lang::get('無資料需要修改');
      }
    }else{
      $returnData['msg'] == Lang::get('無資料需要修改');
    }		

    return $returnData;
  }
  /*新增會員資料*/
  public function insert_user_data($newData, $change_format=true){
    $returnData = ['code'=>0, 'msg'=>"", 'data'=>[]];

    if($change_format){ /*調整帳號資料*/
      $db_formatData = self::arrange_data_to_db_format($newData);
      if($db_formatData['code']==0){ /*調整資料時有錯誤訊息*/
        return $db_formatData;
      }else{
        $newData = $db_formatData['data'];
      }
    }

    /* 判斷帳號唯一性 */
      $orWhereCount = 0; /*計算用來判斷唯一性的欄位數量*/
      $account_column = self::$account_column;
      $where = function ($query) use (&$orWhereCount, $newData, $account_column){
        if(isset($newData['id'])){ /*有設定id*/
          if($newData['id']!=""){
            $query->orWhere('a.id', $newData['id']);
            $orWhereCount++;
          }
        }
        if(isset($newData[$account_column])){ /*有設定帳號*/
          if($newData[$account_column]!=""){
            $query->orWhere('a.'.$account_column, $newData[$account_column]);
            $orWhereCount++;
          }
        }
        if(isset($newData['gmail'])){ /*有設定goolge帳號*/
          if($newData['gmail']!=""){
            $query->orWhere('a.gmail', $newData['gmail']);
            $orWhereCount++;
          }
        }
        if(isset($newData['apple'])){ /*有設定apple*/
          if($newData['apple']!=""){
            $query->orWhere('a.apple', $newData['apple']);
            $orWhereCount++;
          }
        }
        if(isset($newData['line_id'])){ /*有設定line_id*/
          if($newData['line_id']!=""){
            $query->orWhere('a.line_id', $newData['line_id']);
            $orWhereCount++;
          }
        }
        if(isset($newData['FB_id'])){ /*有設定FB_id*/
          if($newData['FB_id']!=""){
            $query->orWhere('a.FB_id', $newData['FB_id']);
            $orWhereCount++;
          }
        }
      };
      $accounts = $this->get_user_data($addr_change="ori", $where);
      if($accounts && $orWhereCount){/*有篩選到帳號 且 有用來判斷唯一性的欄位*/
        $returnData['msg'] = Lang::get('帳號已經存在');
        return $returnData;
      }

    /*額外新會員資料*/
    $newData['number'] = config('extra.shop.subDeparment') . 'US' . date('Ymd') . self::getMemberNumber();
    $newData['createtime'] = time();

    $newData['upline_user'] = isset($newData['upline_user']) ? $newData['upline_user'] : 0;
    if($newData['upline_user']){
      $recommend_user = self::main_db()->table('account')->select('id','name','number','recommend_content')->where('number', $newData['upline_user'])->first();
      $recommend_user = CommonService::objectToArray($recommend_user);
      $newData['upline_user'] = $recommend_user ? $recommend_user['id'] : 0;
    }else{
      $newData['upline_user'] = 0;
    }

    // dd($newData);
    self::main_db()->table(self::$tableName)->insert($newData);
    $target_data = $this->get_user_data($addr_change="combine", $where);

    /*處理檔案*/
    $this->update_user_data([], $where);

    $returnData['data'] = $target_data;
    $returnData['code'] = 1;
    return $returnData;
  }
  /*寄送驗證信*/
  public static function send_user_verify_email($user_data){
    $globalMailData = HelperService::getMailData();
    $do_signup_letter = Lang::get('menu.註冊信消費者');
    $do_signup_letter = str_replace("{name}", $user_data['name'], $do_signup_letter);
    $do_signup_letter = str_replace("{mailFromName}", $globalMailData['mailFromName'], $do_signup_letter);
    $verify_url = url('Login/signcheck')."?id=".$user_data['email'];
    $do_signup_letter = str_replace("{verify_url}", $verify_url, $do_signup_letter);
    $mailBody = "
      <html>
        <head></head>
        <body>
          <div>
            ".$do_signup_letter."
          </div>
          <center></center>
          <div>
            ". $globalMailData['system_email']['signup_complete'] ."
          </div>
          <div style='color:red;'>
            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
          </div>
        </body>
      </html>
    ";
    $mail_return = HelperService::Mail_Send($mailBody, 'client', $user_data['email'], Lang::get('註冊通知'));
    return $mail_return;
  }

  /*依條件計算當前會員的訂單資料*/
  public function get_user_order_data($cond=[]){
    $orderform = self::main_db()->table(self::$orderTableName)->where('user_id', $this->user_id);

    if( isset($cond['status']) ){ /*篩選訂單狀態*/
      if( $cond['status']=='All' ){ /*全部的成立訂單*/
        $orderform = $orderform->whereRaw('status not in ("Cancel", "Return")');
      }
      else if( $cond['status']=='All_no' ){ /*全部的失效訂單*/
        $orderform = $orderform->whereRaw('status in ("Cancel", "Return")');
      }
      else{
        $orderform = $orderform->where('status', $cond['status']);
      }
    }

    /*判斷計算方式*/
    $method = isset($cond['method']) ? $cond['method'] : 'sum'; /*預設加總金額*/
    if( $method=='select' ){
      $calculated_order_price = $orderform->orderByRaw('id desc')->get();
      $calculated_order_price = CommonService::objectToArray($calculated_order_price);
    }
    else if( $method=='count' ){
      $calculated_order_price = $orderform->count();
    }else{
      $calculated_order_price = $orderform->sum('total');
    }

    return $calculated_order_price;
  }

  /*替當前會員建立抽抽樂紀錄*/
  public function set_lucky_draw($price=0, $pay_record_id=0, $order_id=0){
    $times = 0;

    /*檢查登入(使用者資料)*/
    $user_data = $this->get_user_data();
    if(!$user_data){ return $times; }

    /*檢查會員等級*/
    $vip_type_id = $user_data['vip_type'];
    $vip_type_id = $vip_type_id==0 && $vip_type_id=="" ? "-1" : $vip_type_id;
    $vip_type = self::main_db()->table('vip_type')->find($vip_type_id);
    $vip_type = CommonService::objectToArray($vip_type);
    if(!$vip_type){ /*沒設定為VIP會員(不限等級) 或 設定之等級不存在*/
      return $times;
    }else if($vip_type['id']=='0'){
      return $times;
    }

    /*計算消費金額可刮次數*/
    $limit_price = DB::table('consumption_draw_limit')->find(1)->price;
    $times = floor( $price / $limit_price );
    if($times < 1) return $times;

    /*逐次建立刮刮樂資料*/
    foreach( range(1, $times) as $once ){
      /*從1到所有啟用獎品之數量加總中 隨機取得一亂數*/
      $ratios = DB::table('consumption_draw')->where('online', 1)->sum('ratio');
      $rand_num = rand(1, $ratios);

      /*根據取得的亂數確認中獎項目*/
      $draw_id = 0;
      $gift_pic = "";
      $gift_name = "";
      $calculated_num = 0;
      $consumption_draws = DB::table('consumption_draw')->where('online', 1)->orderByRaw('ratio asc, id desc')->get();
      $consumption_draws = CommonService::objectToArray($consumption_draws);
      foreach ($consumption_draws as $consumption_draw) {
        $calculated_num += $consumption_draw['ratio'];
        if( $calculated_num >= $rand_num){
          $draw_id = $consumption_draw["id"];
          $gift_pic = $consumption_draw["pic"];
          $gift_name = $consumption_draw["name"];
          break;
        }
      }

      $data = [
        'user_id' => $this->user_id,
        'pay_record_id' => $pay_record_id,
        'order_id' => $order_id,
        'draw_id' => $draw_id,
        'gift_pic' => $gift_pic,
        'gift_name' => $gift_name,
        'createdate' => time(),
        'show' => 0,
      ];
      DB::table('consumption_draw_record')->insert($data);
    }

    return $times;
  }

  /*依搜尋條件取得會員列表資料*/
  static public function search_member($status, $request, $field='a.*,vip_type.id as vip_id,vip_type.vip_name'){
    // 取得搜尋變數
    $tag = $request->get('tag') ?? '1';
    $search_result['tag'] = $tag;

    $account_column_value = $request->get(self::$account_column) ?? '';
    $search_result[self::$account_column] = $account_column_value;

    /*會員搜尋資料*/
    $memberKey = $request->get('memberKey') ?? '';
    $memberKey = trim($memberKey);
    $search_result['memberKey'] = $memberKey;

    $nameKey = $request->get('nameKey') ?? '';
    $search_result['nameKey'] = $nameKey;

    $vipType = $request->get('vipType') ?? '';
    $search_result['vipType'] = $vipType;

    $userType = $request->get('userType') ?? '';
    $search_result['userType'] = $userType;

    $date_st = $request->get('date_st') ?? '';
    $search_result['date_st'] = $date_st;

    $date_en = $request->get('date_en') ?? '';
    $search_result['date_en'] = $date_en;

    $user_type = $request->get('user_type') ?? '';
    $search_result['user_type'] = $user_type;

    /*註冊商品搜尋資料*/
    $searchKey1 = $request->get('searchKey1') ?? '';
    $searchKey1 = trim($searchKey1);
    $search_result['searchKey1'] = $searchKey1;

    $buy_date_st1 = $request->get('buy_date_st1') ?? '';
    $search_result['buy_date_st1'] = $buy_date_st1;

    $buy_date_en1 = $request->get('buy_date_en1') ?? '';
    $search_result['buy_date_en1'] = $buy_date_en1;

    $reg_date_st = $request->get('reg_date_st') ?? '';
    $search_result['reg_date_st'] = $reg_date_st;

    $reg_date_en = $request->get('reg_date_en') ?? '';
    $search_result['reg_date_en'] = $reg_date_en;

    /*購買商品搜尋資料*/
    $searchKey2 = $request->get('searchKey2') ?? '';
    $searchKey2 = trim($searchKey2);
    $search_result['searchKey2'] = $searchKey2;

    $buy_date_st2 = $request->get('buy_date_st2') ?? '';
    $search_result['buy_date_st2'] = $buy_date_st2;

    $buy_date_en2 = $request->get('buy_date_en2') ?? '';
    $search_result['buy_date_en2'] = $buy_date_en2;

    // 處理sql篩選語句		
    $account_number_list = '';
    $number_list = 'true ';
    $excel_wh = '';
    $account_number = '';
    $his_number = '';
    $excel_number = 0;
    $order_number = 0;

    if($account_column_value){ /*用於比對帳號*/
      $number_list .= "and a.".self::$account_column."='".$account_column_value."'";
    }

    $number_list .= "and ( a.name like '%" . $memberKey . "%' or 
                      a.number like '%" . $memberKey . "%' or 
                      a.email like '%" . $memberKey . "%' or 
                      a.phone like '%" . $memberKey . "%' )";

    if($date_st != ''){
      $number_list .= "and (a.createtime BETWEEN '".strtotime($date_st)."' and '".(strtotime($date_en)+84600)."')";
    }

    if($vipType != ''){
      if($vipType=="-1"){ // 搜尋無等級會員
        $number_list .= " and a.vip_type=0 ";
      }else{ // 搜尋其他等級會員
        $number_list .= " and a.vip_type=".$vipType;
      }
    }

    if($userType != ''){
      $number_list .= " and a.user_type=".$userType;
    }
    if($user_type != ''){
      $number_list .= " and a.user_type=".$user_type;
    }
    if($status !== ''){
      $number_list .= " and a.status='".$status."'";
    }
    //dd($number_list);
    $field = array_map('trim', explode(',', $field));
    // dump($field);exit;
    $rowData = self::main_db()->table(self::$tableName.' as a')
                              ->select($field)
                              ->leftJoin('vip_type', 'a.vip_type', '=', 'vip_type.id')
                              ->whereRaw($number_list)
                              ->orderByRaw('a.id desc')
                              ->get();
    $rowData = CommonService::objectToArray($rowData);
    $search_result['rowData'] = $rowData;

    $do_number = 0;
    $h_number = []; // 已註冊/已購買的會員
    $n_number = []; // 未註冊/未購買的會員
    if($nameKey == "1"){ // 註冊商品搜尋
      foreach ($rowData as $key => $value) {
        $reg_where = "";

        $reg_where = "account_number = '".$value['id'] ."'";
        if($searchKey1 != ''){
          if($reg_where != '')
            $reg_where .= " and ";

          $reg_where .= "product_name like '%".$searchKey1."%'";
        }


        if($buy_date_st1 != ''){
          if($reg_where != '')
            $reg_where .= " and ";
          $reg_where .= "(buytime BETWEEN '".$buy_date_st1."' and '".$buy_date_en1."' ) ";
        }

        if($reg_date_st != ''){
          if($reg_where != '')
            $reg_where .= " and ";	
          $reg_where .= "(regtime BETWEEN '".$reg_date_st."' and '".$reg_date_en."') ";
        }

        //dump($reg_where);
        $ck = DB::connection(config('A_sub'))->table('excel')->whereRaw($reg_where)->groupBy('account_number')->select('account_number')->get();
        $ck = CommonService::objectToArray($ck);
        $rowData[$key]['reg'] = '0';
        if($ck){
          $rowData[$key]['reg'] = '1';
          $do_number++;
          array_push($h_number,$rowData[$key]);
        }else{
          array_push($n_number,$rowData[$key]);
        }
      }
    }else if($nameKey == "2"){ // 購買商品搜尋
      foreach ($rowData as $key => $value) {
        $his_wh = '';

        if($searchKey2 != ''){
          $his_wh = "product like '%" . $searchKey2 . "%'  and";
        }
        if($buy_date_st2 != ''){
          $his_wh .= " create_time > '".strtotime($buy_date_st2)."' and create_time <'".(strtotime($buy_date_en2)+86400)."' and ";			
        }

        //dump($his_wh);
        $ck = self::main_db()->table(self::$orderTableName)->select('user_id')
                            ->whereRaw($his_wh."  user_id = '".$value['id']."'")
                            ->groupBy('user_id')
                            ->get();
        $ck = CommonService::objectToArray($ck);
        $rowData[$key]['buy'] = '0';

        if($ck){
          $rowData[$key]['buy'] = '1';
          $do_number++;
          array_push($h_number,$rowData[$key]);
        }else{
          array_push($n_number,$rowData[$key]);
        }
      }
    }
    $search_result['do_number'] = $do_number;
    $search_result['h_number'] = $h_number;
    $search_result['n_number'] = $n_number;

    return $search_result;
  }
  /*依搜尋條件回傳屬於的user_id sql篩選語法*/
  static public function user_id_sql($cond=[]){
    $user_id_sql = "";
    if(empty($cond)){ return $user_id_sql; }

    $users = self::main_db()->table(self::$tableName.' as a');

    if(isset($cond['searchKey'])){ /*關鍵字篩選(姓名、會員編號)*/
      if($cond['searchKey']){
        $users = $users->where(function ($query) use ($cond) {
            $query->where('name', 'like', "%".$cond['searchKey']."%")
                  ->orWhere('number', 'like', "%".$cond['searchKey']."%");
          });
      }
    }
    $users = $users->get();
    $users = CommonService::objectToArray($users);
    $user_ids = [];
    foreach ($users as $value) {
      array_push($user_ids, $value['id']);
    }
    if($user_ids){
      $user_id_sql = 'user_id in ('. implode(",", $user_ids) .')';
    }

    return $user_id_sql;
  }

  /*取得新會員編號*/
  static public function getMemberNumber(){
    $count = self::main_db()->table(self::$tableName)->where('number','like', config('extra.shop.subDeparment').'US'.date('Ymd').'%')->orderByRaw('id desc')->first();
    $count = CommonService::objectToArray($count);
    $count = $count ? intval(substr($count['number'],-3)) + 1 : 1;
    if($count < 10){
      $count = '00' . $count;
    }else if($count < 100){
      $count = '0' . $count;
    }
    return $count;
  }
  /*調整資料格式以符合資料庫*/
  static public function arrange_data_to_db_format($data){
    $returnData = ['code'=>0, 'msg'=>"", 'data'=>[]];

    /*設定檢查*/
    /*必填欄位*/
    $rule = [];
    $msg = [];
    if( isset($data['email']) ){
      $rule['email'] = 'required|email';
      $msg['email.email'] = Lang::get('email格式錯誤');
      $msg['email.required'] = Lang::get('email不得為空');
    }
    if( isset($data['name']) ){
      $rule['name'] = 'required';
      $msg['name.required'] = Lang::get('名稱不得為空');
    }
    if( isset($data['F_S_NH_Address']) ){
      $rule['F_S_NH_Address'] = 'required';
      $msg['F_S_NH_Address.required'] = Lang::get('地址不得為空');
    }
    // if( isset($data['F_I_CNo']) ){
    // 	$rule['F_I_CNo'] = 'required';
    // 	$msg['F_I_CNo.required'] = Lang::get('請選擇縣市');
    // }
    // if( isset($data['F_I_TNo']) ){
    // 	$rule['F_I_TNo'] = 'required';
    // 	$msg['F_I_TNo.required'] = Lang::get('請選擇鄉鎮區');
    // }
    // if( isset($data['F_S_NH_Zip']) ){
    // 	$rule['F_S_NH_Zip'] = 'required';
    // 	$msg['F_S_NH_Zip.required'] = Lang::get('郵遞區號不得為空');
    // }
    /*進行驗證*/
    $validate = Validator::make($data, $rule, $msg);
    // $validate->rule('regex', '/^.[A-Za-z0-9]+$/');
    if ($validate->fails()) {
        $returnData['msg'] = $validate->errors()->first();
        return $returnData;
    }

    if( isset($data['phone']) ){
      if(!preg_match("/^09[0-9]{8}$/", $data['phone'])){
        $returnData['msg'] = Lang::get('請輸入09開頭的10位手機號碼');
        return $returnData;
      }
    }

    /*檢查密碼*/
    if( isset($data['password']) || isset($data['pwd']) ){
      $password = isset($data['password']) ? $data['password'] : "";
      $password = isset($data['pwd']) ? $data['pwd'] : $password;
      if( !preg_match('/([0-9]+)/' ,$password) || !preg_match('/([a-zA-Z]+)/' ,$password)){
        $returnData['msg'] = Lang::get('密碼需包含英文及數字');
        return $returnData;
      }
      if( isset($data['passwordB']) ){
        if($password != $data['passwordB']){
          $returnData['msg'] = Lang::get('密碼不一致');
            return $returnData;
        }
      }
      $data['pwd'] = $password;
    }

    /*資料修改格式*/
      /*密碼*/
      if(isset($data['pwd'])){
        $data['pwd'] = md5($data['pwd']);
      }

      /*生日*/
      if(isset($data['birthday'])){
        if($data['birthday']){
          $data['birthday'] = strtotime($data['birthday']);
          if(!$data['birthday']){
            $returnData['msg'] = Lang::get('生日格式請輸入YYYY/MM/DD');
            return $returnData;
          }
        }
      }

      /*地址*/
      $home = "";
      if( isset($data['F_I_CNo']) && isset($data['F_I_TNo']) && isset($data['F_S_NH_Zip']) ){
        if( $data['F_I_CNo'] != '' && $data['F_I_TNo'] !='' && $data['F_S_NH_Zip'] !=''){
          $home = $data['F_I_CNo'].'|||'.$data['F_I_TNo'].'|||'.$data['F_S_NH_Zip'].'|||';
        }
      }
      if( isset($data['F_S_NH_Address']) ){
        $home .= $data['F_S_NH_Address'];
      }
      if($home){
        $data['home'] = $home;
      }

    /*刪除非資料庫欄位的輸入值*/
      unset($data['F_I_CNo']);
      unset($data['F_I_TNo']);
      unset($data['F_S_NH_Zip']);
      unset($data['F_S_NH_Address']);

      unset($data['password']);
      unset($data['passwordB']);
      unset($data['term']);

      unset($data['recaptcha']);
      unset($data['g-recaptcha-response']);

      $config = \HTMLPurifier_Config::createDefault();
      $purifier = new \HTMLPurifier($config);
      foreach ($data as $key => $value) {
        $data[$key] = $purifier->purify($value);
      }

    $returnData['code'] = 1;
    $returnData['data'] = $data;
    return $returnData;
  }

  /*取得VIP會員等級資料*/
  static public function get_vip_types(array $params=[], bool $id_as_key=false){
    $db_data = DB::connection('main_db')->table('vip_type')->whereRaw("id != 0");

    if(isset($params['id'])){ $db_data->where('id', $params['id']); }

    $db_data = $db_data->orderby('rule', 'asc')->orderby('id', 'asc')->get();
    $db_data = CommonService::objectToArray($db_data);

    if($id_as_key){
      $temp_data = [];
      foreach ($db_data as $value) {
        $temp_data[$value['id']] = $value;
      }
      $db_data = $temp_data;
    }
    return ['db_data'=>$db_data];
  }
}
