<?php

namespace App\Services\pattern;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class PointRecords
{   
    private $db_connect;
    private $user_id;

    public function __construct($user_id=0) {
        $this->db_connect   = DB::connection('main_db'); /*商品網站用此連線*/
        // $this->db_connect   = DB::connection(); /*訂單網站用此連線*/
        $this->user_id      = $user_id;
    }
    /*跟換物件user_id*/
    public function change_user_id($user_id){
        $this->user_id      = $user_id;
    }

    /*新增點數紀錄*/
    public function add_records($insertData){
        $insertData['user_id'] = $this->user_id;
        $insertData['msg_time'] = date('Y-m-d H:i:s');
        $records = $this->db_connect->table('points_record')->insertGetId($insertData);
        $this->db_connect->table('account')->where('id', $this->user_id)->increment('point', $insertData['points']);
        return $records;
    }
    public function get_records($params, $join_account=false){
        $db = $this->db_connect->table('points_record AS pr');
        
        /*篩選欄位*/
        $field_query = 'pr.*';
        if($join_account){
            $field_query .= ', a.name, a.number, a.user_type';
        }
        $db = $db->selectRaw($field_query);
        if($join_account){
            $db = $db->join('account AS a', 'a.id', 'pr.user_id', 'LEFT');
        }

        /*篩選條件*/
        if(isset($params['user_id'])){
            $db = $db->where('pr.user_id','=',$params['user_id']);
        }
        if(isset($params['date_s'])){
            if($params['date_s']){
                $db = $db->where('pr.msg_time','>=',$params['date_s'].' 00:00:00');
            }
        }
        if(isset($params['date_e'])){
            if($params['date_e']){
                $date_e = strtotime($params['date_e'].'+1Day');
                $db = $db->where('pr.msg_time','<',date('Y-m-d', $date_e).' 00:00:00');
            }
        }
        if(isset($params['point_msg'])){
            $db = $db->where('pr.msg','LIKE', '%'.$params['point_msg'].'%');
        }
        if($join_account){
            if(isset($params['user_key'])){
                $db = $db->where(function($query)use($params){
                    $query->where('a.name','LIKE', '%'.$params['user_key'].'%')
                          ->orWhere('a.number','LIKE', '%'.$params['user_key'].'%');
                });
            }
        }

        /*分頁*/
        if(isset($params['count_of_items'])){ /*有傳入一頁數量*/
            $index_and_length = CommonService::get_model_limit_index_and_length($params['count_of_items'], $params['page'] ?? 1);
            if($index_and_length[1]){ /*需依分頁篩選*/
                $db = $db->offset($index_and_length[0])->limit($index_and_length[1]);
            }
        }
        
        $records = $db->orderBy('pr.id', 'desc')->get();
        $records = CommonService::objectToArray($records);
        return $records;
    }

    /*取得點數紀錄*/
    public function get_user_records(){
        $params = [
            'user_id' => $this->user_id,
        ];
        $records = $this->get_records($params);
        return $records;
    }

    /*取得當前點數*/
    public function get_current_points(){
        $account = $this->db_connect->table('account')->where('id','=',$this->user_id)->first();
        $account = CommonService::objectToArray($account);
        return $account['point'];
    }
    
    /*取得過期點數*/
    public function get_expiring_points($type="expiring"){
        $excel = $this->get_expiring_duration();
        if($excel['value1']=='99'){ /*週期(年)設為99表示不使用有效期限功能*/
            return $expiring_points = 0;
        }

        $points_belong_time = strtotime( $this->expire_point_belongs_time($type) );
        $expiring_points = $this->db_connect->table('points_record')
                                    ->where('user_id','=', $this->user_id)
                                    ->where('belongs_time', '<', $points_belong_time)
                                    ->orderBy('id','desc')->sum('points');
        $current_used_points = $this->db_connect->table('points_record')
                                    ->where('user_id','=', $this->user_id)
                                    ->where('belongs_time', '>', $points_belong_time)
                                    ->where('points', '<', 0)
                                    ->orderBy('id','desc')->sum('points');

        $expiring_points += $current_used_points;

        $expiring_points = $expiring_points < 0 ? 0 : $expiring_points;
        return $expiring_points;
    }
    public function expire_point_belongs_time($type="expiring"){
        $excel = $this->get_expiring_duration();
        $expire_period = $excel['value1']; /*年 ex:1 */
        $expire_date   = $excel['value2']; /*結算日 ex:12-31 */

        $time_str = date('Y').'-'.$expire_date. ' -'.$expire_period.'Year +1Day';

        if($type=="expired") { /*找已過期*/ 
            if( strtotime(date('Y-m-d')) <= strtotime( date('Y').'-'.$expire_date) ){ /*如果當前日期比設定過期日的月日小或一樣*/
                $time_str.=' -1Year'; /*需額外扣除一年*/
            }
        }
        
        return $time_str;
    }
    public function get_expiring_duration(){
        $excel = $this->db_connect->table('excel')->where('id','=','2')->first();
        return CommonService::objectToArray($excel);
    }
    public function expiring_count_date(){
        $expire_date = $this->get_expiring_duration()['value2']; /*結算日 ex:12-31 */
        $count_date = date('Y').'-'.$expire_date;
        return $count_date;
    }

    /*點數記錄依user_id群組起來，用於取得所有有點數的會員，*/
    public function get_member_has_points(){
        $records = $this->db_connect->table('points_record')->groupBy('user_id')->get();
        $records = CommonService::objectToArray($records);
        return $records;
    }
    /*設定點數成過期*/
    public function set_point_expire(){
        $expired_points = $this->get_expiring_points($type="expired");

        if($expired_points>0){
            $expired_points = $expired_points * (-1);
            $time_str = strtotime( $this->expire_point_belongs_timeexpire_point_belongs_timeexpire_point_belongs_time($type="expired").' -1Day');
            $count_date = date('Y-m-d', $time_str);
            $this->add_records([
                'msg'           => Lang::get('點數到期').':'.$count_date,
                'points'        => $expired_points,
                'belongs_time'  => time()
            ]);
        }

        return ['user_id'=>$this->user_id, 'expired_points'=>$expired_points];
    }
}
