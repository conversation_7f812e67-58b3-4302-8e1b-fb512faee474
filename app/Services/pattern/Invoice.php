<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

use App\Services\CommonService;
use App\Services\pattern\HelperService;

class Invoice extends Controller
{
    protected $oService;
    private $control_ecpay_invoice;

    function __construct()
    {
        date_default_timezone_set("Asia/Taipei");

        // 1.載入SDK程式
        include_once(ROOT_PATH . 'app/Services/ThirdParty/Ecpay.Invoice.php');

        // 初始化網路服務物件。
        $oService = new \NetworkService([
            'PlatformID' => config('extra.ecpay.PlatformID'),
            'MerchantID' => config('extra.ecpay.MerchantID'),
            'HashKey' => config('extra.ecpay.HashKey_invoice'),
            'HashIV' => config('extra.ecpay.HashIV_invoice'),
        ]);

        $oService->ServiceURL = config('extra.ecpay.Invoice_Url');

        $this->oService = $oService;
    }

    public static function instance()
    {
        return new Invoice();
    }

    /*設定發票字軌*/
    public function setAphabeticLetter()
    {
        $c_year = intval(date('Y')) - 1911;
        $c_month = intval(date('m'));
        $c_day = intval(date('d'));

        if (in_array($c_month, [1, 3, 5, 7, 9, 11])) {
        }

        if ($c_month >= 2) {
            $c_month = 1;
            $c_year += 1;
        }

        $InvoiceTerm = ceil($c_month / 2);

        // 查詢財政部配號結果
        // 1.設定請求網址
        $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
        $this->oService->ServiceURL .= 'B2CInvoice/GetGovInvoiceWordSetting'; /*B2C查詢財政部配號結果*/

        // 2.寫入資訊
        $arData = [
            "MerchantID" => $this->oService->MerchantID,
            "InvoiceYear" => $c_year,
        ];

        // 3.送出
        $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
        $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

        // 4.將Data解密
        $Return_Info = $this->oService->DecodeReturnInfo($szResult);
        $InvoiceInfo = $Return_Info['InvoiceInfo'][1];

        // 字軌與配號設定
        // 1.設定請求網址
        $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
        $this->oService->ServiceURL .= 'B2CInvoice/AddInvoiceWordSetting'; /*B2C字軌與配號設定*/

        // 2.寫入資訊
        $arData = [
            "MerchantID" => $this->oService->MerchantID,
            "InvoiceTerm" => $InvoiceTerm,
            "InvoiceYear" => $c_year,
            "InvType" => "07",
            "InvoiceCategory" => "1",
            "InvoiceHeader" => $InvoiceInfo['InvoiceHeader'],
            "InvoiceStart" => (string)$InvoiceInfo['InvoiceStart'],
            "InvoiceEnd" => str_pad(($InvoiceInfo['InvoiceStart'] + $InvoiceInfo['InvoiceEnd']), 8, "0", STR_PAD_LEFT),
        ];

        // 3.送出
        $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
        $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

        // 4.將Data解密
        $Return_Info = $this->oService->DecodeReturnInfo($szResult);
    }

    public function check_love_code($LoveCode = '')
    {
        // 1.設定請求網址
        $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
        $this->oService->ServiceURL .= 'B2CInvoice/CheckLoveCode'; /*資料驗證／捐贈碼驗證*/

        // 2.寫入資訊
        $arData = [
            "MerchantID" => $this->oService->MerchantID,
            "LoveCode" => $LoveCode,
        ];

        // 3.送出
        $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
        $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

        // 4.將Data解密
        $Return_Info = $this->oService->DecodeReturnInfo($szResult);

        return $Return_Info;
    }

    public function check_barcode($BarCode = '')
    {
        // 1.設定請求網址
        $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
        $this->oService->ServiceURL .= 'B2CInvoice/CheckBarcode'; /*資料驗證／手機條碼驗證*/

        // 2.寫入資訊
        $arData = [
            "MerchantID" => $this->oService->MerchantID,
            "BarCode" => $BarCode,
        ];

        // 3.送出
        $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
        $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

        // 4.將Data解密
        $Return_Info = $this->oService->DecodeReturnInfo($szResult);

        return $Return_Info;
    }

    /*開立電子發票*/
    public function create_invoice($order_id = "")
    {
        try {
            $this->setAphabeticLetter();
        } catch (\Exception $e) {
        }

        $Return_Info = [
            'OrderID' => "",
            'RelateNumber' => "",
            'InvoiceDate' => "",
            'InvoiceNo' => "",
            'RandomNumber' => "",
            'RtnCode' => 0,
            'RtnMsg' => "",
            'CheckMacValue' => "",
        ];

        $o = DB::connection('main_db')->table('orderform')
            ->select(['orderform.*', 'account.name as user_name'])
            ->leftJoin('account', 'orderform.user_id', '=', 'account.id')
            ->where('orderform.id', $order_id)->first();
        $o = CommonService::objectToArray($o);

        if (!$o) {
            $Return_Info['RtnMsg'] = '資料提供不完整';

            return $Return_Info;
        }

        if ($o['payment'] != 1 && $o['receipts_state'] == 0) {
            $Return_Info['OrderID'] = $order_id;
            $Return_Info['RtnMsg'] = '尚未完成付款';

            return $Return_Info;
        }

        // if ($o['InvoiceNo']) {
        //     $Return_Info['OrderID'] = $order_id;
        //     $Return_Info['RtnMsg'] = '已開立過發票';

        //     return $Return_Info;
        // }

        if (intval($o['total']) == 0) {
            $Return_Info['OrderID'] = $order_id;
            $Return_Info['RtnMsg'] = '需付金額為0，無須開立發票';

            return $Return_Info;
        }

        try {
            // 1.設定請求網址
            $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
            $this->oService->ServiceURL .= 'B2CInvoice/Issue'; /*B2C開立發票*/

            // 2.寫入發票相關資訊
            $ItemTaxType = 1; /*設定含稅*/
            $add_fax_ratio = 1;

            /* 商品資訊 */
            $items = [];
            $globalMailData = HelperService::getMailData();
            array_push($items, [
                'ItemName' => '餅乾食品',
                'ItemCount' => 1,
                'ItemWord' => '個',
                'ItemPrice' => $o['total'],
                'ItemAmount' => $o['total'],
                'ItemTaxType' => $ItemTaxType,
                'ItemRemark' => '商品',
            ]);

            /* 其他發票資料 */
            $RelateNumber = config('extra.shop.company_name') . '_' . $this->randomkeys(5) . '_' . $o['id'];
            $CustomerName = $o['company_title'] ? $o['company_title'] : $o['transport_location_name'];
            $transport_location = strip_tags($o['transport_location']);
            $transport_location_phone = preg_replace("/[^0-9]/", '', $o['transport_location_phone']);
            $arData = [
                'MerchantID' => $this->oService->MerchantID,
                'RelateNumber' => $RelateNumber, /*自訂編號*/
                'CustomerID' => $o['id'], /*客戶編號*/
                'CustomerIdentifier' => trim($o['uniform_numbers']), /*統編*/
                'CustomerName' => $CustomerName, /*客戶名稱，當列印註記=1(列印)時，為必填*/
                'CustomerAddr' => trim($transport_location), /*客戶地址，當列印註記=1(列印)時，為必填*/
                'CustomerPhone' => trim($transport_location_phone), /*客戶手機號碼，當客戶電子信箱為空字串時，為必填*/
                'CustomerEmail' => trim($o['transport_email']), /*客戶電子信箱，當客戶手機號碼為空字串時，為必填*/
                'ClearanceMark' => '', /*通關方式，當課稅類別[TaxType]=2(零稅率)時，為必填*/
                'Print' => $o['Print'], /*列印註記，0：不列印(捐贈註記=1(捐贈)時、載具類別有值時)、1：要列印(統一編號有值時)*/
                'Donation' => $o['InvoiceStyle'] == 5 ? 1 : 0, /*捐贈註記，0：不捐贈(統一編號有值時、載具類別有值時)、1：要捐贈*/
                'LoveCode' => $o['LoveCode'], /*捐贈碼，當捐贈註記=1時，為必填*/
                'CarrierType' => $o['CarrierType'], /*載具類別*/
                'CarrierNum' => $o['CarrierNum'], /*載具編號*/
                'TaxType' => $ItemTaxType, /*課稅類別*/
                'SalesAmount' => $o['total'], /*發票總金額(含稅)*/
                'InvoiceRemark' => '', /*發票備註*/
                'Items' => $items, /*商品*/
                'InvType' => '07', /*字軌類別*/
                'vat' => '', /*商品單價是否含稅*/
            ];

            // 3.送出
            $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
            $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

            // 4.將Data解密
            $Return_Info = $this->oService->DecodeReturnInfo($szResult);

            // 5.更新資料庫
            if ($Return_Info['RtnCode']) { /*開立成功*/
                $ticket_data = [
                    'InvoiceNo'     => $Return_Info['InvoiceNo'],   /*發票號碼*/
                    'RandomNumber'  => $Return_Info['RandomNumber'],/*發票隨機碼*/
                    'InvoiceDate'   => $Return_Info['InvoiceDate'], /*發票日期*/
                ];

                DB::connection('main_db')->table('orderform')->where('id', $order_id)->update($ticket_data);
            }
        } catch (\Exception $e) {
            // 例外錯誤處理。
            $sMsg = $e->getMessage();
            $Return_Info = [
                'RelateNumber' => '',
                'InvoiceDate' => '',
                'InvoiceNo' => '',
                'RandomNumber' => '',
                'RtnCode' => 0,
                'RtnMsg' => $sMsg,
                'CheckMacValue' => '',
            ];
        }

        return $Return_Info;
    }

    /*作廢電子發票*/
    public function delete_invoice($money_table = '', $payment_id = '')
    {
        $Return_Info = [
            'RtnCode' => 0,
            'RtnMsg' => '',
            'InvoiceNo' => '',
        ];

        if (self::$control_ecpay_invoice != 1) {
            $Return_Info['RtnMsg'] = '未使用電子發票功能';

            return $Return_Info;
        }
        if (!$money_table || !$payment_id) {
            $Return_Info['RtnMsg'] = '資料提供不完整';

            return $Return_Info;
        }

        $payment = DB::table($money_table)->where('id', $payment_id)->get();
        $payment = CommonService::objectToArray($payment);

        if (!$payment) {
            $Return_Info['RtnMsg'] = '無此付款紀錄';

            return $Return_Info;
        }

        // 1.設定請求網址
        $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
        $this->oService->ServiceURL .= 'B2CInvoice/Invalid'; /*B2C作廢發票*/

        // 2.寫入發票相關資訊
        $arData = array(
            'MerchantID' => $this->oService->MerchantID,
            'InvoiceNo' => $payment['ticket'], /*發票號碼*/
            'InvoiceDate' => date('Y-m-d', $payment['ticketdate']), /*發票開立日期*/
            'Reason' => '發票作廢'
        );

        // 3.送出
        $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
        $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

        // 4.將Data解密
        $Return_Info = $this->oService->DecodeReturnInfo($szResult);
        // dump($Return_Info);

        // 5.更新資料庫
        $ticket_data = [
            'ticket' => '',  /*發票號碼*/
            'ticket_rand' => '',  /*發票隨機碼*/
            'ticketdate' => '',  /*發票日期*/
        ];
        DB::table($money_table)->where('id', $payment['id'])->insert($ticket_data);

        return $Return_Info;
    }

    /*列印發票(取得發票樣張)*/
    public function print_invoice($order_id = '')
    {
        $Return_Info = [
            'RtnCode' => 0,
            'RtnMsg' => '',
            'InvoiceHtml' => '',
        ];

        $o = DB::connection('main_db')->table('orderform')->select('orderform.*', 'account.name as user_name')
            ->leftJoin('account', 'orderform.user_id', 'account.id')
            ->where('orderform.id', $order_id)->first();
        $o = CommonService::objectToArray($o);

        if (!$o) {
            $Return_Info['RtnMsg'] = '資料提供不完整';

            return $Return_Info;
        }

        if (!$o['InvoiceNo'] || !$o['InvoiceDate'] || !$o['RandomNumber']) {
            $Return_Info['RtnMsg'] = '發票資訊不完整';

            return $Return_Info;
        }

        if ($o['Print'] == 0) {
            $Return_Info['RtnMsg'] = '此發票免列印';

            return $Return_Info;
        }

        // 1.設定請求網址
        $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
        $this->oService->ServiceURL .= 'B2CInvoice/InvoicePrint'; /*B2C列印發票*/

        // 2.寫入發票相關資訊
        $arData = array(
            'MerchantID' => $this->oService->MerchantID,
            'InvoiceNo' => $o['InvoiceNo'],                      /*發票號碼*/
            'InvoiceDate' => date('Y-m-d', strtotime($o['InvoiceDate'])),   /*發票開立日期*/
            'IsShowingDetail' => '1',                            /*是否顯示明細*/
        );

        // 3.送出
        $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
        $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

        // 4.將Data解密
        $Return_Info = $this->oService->DecodeReturnInfo($szResult);

        return $Return_Info;
    }

    /*發送發票通知*/
    public function invoice_notify($order_id = '')
    {
        $Return_Info = [
            'RtnCode' => 0,
            'RtnMsg' => '',
        ];

        $o = DB::connection('main_db')->table('orderform')->select('orderform.*', 'account.name as user_name')
            ->leftJoin('account', 'orderform.user_id', 'account.id')
            ->where('orderform.id', $order_id)->first();
        $o = CommonService::objectToArray($o);

        if (!$o) {
            $Return_Info['RtnMsg'] = '資料提供不完整';

            return $Return_Info;
        }

        if ($o['receipts_state'] == 0) {
            $Return_Info['OrderID'] = $order_id;
            $Return_Info['RtnMsg'] = '尚未完成付款';

            return $Return_Info;
        }

        if (!($o['InvoiceNo'])) {
            $Return_Info['OrderID'] = $order_id;
            $Return_Info['RtnMsg'] = '尚未開立發票';

            return $Return_Info;
        }

        try {
            // 1.設定請求網址
            $this->oService->ServiceURL = explode('B2CInvoice', $this->oService->ServiceURL)[0];
            $this->oService->ServiceURL .= 'B2CInvoice/InvoiceNotify'; /*B2C發送通知／發送發票通知*/

            $arData = [
                'MerchantID' => $this->oService->MerchantID,
                'InvoiceNo' => $o['InvoiceNo'], /*發票號碼*/
                'AllowanceNo' => '', /*折讓編號*/
                'Phone' => $o['transport_location_phone'], /*發送簡訊號碼*/
                'NotifyMail' => $o['transport_email'], /*發送電子郵件*/
                'Notify' => 'A', /*發送方式*/
                'InvoiceTag' => 'I', /*發送內容類型*/
                'Notified' => 'A', /*發送對象*/
            ];

            // 2.送出
            $arParameters = $this->oService->CreatePostData($arData);   /*產生post資料*/
            $szResult = $this->oService->ServerPost($arParameters);     /*傳遞參數至遠端*/

            // 3.將Data解密
            $Return_Info = $this->oService->DecodeReturnInfo($szResult);
        } catch (\Exception $e) {
            $sMsg = $e->getMessage();
            $Return_Info = [
                'RtnCode' => 0,
                'RtnMsg' => $sMsg,
            ];
        }

        return $Return_Info;
    }

    // PHP 7.4 開始，使用大括號（curly braces）存取陣列或字串偏移已被廢棄。
    // public function randomkeys($length)
    // {
    //     $pattern = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890";
    //     $key = '';

    //     for ($i = 0; $i < $length; $i++) {
    //         $key .= $pattern{
    //             rand(0, 9)};
    //     }

    //     return $key;
    // }

    public function randomkeys($length)
    {
        $pattern = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890";
        $key = '';
        $patternLength = strlen($pattern);

        for ($i = 0; $i < $length; $i++) {
            $key .= $pattern[rand(0, $patternLength - 1)];
        }

        return $key;
    }
}
