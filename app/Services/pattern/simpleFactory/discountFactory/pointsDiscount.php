<?php
namespace App\Services\pattern\simpleFactory\discountFactory;

use Illuminate\Support\Facades\Lang;
use App\Services\pattern\PointRecords;

 /*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is new
 * @depend: (1)thinkPHP5.x think\Db static class
 *
*/

class pointsdiscount extends discount
{
    public function getdiscountAndTotal($OrderData) {
        $PointRecords = new PointRecords(session()->get('user.id'));
        $records = $PointRecords->add_records([
            'msg'           => Lang::get('使用紅利點數'),
            'points'        => $this->discountId * (-1),
            'belongs_time'  => time()
        ]);

        return [
            'total' => $this->total - $this->discountId,
            'discount' => urldecode(json_encode([
                [
                    'type' => urlencode(Lang::get('使用紅利點數')),
                    'name' => urlencode(Lang::get('數量') . (int)$this->discountId),
                    'count' => urlencode(Lang::get('扣') . config('extra.shop.dollar_symbol') . (int)$this->discountId),
					'dis' => (int)$this->discountId
                ]
            ]))
        ];
    }
}
