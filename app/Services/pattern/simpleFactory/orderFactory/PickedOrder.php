<?php
namespace App\Services\pattern\simpleFactory\orderFactory;

use Illuminate\Support\Facades\DB;

/*
*
* @author: MazeR
* @email: <EMAIL>
* @lastUpdate: Nov 08 2017
* @Description: orderClass that status is picked
* @depend: none
*
*/
class PickedOrder extends Order
{
    public function changeStatus2Next() {
        if($this->already2Next){
            throw new \LogicException('Status already change to next'); 
        }
        try{
            $this->setTransportState(1);
            $this->already2Next = true;
        }catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    public function changeStatus2Return($reason) {
        parent::returndiscount();
        parent::changeStatus2Return($reason);
    }

    public function changeStatus2Cancel($reason) {
        parent::returndiscount();
        parent::changeStatus2Cancel($reason);
    }
    public function changeStatus2Restore() {
        throw new \LogicException('PickedOrder can\'t be restored');
    }
}
