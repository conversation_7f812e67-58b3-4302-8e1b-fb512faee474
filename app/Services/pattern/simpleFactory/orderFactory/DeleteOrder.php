<?php

namespace App\Services\pattern\simpleFactory\orderFactory;

 /*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is delete
 * @depend: none
 *
*/

class DeleteOrder extends Order
{
    public function changeStatus2Next() {
        throw new \LogicException('Delete status without next status');
    }
    public function changeStatus2Return($reason) {
        throw new \LogicException('Delete status without next status');
    }
    public function changeStatus2Cancel($reason) {
        throw new \LogicException('Delete status without next status');
    }
    public function changeStatus2Restore() {
        throw new \LogicException('DeleteOrder can\'t be restored');
    }
}
