<?php
namespace App\Services\pattern\simpleFactory\orderFactory;

use Illuminate\Support\Facades\Lang;
/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is delete
 * @depend: none
 *
*/
class PickableOrder extends Order
{
    public function changeStatus2Next() {
        throw new \LogicException(Lang::get('訂單已待處理，請進行揀貨'));
    }
    public function changeStatus2Return($reason) {
        throw new \LogicException(Lang::get('訂單已待處理，不可取消'));
    }
    public function changeStatus2Cancel($reason) {
        throw new \LogicException(Lang::get('訂單已待處理，不可取消'));
    }
    public function changeStatus2Restore() {
        throw new \LogicException('PickableOrder can\'t be restored');
    }
    public function changeStatus2Picked() {
        try {
            parent::changeStatus2Picked();
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }
}
