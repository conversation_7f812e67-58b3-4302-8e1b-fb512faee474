<?php
namespace App\Services\pattern\simpleFactory\orderFactory;
 /*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is cancel
 * @depend: none
 *
*/
use Illuminate\Support\Facades\Lang;

class CancelOrder extends Order
{
    public function changeStatus2Next() {
        throw new \LogicException(Lang::get('訂單已取消'));
    }

    public function changeStatus2Return($reason) {
        throw new \LogicException(Lang::get('訂單已取消'));
    }

    public function changeStatus2Cancel($reason) {
        throw new \LogicException(Lang::get('訂單已取消'));
    }

    public function changeStatus2Restore() {
        parent::changeStatus2Restore();
    }
}
