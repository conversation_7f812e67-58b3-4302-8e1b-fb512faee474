<?php

namespace App\Services\pattern\simpleFactory\orderFactory;

use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\PointRecords;
use App\Services\pattern\MemberInstance;
use App\Http\Controllers\admin\Payfee;
/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: Abstract Class that all orderClass's parent
 * @depend: (1)thinkPHP5.x think\Db static class
 *
*/

abstract class Order extends Controller
{
    protected $id;
    protected $config_db;
    protected $tableName;
    protected $coupon_tableName;
    protected $already2Next = false;
    protected $order_db = "main_db";

    public function __construct($id, $tableName, $coupon_tableName)
    {
        $this->id = $id;
        $this->tableName = $tableName;
        $this->coupon_tableName = $coupon_tableName;

        if ($this->id) {
            $orderform = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
            $orderform = CommonService::objectToArray($orderform);
            // $this->config_db = substr($orderform['order_number'], 0, 1) . '_sub';
            $this->config_db = 'A_sub';
        }
    }

    abstract public function changeStatus2Next();

    /*回傳ID*/
    public function id()
    {
        return $this->id;
    }

    // 修改匯款碼
    public function setReportNumber($reportNumber, $user_id)
    {
        $id = request()->post('id');
        $order_data = DB::connection('main_db')->table('orderform')->find($id);
        $order_data = CommonService::objectToArray($order_data);

        if (!$order_data) {
            return ['code' => 0, 'msg' => Lang::get('資料有誤'), 'url' => '/'];
        }

        if ($order_data['user_id'] != 0 && $order_data['user_id'] != $user_id) { // 訂單為會員訂單，且帳號不同
            return ['code' => 0, 'msg' => Lang::get('請先登入會員'), 'url' => '/'];
        }

        if ($order_data['report'] != '') { // 已經回報過
            return ['code' => 0, 'msg' => Lang::get('已回報'), 'url' => '/'];
        }

        if ($reportNumber == '') {
            return ['code' => 0, 'msg' => Lang::get('資料不完整'), 'url' => '/'];
        }

        DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update(['report' => $reportNumber]);

        // 寄送提醒信
        $report_code_letter = Lang::get('匯款回報信管理者');
        $report_code_letter = str_replace("{order_number}", $order_data['order_number'], $report_code_letter);

        $mailBody = '<html>';
        $mailBody .= '<head></head>';
        $mailBody .= '<body>';
        $mailBody .= '<div>';
        $mailBody .= $report_code_letter;
        $mailBody .= '</div>';
        $mailBody .= '<div style="color: red;">';
        $mailBody .= '≡ ' . Lang::get('此信件為系統自動發送，請勿直接回覆') . '(' . $order_data['id'] . ') ≡';
        $mailBody .= '</div>';
        $mailBody .= '</body>';
        $mailBody .= '</html>';

        $distributor_id = $order_data['distributor_id'];

        if ($distributor_id == 0) {
            HelperService::Mail_Send($mailBody, 'admin_order_remit', '', Lang::get('匯款回報提醒'));
        } else {
            $MemberInstance = new MemberInstance($distributor_id);
            $user_data = $MemberInstance->get_user_data_distributor();

            if ($user_data) {
                HelperService::Mail_Send($mailBody, 'client', $user_data['email'], Lang::get('匯款回報提醒'));
            }
        }

        return ['code' => 1, 'msg' => Lang::get('操作成功'), 'url' => '/'];
    }

    // 修改回報匯款狀態
    public function setReportState()
    {
        try {
            DB::connection($this->order_db)->table($this->tableName)
                ->where('id', $this->id)
                ->update(['report_check_time' => time()]);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 更新收款狀態
    public function setReceiptsState($state)
    {
        $o = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
        $o = CommonService::objectToArray($o);

        if (!$o) {
            return;
        }

        try {
            DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update(['receipts_state' => $state]);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }

        if ($state == 1) {
            // 寄送提醒信
            $mailBody = '<html>';
            $mailBody .= '<head></head>';
            $mailBody .= '<body>';
            $mailBody .= '<div>';
            $mailBody .= str_replace("{order_number}", $o['order_number'], Lang::get('確認付款信消費者'));
            $mailBody .= '</div>';
            $mailBody .= '<div style="color: red;">';
            $mailBody .= '≡ ' . Lang::get('此信件為系統自動發送，請勿直接回覆') . '(' . $o['id'] . ') ≡';
            $mailBody .= '</div>';
            $mailBody .= '</body>';
            $mailBody .= '</html>';

            HelperService::Mail_Send($mailBody, 'client', $o['transport_email'], Lang::get('確認付款通知'));

            $mailBody = '<html>';
            $mailBody .= '<head></head>';
            $mailBody .= '<body>';
            $mailBody .= '<div>';
            $mailBody .= str_replace("{order_number}", $o['order_number'], Lang::get('確認付款信管理者'));
            $mailBody .= '</div>';
            $mailBody .= '<div style="color: red;">';
            $mailBody .= '≡ ' . Lang::get('此信件為系統自動發送，請勿直接回覆') . '(' . $o['id'] . ') ≡';
            $mailBody .= '</div>';
            $mailBody .= '</body>';
            $mailBody .= '</html>';

            HelperService::Mail_Send($mailBody, 'admin_order_paid', '', Lang::get('確認付款通知'));
        }
    }

    // 更新出貨狀態
    public function setTransportState($state)
    {
        try {
            $o = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
            $o = CommonService::objectToArray($o);

            if (!$o['transport_date']) {
                $transport_date = date('Y-m-d');
            } else {
                $transport_date = $o['transport_date'];
            }

            if ($state == 1) { // 是改成「已寄出」
                if (in_array($o['status'], ['Complete'])) { /*檢查訂單是否已為「完成」*/
                    throw new \LogicException(Lang::get('已拋轉過完成'));
                } else if (in_array($o['status'], ['Cancel', 'Return'])) { /*檢查訂單是否已為「完成」*/
                    throw new \LogicException(Lang::get('請先恢復訂單'));
                } else if (in_array($o['status'], ['Pickable'])) { /*是否已在揀貨流程*/
                    if (empty(config('control.close_function_current')['揀貨列表'])) {
                        throw new \LogicException(Lang::get('請先進行揀貨'));
                    }
                } else { /*原本還是「未出貨」*/
                    $tcat_exclude_dates = DB::table('tcat_exclude_dates')->orderBy('date')->pluck('date');
                    $tcat_exclude_dates = CommonService::objectToArray($tcat_exclude_dates);

                    $exclude_dates = DB::table('shipping_exclude_dates')->where('removed', 0)->orderBy('date')->pluck('date');
                    $exclude_dates = CommonService::objectToArray($exclude_dates);

                    $dates = array_merge($tcat_exclude_dates, $exclude_dates);

                    if (in_array($transport_date, $dates)) {
                        throw new \LogicException('出貨日為排除出貨日，請擇日再進行出貨');
                    }

                    // 派發優惠券
                    $this->change_coupon('send');

                    // 增加點數
                    if ($o['add_point'] > 0) {
                        $PointRecords = new PointRecords($o['user_id']);
                        $PointRecords->add_records([
                            'msg'           => Lang::get('贈送點數') . '(' . Lang::get('完成訂單') . ')：' . $o['order_number'],
                            'points'        => $o['add_point'],
                            'belongs_time'  => time()
                        ]);
                    }

                    // 累積消費金額
                    DB::connection($this->order_db)->table('account')->where('id', $o['user_id'])->increment('total', $o['total']);
                }
            }

            // 寄送提醒信
            $globalMailData = HelperService::getMailData();
            $order_transport_letter = str_replace("{arrival_date}", $o['arrival_date'], Lang::get('訂單出貨通知信消費者'));
            $order_transport_letter = str_replace("{globalMailData_mailFromName}", $globalMailData['mailFromName'], $order_transport_letter);
            $mailBody = '<html>';
            $mailBody .= '<head></head>';
            $mailBody .= '<body>';
            $mailBody .= '<div>';
            $mailBody .= $order_transport_letter;
            $mailBody .= '</div>';
            $mailBody .= '<div style="color: red;">';
            $mailBody .= '≡ ' . Lang::get('此信件為系統自動發送，請勿直接回覆') . '(' . $o['id'] . ') ≡';
            $mailBody .= '</div>';
            $mailBody .= '</body>';
            $mailBody .= '</html>';

            HelperService::Mail_Send($mailBody, 'client', $o['transport_email'], Lang::get('訂單出貨通知'));

            // 修改訂單狀態
            DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update([
                'transport_state' => $state,
                'status' => 'Complete',
                'transport_date' => $transport_date,
            ]);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 更新備註
    public function setPS($ps)
    {
        try {
            DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update(['ps' => $ps]);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 退貨
    public function changeStatus2Return($reason)
    {
        try {
            // 收回優惠券
            $this->change_coupon('take_back');

            // 修改訂單狀態
            DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update([
                'status' => 'Return',
                'return_ps' => $reason,
                'cancel_date' => date('Y-m-d H:i:s'),
            ]);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 移至待出貨
    public function changeStatus2Picked()
    {
        try {
            $result = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update(['status' => 'Picked']);

            if ($result == 0) {
                throw new \LogicException(Lang::get('訂單狀態不在待出貨中'));
            }
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 取消訂單
    public function changeStatus2Cancel($reason)
    {
        try {
            // 收回優惠券
            $this->change_coupon('take_back');

            // 修改訂單狀態
            DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update([
                'status' => 'Cancel',
                'cancel_ps' => $reason,
                'cancel_date'  => date('Y-m-d H:i:s'),
            ]);

            $o = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
            $o = CommonService::objectToArray($o);

            $res_goods = '';
            $result = (array) json_decode($o['product'], true);

            foreach ($result as $key => $value) {
                $res_goods .= $value['name'];

                if (!empty($result[$key + 1])) {
                    $res_goods .= '、';
                }
            }

            /*取得寄信通用資料*/
            $globalMailData = HelperService::getMailData($this->config_db);
            $payment_name = Payfee::get_payment_name($o['payment']);

            $mailBody = '<html>';
            $mailBody .= '<head></head>';
            $mailBody .= '<body>';
            $mailBody .= '<div>';
            $mailBody .= Lang::get('訂單取消信消費者');
            $mailBody .= Lang::get('訂單編號') . '：' . $o['order_number'] . '<br>';
            $mailBody .= Lang::get('訂單時間') . '：' . date('Y/m/d H:i', $o['create_time']) . '<br>';
            $mailBody .= Lang::get('訂購商品') . '：' . $res_goods . '<br>';
            $mailBody .= Lang::get('訂單金額') . '：' . $o['total'] . '<br>';
            $mailBody .= Lang::get('收件人') . '：' . $o['transport_location_name'] . '<br>';
            $mailBody .= Lang::get('出貨地址') . '：' . $o['transport_location'] . '<br>';
            $mailBody .= Lang::get('電子信箱') . '：' . $o['transport_email'] . '<br>';
            $mailBody .= Lang::get('行動電話') . '：' . $o['transport_location_phone'] . '<br>';
            $mailBody .= Lang::get('聯絡電話') . '：' . $o['transport_location_tele'] . '<br>';
            $mailBody .= Lang::get('付款方式') . '：' . $payment_name . '<br>';
            $mailBody .= Lang::get('備註') . '：' . $o['transport_location_textarea'] . '<br>';
            $mailBody .= '</div>';
            $mailBody .= '<div>';
            $mailBody .= $globalMailData['system_email']['order_cancel'];
            $mailBody .= '</div>';
            $mailBody .= '<div style="color: red;">';
            $mailBody .= '≡ ' . Lang::get('此信件為系統自動發送，請勿直接回覆') . '(' . $o['id'] . ') ≡';
            $mailBody .= '</div>';
            $mailBody .= '</body>';
            $mailBody .= '</html>';

            HelperService::Mail_Send($mailBody, 'client', $o['transport_email'], Lang::get('訂單取消通知'), $this->config_db);

            /*通知管理者*/
            $mailBody = '<html>';
            $mailBody .= '<head></head>';
            $mailBody .= '<body>';
            $mailBody .= "<div>";
            $mailBody .= Lang::get('訂單取消信管理者');
            $mailBody .= Lang::get('訂單編號') . '：' . $o['order_number'] . '<br>';
            $mailBody .= Lang::get('訂單時間') . '：' . date('Y/m/d H:i', $o['create_time']) . '<br>';
            $mailBody .= Lang::get('訂購商品') . '：' . $res_goods . '<br>';
            $mailBody .= Lang::get('訂單金額') . '：' . $o['total'] . '<br>';
            $mailBody .= Lang::get('收件人') . '：' . $o['transport_location_name'] . '<br>';
            $mailBody .= Lang::get('出貨地址') . '：' . $o['transport_location'] . '<br>';
            $mailBody .= Lang::get('電子信箱') . '：' . $o['transport_email'] . '<br>';
            $mailBody .= Lang::get('行動電話') . '：' . $o['transport_location_phone'] . '<br>';
            $mailBody .= Lang::get('聯絡電話') . '：' . $o['transport_location_tele'] . '<br>';
            $mailBody .= Lang::get('付款方式') . '：' . $payment_name . '<br>';
            $mailBody .= Lang::get('備註') . '：' . $o['transport_location_textarea'] . '<br>';
            $mailBody .= '</div>';
            $mailBody .= '<div style="color: red;">';
            $mailBody .= '≡ ' . Lang::get('此信件為系統自動發送，請勿直接回覆') . '(' . $o['id'] . ') ≡';
            $mailBody .= '</div>';
            $mailBody .= '</body>';
            $mailBody .= '</html>';

            HelperService::Mail_Send($mailBody, 'admin_order_cancel', '', Lang::get('訂單取消通知'), $this->config_db);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 恢復訂單
    public function changeStatus2Restore()
    {
        $this_order = DB::connection('main_db')->table($this->tableName)->find($this->id);
        $this_order = CommonService::objectToArray($this_order);

        /*扣回數量*/
        $return_num = json_decode($this_order['product'], true);

        foreach ($return_num as $v) {/*先檢查還有沒有數量*/
            if (isset($v['type_id'])) {
                $ck = DB::connection(config('A_sub'))->table('productinfo_type')->whereRaw("id = '" . $v['type_id'] . "'")->select('num')->value('num');

                if (intval($ck) - intval($v['num']) < 0) {
                    throw new \RuntimeException('庫存數量不足，此訂單無法恢復');
                }
            }
        }

        foreach ($return_num as $v) {/*扣除數量*/
            if (isset($v['type_id'])) {
                DB::connection('A_sub')->table('productinfo_type')->whereRaw("id = '" . $v['type_id'] . "'")->decrement('num', $v['num']);
            }
        }
        /*扣回數量*/

        /*使用訂單所設優惠*/
        $user_point = DB::connection('main_db')->table('account')->select('point')->find($this_order['user_id'])->point ?? 0;
        $this_order['discount'] = json_decode($this_order['discount'], true);

        if ($this_order['discount']) {
            switch ($this_order['discount'][0]['type']) {
                case Lang::get('使用紅利點數'):
                    if ($user_point <= 0 || $user_point < $this_order['discount'][0]['dis']) {
                        throw new \RuntimeException('會員點數不足');
                    }

                    $PointRecords = new PointRecords($this_order['user_id']);
                    $PointRecords->add_records([
                        'msg'           => '恢復訂單：' . $this_order['order_number'] . '，使用紅利線上購物',
                        'points'        => $this_order['discount'][0]['dis'] * (-1),
                        'belongs_time'  => $this_order['create_time']
                    ]);

                    break;
                case Lang::get('會員優惠券'):
                    $config_db = OrderHelper::get_shop_db_config($this_order['order_number']);
                    $coupon_id = DB::connection($config_db)->table('coupon_pool')->find($this_order['discount'][0]['coupon_pool_id'])['coupon_id'];
                    $coupon_pool_id = DB::connection($config_db)->table('coupon_pool')
                        ->where('coupon_id', $coupon_id)
                        ->whereNotNull('login_time')
                        ->whereNull('use_time')
                        ->where('owner', $this_order['user_id'])
                        ->first();
                    $coupon_pool_id = CommonService::objectToArray($coupon_pool_id);

                    if ($coupon_pool_id) {
                        $coupon_pool_id = $coupon_pool_id['id'];
                        DB::connection($config_db)->table('coupon_pool')->where('id', $coupon_pool_id)->update(['use_time' => time()]);
                        $this_order['discount'][0]['coupon_pool_id'] = $coupon_pool_id;
                        DB::connection('main_db')->table($this->tableName)->where('id', $this->id)->update(['discount' => json_encode($this_order['discount'], JSON_UNESCAPED_UNICODE)]);
                    } else {
                        throw new \RuntimeException('優惠券不足');
                    }

                    break;
                default:
                    break;
            }
        }

        DB::connection('main_db')->table($this->tableName)->where('id', $this->id)->update(['status' => 'New']);
    }

    // 刪除訂單
    public function delete()
    {
        try {
            DB::connection($this->order_db)->table($this->tableName)->delete($this->id);

            return true;
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 返回下單使用的優惠
    public function returndiscount()
    {
        $discount = DB::connection($this->order_db)->table('orderform')->find($this->id);
        $discount = CommonService::objectToArray($discount);
        $discount['discount'] = json_decode($discount['discount'], true);

        if ($discount['discount']) {
            switch ($discount['discount'][0]['type']) {
                case Lang::get('使用紅利點數'):
                    $PointRecords = new PointRecords($discount['user_id']);
                    $PointRecords->add_records([
                        'msg' => Lang::get('返還使用點數') . '(' . Lang::get('取消訂單') . ')：' . $discount['order_number'],
                        'points' => $discount['discount'][0]['dis'],
                        'belongs_time' => $discount['create_time']
                    ]);
                    $PointRecords->set_point_expire(); /*扣除過期點數*/

                    break;
                case Lang::get('會員優惠券'):
                    DB::connection($this->config_db)->table('coupon_pool')->where('id', $discount['discount'][0]['coupon_pool_id'])->update(['use_time' => null]);

                    break;
            }
        }

        return;
    }

    // 依傳入方法派送/收回優惠券
    public function change_coupon($mothod)
    {
        $this_order = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
        $this_order = CommonService::objectToArray($this_order);

        $product = OrderHelper::get_orderform_products([$this_order['id']]);

        $db_coupon_pool = DB::connection($this->config_db);

        $db_coupon_pool->beginTransaction();

        foreach ($product as $key => $value) {
            if (isset($value['key_type'])) {
                if ($value['key_type'] == 'coupon') {
                    $copuon_id = explode('_', $value['type_id'])[0];

                    switch ($mothod) {
                        case 'send': /*贈送*/
                            $change_data = ['login_time' => time()]; /*設定領取時間*/
                            $cuopon_where = " coupon_id=" . $copuon_id . " and owner=" . $this_order['user_id'] . " and use_time is null and login_time is null";

                            break;
                        case 'take_back': /*收回*/
                            $change_data = ['login_time' => null, 'owner' => null];  /*清空領取時間、領取人id*/
                            $cuopon_where = " coupon_id=" . $copuon_id . " and owner=" . $this_order['user_id'] . " and use_time is null and login_time is null";

                            break;
                        default:
                            $db_coupon_pool->rollback();

                            throw new \RuntimeException(Lang::get('優惠券不足'));

                            break;
                    }

                    $coupon_pool_ids = DB::connection($this->config_db)->table($this->coupon_tableName)->whereRaw($cuopon_where)->limit($value['num'])->pluck('id');
                    $coupon_pool_ids = CommonService::objectToArray($coupon_pool_ids);

                    if (count($coupon_pool_ids) < $value['num']) {
                        $db_coupon_pool->rollback();

                        throw new \RuntimeException(Lang::get('優惠券不足'));
                    }

                    $in_id = array_column($coupon_pool_ids, 'id');

                    if ($in_id) {
                        $db_coupon_pool->table($this->coupon_tableName)->whereIn('id', $in_id)->update($change_data);
                    }
                }
            }
        }

        $db_coupon_pool->commit();
    }
}
