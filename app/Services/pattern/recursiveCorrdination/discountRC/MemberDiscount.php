<?php
namespace App\Services\pattern\recursiveCorrdination\discountRC;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Services\pattern\MemberInstance;


class MemberDiscount extends TeamMember {

    public static function createFactory($Proposal) {
        $instance = new self($Proposal);
		return $instance->participate();
	}

    public function participate() {
        $MemberInstance = new MemberInstance(session()->get('user.id'));
        $member = $MemberInstance->get_user_data();

        if(!$member){ // 未登入
            $this->Proposal->projectData['firstBuyDiscount'] = [
                'can_use' => 0,
                'discount' => 0,
                'vip_name' => "",
                'note'  => "",
            ];

            $this->Proposal->projectData['vipDiscount'] = [
                'can_use' => 0,
                'discount' => 0,
                'vip_name' => "",
                'note'  => "",
            ];

            return $this->send2Next();
        }

        // 首購優惠
        $orderform = DB::connection('main_db')->table('orderform')
                    ->whereRaw('status!="Cancel" and status!="Return"')
                    ->whereRaw('user_id="'.$member['id'].'" AND id!="'.$member['from_order'].'"')
                    ->get();
        $orderform = CommonService::objectToArray($orderform);

        $can_use_discount = true;
        $Total = 0;
        $require = $this->Proposal->getRequire();   
        foreach ($require['cartData'] as $key => $value) {
            $Total += (int)($value['countPrice']) * (int)$value['num'];

            if(substr($value['key_type'] , 0, 8)=='askprice'){
                $can_use_discount = false; /*不允許使用優惠*/
            }
        };
        // dump($Total);

        if(!$orderform && $can_use_discount){ // 未有 新或完成的訂單
            $this->Proposal->projectData['firstBuyDiscount'] = $this->get_discount('0', $Total);
        }
        else{ // 有 新或完成 的訂單
            $this->Proposal->projectData['firstBuyDiscount'] = [
                'can_use' => 0,
                'discount' => 0,
                'vip_name' => "",
                'note'  => "",
            ];
        }
        
        // Vip等級優惠優惠
        if($member['vip_id']!=0 && $can_use_discount){ // 等級不是0
            $this->Proposal->projectData['vipDiscount'] = $this->get_discount($member['vip_id'], $Total); 
        }else{
            $this->Proposal->projectData['vipDiscount'] = [
                'can_use' => 0,
                'discount' => 0,
                'vip_name' => "",
                'note'  => "",
            ];
        }
        return $this->send2Next();
    }

    public function get_discount($vip_id, $total){
        $vip_type = DB::connection('main_db')->table('vip_type')->find($vip_id);
        $vip_type = CommonService::objectToArray($vip_type);

        if($vip_type['type']=="0"){ // 打折
            $discount = round( (1 - ((float)$vip_type['discount'])) * $total );
        }else if($vip_type['type']=="1"){ // 扣元
            $discount = $vip_type['discount'] > $total ? $total : $vip_type['discount'];
        }else{
            $discount = 0;
        }

        return [
                'can_use' => 1,
                'discount' => $discount,
                'vip_name'  => $vip_type['vip_name'],
                'note'  => $vip_type['note'],
        ];
    }
}
