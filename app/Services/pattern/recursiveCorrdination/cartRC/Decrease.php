<?php

namespace App\Services\pattern\recursiveCorrdination\cartRC;

 /*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: 
 * @Description: 
 * @depend: 
 *
*/

class Decrease extends TeamMember
{
    public static function createFactory($Proposal) {
        $instance = new self($Proposal);
        $require = $instance->Proposal->getRequire();
        if($require['cmd'] != 'decrease') {
            return $instance->send2Next();
        }
		return $instance->participate();
	}

    public function participate() {
        $require = $this->Proposal->getRequire();
        if($require['num'] <= 0){
            return $this->send2Next();
        }
        $count = $this->GetCount($require['id']);
        $afterCount = $count - $require['num'];
        if($afterCount <= 0){
            unset($this->Proposal->projectData['data'][$require['id']]);
            return $this->send2Next();
        }
        $this->Proposal->projectData['data'][$require['id']] = $afterCount;
        return $this->send2Next();
    }

    private function GetCount($item){
        $projectData = $this->Proposal->projectData['data'];
        $filtered = array_filter($projectData, function ($key) use ($item) {
            return ($key == $item);
        }, ARRAY_FILTER_USE_KEY );
        return $filtered[$item] ?? 0;
    }
}
