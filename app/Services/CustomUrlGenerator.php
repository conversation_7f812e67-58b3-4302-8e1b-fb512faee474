<?php
namespace App\Services;

use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

class CustomUrlGenerator extends UrlGenerator
{
    public function to($path, $extra = [], $secure = null)
    {        
        // 判斷是否為絕對網址
        if(strpos('http://', $path) || strpos('https://', $path)){
            $path = ltrim($path, '/');
        }
        else{ // 相對網址
            $path_query = explode('?', $path);
            $path_list = explode('/', $path_query[0]);
            
            /*統一轉蛇型(逐個單詞轉)*/
            foreach ($path_list as $key => $value) {
                $path_list[$key] = Str::snake($value);
            }
            $path = implode('/', $path_list);
            
            // 處理網址冠上module
            if(count($path_list)>=3){ /*已包含module*/
                $path = ltrim($path, '/');
            }else{
                $module = Route::current()->action['module'] ?? 'index';
                if($module){
                    $path = $module.'/'.ltrim($path, '/');
                }else{
                    $path = ltrim($path, '/');
                }
            }
            if(count($path_query)>1){
                $path .= '?'.implode('?', array_slice($path_query, 1));
            }
        }

        return parent::to($path, $extra, $secure);
    }
}
