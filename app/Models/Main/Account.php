<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;


class Account extends Model
{
    protected $connection= 'main_db';
    protected $table = 'account';
    //protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id','name','user_type','user_type_radio','product_view_id','vip_type',
        'from_order','number','status','total','point','email','gmail','line_id','FB_id',
        'phone','tele','birthday','home','invoice','createtime','ordernum','gender','export',
        'upline_user','recommend_content','share_pic','share_title','share_text','shop_name',
        'file_company','file_person','bank','bank_code','bank_account_name','bank_account_code'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'f_code'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'update_time' => 'datetime',
    ];

    public function viptypes() {
        return $this->hasMany('App\Models\Viptype','id','vip_type');
    }

}
