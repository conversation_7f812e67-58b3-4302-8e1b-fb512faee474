<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;

class Viptype extends Model
{
    protected $connection= 'main_db';
    protected $table = 'vip_type';
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id','type','vip_name','rule','discount','note'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        
    ];

    public function accounts() {
        return $this->belongsTo('App\Models\Account','id');
    }
}
