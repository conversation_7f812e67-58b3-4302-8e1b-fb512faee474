<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Typeinfostr extends Model
{
    protected $table = 'typeinfo_str';
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id','pic','sub_pics','title','content','url','online','parent_id','orders'       
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        
    ];
}
