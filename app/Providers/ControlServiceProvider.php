<?php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\pattern\HelperService;

class ControlServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $excel_function = HelperService::get_excel_function();
        foreach ($excel_function as $key => $value) {
            config(['control.' . $key => $value]);
        }

        $function_result_current = HelperService::get_frontend_user_use_function();
        config(['control.show_list_current' => $function_result_current['show_list']]);
        config(['control.show_list_group_current' => $function_result_current['show_list_group']]);
        config(['control.close_function_current' => $function_result_current['close_function']]);
        config(['control.close_desk_current' => $function_result_current['close_desk']]);
        config(['control.close_desk_admin_current' => $function_result_current['close_desk_admin']]);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
