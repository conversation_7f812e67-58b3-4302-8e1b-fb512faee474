<?php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\CustomUrlGenerator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('url', function ($app) {
            return new CustomUrlGenerator($app['router']->getRoutes(), $app['request']);
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
