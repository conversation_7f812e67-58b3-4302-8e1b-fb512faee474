<?php
namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        //綠界選超商後的跳轉
        'index/cart/cart',
        'index/cart/cart_one',

        //綠界回傳授權結果
        'index/ecreturn/returnurl/*',
        'index/ecreturn/returnurl_atm/*',
        'index/ecreturn/returnurl_atm_payment_info/*',
        //台新回傳授權結果
        'index/tspg/returnurl/*',
        //LinePay回傳授權結果
        'index/iine_pay/returnurl/*',

        //綠界回傳物流狀態
        'ajax/ecpaylogistic/update_status/*',
    ];
}
