<?php

namespace App\Http\Controllers;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class Mailsend extends Mailable
{
    use Queueable, SerializesModels;

    public $subject;
    public $Body;

    public function __construct($subject, $Body)
    {
        $this->subject = $subject;
        $this->Body = $Body;
    }

    public function build()
    {
        return $this->from(config('mail.from.address'), config('mail.from.name'))
            ->subject($this->subject)
            ->with(['body' => $this->Body])
            ->view('emails.base');
    }
}
