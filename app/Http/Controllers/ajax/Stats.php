<?php

namespace App\Http\Controllers\ajax;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

// Photonic Class
use App\Services\CommonService;

class Stats extends Controller
{
    public static function calculate(Request $request) {
        if (empty($request->all()) == false) {
            $date = $request->input('date');
        } else {
            $date = date('Y-m-d', strtotime('-1 day'));
        }

        $rawData = DB::connection('main_db')
        ->table('visitor_logs')
        ->select('product_id', DB::raw('count(*) as amount'))
        ->whereDate('created_at', $date)
        ->groupBy('product_id')
        ->orderBy('product_id')
        ->get();
        $rawData = CommonService::objectToArray($rawData);

        foreach ($rawData as $item) {
            $record_count = DB::connection('main_db')
            ->table('customer_report')
            ->where([
                ['product_id', $item['product_id']],
                ['year', date('Y', strtotime($date))],
                ['month', date('m', strtotime($date))],
                ['day', date('d', strtotime($date))],
            ])
            ->count();

            if ($record_count == 0) {
                DB::connection('main_db')
                ->table('customer_report')
                ->insert([
                    'year' => date('Y', strtotime($date)),
                    'month' => date('m', strtotime($date)),
                    'day' => date('d', strtotime($date)),
                    'product_id' => $item['product_id'],
                    'amount' => $item['amount'],
                ]);

                if ($item['product_id'] == 0) {
                    echo "新增 {$date} 進站的人數為 {$item['amount']} 人<br>";
                } else {
                    echo "新增 {$date} 瀏覽商品編號為 {$item['product_id']} 的人數為 {$item['amount']} 人<br>";
                }
            } else {
                DB::connection('main_db')
                ->table('customer_report')
                ->where([
                    ['product_id', $item['product_id']],
                    ['year', date('Y', strtotime($date))],
                    ['month', date('m', strtotime($date))],
                    ['day', date('d', strtotime($date))],
                ])
                ->update([
                    'amount' => $item['amount'],
                ]);

                if ($item['product_id'] == 0) {
                    echo "更新 {$date} 進站的人數為 {$item['amount']} 人<br>";
                } else {
                    echo "更新 {$date} 瀏覽商品編號為 {$item['product_id']} 的人數為 {$item['amount']} 人<br>";
                }
            }
        }
    }
}
