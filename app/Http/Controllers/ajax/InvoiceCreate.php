<?php

namespace App\Http\Controllers\ajax;

use App\Services\CommonService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Http\Request;
use App\Services\pattern\Invoice;

class InvoiceCreate extends Controller
{
    protected $InvoiceDays;

    public function __construct()
    {
        parent::__construct();

        $invoice_status = config('control.thirdpart_invoice');

        if ($invoice_status != 1) {
            $this->error(Lang::get('發票功能未開啟'));
        }

        $this->InvoiceDays = config('extra.ecpay.invoice_days');
    }

    public function invoice()
    {
        $result_transportComplete = Db::connection('main_db')->table('orderform')
            ->whereNotIn('status', ['Cancel', 'Return'])
            ->where('transport_state', 1)
            ->where('transport_date', '<=', date('Y-m-d', strtotime('-' . $this->InvoiceDays . ' day')))
            ->where(function ($query) {
                $query->whereNull('InvoiceNo')->orWhere('InvoiceNo', '');
            })
            ->where(function ($query) {
                $query->whereNull('InvoiceDate')->orWhere('InvoiceDate', '');
            })
            ->where(function ($query) {
                $query->whereNull('RandomNumber')->orWhere('RandomNumber', '');
            })
            ->get();
        $result_transportComplete = CommonService::objectToArray($result_transportComplete);

        $result_receiptsComplete = Db::connection('main_db')->table('orderform')
            ->where('status', 'New')
            ->where('receipts_state', 1)
            ->where(function ($query) {
                $query->whereNull('InvoiceNo')->orWhere('InvoiceNo', '');
            })
            ->where(function ($query) {
                $query->whereNull('InvoiceDate')->orWhere('InvoiceDate', '');
            })
            ->where(function ($query) {
                $query->whereNull('RandomNumber')->orWhere('RandomNumber', '');
            })
            ->get();
        $result_receiptsComplete = CommonService::objectToArray($result_receiptsComplete);

        $result = array_merge($result_transportComplete, $result_receiptsComplete);

        if (empty($result)) {
            echo '沒有資料';

            exit;
        }

        $result = array_map(function ($item) {
            return json_encode($item, JSON_UNESCAPED_UNICODE);
        }, $result);

        $result = array_unique($result);

        foreach ($result as $key => $arr) {
            $display_arr = [];

            $arr = json_decode($arr, true);

            $id = $arr['id'];
            $display_arr['訂單ID'] = $id;
            $display_arr['訂單編號'] = $arr['order_number'];

            /*開立發票*/
            $invoice_result = Invoice::instance()->create_invoice($id);

            if ($invoice_result['RtnCode'] != 1) {
                $display_arr['發票號碼'] = $invoice_result['RtnMsg'];
            } else {
                $display_arr['發票日期'] = $invoice_result['InvoiceDate'];
                $display_arr['發票號碼'] = $invoice_result['InvoiceNo'];
                $display_arr['發票隨機碼'] = $invoice_result['RandomNumber'];

                /*發送發票通知*/
                if (config('extra.ecpay.incoice_notify') == 1) {
                    $invoice_notify = Invoice::instance()->invoice_notify($id);
                    $display_arr['發票寄送通知'] = $invoice_notify['RtnMsg'];
                }
            }

            // 將display_arr的內容轉成key: value的形式
            $result = '';

            foreach ($display_arr as $key => $value) {
                $result .= $key . ': ' . $value . "<br>";
            }

            echo $result . "<br>";
        }

        echo Lang::get('操作成功');
    }

    public function print()
    {
        $order_id = request()->get('order_id');
        $captcha = request()->get('captcha');

        $singleData = Db::connection('main_db')->table('orderform')->where('id', $order_id)->first();
        $order_number = $singleData->order_number;
        $redirect_url = url('Orderform/orderform_c') . '?' . http_build_query(['id' => $order_number]);

        if (isset($captcha) == false) {
            $this->error(Lang::get('連結有誤'), $redirect_url);
        }

        if ($captcha != hash('sha256', $order_id . '-photonic-' . date('Ymd') . 'ahJPzRUqpYNCxHgKFGA3')) {
            $this->error(Lang::get('連結有誤'), $redirect_url);

            $result = Invoice::instance()->print_invoice($order_id);

            if ($result['RtnCode'] != 1) {
                $this->error($result['RtnMsg'], $redirect_url);
            } else {
                $this->redirect($result['InvoiceHtml']);
            }
        }
    }

    public function create_one(Request $request)
    {
        try {
            $request_data = $request->post();

            if (empty($request_data)) {
                throw new \Exception('請求資料為空');
            }

            if (isset($request_data['id']) == false || empty($request_data['id'])) {
                throw new \Exception('請求資料錯誤');
            }

            $id = $request_data['id'];

            $db_result = Db::connection('main_db')->table('orderform')->select(['status', 'InvoiceNo', 'InvoiceDate', 'RandomNumber'])->find($id);
            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result)) {
                throw new \Exception('訂單不存在');
            }

            if (in_array($db_result['status'], ['Picked', 'Complete']) == false) {
                throw new \Exception('訂單狀態不正確，無法開立發票');
            }

            if (empty($db_result['InvoiceNo']) == false || empty($db_result['InvoiceDate']) == false || empty($db_result['RandomNumber']) == false) {
                throw new \Exception('訂單已開立發票，無法重複開立');
            }

            $invoice_result = Invoice::instance()->create_invoice($id);

            if ($invoice_result['RtnCode'] != 1) {
                throw new \Exception($invoice_result['RtnMsg']);
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success(Lang::get('操作成功'));
    }
}
