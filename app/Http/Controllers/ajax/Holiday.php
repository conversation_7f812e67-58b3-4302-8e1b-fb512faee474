<?php

namespace App\Http\Controllers\ajax;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class Holiday extends Controller
{
    public function __construct()
    {
        parent::__construct();

        DB::table('tcat_exclude_dates')->truncate();
    }

    /**
     * 取得今年的假日資料
     * 資源：https://github.com/ruyut/TaiwanCalendar
     * 
     * @return null
     */
    public function get_holiday()
    {
        echo date('Y-m-d H:i:s') . '開始更新今年之假日資料' . '<br>';

        $holiday = file_get_contents('https://cdn.jsdelivr.net/gh/ruyut/TaiwanCalendar/data/' . date('Y') . '.json');

        if (empty($holiday)) {
            echo '查無資料';

            exit;
        }

        $holiday = json_decode($holiday, true);

        foreach ($holiday as $item) {
            if ($item['isHoliday'] == true) {
                if (empty($item['description']) == false) {
                    $this->set_holiday(date('Y-m-d', strtotime($item['date'])));
                } else {
                    if ($item['week'] == '日') {
                        $this->set_holiday(date('Y-m-d', strtotime($item['date'])));
                    }
                }
            }
        }

        echo date('Y') . '年假日資料已更新';
    }

    /**
     * 設定假日
     *
     * @param string $date 假日日期，格式為 Y-m-d
     * @return null
     */
    public function set_holiday($date)
    {
        if (DB::table('tcat_exclude_dates')->where('date', $date)->count() == 0) {
            DB::table('tcat_exclude_dates')->insert([
                'date' => $date,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
