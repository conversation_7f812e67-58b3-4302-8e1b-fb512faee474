<?php

namespace App\Http\Controllers\ajax;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\CommonService;
use Illuminate\Support\Facades\DB;

class Tcatlogistic extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    public function createTransportPaper(Request $request, $id)
    {
        $html = '';

        try {
            // 建立黑貓宅急便訂單
            $this->expressCreate($id, $request->get('thermosphere', 1));

            // 黑貓宅急便託運單
            $html = $this->getTransportPaper($id);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return $html;
    }

    public function launch_curl($uri, $post_fields = [])
    {
        $basic_info = [
            'CustomerId' => config('extra.tcat.customer_id'),
            'CustomerToken' => config('extra.tcat.customer_token'),
        ];

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => config('extra.tcat.service_url') . $uri,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode(array_merge($basic_info, $post_fields), JSON_UNESCAPED_UNICODE),
        ]);
        $result = curl_exec($curl);
        curl_close($curl);

        return $result;
    }

    public function expressCreate($id, $thermosphere)
    {
        $columns = [
            'order_number',
            'payment',
            'total',
            'transport_location_name',
            'transport_location_phone',
            'transport_location',
            'transport_date',
            'arrival_date',
        ];

        $order_info = Db::connection('main_db')->table('orderform')->select($columns)->find($id);
        $order_info = CommonService::objectToArray($order_info);

        if (empty($order_info)) {
            throw new \Exception('訂單不存在');
        }

        $post_fields = [
            'PrintType' => '01',
            'PrintOBTType' => '01',
            'Orders' => [
                [
                    'OBTNumber' => '',
                    'OrderId' => $order_info['order_number'],
                    'Thermosphere' => str_pad($thermosphere, 4, '0', STR_PAD_LEFT), // 1:常溫, 2:冷藏, 3:冷凍
                    'Spec' => '0001',
                    'ReceiptLocation' => '01',
                    'ReceiptStationNo' => '',
                    'RecipientName' => $order_info['transport_location_name'],
                    'RecipientTel' => $order_info['transport_location_phone'] ?? '',
                    'RecipientMobile' => $order_info['transport_location_phone'] ?? '',
                    'RecipientAddress' => $order_info['transport_location'],
                    'SenderName' => config('extra.shop.sender_name'),
                    'SenderTel' => config('extra.shop.sender_phone'),
                    'SenderMobile' => config('extra.shop.sender_cellphone'),
                    'SenderAddress' => config('extra.shop.sender_address'),
                    'ShipmentDate' => date('Ymd', strtotime($order_info['transport_date'])),
                    'DeliveryDate' => date('Ymd', strtotime($order_info['arrival_date'])),
                    'DeliveryTime' => '04',
                    'IsFreight' => 'N',
                    'IsCollection' => 'N',
                    'CollectionAmount' => 0,
                    'IsSwipe' => 'N',
                    'IsDeclare' => 'N',
                    'DeclareAmount' => 0,
                    'ProductTypeId' => '0001',
                    'ProductName' => config('extra.shop.sender_name'),
                ]
            ],
        ];

        if (intval($order_info['payment']) === 1) {
            $post_fields['Orders'][0]['IsCollection'] = 'Y';
            $post_fields['Orders'][0]['CollectionAmount'] = $order_info['total'];
        }

        $getZipCodeResponse = $this->getZipCode(config('extra.shop.sender_address'));

        if ($getZipCodeResponse['code'] === 0) {
            throw new \Exception($getZipCodeResponse['msg']);
        }

        $post_fields['Orders'][0]['SenderZipCode'] = $getZipCodeResponse['msg'];

        $result = json_decode($this->launch_curl('PrintOBT', $post_fields), true);

        if ($result['IsOK'] !== 'Y') {
            throw new \Exception($result['Message']);
        }

        $prin_date_time = $result['Data']['PrintDateTime'];
        $year = substr($prin_date_time, 0, 4);
        $month = substr($prin_date_time, 4, 2);
        $day = substr($prin_date_time, 6, 2);
        $hour = substr($prin_date_time, 8, 2);
        $minute = substr($prin_date_time, 10, 2);
        $second = substr($prin_date_time, 12, 2);
        $print_date_time = "{$year}-{$month}-{$day} {$hour}:{$minute}:{$second}";

        $update_fields = [
            'tcat_print_datetime' => $print_date_time,
            'tcat_obt_number' => $result['Data']['Orders'][0]['OBTNumber'],
            'tcat_file_no' => $result['Data']['FileNo'],
        ];

        $update_result = Db::connection('main_db')->table('orderform')->where('id', $id)->update($update_fields);

        if ($update_result === false) {
            throw new \Exception('更新訂單資料失敗');
        }
    }

    public function getTransportPaper($id)
    {
        $db_result = Db::connection('main_db')->table('orderform')->select(['tcat_obt_number', 'tcat_file_no', 'order_number'])->find($id);
        $db_result = CommonService::objectToArray($db_result);

        if (empty($db_result)) {
            throw new \Exception('物流單號不存在，請先建立物流訂單');
        }

        $result = $this->launch_curl('DownloadOBT', ['FileNo' => $db_result['tcat_file_no'], 'Orders' => [['OBTNumber' => $db_result['tcat_obt_number']]]]);

        if (isset($result['IsOK']) && $result['IsOK'] !== 'Y') {
            throw new \Exception($result['Message']);
        }

        if (file_exists(ROOT_PATH . 'public/downloads') == false) {
            mkdir(ROOT_PATH . 'public/downloads');
        }

        if (file_exists(ROOT_PATH . 'public/downloads/logistic') == false) {
            mkdir(ROOT_PATH . 'public/downloads/logistic');
        }

        if (file_exists(ROOT_PATH . 'public/downloads/logistic/Tcat') == false) {
            mkdir(ROOT_PATH . 'public/downloads/logistic/Tcat');
        }

        $file_name = ROOT_PATH . 'public/downloads/logistic/Tcat/' . $db_result['order_number'] . '.pdf';

        file_put_contents($file_name, $result);

        if (!file_exists($file_name)) {
            throw new \Exception('物流單檔案建立失敗');
        }

        return '<a id="download_link" target="_blank" href="/downloads/logistic/Tcat/' . $db_result['order_number'] . '.pdf" download></a>
        <button onclick="window.close();">點擊關閉分頁</button>
        <script>document.getElementById("download_link").click();</script>';
    }

    public function getZipCode($address)
    {
        $response = [
            'code' => 1,
            'msg' => '',
        ];

        try {
            if (empty($address)) {
                throw new \Exception('地址不可為空');
            }

            $post_fields = [
                'PostType' => '03',
                'Addresses' => [
                    ['Search' => $address],
                ],
            ];

            $result = json_decode($this->launch_curl('ParsingAddress', $post_fields), true);

            if ($result['IsOK'] !== 'Y') {
                throw new \Exception($result['Message']);
            }

            $response['msg'] = $result['Data']['Addresses'][0]['PostNumber'];
        } catch (\Exception $e) {
            $response['code'] = 0;
            $response['msg'] = $e->getMessage();
        }

        return $response;
    }

    public function obtStatus()
    {
        $db_result = Db::connection('main_db')->table('orderform')->select(['id', 'order_number', 'tcat_obt_number'])
            ->where('status', 'Complete')
            ->where('transport_state', 1)
            ->whereNotNull('tcat_obt_number')
            ->where('transport_date', '<=', date('Y-m-d'))
            ->where(function ($query) {
                $query->where('tcat_latest_status', '<', 301)->orwhere('tcat_latest_status', '>', 301);
            })
            ->whereIn('transport', config('extra.tcat.shippable'))
            ->get();
        $db_result = CommonService::objectToArray($db_result);

        if (empty($db_result)) {
            echo '沒有可查詢的物流訂單';

            exit;
        }

        foreach ($db_result as $order) {
            $post_fields = [
                'OBTNumbers' => [
                    $order['tcat_obt_number']
                ],
            ];

            $result = json_decode($this->launch_curl('OBTStatus', $post_fields), true);

            if ($result['IsOK'] != 'Y') {
                echo $order['order_number'] . '訂單問題：' . $result['Message'] . PHP_EOL;

                continue;
            }

            $update_result = Db::connection('main_db')->table('orderform')
                ->where('id', $order['id'])
                ->update(['tcat_latest_status' => $result['Data']['OBTs'][0]['StatusId']]);

            if ($update_result === false) {
                echo $order['order_number'] . '訂單問題：' . '更新訂單狀態失敗' . PHP_EOL;

                continue;
            }
        }

        echo '物流訂單狀態更新完成：' . implode(',', array_column($db_result, 'order_number'));
    }
}
