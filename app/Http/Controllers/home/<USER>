<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\pattern\OrderHelper;

class Kol extends PublicController {
  private $DBTextConnecter;
  private $resTableName;
  const PER_PAGE_ROWS = 50;

  public function __construct() {
    parent::__construct(request()); // PublicController建構時需傳入Request物件
    $this->DBTextConnecter = DBTextConnecter::withTableName('kol');
    $this->resTableName = 'kol';
  }

  public function pagelogin(Request $request) {
    $email = $request->post('email');
    $password = $request->post('password');
    $rule = [
      'email'  => 'required',
      'password' => 'required'
    ];
    $msg = [
      'email.required' => Lang::get('帳號不得為空'),
      'password.required' => Lang::get('密碼不得為空'),
    ];
    $data = [
      'email'  => $email,
      'password' => $password
    ];
    $validate = Validator::make($data, $rule, $msg);

    if ($validate->fails()) {
      $this->error($validate->errors()->first());
    }
    $where = [
      'email' => $email
    ];
    $kolData = DB::table($this->resTableName)->where($where)->first();
    $kolData = CommonService::objectToArray($kolData);

    if($kolData){
      if($kolData['password'] == $password){
        session()->put('kol', $kolData);
        $this->success(Lang::get('操作成功'), url('Kol/kol_data'));
      }else{
        $this->error(Lang::get('帳號或密碼錯誤'));
      }
    }else{
      $this->error(Lang::get('帳號或密碼錯誤'));
    }
  }

  public function logout(Request $request) {
    session()->forget('kol');
    $this->redirect(url('Index/index'));
  }

  private function check_login(){
    if( empty(session()->get('kol')) ){
      $this->error(Lang::get('請以網紅身分登入'), url('Index/index'));
    }else{
      // dump(session()->get('kol'));
      return true;
    }
  }

  public function kol_data(Request $request){
    $this->check_login();

    $kol = session()->get('kol');
    $this->data['kol'] = $kol;

    $this->data['kol_active'] = 'kol_data';

    return view('home.kol.kol_data',['data'=>$this->data]);
  }

  public function sale_record(Request $request){
    $this->check_login();

    $kol = session()->get('kol');
    $this->data['kol'] = $kol;

    $this->data['kol_active'] = 'sale_record';

    return view('home.kol.sale_record',['data'=>$this->data]);
  }

  public function selling_product(Request $request){
    $this->check_login();

    $kol = session()->get('kol');
    $this->data['kol'] = $kol;

    $this->data['kol_active'] = 'selling_product';
    
    return view('home.kol.selling_product',['data'=>$this->data]);
  }

  public function sale_list(Request $request){
    $this->check_login();

    $kol = session()->get('kol');
    $this->data['kol'] = $kol;

    $kol_id = $kol['id'];

    $type = $request->get('type') ? $request->get('type') : '0'; // 預設未結算
    $this->data['type'] = $type;

    $page = $request->get('page') ? $request->get('page') : 1; // 預設第一頁
    $this->data['page'] = $page;

    // 取得符合搜尋區間及類型的訂單
    $start = $request->get('start');
    $end = $request->get('end');
    $this->data['start'] = $start;
    $this->data['end'] = $end;

    $this->data['kol_active'] = 'sale_list';

    switch ($type) {
      case '0': // 未結算
        $return_data = $this->get_target_order($kol_id, $type, $start, $end, $page); // 找出符合條件的訂單(依頁數)
        // 隱藏訂單資料
        $return_data['orderform'] = $this->hide_info($return_data['orderform']);
        $this->data['orderform'] = $return_data['orderform'];
        $return_data_all = $this->get_target_order($kol_id, $type, $start, $end); // 找出符合條件的訂單(全部)
        $this->data['totalpage'] = ceil(count($return_data_all['orderform'])/self::PER_PAGE_ROWS);
        return view('home.kol.sale_list',['data'=>$this->data]);
        break;

      case '1' || '2': // 結算中or已結算
        $periods = $this->get_target_periods($kol_id, $type, $start, $end, $page); // 找出符合條件的對帳單(依頁數)
        foreach ($periods as $key => $value) { /*各對帳單計算金額*/
           // 找出該對帳單的對應訂單
          $return_data = $this->get_target_order($kol_id, $type, $value['period_start'], $value['period_end']);
          $total = $this->count_orderform_total($kol_id, $return_data['orderform']);
          $periods[$key]['total'] = $total;
          $periods[$key]['confrim_content'] = json_decode($value['confrim_content'], true);
        }
        $this->data['period_sale'] = $periods;
        $periods_all = $this->get_target_periods($kol_id, $type, $start, $end); // 找出符合條件的訂單(全部)
        $this->data['totalpage'] = ceil(count($periods_all)/self::PER_PAGE_ROWS);

        return view('home.kol.sale_preriod',['data'=>$this->data]);
        break;
    }
  }
  public function sale_detail(Request $request){
    $this->check_login();

    $kol = session()->get('kol');
    $this->data['kol'] = $kol;

    $kol_id = $kol['id'];

    // 所選期數
    if ($request->get('period')) {
      $period = $request->get('period');
    } else {
      $this->error('請選擇期數');
    }
    $this->data['period'] = $period;

    $kol_period_term = DB::table('kol_period_term')->whereRaw('id ='.$period)->first();
    $kol_period_term = CommonService::objectToArray($kol_period_term);
    $type = $kol_period_term['confirm_date'] ? 2 : 1;
    $return_data = self::get_target_order($kol_id, $type, $kol_period_term['period_start'], $kol_period_term['period_end']);
    $return_data['orderform'] = $this->hide_info($return_data['orderform']);
    $this->data['orderform'] = $return_data['orderform'];

    $kol_period_term['total'] = self::count_orderform_total($kol_id, $return_data['orderform']);
    $kol_period_term['confrim_content'] = json_decode($kol_period_term['confrim_content'], true);
    $this->data['kol_period_term'] = $kol_period_term;

    $this->data['kol_active'] = 'sale_list';
    
    return view('home.kol.sale_detail',['data'=>$this->data]);
  }

  /*取得符合搜尋結果的訂單*/
  public static function get_target_order($kol_id, $type, $start=false, $end=false, $page=false){
    $orderform = [];

    $target_periods = self::get_target_periods($kol_id, $type, $start, $end, $page);
    if( in_array($type, ['0', '1']) ){ //未結算 or 結算中
      $offset='';
      $limit='';
      $order_where_query = [];
      foreach ($target_periods as $value) {
        array_push($order_where_query, '(o.create_time>='.strtotime($value['period_start']).' AND o.create_time<'.strtotime($value['period_end'].' +1Day').')');
      }
      if(count($order_where_query)>0){
        $order_where_query = '('.implode(' OR ', $order_where_query).')';
      }else{
        $order_where_query = '1=0';
      }
      // dump($order_where_query);exit;
      
      if($type=='0'){ //未結算 or 結算中
        if($page){ // 處理page條件(limit)
          $offset = ($page-1)*self::PER_PAGE_ROWS;
          $limit = self::PER_PAGE_ROWS;
        }
        if($start){ $order_where_query .=' AND o.create_time >= '.strtotime($start); }
        if($end){ $order_where_query .=' AND o.create_time < '.strtotime($end.' +1Day'); }
      }

      // 選出有該網紅推廣商品的訂單
      $orderform = DB::connection('main_db')->table('orderform as o')
                                            ->select('o.order_number', 'o.create_time', 'o.transport_location', 'o.product', 'a.name as user_name', 'o.id')
                                            ->leftJoin('account as a', 'a.id', '=', 'o.user_id')
                                            ->whereRaw('o.status ="Complete" AND o.product like "%_kol'.$kol_id.'\"%"') /*完成訂單 且 有買該網紅的商品*/
                                            ->whereRaw($order_where_query) /*符合時間條件*/
                                            ->orderByRaw('o.id desc');
      // $type==0時才會用到
      if ($type=='0') {
        if ($limit) {
          $orderform = $orderform->limit($limit);
        }
        if ($offset) {
          $orderform = $orderform->offset($offset);
        }
      }
      $orderform = $orderform->get();
      $orderform = CommonService::objectToArray($orderform);

    }else if($type=='2'){ // 已結算(顯示核可當下的紀錄)
      foreach ($target_periods as $key => $value) {
        $confrim_content = json_decode($value['confrim_content'], true);
        if ($confrim_content) {
          $orderform = array_merge($orderform, $confrim_content);
        }
      }
    }

    array_walk($orderform, function(&$item, $key) {
      if (gettype($item['product']) == 'string') {
        $item['product'] = json_decode($item['product'], true);
      }
    });

    $return_data = [
      'orderform' => $orderform   // 回傳目標訂單
    ];
    return $return_data;
  }
  /*取得符合搜尋結果的對帳單*/
  public static function get_target_periods($kol_id, $type, $start=false, $end=false, $page=false){
    $target_periods = [];

    $current_time_stamp = time();
    $current_time = date('Y-m-d', $current_time_stamp);
    // 找出正在進行或已結束的期數
    $kol_period_term = DB::table('kol_period_term as kpt')->select('kpt.*','kp.count_days')
                          ->rightJoin('kol_period as kp', 'kp.id', '=', 'kpt.kol_period_id')
                          ->where('kp.kol_id', $kol_id)
                          ->whereRaw('
                            (kpt.period_start <= "'.$current_time.'" AND "'.$current_time.'" <= kpt.period_end) OR
                            kpt.period_end <= "'.$current_time.'"
                          ');
    // dump($kol_period_term);
    switch ($type) {
      case '0': // 未結算 (最近兩期)
        $kol_period_term = $kol_period_term->orderByRaw('kpt.period_start desc')->get();
        $kol_period_term = CommonService::objectToArray($kol_period_term);
        foreach ($kol_period_term as $key => $value) {
          if(count($target_periods)==2){ break; }
          if(strtotime($value['period_end'].' +'.(2*$value['count_days']).'Days')>$current_time_stamp){ /*結束未超過兩期*/
            array_push($target_periods, $value);
          }
        }
        break;

      case '1' || '2': // 結算中or已結算(最近兩期之外)
        $current_two = self::get_target_periods($kol_id, 0);
        if(count($current_two)){
          $current_two_ids = [];
          foreach ($current_two as $key => $value) {
            array_push($current_two_ids, $value['id']);
          }
          $not_in_current_two_wehere = 'kpt.id not in ('.implode(',', $current_two_ids).')';
          $kol_period_term = $kol_period_term->whereRaw($not_in_current_two_wehere);
        }

        if($start){ $kol_period_term = $kol_period_term->whereRaw('kpt.period_start >= "'.$start.'"'); }
        if($end){ $kol_period_term = $kol_period_term->whereRaw('kpt.period_start <= "'.$end.'"'); }

        if($type=='1'){ // 未結算
          $kol_period_term = $kol_period_term->whereRaw('confirm_date is null');
        }else{
          $kol_period_term = $kol_period_term->whereRaw('confirm_date is not null');
        }

        if($page){
          $kol_period_term = $kol_period_term->offset(($page-1)*self::PER_PAGE_ROWS)->limit(self::PER_PAGE_ROWS);
        }

        if($type=='1'){ // 未結算
          $kol_period_term = $kol_period_term->orderByRaw('kpt.period_start asc');
        }else{
          $kol_period_term = $kol_period_term->orderByRaw('kpt.period_start desc');
        }

        $kol_period_term = $kol_period_term->get();

        $target_periods = CommonService::objectToArray($kol_period_term);
        break;
    }

    return $target_periods;
  }
  /*依給入網紅及訂單計算總金額*/
  public static function count_orderform_total($kol_id, $orderform){
    $total = 0;
    foreach ($orderform as $key => $value) { // 各訂單計算金額
      $product = OrderHelper::get_orderform_products([$value['id']]);
      foreach ($product as $k_p => $v_p) { // 檢查每一個商品
        if(!isset($v_p['key_type'])) continue;
        if($v_p['key_type']=='kol'.$kol_id){ // 該商品是此網紅推廣的
          $total += $v_p['total'];                   
        }
      }
    }
    return $total;
  }

  /*AJAX:取得合作期間*/
  public function get_sale_period(Request $request){
    if(session()->get('kol')){ // 有用網紅登入就用網紅看
      $kol = session()->get('kol');
      $kol_id = $kol['id'];
    }else if(session()->get('admin')){ // 用admin登入則依查詢值看
      if ($request->post('kol_id')) {
        $kol_id = $request->post('kol_id');
      }else{
        $this->error(Lang::get('資料不完整'));
      }
    }else{ // 都沒有就不允許使用
      $this->error(Lang::get('資料不完整'));
    }

    $sale_period = DB::table('kol_period')->whereRaw('kol_id='.$kol_id)->orderByRaw('date_start desc')->get();
    return CommonService::objectToArray($sale_period);
  }
  /*AJAX:取得過往所有銷售記錄*/
  public function get_sale_record(Request $request){
    if(session()->get('kol')){ // 有用網紅登入就用網紅看
      $kol = session()->get('kol');
      $kol_id = $kol['id'];
    }else if(session()->get('admin')){ // 用admin登入則依查詢值看
      if($request->post('kol_id')){
        $kol_id = $request->post('kol_id');
      }else{
        $this->error(Lang::get('資料不完整'));
      }
    }else{ // 都沒有就不允許使用
      $this->error(Lang::get('資料不完整'));
    }

    if($request->post('type')){ // 未結算
      $type = $request->post('type');
    }else{
      $this->error(Lang::get('資料不完整'));
    }

    // 不設時間，取得所有已結算的訂單
    $return_data = $this->get_target_order($kol_id, $type);
    // dump($return_data);exit;

    // 依產品+品項整理
    $product_list = [];
    foreach ($return_data['orderform'] as $key => $value) { // 處理每一筆訂單
      $product = OrderHelper::get_orderform_products([$value['id']]);
      foreach ($product as $k_p => $v_p) { // 檢查每一個商品
        if(!isset($v_p['key_type'])) continue;
        if($v_p['key_type']=='kol'.$kol_id){ // 該商品是此網紅推廣的
          if( !isset($product_list[$v_p['name']]) ) $product_list[$v_p['name']] = ['num'=>0,'total'=>0, 'product'=>[]];
          $product_list[$v_p['name']]['num'] += $v_p['num'];                    
          $product_list[$v_p['name']]['total'] += $v_p['total'];                    
          $product_list[$v_p['name']]['product'] = $v_p;                    
        }
      }
    }

    // 找出開賣日期
    foreach ($product_list as $key => $value) {
      // 找此網紅對此商品最早的設定記錄
      if(isset($value['product']['info_id'])){
        $kol_productinfo = DB::table('kol_productinfo')->whereRaw('kol_id='.$kol_id.' AND productinfo_id='.$value['product']['info_id'])->orderByRaw('id asc')->first();
        $kol_productinfo = CommonService::objectToArray($kol_productinfo);
        if($kol_productinfo){
          $product_list[$key]['s_time'] = date('Y-m-d', $kol_productinfo['time']);
        }else{
          $product_list[$key]['s_time'] = '';
        }
      }else{
        $product_list[$key]['s_time'] = '';
      }
    }

    return $product_list;
  }
  /*AJAX:取得所有代銷中的商品*/
  public function get_selling_product(Request $request){
    if(session()->get('kol')){ // 有用網紅登入就用網紅看
      $kol = session()->get('kol');
      $kol_id = $kol['id'];
    }else if(session()->get('admin')){ // 用admin登入則依查詢值看
      if($request->get('kol_id')){
        $kol_id = $request->get('kol_id');
      }else{
        $this->error(Lang::get('資料不完整'));
      }
    }else{ // 都沒有就不允許使用
      $this->error(Lang::get('資料不完整'));
    }
    $where_query = 'kp.kol_id ='.$kol_id;

    $is_using = $_GET['is_using'] ?? '1'; /*沒傳is_using則預設搜1*/
     $is_using =  $is_using!='' ? $is_using : '1'; /*is_using為空則預設搜1*/
    if($is_using!='-1'){ $where_query .=' AND kp.is_using='.$is_using; }

    // dump($where_query);exit;
    $productinfo = DB::table('productinfo as p')
                      ->select('p.id', 'p.title', 'p.product_id', 'p.pic', 
                                'kp.kol_id', 'kp.time', 'kp.time_e')
                      ->leftJoin('kol_productinfo as kp', 'kp.productinfo_id', '=', 'p.id')
                      ->whereRaw($where_query)
                      ->orderByRaw('kp.id desc')
                      ->get();
    $productinfo = CommonService::objectToArray($productinfo);
    array_walk($productinfo,  function($item, $key) use(&$productinfo){
      $productinfo[$key]['pic'] = request()->server('REQUEST_SCHEME').'://'.request()->server('HTTP_HOST').'/public/static/index/'.json_decode($item['pic'],true)[0];
      $productinfo[$key]['time_format'] = date('Y-m-d', $item['time']);
      $productinfo[$key]['time_e_format'] = $item['time_e'] ? date('Y-m-d', $item['time_e']) : '';
    });
    return $productinfo;
  }

  // 訂單加密
  private function hide_info($orderform){
    foreach ($orderform	 as $key => $value) {
      $orderform[$key]['order_number'] = parent::hidestr($value['order_number'], -6);
      $orderform[$key]['user_name'] = $value['user_name'] ? parent::hidestr($value['user_name'],1,-1) : '';
      $orderform[$key]['transport_location'] = parent::hidestr($value['transport_location'],6);
    }
    return $orderform;
  }
}

