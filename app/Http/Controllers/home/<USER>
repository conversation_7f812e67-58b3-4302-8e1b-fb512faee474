<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\pattern\HelperService;
use App\Services\pattern\AskpriceHelper;
use App\Services\pattern\MemberInstance;
use App\Services\DBtool\DBTextConnecter;
use App\Services\ReCaptcha\ReCaptcha;

class Askprice extends PublicController{
  private $resTableName;
  private $resTableName_record;
  private $DBTextConnecter;
  private $DBTextConnecter_record;

  public function __construct(){
    parent::__construct(request());
    if(isset(config('control.close_function_current')['詢價回函'])){
      $this->error(Lang::get('查無此頁'));
    }
    if($this->user["id"]==0){ 
      $this->error(Lang::get('請先登入會員'), url('Login/login'));
    }

    $this->resTableName = 'askprice';
    $this->resTableName_record = 'askprice_record';
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->resTableName);
    $this->DBTextConnecter_record = DBTextConnecter::withTableName($this->resTableName_record);
  }

  public function doContact(Request $request){
    $user_id = $this->user["id"];
    $product_id = $request->post('product_id') ?? '';
    $product_name = $request->post('product_name') ?? '';
    $product_type_id = $request->post('product_type_id') ?? '';
    $product_type_name = $request->post('product_type_name') ?? '';
    $price =  $request->post('price') ?? '';
    $num = $request->post('num') ?? '';
    $ask = $request->post('ask') ?? '';

    if(!$user_id){ $this->error(Lang::get('請先登入會員')); }
    $MemberInstance = new MemberInstance($user_id);
    $user = $MemberInstance->get_user_data();
    if(!$user){ $this->error(Lang::get('請先登入會員')); }

    if($product_id==""){ $this->error(Lang::get('請選擇詢問商品')); }
    if($product_type_id==""){ $this->error(Lang::get('請選擇詢問品項')); }

    $productinfo = DB::table('productinfo')->find($product_id);
    if(!$productinfo){ $this->error(Lang::get('資料有誤')); }
    $productinfo_type = DB::table('productinfo_type')->find($product_type_id);
    if(!$productinfo_type){ $this->error(Lang::get('資料有誤')); }

    if($price==""){ $this->error(Lang::get('請輸入金額')); }
    if($num==""){ $this->error(Lang::get('請輸入數量')); }

    /*處理distributor_id*/
    if(config('control.control_platform')==1){
      $distributor_id = $productinfo->distributor_id;
    }else{
      $distributor_id = 0;
    }

    if (config('extra.shop.google_recaptcha_sitekey')) {
      $google_recaptcha_seckey = config('extra.shop.google_recaptcha_seckey');
      if($google_recaptcha_seckey){
        $recaptcha = new ReCaptcha($google_recaptcha_seckey);
        $gRecaptchaResponse = $request->post('recaptcha');
        $remoteIp = $request->server('REMOTE_ADDR');
        $resp = $recaptcha->verify($gRecaptchaResponse, $remoteIp);
        if(!$resp->isSuccess()){
          $this->error(Lang::get('請先證明您不是機器人'));
        }
      }
    }
    try{
      $show_phone = $user['phone'] ? $user['phone'] : $user['tele'];
      $newData = [
        'distributor_id' => $distributor_id,
        'user_id' => $user_id,
        'name' => $user['name'],
        'phone' => $show_phone,
        'email' => $user['email'],
        'product_id' => $product_id,
        'product_name' => $product_name,
        'product_type_id' => $product_type_id,
        'product_type_name' => $product_type_name,
        'create_time' => time(),
      ];
      // dump($num);exit;
      $this->DBTextConnecter->setDataArray($newData);
      $new_id = $this->DBTextConnecter->createTextRow();

      $newData_record = [
        'askprice_id' => $new_id,
        'ask' => $ask,
        'ask_time' => time(),
        'price' => $price,
        'num' => $num,
      ];
      // dd($newData_record);
      $this->DBTextConnecter_record->setDataArray($newData_record);
      $new_id_record = $this->DBTextConnecter_record->createTextRow();

      $update_data = [
        'id' => $new_id,
        'askprice_record_id' => $new_id_record,
      ];
      $this->DBTextConnecter->setDataArray($update_data);
      $this->DBTextConnecter->upTextRow();
    } catch (\Exception $e){
      $this->error(Lang::get('發生錯誤'));
    }

    $show_product_name = $product_name;
    $show_product_name .= $product_type_name ? ('-'.$product_type_name) : '';

    $globalMailData = HelperService::getMailData();
    $askprice_letter = Lang::get('menu.詢價成功信消費者');
    $askprice_letter = str_replace("{name}", $user['name'], $askprice_letter);
    $askprice_letter = str_replace("{show_product_name}", $show_product_name, $askprice_letter);
    $askprice_letter = str_replace("{num}", $num, $askprice_letter);
    $askprice_letter = str_replace("{price}", $price, $askprice_letter);
    $askprice_letter = str_replace("{ask}", str_replace("\n", '<br>', $ask), $askprice_letter);
    $mailBody = "
    <html>
      <head></head>
      <body>
        <div>
          ".$askprice_letter."
        </div>
        <div>
          ". $globalMailData['system_email']['contact_complete'] ."
        </div>
        <div style='color:red;'>
          ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
        </div>
      </body>
    </html>
    ";
    $mail_return = HelperService::Mail_Send($mailBody, 'client', $user['email'], Lang::get('詢價建立成功'));

    $admin_type = $distributor_id == 0 ? 'admin' : 'distribution';
    $url = 'http://'.$request->server('HTTP_HOST').url($admin_type.'/Askprice/index');

    $askprice_letter_admin = Lang::get('menu.詢價成功信管理者');
    $askprice_letter_admin = str_replace("{name}", $user['name'], $askprice_letter_admin);
    $askprice_letter_admin = str_replace("{show_phone}", $show_phone, $askprice_letter_admin);
    $askprice_letter_admin = str_replace("{email}", $user['email'], $askprice_letter_admin);
    $askprice_letter_admin = str_replace("{show_product_name}", $show_product_name, $askprice_letter_admin);
    $askprice_letter_admin = str_replace("{num}", $num, $askprice_letter_admin);
    $askprice_letter_admin = str_replace("{price}", $price, $askprice_letter_admin);
    $askprice_letter_admin = str_replace("{ask}", str_replace("\n", '<br>', $ask), $askprice_letter_admin);
    $askprice_letter_admin = str_replace("{url}", $url, $askprice_letter_admin);
    $mailBody = "
    <html>
      <head></head>
      <body>
      <div>
        ".$askprice_letter_admin."
      </div>
      <div style='color:red;'>
        ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
      </div>
      </body>
    </html>
    ";
    if($distributor_id==0){
      $mail_return = HelperService::Mail_Send($mailBody, 'admin_askprice_new', '', Lang::get('新詢價回函提醒'));
    }else{
      $MemberInstance->change_user_id($distributor_id);
      $user_data = $MemberInstance->get_user_data_distributor();
      // dd($user_data);
      if($user_data){
        $mail_return = HelperService::Mail_Send($mailBody, 'client', $user_data['email'], Lang::get('新詢價回函提醒'));
      }
    }

    $this->success(Lang::get('操作成功'), url('Product/productinfo').'?id='.$product_id);
  }
  public function do_ask_again(Request $request){
    $user_id = $this->user['id'];
    $contact_id =  $request->post('contact_id') ?? '';
    $price =  $request->post('price') ?? '';
    $num =  $request->post('num') ?? '1';
    $ask = $request->post('ask') ?? '';

    if(!$user_id){ $this->error(Lang::get('請先登入會員')); }
    $MemberInstance = new MemberInstance($user_id);
    $user = $MemberInstance->get_user_data();
    if(!$user){ $this->error(Lang::get('請先登入會員')); }

    $AskpriceHelper = new AskpriceHelper();
    $result = $AskpriceHelper->getOne('a.user_id ="'.$user_id.'"');
    $main = $result['main'];
    if(!$main){ $this->error(Lang::get('資料有誤')); }
    if($main['user_id'] != $user_id){ $this->error(Lang::get('資料有誤')); }
    $main_record = $result['current'];
    if(!$main_record){ $this->error(Lang::get('資料有誤')); }
    if($main_record['status'] != 1){ $this->error(Lang::get('此詢價尚未被回覆')); }

    if($price==""){ $this->error(Lang::get('請輸入金額')); }

    $newData_record = [
      'askprice_id' => $contact_id,
      'ask' => $ask,
      'ask_time' => time(),
      'price' => $price,
      'num' => $num,
    ];
    $this->DBTextConnecter_record->setDataArray($newData_record);
    $new_id_record = $this->DBTextConnecter_record->createTextRow();

    $update_data = [
      'id' => $contact_id,
      'askprice_record_id' => $new_id_record,
    ];
    $this->DBTextConnecter->setDataArray($update_data);
    $this->DBTextConnecter->upTextRow();

    $show_product_name = $main['product_name'];
    $show_product_name .= $main['product_type_name'] ? ('-'.$main['product_type_name']) : '';

    $globalMailData = HelperService::getMailData();
    $askprice_again_letter = Lang::get('menu.再次詢價成功信消費者');
    $askprice_again_letter = str_replace("{name}", $user['name'], $askprice_again_letter);
    $askprice_again_letter = str_replace("{show_product_name}", $show_product_name, $askprice_again_letter);
    $askprice_again_letter = str_replace("{num}", $num, $askprice_again_letter);
    $askprice_again_letter = str_replace("{price}", $price, $askprice_again_letter);
    $askprice_again_letter = str_replace("{ask}", str_replace("\n", '<br>', $ask), $askprice_again_letter);
    $mailBody = "
    <html>
      <head></head>
      <body>
        <div>
          ".$askprice_again_letter."
        </div>
        <center></center>
        <div>
          ". $globalMailData['system_email']['contact_complete'] ."
        </div>
        <div style='color:red;'>
          ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
        </div>
      </body>
    </html>
    ";
    $mail_return = HelperService::Mail_Send($mailBody, 'client', $user['email'], Lang::get('再次詢價成功'));

    $admin_type = $result['main']['distributor_id'] == 0 ? 'admin' : 'distribution';
    $url = 'http://'.$request->server('HTTP_HOST').url($admin_type.'/Askprice/index');
    $askprice_again_letter_admin = Lang::get('menu.再次詢價成功信管理者');
    $askprice_again_letter_admin = str_replace("{name}", $main['name'], $askprice_again_letter_admin);
    $askprice_again_letter_admin = str_replace("{show_phone}", $main['phone'], $askprice_again_letter_admin);
    $askprice_again_letter_admin = str_replace("{email}", $main['email'], $askprice_again_letter_admin);
    $askprice_again_letter_admin = str_replace("{show_product_name}", $show_product_name, $askprice_again_letter_admin);
    $askprice_again_letter_admin = str_replace("{num}", $num, $askprice_again_letter_admin);
    $askprice_again_letter_admin = str_replace("{price}", $price, $askprice_again_letter_admin);
    $askprice_again_letter_admin = str_replace("{ask}", str_replace("\n", '<br>', $ask), $askprice_again_letter_admin);
    $askprice_again_letter_admin = str_replace("{url}", $url, $askprice_again_letter_admin);

    $mailBody = "
    <html>
      <head></head>
      <body>
        <div>
          ".$askprice_again_letter_admin."
        </div>
        <div style='color:red;'>
          ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
        </div>
      </body>
    </html>
    ";
    if($main['distributor_id']==0){
      $mail_return = HelperService::Mail_Send($mailBody, 'admin_askprice_again', '', Lang::get('再次詢價回函提醒'));
    }else{
      $MemberInstance->change_user_id($main['distributor_id']);
      $user_data = $MemberInstance->get_user_data_distributor();
      // dump($user_data);exit;
      if($user_data){
        $mail_return = HelperService::Mail_Send($mailBody, 'client', $user_data['email'], Lang::get('再次詢價回函提醒'));
      }
    }

    session()->forget('cart');
    $this->success(Lang::get('操作成功'));
  }

  public function askprice(Request $request){
    $this->data['mebermenu_active'] = 'askprice';
    return view('home.askprice.askprice',['data'=> $this->data]);
  }
  public function getList(Request $request){
    $AskpriceHelper = new AskpriceHelper();
    $result = $AskpriceHelper->getList('a.user_id ="'.$this->user['id'].'"', true);
    return $result;
  }
  public function getOne(Request $request){
    $AskpriceHelper = new AskpriceHelper();
    $result = $AskpriceHelper->getOne('a.user_id ="'.$this->user['id'].'"');
    return $result;
  }
}