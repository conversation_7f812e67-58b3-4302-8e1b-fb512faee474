<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Services\pattern\ProductHelpler;

class IndexController extends PublicController
{
    protected $index_excel;

    public function index(){
        if( !empty(config('control.close_function_current')['首頁編輯']) ){
            $this->redirect(url('product/search'));
        }

        $this->data['notification_pubkey'] = config('extra.notification.PUBKEY');

        $tag = DB::table('frontend_data_name')->where('show_type', "tag")->orderBy('id','asc')->get();
        $this->data['tag'] = CommonService::objectToArray($tag);

        $product = DB::table('product')
                    ->select('id', 'title','pic_icon',
                        'index_adv01_pic', 'index_adv01_link',
                        'index_adv02_pic', 'index_adv02_link',
                        'index_adv03_pic', 'index_adv03_link',
                        'index_adv04_pic', 'index_adv04_link',
                        'index_adv05_pic', 'index_adv05_link',
                        'index_adv06_pic', 'index_adv06_link',
                        'index_adv07_pic', 'index_adv07_link')
                    ->where(['ad_online' => 1,'online' => 1]) /*有啟用廣告區塊 也有顯示分館*/
                    ->orderByRaw('order_id')->get();
        foreach ($product as $key => $value) {
            // 顯示分館的子分類
            $typeinfo = DB::table('typeinfo')
                        ->select('typeinfo.title', 'typeinfo.id', 'typeinfo.pic')
                        ->whereRaw('typeinfo.parent_id = ' . $value->id . "
                            AND typeinfo.online = 1
                            AND (
                                typeinfo.end <= 0
                                OR
                                ((typeinfo.start < " . time() . ") AND (typeinfo.end > " . time() . "))
                            )")
                        ->orderByRaw('typeinfo.order_id')->get();
            $product[$key]->typeinfo = CommonService::objectToArray($typeinfo);
            if(count($product[$key]->typeinfo) == 0){
                $product[$key]->typeinfo = [
                    [
                        'id' => 0,
                        'title' => Lang::get('查無此頁'),
                        'pic' => ''
                    ]
                ];
            }
        }
        //var_dump($product);
        $this->data['product'] = $product;
        //dump($product);

        $slideshow = DB::table('slideshow')->select('title', 'pic', 'link')->where('online', 1)->orderBy('id')->get();
        $this->data['slideshow'] = CommonService::objectToArray($slideshow);

        $index_ad = DB::table('index_ad')->where('online', 1)->orderBy('orders')->get();
        $this->data['index_ad'] = CommonService::objectToArray($index_ad);

        $expiring_product = $this->productInfo('expiring_product');
        $this->data['expiring_product'] = $expiring_product;

        $hot_product = $this->productInfo('hot_product');
        $this->data['hot_product'] = $hot_product;

        $recommend_product = $this->productInfo('recommend_product');
        $this->data['recommend_product'] = $recommend_product;

        $spe_price_product = $this->productInfo('spe_price_product', 'orders asc, table.id desc');
        $this->data['spe_price_product'] = $spe_price_product;

        $edmID = DB::table('index_excel')->select('data3')->find(37)->data3;
        $this->data['edmID'] = $edmID;
        
        /* 限時搶購 */
        $timeRange = DB::table('index_excel')->select('data2', 'data3')->find(32);
        $time = time();
        $this->data['time_range'] = 1;
        if($timeRange->data2 <= $time && ($time <= $timeRange->data3 + 86400 || 0 >= $timeRange->data3 + 86400)){
            $time_product = $this->productInfo('time_product');
            $this->data['startTime'] = date('Y-m-d H:i', $timeRange->data2);
            $this->data['endTime'] = date('Y-m-d H:i', $timeRange->data3);
            $this->data['time_product'] = $time_product;
            $this->data['time_block'] = 1;

            if($timeRange->data3 > 0){
                $reciprocal = $timeRange->data3 + 86400 - $time;
                $reciprocalTime['d'] = floor($reciprocal / (24 * 60 * 60));
                $reciprocalTime['hr'] = floor($reciprocal % (24 * 60 * 60) / (60 * 60));
                $reciprocalTime['mn'] = floor(($reciprocal % (60 * 60)) / 60);
                $reciprocalTime['sc'] = $reciprocal % 60;

                $this->data['reciprocalTime'] = $reciprocalTime;
            }else{
                $this->data['time_range'] = 0;
            }
        }else{
            $this->data['time_block'] =  0;
        }

        /* 圖文廣告(大圖說明+2商品) */
        $this->index_excel[15]['product'] = self::set_index_recomand_product($this->index_excel[15]['data3']);
        $this->index_excel[16]['product'] = self::set_index_recomand_product($this->index_excel[16]['data3']);
        //dump($this->index_excel);
        $this->data['index_excel'] = $this->index_excel;

        return view('home.index.index', ['data'=>$this->data]);
    }

    private function set_index_recomand_product($productinfo_id){
        $productinfo_data = DB::table('productinfo')->select('title', 'content', 'id','pic', 'has_price')
                                        ->where('online', 1)
                                        ->find($productinfo_id);
        $productinfo_data = CommonService::objectToArray($productinfo_data);
        if(empty($productinfo_data)){ return null; }
        if ($productinfo_data['has_price'] == 1){
            $price = $price = ProductHelpler::get_price($productinfo_data['id']);
            if(!empty($price)){
                $productinfo_data['subtitle'] = $price[0]['title'];
                $productinfo_data['has_price'] = config('extra.shop.dollar').config('extra.shop.dollar_symbol').'<span class="price">'.number_format($price[0]['count']).'</span>';
            }else{
                $productinfo_data['subtitle'] = '';
                $productinfo_data['has_price'] = '<span class="price">'.Lang::get('請詢價').'</span>';
            }
        }else{
            $productinfo_data['subtitle'] = '';
            $productinfo_data['has_price'] = '<span class="price">'.Lang::get('請詢價').'</span>';
        }
        $productinfo_data['pic'] = json_decode($productinfo_data['pic'],true)[0];
        return $productinfo_data;
    }

    private function productInfo($tableName, $order = '') {			
        $productInfo = DB::table($tableName.' as table')
                ->select('productinfo.title', 'productinfo.id',
                    'productinfo.pic', 'productinfo.has_price', 'productinfo.is_registrable')
                ->where('productinfo.online',1)
                ->where('table.product_id', '<>', 0)
                ->join('productinfo','productinfo.id','=','table.product_id');
        if($tableName == 'spe_price_product'){
            $productInfo = $productInfo->orderbyRAW($order);
        }else{
            $productInfo = $productInfo->orderBy('table.id','asc');
        }
        $productInfo = $productInfo->get();
        $productInfo = CommonService::objectToArray($productInfo);

        foreach ($productInfo as &$vo) {

            $vo['pic'] = json_decode($vo['pic'],true)[0];

            // 顯示商品價格
            $vo['show'] = ProductHelpler::get_product_price_option($vo);

            // 標記優惠券
            $vo[$vo['id']]['coupon_button'] = ProductHelpler::find_prod_coupon($vo['id']);

            // 標記活動或優惠
            $vo['act_data'] = ProductHelpler::find_prod_act($vo['id']);
        }

        return $productInfo;
    }

    public function subscripe(){
        // $subscription = request()->post();
        $subscription = json_decode(file_get_contents('php://input'), true);
        // dump($subscription);

        if (!isset($subscription['endpoint'])) {
            $this->error('Error: not a subscription');
        }

        $method = request()->server('REQUEST_METHOD');

        $user_id = session('user') ? session('user.id') : 0;
        if(!$user_id){ $this->error("Error: user_id"); }
        $data = [
            'user_id'         => $user_id,
            'endpoint'        => $subscription['endpoint'],
            'expirationTime'  => $subscription['expirationTime'],
            'auth'            => $subscription['keys']['auth'],
            'p256dh'          => $subscription['keys']['p256dh'],
        ];

        switch ($method) {
            case 'POST' :
                $subscription = DB::connection('main_db')->table('subscription')->where([
                    ['endpoint', '=', $data['endpoint']],
                    ['user_id', '=', $data['user_id']]
                ])
                ->first();
                if($subscription){
                    // update the key and token of subscription corresponding to the endpoint
                    if($user_id==0){ unset($data['user_id']); }
                    DB::connection('main_db')->table('subscription')->where('id', $subscription->id)->update($data);
                }else{
                    // create a new subscription entry in your database (endpoint is unique)
                    DB::connection('main_db')->table('subscription')->insert($data);
                }
                break;
            case 'DELETE':
                // delete the subscription corresponding to the endpoint
                break;
            default:
                $this->error("Error: method not handled");
        }
    }
}



