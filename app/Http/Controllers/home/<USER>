<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\MemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\CouponCheck;
use App\Services\pattern\simpleFactory\discountFactory\DiscountFactory;
use App\Services\DBtool\DBFileConnecter;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\ProductHelpler;

class Product extends PublicController {

  const PER_PAGE_ROWS = 30;
  const SIMPLE_MODE_PAGINATE = false;
  public function __construct(Request $request)
  {
    parent::__construct($request);
    if(empty(config('control.close_function_current')['會員瀏覽商品設定'])){ /*有啟用*/
      if($this->user["id"]==0){
        $this->error(Lang::get('請先登入會員'), url('Login/login').'?jumpUri='.request()->server('REQUEST_URI'));
      };
    }
  }

  private function getRandADV($rid) {
    $randAdv = DB::table('product')
                ->select('id','title','inner_adv01_pic','inner_adv01_link','content','pic','webtype_keywords','webtype_description') //多加webtype_keywords,webtype_description
                ->whereRaw('online = 1 AND id <> '.$rid)
                ->orderByRaw('RAND()')
                ->limit(2)
                ->get();
    return CommonService::objectToArray($randAdv);
  }

  private function get_product_price_searchs($distributor_id=0){
    $product_price_searchs = DB::table('product_price_search')
                                ->whereRaw('online=1')
                                ->whereRaw('distributor_id="'.$distributor_id.'"')
                                ->orderByRaw('orders asc, id asc')->get();
    $this->data['product_price_searchs'] = CommonService::objectToArray($product_price_searchs);
  }

  public function product() {
    $this->get_product_price_searchs(); /*價格搜尋選項*/

    // 找出本層分館
    $id = request()->get('id');

    $product = DB::table('product')->select('id',
                                            'title',
                                            'out_ID',
                                            'inner_adv01_pic',
                                            'inner_adv01_link',
                                            'inner_adv02_pic',
                                            'inner_adv02_link',
                                            'webtype_keywords',
                                            'online',
                                            'webtype_description'); //多加webtype_keywords,webtype_description
    if($id){
      if(!is_numeric($id)){ $this->error(Lang::get('查無此頁'),'/'); }
      $product = $product->find($id);
      $search_cond = [ 'searchPrev' => $id, ];
    }else{
      $searchPrev=request()->get('searchPrev');
      $product = $product->whereRaw('online=1')->whereRaw('id ='.$searchPrev)->first();

      $search_cond = [ 'searchPrev' => $searchPrev, ];
    }
    $product = CommonService::objectToArray($product);
    if($product['online'] =='2'){ $this->error(Lang::get('查無此頁'), '/'); }
    $this->data['product'] = $product;
    $id = $product['id'];

    $this->data['layer_type'] = 'product';
    
    // 製作麵包屑
    $title_array = $this->get_title_array(['prev_id'=>$product['id'], 'branch_id'=>0]);
    // dd($title_array);
    session()->put('title_array', $title_array); /*記憶分類麵包屑*/
    $this->data['title_array'] = $title_array;

    $main_layer = [
      'id' => $product['id'],
      'title' => $product['title'],
      'out_ID' => $product['out_ID'],
      'webtype_keywords' => $product['webtype_keywords'],
      'webtype_description' => $product['webtype_description'],
    ];
    $this->data['main_layer'] = $main_layer;

    /*依搜尋條件取得商品(含排序)*/
    $search_cond = [ 'searchPrev' => $id, ];
    $search_result = $this->get_search_product($search_cond);
    $this->data['sort'] = $search_result['sort'];
    $this->data['price_range'] = $search_result['price_range'];
    $this->data['productinfo'] = $search_result['rowData'];
    $rowCount = $search_result['rowCount'];
    $coupon_button = $search_result['coupon_button'];
    $act_button = $search_result['act_button'];
    // dump($search_result['rowData']);

    // 找出所有子分類
    $subProduct = DB::table('typeinfo')
                    ->select('typeinfo.title', 'typeinfo.id')
                    ->whereRaw("typeinfo.parent_id = {$product['id']}
                        AND typeinfo.branch_id = 0
                        AND typeinfo.online = 1
                        AND (
                          typeinfo.end <= 0 OR
                          (typeinfo.start < " . time() . " AND typeinfo.end > " . time() . ")
                        )
                    ")
                    ->orderByRaw('typeinfo.order_id')->get();
    $subProduct=CommonService::objectToArray($subProduct);
    // dump($subProduct);
    // 從子分類找出屬於該分類的商品
    foreach ($subProduct as $key => $vo) {
      $search_cond = [ 'searchPrev' => $id, 'searchBranch'=>$vo['id'] ];
      $search_result = $this->get_search_product($search_cond, $coupon_button, $act_button);
      $subProduct[$key]['productinfo'] = $search_result['rowData'];
      $rowCount += $search_result['rowCount'];
      $coupon_button = $search_result['coupon_button'];
      $act_button = $search_result['act_button'];
    }
    $this->data['subProduct'] = $subProduct;
    $this->data['rowCount'] = $rowCount;
    $this->data['coupon_button']                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          = $coupon_button;
    $this->data['act_button'] = $act_button;

    /*assign商品列表所需參數*/
    $this->assign_view_data_product($product['id']);

    return view('home.product.product',['data'=>$this->data]);
  }
  public function typeinfo() {
    $this->get_product_price_searchs(); /*價格搜尋選項*/

    $id = request()->get('id');
    if($id==null){
      $searchBranch=request()->get('searchBranch');
      $searchPrev=request()->get('searchPrev');
      //找出次層分類
      $typeinfo = $this->get_typeinfo_sub($searchBranch,$searchPrev);  
    }
    else{
      // 找出第一層分類
      $typeinfo = $this->get_typeinfo($id);
    }

    //dump($typeinfo);
    $this->data['layer_type'] = 'typeinfo';

    // 找出此層分類下掛於哪個分館
    $product = DB::table('product')
                  ->select('id',
                      'title',
                      'out_ID',
                      'inner_adv01_pic',
                      'inner_adv01_link',
                      'inner_adv02_pic',
                      'inner_adv02_link',
                      'online')
                  ->find($typeinfo['parent_id']);
    $product = CommonService::objectToArray($product);
    $this->data['product'] = $product;
    //dump($product);
    
    if($product['online'] == '2'){
      $this->error(Lang::get('查無此頁'));
    }

    // 製作麵包屑
    $title_array = $this->get_title_array(['prev_id'=>$product['id'], 'branch_id'=>$typeinfo['id']]);

    //dd($title_array);
    session('title_array', $title_array); /*記憶分類麵包屑*/
    $this->data['title_array'] = $title_array;

    $main_layer = [
      'id' => $typeinfo['id'],
      'title' => $typeinfo['title'],
      'out_ID' => $product['out_ID'],
      'webtype_keywords' => $typeinfo['webtype_keywords'],
      'webtype_description' => $typeinfo['webtype_description'],
    ];
    $this->data['main_layer'] = $main_layer;

    // 從本層分類找出商品
    $search_cond = [ 'searchBranch' => $typeinfo['id'], ];
    $search_result = $this->get_search_product($search_cond);
    $this->data['sort'] = $search_result['sort'];
    $this->data['price_range'] = $search_result['price_range'];
    $this->data['productinfo'] = $search_result['rowData'];
    $rowCount = $search_result['rowCount'];
    $coupon_button = $search_result['coupon_button'];
    $act_button = $search_result['act_button'];

    // dump($search_result['rowData']);
    //dd($id);
    // 找出所有子分類
    $subProduct = DB::table('typeinfo')
        ->select('typeinfo.title', 'typeinfo.id')
        ->whereRaw('typeinfo.branch_id = ' . $typeinfo['id'] . '	AND typeinfo.parent_id = ' . $typeinfo['parent_id'] . ' AND typeinfo.online = 1
                    AND (typeinfo.end <= 0 OR	(typeinfo.start < " . time() . " AND typeinfo.end > " . time() . "))
        ')
        ->orderBy('typeinfo.order_id')->get();
    $subProduct = CommonService::objectToArray($subProduct);
    // 從子分類找出屬於該分類的商品
    foreach ($subProduct as $key => $vo) {
      $search_cond = [ 'searchBranch' => $vo['id'], ];
      $search_result = $this->get_search_product($search_cond, $coupon_button, $act_button);
      $subProduct[$key]['productinfo'] = $search_result['rowData'];
      $rowCount += $search_result['rowCount'];
      $coupon_button = $search_result['coupon_button'];
      $act_button = $search_result['act_button'];
    }
    $this->data['subProduct'] = $subProduct;
    $this->data['rowCount'] = $rowCount;
    $this->data['coupon_button'] = $coupon_button;
    $this->data['act_button'] = $act_button;
    
    /*assign商品列表所需參數*/
    $this->assign_view_data_product($typeinfo['parent_id']);

    return view('home.product.product',['data'=>$this->data]);
  }
  public function distributor(){
    if(config('control.control_platform')!=1){ $this->error(Lang::get('查無此頁')); }

    $id = request()->get('id') ?? 0;

    /*顯示供應商*/
    $distributor_data = [ 'id' => 0 ];
    $MemberInstance = new MemberInstance($id);
    $user_data = $MemberInstance->get_user_data();
    // dump($user_data);exit;
    if($user_data){
      if($user_data['user_type']==1){
        $distributor_data['id'] = $id;
        $distributor_data['shop_name'] = $user_data['shop_name'] ? $user_data['shop_name'] : $user_data['name'];
      }
    }
    if($distributor_data['id'] == 0){ $this->error(Lang::get('查無此頁')); }

    // 製作麵包屑
    $title_array = [
      [
        "id" => $id,
        "title" => $distributor_data['shop_name'],
        "type" => 'distributor',
      ]
    ];
    session('title_array', $title_array); /*記憶分類麵包屑*/
    $this->data['title_array'] =  $title_array;

    $main_layer = [
      'id' => $distributor_data['id'],
      'title' => $distributor_data['shop_name'],
      'webtype_keywords' => '',
      'webtype_description' => '',
    ];
    $this->data['main_layer'] = $main_layer;

    /*依搜尋條件取得商品(含排序)*/
    $search_cond = [ 'distributor_id' => $id, ];
    $search_result = $this->get_search_product($search_cond);
    // dump($search_result);exit;
    $this->data['sort'] = $search_result['sort'];
    $this->data['price_range'] = $search_result['price_range'];
    $this->data['productinfo'] = $search_result['rowData'];
    $rowCount = $search_result['rowCount'];
    $coupon_button = $search_result['coupon_button'];
    $act_button = $search_result['act_button'];
    // dump($search_result['rowData']);

    // 找出所有子分類
    $this->data['subProduct'] = [];
    $this->data['rowCount'] = $rowCount;
    $this->data['coupon_button'] = $coupon_button;
    $this->data['act_button'] = $act_button;

    /*assign商品列表所需參數*/
    $this->assign_view_data_product();

    return view('home.product.product',['data'=>$this->data]);
  }
  public function activity() {
    $this->get_product_price_searchs(); /*價格搜尋選項*/

    $id = request()->get('id');
    $acts = DB::table('act as a')->whereRaw("a.online = 1
                                              AND (
                                                a.end <= 0
                                                OR
                                                ( a.start < " . time() . " AND  a.end > " . time() . ")
                                              )");
    if($id){
      $acts = $acts->where('id', $id);
      $query = [ 'id' => $id ];
    }else{
      $query = [];
    }
    $acts = $acts->get();
    if(count($acts)==0){ 
      // $this->error(Lang::get('無資料']);
      $this->data['act']= ['name'=> $this->data['frontend_menu']['product']['second_menu']['activity']['name']];
      $this->data['sort'] = '';
      $this->data['price_range'] = '';
    }else{
      if(count($acts)==1){
        $this->data['act'] = CommonService::objectToArray($acts[0]);
      }else{
        $this->data['act'] = ['name'=> $this->data['frontend_menu']['product']['second_menu']['activity']['name']];
      }
    }

    // dump($acts);exit;
    $rowCount = 0; $coupon_button = []; $act_button = [];
    foreach ($acts as $key => $value) {
      $search_cond = [ 'act_id' => $value->id, ];
      $search_result = $this->get_search_product($search_cond, $coupon_button, $act_button);
      
      $acts[$key]->productinfo = $search_result['rowData'];
      $this->data['sort'] = $search_result['sort'];
      $this->data['price_range'] = $search_result['price_range'];
      $rowCount += $search_result['rowCount'];
      $coupon_button = $search_result['coupon_button'];
      $act_button = $search_result['act_button'];
    }
    $this->data['rowCount'] = $rowCount;
    $this->data['coupon_button'] = $coupon_button;
    $this->data['act_button'] = $act_button;
    $this->data['acts'] = $acts;

    /*assign商品列表所需參數*/
    $this->assign_view_data_product();

    return view('home.product.activity',['data'=>$this->data]);
  }
  public function search() {
    $this->get_product_price_searchs(); /*價格搜尋選項*/

    $keyword = request()->get('keyword') ? request()->get('keyword') : '';
    $this->data['keyword'] = $keyword;

    /*處理搜尋tag的sql語法*/
    $search_cond = [ 'searchKey' => $keyword ];
    $search_result = $this->get_search_product($search_cond);
    $this->data['sort'] = $search_result['sort'];
    $this->data['price_range'] = $search_result['price_range'];
    $this->data['productinfo'] = $search_result['rowData'];
    $this->data['rowCount'] = $search_result['rowCount'];
    $this->data['coupon_button'] = $search_result['coupon_button'];
    $this->data['act_button'] = $search_result['act_button'];
    /*assign商品列表所需參數*/
    $this->assign_view_data_product(0);

    return view('home.product.search',['data'=>$this->data]);
  }
  
  private function get_search_product($search_cond=[], $coupon_button=[], $act_button=[]){
    $search_cond['online'] = 1; 																												/*限看顯示的*/
    $search_cond['frontend_view'] = true; 																							/*前台查看*/
    $search_cond['frontend_user_id'] = $this->user['id'];																/*前台登入的會員帳號*/
    $search_cond['sort_method'] = request()->get('sort') ?? '';								/*排序方式*/
    $search_cond['price_range_search'] = request()->get('price_range') ?? ''; /*金額範圍*/
    $search_result = ProductHelpler::search_product($search_cond);
    if (empty($search_result['productinfo']->items()) == false) {
      foreach ($search_result['productinfo']->items() as $obj) {
        $obj->pic1 = json_decode($obj->pic, true)[0];
        //  顯示商品價格
        $obj->show = ProductHelpler::get_product_price_option(CommonService::objectToArray($obj));
        // 標記優惠券
        $coupon_button[$obj->id] = ProductHelpler::find_prod_coupon($obj->id);
        // 標記活動或折扣
        $act_button[$obj->id]['act_data'] = ProductHelpler::find_prod_act($obj->id);

        $obj->title = str_replace('\\"', '"', $obj->title);
      }
    }

    return [
      'sort' => $search_result['sort_method'],
      'price_range' => $search_result['price_range_search'],
      'rowCount' => $search_result['rowCount'],
      'rowData' => $search_result['productinfo'],
      'coupon_button' => $coupon_button,
      'act_button' => $act_button,
    ];
  }

  private function assign_view_data_product($product_id=0){
    // 推薦分館(排除$product_id)
    $productRand = $this->getRandADV($product_id);
    $this->data['productRand'] = CommonService::objectToArray($productRand);

    $store_products = $this->get_store_products();
    $this->data['store_products'] = CommonService::objectToArray($store_products);
  }
  private function get_store_products(){
    $result = self::get_record('product_store');
    $store_products = [];
    foreach ($result['records'] as $key => $value) {
      array_push($store_products, $value['product_id']);
    }
    return $store_products;
  }


  public function coupon() {
    // 有設定prod_id的話就只顯示是用此商品的優惠券
    $prod_id_where = isset($_GET['prod_id']) ? " AND ( (coupon.area_id=".$_GET['prod_id']." AND coupon.area=1) OR coupon.area=0)" : '';
    $coupon_sql = CouponCheck::arrange_search_sql();
    // dd($coupon_sql);
    $productinfo = DB::table('coupon')
                      ->select('coupon.title AS title',
                              'coupon.id AS id',
                              'coupon.pic AS pic',
                              'coupon.end AS end',
                              'coupon.discount AS discount',
                              'coupon.price AS price')
                      ->whereRaw("coupon.type = 0".$prod_id_where)
                      ->whereRaw($coupon_sql)
                      ->orderBy('id','desc')
                      ->get();
    $productinfo = CommonService::objectToArray($productinfo);
    
    $productinfo = array_filter($productinfo, function ($value){
      return (DB::table('coupon_pool')
        ->whereNull('owner')
        ->whereRaw("login_time < " . (time() - 21600) . ' OR login_time IS NULL')
        ->whereRaw("coupon_id = " . $value['id'])
        ->count()
      );
    });
    $this->data['productinfo'] = $productinfo;
    return view('home.product.coupon',['data'=>$this->data]);
  }
  public function couponinfo(Request $request) {
    $id = $request->get('id');
    $coupon_sql = CouponCheck::arrange_search_sql();
    $productinfo = DB::table('coupon')
                      ->whereRaw("coupon.type = 0")
                      ->whereRaw($coupon_sql)
                      ->where('id', $id)
                      ->get();
    $productinfo = CommonService::objectToArray($productinfo);
    $productinfo = $productinfo[0] ?? [];
    if(empty($productinfo)){
      $this->error(Lang::get('無此頁面'));
    }
    // dd($productinfo);
    $this->data['productinfo'] = $productinfo;
    return view('home.product.couponinfo',['data'=>$this->data]);
  }

  public function productinfo_one(Request $request) {
    $this->data['close_nav'] = true;
    return $this->productinfo($request);
  }
  public function productinfo(Request $request) {
    $productinfo_recommend = $request->get('recommend');
    if($productinfo_recommend){
      session()->put('productinfo_recommend', $productinfo_recommend);
    }

    $id = $request->get('id');
    if(!$id){
      $this->error(Lang::get('無資料'));
    }
    /*商品資料*/
    $columns = [
      'id',
      'product_id',
      'title',
      'ISBN',
      'Author',
      'house',
      'house_date',
      'keywords',
      'out_ID',
      'prodesc',
      'pic',
      'pushitem',
      'online',
      'content',
      'has_price',
      'display',
      'text1',
      'text1_online',
      'text2',
      'text2_online',
      'text3',
      'text3_online',
      'text4',
      'text4_online',
      'text5',
      'text5_online',
      'is_registrable',
      'card_pay',
      'pre_buy',
      'pre_buy_limit',
      'final_array',
      'ask_price',
      'distributor_id',
      'is_box',
      'is_customized_box',
    ];
    $productinfo = DB::table('productinfo')->select($columns);
    //dd($this->user);
    $where_in_sql = ProductHelpler::get_user_view_product_limit($this->user['id']);
    if($where_in_sql){
      //$productinfo = $productinfo->whereRaw($where_in_sql);
    }
    
    $productinfo = $productinfo->find($id);
    $productinfo = CommonService::objectToArray($productinfo);

    if( empty($productinfo) ){
      $msg = Lang::get('無資料');
      if(empty(config('control.close_function_current')['會員瀏覽商品設定'])){ $msg .= Lang::get('，或您的瀏覽權限不足'); }
      $this->error($msg);
    }

    if($productinfo['online'] == '2'){
      $this->error(Lang::get('無資料'));
    }	

    $productinfo['title'] = str_replace('\\"', '"', $productinfo['title']);

    $kol_id = $request->get('kol');
    if(empty(config('control.close_function_current')['網紅列表'])){
      if($kol_id){
        $kol_productinfo = DB::table('kol_productinfo')->where('productinfo_id',$id)->where('kol_id',$kol_id)->where('is_using',1)->get();
        $kol_name = DB::table('kol')->find($kol_id)->kol_name;
        $this->data['kol_name'] = $kol_name;
        if(!$kol_productinfo){$this->redirect(url('Product/productinfo').'?id='.$id);}
      }
    }else{
      if($kol_id){$this->redirect(url('Product/productinfo').'?id='.$id);}
    }

    $productinfo['pic'] = json_decode($productinfo['pic'],true);
    $productinfo['text1'] = str_replace("&lt;","<",$productinfo['text1']);
    $productinfo['text1'] = str_replace("&gt;",">",$productinfo['text1']);

    /*顯示供應商*/
    if(config('control.control_platform')==1){
      $MemberInstance = new MemberInstance($productinfo['distributor_id']);
      $user_data = $MemberInstance->get_user_data();
      // dump($user_data);exit;
      $productinfo['distributor_id'] = 0;
      $productinfo['shop_name'] = '';
      if($user_data){
        if($user_data['user_type']==1){
          $productinfo['distributor_id'] = $user_data['id'];
          $productinfo['shop_name'] = $user_data['shop_name'] ? $user_data['shop_name'] : $user_data['name'];
        }
      }
    }
    //dd($productinfo);
    $this->data['productinfo'] = $productinfo;

    /*取得商品品項*/
    $price = ProductHelpler::get_price($productinfo['id']);
    $price = CommonService::objectToArray($price);
    //dd($price);
    $this->data['price'] = $price;

    /*取得人氣紀錄*/
    $love = $this->get_love_record($productinfo['id']);
    // dump($love);
    $this->data['love'] = $love;
    /*取得收藏紀錄*/
    $store = $this->get_store_record($productinfo['id']);
    // dump($store);
    $this->data['store'] = $store;

    //dd($this->data);
    // 處理麵包屑紀錄
    $title_array = session('title_array') ? session('title_array') : []; /*取得分類麵包屑*/
    // dump($title_array);
    $layer_type = $request->get('layer_type');	/*取得當前開啟商品的分類(product/typeinfo)*/
    $layer_id = $request->get('layer_id');		/*取得當前開啟商品的分類的id*/
    if($layer_type && $layer_id){
      if(	(count($title_array)==1 && $layer_type=='typeinfo') ||			/*只記錄到分館，且商品是在分類中開啟*/
        (count($title_array)>1 && end($title_array)['id']!=$layer_id)	/*記錄到分類，但商品開啟的分類與記錄的不同*/
      ){ 
        $add_layer = $this->get_typeinfo($layer_id);
        array_push($title_array, ['id'=> $add_layer['id'], 'title'=>$add_layer['title'], 'type'=>'typeinfo']);
      }
    }

    // 檢查 分館/分類是否關閉 及 麵包屑紀錄是否有效
    $position = ProductHelpler::check_product_and_infotype_close($productinfo['final_array'], $title_array);
    if($position->close){$this->error(Lang::get('查無此頁'),'index/index');}

    /* 依照檢查結果回傳麵包屑紀錄 */
    if(count($position->title_array)==0){ /*檢查麵包屑無效*/
      $title_array = $this->get_title_array($position->rand_layer); /*依商品階層隨機指派麵包屑*/
    }else{
      $title_array = $position->title_array;
    }
    // dump($title_array);exit;
    $this->data['title_array'] = $title_array;

    $current_product_layer_id = $title_array[0]['id']; /*當前瀏覽的分館id*/

    // 目前分館
    $product = DB::table('product')
                  ->select('id','title','inner_adv01_pic','inner_adv01_link','online')
                  ->find($current_product_layer_id);
    $this->data['product'] = CommonService::objectToArray($product);
    
    // 其他推薦分館
    $productRand = $this->getRandADV($current_product_layer_id);
    $this->data['productRand'] = CommonService::objectToArray($productRand);


    /*推薦商品*/
    $pushItem = [];
    $pushitem_product_ids = explode(',', $productinfo['pushitem']);
    $product_id_where = [];
    foreach ($pushitem_product_ids as $k => $v) {
      array_push($product_id_where, $v);
    }
    // dump($product_id_where);
    if(count($product_id_where)>0){
      // $product_id_where = implode(',', $product_id_where);
      $pushItem = DB::table('productinfo')->whereIn('id', $product_id_where)->get();
      $pushItem = CommonService::objectToArray($pushItem);
      foreach ($pushItem as $k => $v) {
        $pushItem[$k]['pic'] = json_decode($v['pic'],true)[0];
        $pushItem[$k]['price'] = ProductHelpler::get_product_price_option($v);
      }
    }
    // dump($pushItem); exit;
    $this->data['pushItem'] = $pushItem;

    /*活動/折扣標示*/
    $act = ProductHelpler::find_prod_act($id);
    $this->data['act'] = $act;

    /*優惠券標示*/
    $coupon_button = ProductHelpler::find_prod_coupon($id);
    $this->data['coupon_button'] = $coupon_button;

    # 特殊標記(tag)
    parent::product_species($id);

    /*顯示加價購商品*/
    $type_ids = [];
    foreach ($price as $key => $value) {
      array_push($type_ids, $value['id']);
    }
    $addprice_resault = Proposal::get_addprice_products($type_ids);
    $this->data['addprice_type_group'] = $addprice_resault['addprice_type_group'];

    parent::set_visitor_record($request, $id);

    return view('home.product.productinfo',['data'=>$this->data]);
  }

  /* 製作麵包屑 */
  private function get_title_array($layer){
    // $layer範例：['prev_id'=>'', 'branch_id'=>''];
    $layer = (array)$layer;
    // dump($layer);
    if(!isset($layer['branch_id'])){ $this->error(Lang::get('查無此頁')); }else{ $branch_id=$layer['branch_id']; }
    if(!isset($layer['prev_id'])){ $this->error(Lang::get('查無此頁')); }else{ $prev_id=$layer['prev_id']; }

    /*處理分類*/
    $title_array = [];
    while($branch_id != 0){
      $type_info = $this->get_typeinfo($branch_id);
      array_unshift($title_array, ['id'=> $type_info['id'], 'title' => $type_info['title'], 'type'=>'typeinfo']);
      $branch_id =  $type_info['branch_id'];
    }

    /*處理分館*/
    $product = DB::table('product')->where('id',$prev_id)->first();
    $product = CommonService::objectToArray($product);
    
    array_unshift($title_array, ['id'=> $product['id'], 'title' => $product['title'], 'type'=>'product']);
    // dump($title_array);exit;

    /*顯示供應商*/
    if(config('control.control_platform')==1){
      $MemberInstance = new MemberInstance($product['distributor_id']);
      $user_data = $MemberInstance->get_user_data();
      // dump($user_data);exit;
      if($user_data){
        if($user_data['user_type']==1){
          $shop_name = $user_data['shop_name'] ? $user_data['shop_name'] : $user_data['name'];
          array_unshift($title_array, [
            "id" => $user_data['id'],
            "title" => $shop_name,
            "type" => 'distributor',
          ]);
        }
      }
    }
    return $title_array;
  }

  private function get_typeinfo_sub($searchBranch, $searchPrev) {
    $typeinfo = DB::table('typeinfo')->select('title', 'id', 'parent_id','branch_id','webtype_keywords','webtype_description','online')
              ->whereRaw(" (
                    typeinfo.end <= 0
                    OR
                    (typeinfo.start < " . time() . " AND typeinfo.end > " . time() . ")
                    )")
              ->where([
                'id' => $searchBranch,
                'parent_id' => $searchPrev,
              ])->first();
                
    if($typeinfo==null){
      $result = $this->error(Lang::get('查無此頁'), getenv('HTTP_REFERER'));
    }
    $typeinfo=CommonService::objectToArray($typeinfo);
    
    if(!$typeinfo)$this->error(Lang::get('查無此頁'));
    if($typeinfo['online']==2)$this->error(Lang::get('查無此頁'));
    return $typeinfo;
  }

  /* 取得分類 */
  private function get_typeinfo($id){
    if($id){
      if(!is_numeric($id)){ $this->error(Lang::get('查無此頁'),'/'); }
    }
    $typeinfo = DB::table('typeinfo')->select('title', 'id', 'parent_id','branch_id','webtype_keywords','webtype_description','online')
              ->whereRaw(" (
                    typeinfo.end <= 0
                    OR
                    (typeinfo.start < " . time() . " AND typeinfo.end > " . time() . ")
                    )")
              ->where('id',$id)->first();
    if($typeinfo==null){
      $result = $this->error(Lang::get('查無此頁'), getenv('HTTP_REFERER'));
    }
    $typeinfo=CommonService::objectToArray($typeinfo);
    
    if(!$typeinfo)$this->error(Lang::get('查無此頁'));
    if($typeinfo['online']==2)$this->error(Lang::get('查無此頁'));
    return $typeinfo;
  }


  public function getFreeCoupon() {
    $coupon_id = request()->post('coupon_id');
    $num = (Int)request()->post('num');

    $respData = ProductHelpler::checkCouponNum($coupon_id, $this->user['id'], $num);
    if($respData['code']){
      $coupon_pool_ids = DB::table('coupon_pool as cp')
                            ->select('cp.id')
                            ->join('coupon', 'cp.coupon_id','coupon.id')
                            ->where('coupon.price', 0)
                            ->where('coupon.type', 0)
                            ->whereNull('cp.owner')
                            ->where('cp.coupon_id', $coupon_id)
                            ->limit($num)
                            ->get();
      $coupon_pool_ids=CommonService::objectToArray($coupon_pool_ids);
      $in_id = [];
      foreach ($coupon_pool_ids as $key => $value) {
        array_push($in_id, $value['id']);
      }
      if($in_id){
        $in_id = '('.join(',', $in_id).')';
        DB::table('coupon_pool')
            ->whereRaw('id in '.$in_id)
            ->update([
              'owner' => $this->user['id'],
              'login_time' => time()
            ]);
      }else{
        $respData['msg'] = Lang::get('無資料');
      }
    }
    return $respData;
  }
  
  public function cart() {
    $Proposal = Proposal::withTeamMembersAndRequire(
      ['GetCartData'],
      [
        'cart_session' => 'cart_all',
        'user_id' => $this->user['id'],
      ]
    );
    $Proposal = MemberFactory::createNextMember($Proposal);

    $cartData = [];
    $product_id_Data=[];
    $cartTotal=0;
    foreach (json_decode($Proposal->projectData['data'], true) as $key => $value) {
      $singleData = Proposal::get_singleData($key, 'online');

      $singleData['num'] = $value;
      array_push($cartData, $singleData);
      $cartTotal=$cartTotal+($singleData['countPrice']*$singleData['num']);
    }

    return $cartTotal;
  }

  /*APT：取得註冊商品商品選項(依傳入distributor_id)*/
  public function get_reg_prod(){
    /*處理distributor_id*/
    if(config('control.control_platform')==1){
      $distributor_id = request()->post('distributor_id') ?? 0;
      if($distributor_id!=0){
            $MemberInstance = new MemberInstance($distributor_id);
        $user_data = $MemberInstance->get_user_data_distributor();
        if($user_data){
          $distributor_id = $user_data['id'];
        }else{
          $distributor_id = 0;
        }
      }
    }else{
      $distributor_id = 0;
    }

    $product_names = DB::table('excel')->select('product_name')
                        ->where('distributor_id', $distributor_id)
                        ->groupby('product_name')
                        ->orderBy('product_name','asc')
                        ->get();
    $product_names = CommonService::objectToArray($product_names);
    $this->success($product_names);
  }
  /*API：商品註冊*/
  public function reg() {
    $user_id = Session('user.id');
    if(empty($user_id)){
      $this->error(Lang::get('請先登入會員'));
    }
    $MemberInstance = new MemberInstance($user_id);
    $user_data = $MemberInstance->get_user_data();
    if(!$user_data['name']){
      $this->error(Lang::get('請確認會員後台已填寫會員姓名'));
    }
    $post = request()->post();
    unset($post['_token']);
    if(empty( DB::table('excel')->where("product_name = '".$post['product_name']."'")->select() )){
      $this->error(Lang::get('輸入的商品名稱有誤'));
    }

    /*處理distributor_id*/
    if(config('control.control_platform')==1){
      $distributor_id = $post['distributor_id'] ?? 0;
    }else{
      $distributor_id = 0;
    }

    $compare_code = DB::table('excel')->whereRaw("product_code = 'XXXXXXXXXXXXXX'")
                      ->whereRaw("product_name = '".$post['product_name']."'")
                      ->whereRaw("distributor_id='".$distributor_id."'")
                      ->get();
    $compare_code = CommonService::objectToArray($compare_code);
    if(empty($compare_code)){ // 當沒有特殊機身碼，要比對有無機身碼
      $has_code = DB::table('excel')->where("product_code = '".$post['product_code']."'")
                      // ->where("product_name = '".$post['product_name']."'")
                      ->where("distributor_id='".$distributor_id."'")
                      ->get();
      if(empty($has_code)){
        $this->error(Lang::get('機身碼不存在(請注意前後是否有輸入到空白或英文數字以外的符號)'));
      }
    }

    // 比對是否被註冊過
    $had_registered = DB::table('excel')->whereRaw("product_code = '".$post['product_code']."'")
                      // ->where("product_name = '".$post['product_name']."'")
                      ->whereRaw("status != '0'")
                      ->whereRaw("distributor_id='".$distributor_id."'")
                      ->get();
        $had_registered = CommonService::objectToArray($had_registered);
    if(!empty($had_registered)){ $this->error(Lang::get('機身碼已被註冊')); }

    // 比對是否僅輸入數字+英文
    // if(preg_match('/^([0-9A-Za-z]+)$/',$post['product_code']) ==0 ){
    // 	$this->error('寄送失敗，機身碼僅可輸入英文及數字');
    // }

    $width = 370; $height = 370;
    $image = request()->file('image');
    if($image){
      $DBFileConnecter = new DBFileConnecter();
      $post['pic'] = $DBFileConnecter->fixedFileUp($image, 'reg_' . time(), $width, $height);
    }

    $post['regtime'] = date('Y-m-d');
    $post['account_number'] = $user_id;
    $post['status'] = '0';
    $post['pro_id'] = DB::table('excel_reply')->whereRaw("regtime = '".date('Y-m-d')."'")->count();
    $post['pro_id']++;
    $post['pro_id'] = date('Ymd').str_pad($post['pro_id'],5,'0',STR_PAD_LEFT);
    $post['account_name'] = $user_data['name'];
    // dump($post);exit;
    DB::table('excel_reply')->insert($post);

    $product_register_letter = Lang::get('menu.註冊商品成功信管理者');
    $product_register_letter = str_replace("{name}", $user_data['name'], $product_register_letter);
    $product_register_letter = str_replace("{regtime}", $post['regtime'], $product_register_letter);
    $product_register_letter = str_replace("{pro_id}", $post['pro_id'], $product_register_letter);
    $mailBody = "
      <html>
        <head></head>
        <body>
          <div>
            ".$product_register_letter."
          </div>
          <div style='color:red;'>
            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
          </div>
        </body>
      </html>
    ";
    if($distributor_id==0){
      $mail_return = HelperService::Mail_Send($mailBody, 'admin_product_register', '', Lang::get('註冊商品提醒'));
    }else{
          $MemberInstance = new MemberInstance($distributor_id);
      $user_data = $MemberInstance->get_user_data_distributor();
      // dump($user_data);
      if($user_data){
        $mail_return = HelperService::Mail_Send($mailBody, 'client', $user_data['email'], Lang::get('註冊商品提醒'));
      }
    }
    $this->success(Lang::get('我們已收到您的註冊資料，將盡速為您處理'));
  }

  public function aj_get_productinfo_and_price(){
    $prod_id = request()->post('prod_id') ? request()->post('prod_id') : '0';
    $productinfo = DB::table('productinfo')
                      ->select('id','product_id','title','ISBN','Author','house','house_date','keywords','out_ID','prodesc','pic',
                          'pushitem','online',
                          'content', 'has_price',
                          'display',
                          'text1', 'text1_online',
                          'text2', 'text2_online',
                          'text3', 'text3_online',
                          'text4', 'text4_online',
                          'text5', 'text5_online',
                          'is_registrable', 
                          'card_pay',
                          'pre_buy',
                          'pre_buy_limit',
                          'final_array')
                      ->whereRaw('online != "2"')
                      ->find($prod_id);
    $productinfo = CommonService::objectToArray($productinfo);
    if(!$productinfo){ $this->error(Lang::get('無資料')); }

    // 檢查 分館/分類是否關閉
    $position = ProductHelpler::check_product_and_infotype_close($productinfo['final_array']);
    if($position->close){$this->error(Lang::get('查無此頁'));}

    $price = ProductHelpler::get_price($prod_id);
    if($productinfo['has_price'] ==0 || count($price) == 0){
      $this->error(Lang::get('無資料'));
    }

    $productinfo['price'] = $price;
    
    $this->success(json_encode($productinfo, JSON_UNESCAPED_UNICODE));
  }

  /*依商品取得人氣紀錄*/
  private static function get_love_record($id)
  {
    $result = self::get_record('product_love', $id);
    return $result;
  }
  /*依商品取得收藏紀錄*/
  private static function get_store_record($id)
  {
    $result = self::get_record('product_store', $id);
    return $result;
  }
  /*依給定資料表、商品id取得紀錄*/
  private static function get_record($tableName, $id=null)
  {
    $user_id = Session('user.id');

    $records = [];	/*點擊紀錄*/
    $record = 0;	/*是否有點過*/
    if($user_id){
      $records = DB::table($tableName)->where('user_id', $user_id);
      if($id){ $records = $records->where('product_id', $id); }
      $records = $records->get();
      $records = CommonService::objectToArray($records);
      $record = $records ? 1 : 0;
    }

    /*此商品被多少人點了*/
    $num = 0;
    if($id){ $num = DB::table($tableName)->where('product_id', $id)->count(); }

    return [
      'records' => $records,
      'record' => $record,
      'num' => $num,
    ];
  }

  // 客製化禮盒
  public function customize_box(Request $request) {
    try {
      $request_data = $request->all();

      if (empty($request_data['id'])) {
        throw new \Exception(Lang::get('無資料'));
      }

      $db_result = DB::table('customized_box_info')
      ->select(['productinfo.title', 'customized_box_info.*'])
      ->rightJoin('productinfo', 'customized_box_info.productinfo', '=', 'productinfo.id')
      ->where('productinfo', $request_data['id'])
      ->where('removed', 0)
      ->first();
      $db_result = CommonService::objectToArray($db_result);

      if (empty($db_result)) {
        throw new \Exception(Lang::get('無資料'));
      }

      $this->data['product_id'] = $request_data['id'];
      $this->data['productinfo_type_id'] = DB::table('productinfo_type')->where('product_id', $request_data['id'])->first()->id;
      $this->data['product_title'] = $db_result['title'];

      $this->data['box_layer'] = $db_result['layers'];
      $this->data['min_purchase'] = $db_result['min_purchase'];

      $layout_model = [];

      foreach (json_decode($db_result['layouts_allowed'], true) as $id) {
        $insert = [];

        $layout_info = DB::table('box_layout')->where('id', $id)->where('removed', 0)->first();
        $layout_info = CommonService::objectToArray($layout_info);

        if (empty($layout_info) == true) {
          throw new \Exception(Lang::get('無資料'));
        }

        $insert['id'] = $layout_info['id'];
        $insert['image'] = $layout_info['image_path'];

        $db_result = DB::table('box_layout_block')->where('layout_id', $id)->where('removed', 0)->get();
        $db_result = CommonService::objectToArray($db_result);

        if (empty($db_result) == true) {
          throw new \Exception(Lang::get('無資料'));
        }

        $blocks = [];

        foreach ($db_result as $block) {
          $block_info = [
            'width' => $block['width'],
            'height' => $block['height'],
            'left' => $block['left'],
            'top' => $block['top'],
            'items' => array_map(function($id) { return intval($id); }, json_decode($block['items'], true)),
          ];

          $blocks[] = $block_info;
        }

        $insert['blocks'] = array_values($blocks);

        $layout_model[] = $insert;
      }

      $this->data['layout_model'] = $layout_model;

      $content_model = [];

      $db_result = DB::table('box_content_cate')->where([['removed', '=', 0], ['online', '=', 1]])->orderBy('order')->orderBy('id')->get();
      $db_result = CommonService::objectToArray($db_result);

      if (empty($db_result) == true) {
        throw new \Exception(Lang::get('無資料'));
      }

      foreach ($db_result as $item) {
        $insert = [];

        $insert['id'] = $item['id'];
        $insert['name'] = $item['name'];

        $db_result = DB::table('box_content')
        ->select([
          'productinfo_type.id',
          'productinfo_type.title AS productinfo_type_title',
          'productinfo_type.pic_index',
          'productinfo_type.price',
          'productinfo_type.num',
          'productinfo.title AS productinfo_title',
          'productinfo.pic'
        ])
        ->rightJoin('productinfo_type', 'box_content.productinfo_type', '=', 'productinfo_type.id')
        ->rightJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
        ->where([
          ['box_content.removed', '=', 0],
          ['box_content.cate_id', '=', $item['id']],
          ['productinfo.online', '<', 2],
        ])
        ->orderBy('box_content.productinfo_type')
        ->get();
        $db_result = CommonService::objectToArray($db_result);

        if (empty($db_result) == true) {
          continue;
        }

        $insert['items'] = array_map(function($item) {
          $item_info = [];
          $item_info['id'] = $item['id'];
          $item_info['productinfo_type_title'] = $item['productinfo_type_title'];
          $item_info['price'] = $item['price'];
          $item_info['num'] = intval($item['num']);
          $item_info['productinfo_title'] = $item['productinfo_title'];
          $item_info['pic'] = __PUBLIC__ . json_decode($item['pic'], true)[$item['pic_index'] - 1];

          return $item_info;
        }, $db_result);

        $insert['item_ids'] = array_column($insert['items'], 'id');

        $content_model[] = $insert;
      }

      $this->data['content_model'] = $content_model;
    }
    catch (\Exception $e) {
      $this->error($e->getMessage());
    }

    return view('home.product.customized',['data' => $this->data]);
  }
}
