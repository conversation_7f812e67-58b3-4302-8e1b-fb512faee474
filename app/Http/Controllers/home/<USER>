<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
use ReflectionClass;

use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\pattern\ExaminationHelper;
use App\Services\pattern\MemberInstance;

class Examination extends PublicController
{
  public function __construct()
  {
    parent::__construct(request());
    $this->data['mebermenu_active'] = 'examination';

    $consent = DB::table('consent')->where("id",1)->first();
    $this->data['consent_other'] =  $consent->other;
    if(!session('admin.id')){
      $action = request()->post();
      // dump($action);exit;
      if( !$this->user && !in_array($action, [
          'tracking_registration', 
          'tracking_registration_view', 
          'examinee_panel', 
          'examinee_save', 
          'qrcode_roll_call', 
          'send_act_remind_mail',
        ])
      ){ 
        $this->error(Lang::get('請先登入會員'));
      };
    }
  }

  /*我的報名項目列表*/
  public function examination(){
    $re = $this->get_registered_examination();
    $this->data['re'] = $re;

    return view('home.examination.examination',['data'=>$this->data]);
  }
  /*報名項目的報名者列表*/
  public function examinee_list(){
    $type_id = request()->get('type_id');
    $post_data = [];
    $type_id_ori = explode('_', $type_id)[0];
    $post_data['search_cond'] = ['type_id'=> $type_id_ori];
    try {
      $examinee_list_tableView = ExaminationHelper::create_examinee_list_table_view($this->data, $type_id_ori, $order_id=-1, false, $post_data);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $reflection = new ReflectionClass($examinee_list_tableView);
    $property = $reflection->getProperty('data');
    $property->setAccessible(true);
    $examinee_list_tableView_data = $property->getValue($examinee_list_tableView);
    // dump($examinee_list_tableView_data);exit;
    $this->data['examinee_list_table'] = "{$examinee_list_tableView}";
    $this->data['examinees'] = $examinee_list_tableView_data['data']['examinees'];

    return view('home.examination.examinee_list', ['data'=>$this->data]);
  }
  /* 取得報名項目的報名者列表的報名者表格 (orderform_c、orderform_success) */
  public function examinee_list_table(){
    $type_id = request()->get('type_id');
    $post_data = [];
    $post_data['search_cond'] = [];
    $post_data['search_cond']['order_number'] = request()->get('order_number');
    try {
      $examinee_list_tableView = ExaminationHelper::create_examinee_list_table_view($this->data, $type_id, $order_id=null, $edit_able=null, $post_data);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $reflection = new ReflectionClass($examinee_list_tableView);
    $property = $reflection->getProperty('data');
    $property->setAccessible(true);
    $examinee_list_tableView_data = $property->getValue($examinee_list_tableView);
    // dump($examinee_list_tableView_data);exit;
    return "{$examinee_list_tableView}";
  }


  /* 取得報名者填寫頁面 */
  public function examinee_panel(){
    $post = request()->post();
    $post['backstage'] = false;
    return ExaminationHelper::create_examinee_panel_view($this->data, $post, $this->user['id']);
  }

  /*報名資料查詢頁面*/
  public function tracking_registration(){
    $g_process = DB::table('consent')->where("id",1)->first();

    $this->data['g_process'] = $g_process->g_process;
    return view('home.Examination.tracking_registration',['data'=>$this->data]);
  }
  /*報名資料查詢結果頁面*/
  public function tracking_registration_view(){
    $name = request()->post('name')?? "";
    if($name==""){ $this->error(Lang::get('請輸入姓名')); }
    $email = request()->post('email')?? "";
    if($email==""){ $this->error(Lang::get('請輸入信箱')); }
    
    /*找出某信箱的含報名項目(含報名資料)*/
    $re = $this->get_registered_examination($name, $email, $group_sql="");
    /*根據報名項目指派報名欄位*/
    foreach ($re as $key => $value) {
      $re[$key]['register_fields'] = ExaminationHelper::get_fields_by_prod_id($value['prod_id']);
    }
    $this->data['re'] = $re;

    // $email = isset($email) ? parent::hidestr($email,3,6) : '';
    $this->data['email'] = $email;

    $g_process = DB::table('consent')->where("id",1)->first();
    $this->data['g_process'] = $g_process->g_process;

    // dump($this->data);exit;
    return view('home.examination.tracking_registration_view',['data'=>$this->data]);
  }
  /*取得報名的品項們*/
  private function get_registered_examination($name="", $email="", $group_sql="ex.type_id"){
    $re = DB::table('examinee_info AS ex')
              ->select('ex.*', 'info.title AS in_title')
              ->join('productinfo AS info','info.id', '=', 'ex.prod_id')
              ->whereRaw("ex.order_id!=0");
    //排除取消訂單
    $not_include_cancel_order = ExaminationHelper::get_not_include_cancel_order_where();
    if($not_include_cancel_order != ''){
      $re = $re->whereRaw($not_include_cancel_order);
    }

    if($name=='' && $email==''){/*姓名跟信箱都未提供*/
      /*用登入的會員id查詢*/
      $re = $re->whereRaw("ex.user_id = '".$this->user['id']."'");
    }else{
      $re = $re->where('name', $name)->where('email', $email);
    }
    if($group_sql){
      $re = $re->groupBy($group_sql);
    }
    $re = $re->get();
    $re = CommonService::objectToArray($re);

    /*按商品id-品項原始id組合報名資料*/
    $re_group = [];
    $re_group_key = [];
    foreach ($re as $key => $value) {
      $type_id_ori = explode("_", $value['type_id'])[0];
      if( !in_array($value['prod_id'].'-'.$type_id_ori, $re_group_key) ){
        $value['type_id_ori'] = $type_id_ori;
        array_push($re_group, $value);
        array_push($re_group_key, $value['prod_id'].'-'.$value['type_id_ori']);
      }
    }

    /*找出各報名資料對應的品項*/
    foreach ($re_group as $key => $value) {
      $tinfo = DB::table('productinfo_type')->where("id", $value['type_id_ori'])->first();
      $tinfo = CommonService::objectToArray($tinfo);
      if($tinfo){
        $re_group[$key]['tinfo_id'] = $tinfo['id'];
        $re_group[$key]['area_title'] = $tinfo['title'];
        $re_group[$key]['act_time'] = $tinfo['act_time'];
      }else{
        $re_group[$key]['tinfo_id'] = "";
        $re_group[$key]['area_title'] = "";
        $re_group[$key]['act_time'] = "";
      }
    }

    /*重新排序，活動開始時間越小的越前*/
    usort($re_group, function($a, $b) { 
      return $a['act_time'] > $b['act_time'];
    });

    return $re_group;
  }


  /*新增/修改報名資料*/
  public function examinee_save(){
    try {
      $data = ExaminationHelper::examinee_save(false, $this->user['id'], request()->post());
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success(json_encode($data, JSON_UNESCAPED_UNICODE));
  }
  /*刪除報名資料*/
  public function examinee_delete(){
    $post = request()->post();
    $examinee_id = isset($post['examinee_id']) ? $post['examinee_id'] : "0";
    if($examinee_id=="0"){ $this->error(Lang::get('資料不完整')); }

    $my_examinee_info = ExaminationHelper::get_myself_type_examinee_info($type_id=0, $examinee_id, $order_id=0);
    if(count($my_examinee_info)==0){
      $this->error(Lang::get('資料有誤'));
    }

    $resault = DB::table('examinee_info')->where('id', $examinee_id)->delete();
    if($resault){
      $this->success(Lang::get('操作成功'));
    }else{
      $this->error(Lang::get('操作失敗'));
    }
  }

  /*AJAX 依商品ID取得報名欄位*/
  public function get_fields_by_prod_id(){
    $prod_id = request()->get('prod_id') ?? '';
    if($prod_id==""){ $this->error(Lang::get('資料不完整')); }
    $fields = ExaminationHelper::get_fields_by_prod_id($prod_id);
    return $fields;
  }


  /*寄送報名提醒信*/
  public function send_act_remind_mail(){
    $globalMailData = HelperService::getMailData();

    $db = DB::table('productinfo_type AS py');
    $productinfo_type = $db->select('py.*', 'p.title AS p_title', 'p.is_registrable', 'p.is_roll_call', 'p.remind_msg')
                          ->join('productinfo AS p', 'p.id', '=', 'py.product_id')
                          ->whereRaw('p.is_registrable=1')
                          ->whereRaw('py.closed=0 AND py.online=1 AND py.act_remind_time!=""')
                          ->orderByRaw('py.order_id ASC, py.id ASC')
                          ->get();
    $productinfo_type = CommonService::objectToArray($productinfo_type);
    // dump($db->getLastSql());
    $current_date = date("Y-m-d H");
    //排除取消訂單
    $not_include_cancel_order = ExaminationHelper::get_not_include_cancel_order_where();
    // dump($not_include_cancel_order);exit;
    $Memberinstance = new MemberInstance(0);
    foreach ($productinfo_type as $key => $value) {
      $value['act_remind_time'] = str_replace("T", " ", $value['act_remind_time']);
      $value['act_remind_time'] = substr($value['act_remind_time'], 0, 13);

      if($current_date==$value['act_remind_time']){
        dump('send type_id：'.$value['id']);
        $type_id = $value['id'];
        $examinee_info = DB::table('examinee_info AS ex')
                            ->whereRaw('ex.type_id like "'.$type_id.'_%"')
                            ->where('ex.cancel', 0)
                            ->whereRaw('ex.order_id!=0 AND ex.reg_status IN (1,2)');
        if($not_include_cancel_order != ''){ //排除取消訂單
          $examinee_info = $examinee_info->whereRaw($not_include_cancel_order);
        }
        $examinee_info = $examinee_info->get();
        $examinee_info = CommonService::objectToArray($examinee_info);
        // dump($type_id);
        // dump($examinee_info);exit;
        foreach ($examinee_info as $exk => $exv) {
          $exv['register_data'] = json_decode($exv['register_data'], true);
          $ex_name = ExaminationHelper::get_examinee_common_value($exv['id'], "1");

          $roll_code_img = ExaminationHelper::get_roll_code_img($value, $exv['id']);
          if($value['remind_msg']){
            $dom = new \domdocument();
            $dom->loadHtml(mb_convert_encoding($value['remind_msg'],'HTML-ENTITIES','UTF-8'));
            $images = $dom->getelementsbytagname('img');
            foreach($images as $k => $img){
              $img_src = $img->getattribute('src');
              if (!preg_match('/https?:\/\//', $img_src)){
                $img->setattribute('src', request()->server('REQUEST_SCHEME').'://'.request()->server('HTTP_HOST').$img_src);
              }//if
            }//foreach

            $value['remind_msg'] = $dom->savehtml($dom);
          }

          $register_remind_letter = Lang::get('menu.活動提醒信消費者');
          $register_remind_letter = str_replace("{ex_name}", $ex_name, $register_remind_letter);
          $register_remind_letter = str_replace("{show_product_name}", $value['p_title']." ".$value['title'], $register_remind_letter);
          $register_remind_letter = str_replace("{date_time}", str_replace("T", " ", $value['act_time']), $register_remind_letter);
          $register_remind_letter = str_replace("{remind_msg}", $value['remind_msg'], $register_remind_letter);
          $register_remind_letter = str_replace("{roll_code_img}", $roll_code_img, $register_remind_letter);
          $mailBody = "
            <html>
              <head></head>
              <body>
                <div>
                  ".$register_remind_letter."
                </div>
                <div>
                  ". $globalMailData['system_email']['act_remind'] ."
                </div>
                <div style='color:red;'>
                  ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
                </div>
              </body>
            </html>
          ";
          $email = $exv['email'];
          $regex = '/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$/'; 
          if(!preg_match($regex, $email)){
            $Memberinstance->change_user_id($exv['user_id']);
            $user_data = $Memberinstance->get_user_data();
            $email = $user_data['email'] ?? '';
          }
          if($email){
            // dump($mailBody);dump($email);exit;
            $result = HelperService::Mail_Send($mailBody, 'client', $email, Lang::get('活動提醒'));
            $converted_res = $result ? 'true' : 'false';
            dump($ex_name.':'.$email."=>".$converted_res);
          }else{
            dump($ex_name.':'.$email."=>".'無寄送信箱');
          }
        }
      }
    }
    dump('end');
  }

  // /*用於合成准考證的頁面(目前無用)*/
  public function personal_show(){
    // $id = request()->get('id');
    // $personal = DB::table('examinee_info as e')
    //                 ->select('e.*','type.title','info.title as in_title')
    //                 ->join('productinfo_type as type','type.id', '=', 'e.type_id')
    //                 ->join('productinfo as info','info.id', '=', 'e.prod_id')
    //                 ->whereRaw('e.id="'.$id.'"')
    //                 ->get();
    // $personal = CommonService::objectToArray($personal);
    // array_walk($personal, function($item, $key)use(&$personal){
    //   $personal[$key]  = str_replace('&nbsp;', '<sws></sws>', htmlentities($item));
    // });
    // // dump($personal);
    // $this->data['p', $personal);

    // return view('home.examination.personal_show',['data'=>$this->data]);
  }
}

