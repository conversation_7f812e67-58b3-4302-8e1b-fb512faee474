<?php
namespace App\Http\Controllers\home;
use App\Http\Controllers\home\GlobalController;
use App\Services\CommonService;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Http\Request;

use App\Services\pattern\HelperService;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;

class PublicController extends GlobalController {
  protected $user;
  protected $kol;
  protected $index_excel;

  public function __construct()
  {
    parent::__construct();
    if(!defined('__PUBLIC__')) define('__PUBLIC__', '/public/static/index');
    if(!defined('__UPLOAD__')) define('__UPLOAD__', '/public/static/index');

    $this->data['close_nav'] = false;
    //auth system
    $this->user = session('user');
    $this->user["id"] = $this->user["id"] ?? 0;
    if($this->user["id"]==0){
      $this->user = [
        'id'		=> '0',
        'name'		=> '',
        'email'		=> '',
        'phone'		=> '',
        'tele'		=> '',
        'permission'=> '',
        'user_type' => '0',
        'product_view_id'=> '1',
      ];
    }
    $this->data['user'] = $this->user;

    $Proposal = Proposal::withTeamMembersAndRequire(
      ['GetCartData'],
      [
        'cart_session' => 'cart_all',
        'user_id' => $this->user["id"],
      ]
    );
    $Proposal = MemberFactory::createNextMember($Proposal);
    /* 檢查購物車 */
    $check_cart = json_decode($Proposal->projectData['data'],true);
    $check_cart = json_encode($check_cart);
    $Proposal->projectData['data'] = $check_cart;
    /*檢查購物車 */
    $this->data['cartCount'] = sizeof(json_decode($Proposal->projectData['data'], true));

    // 檢查網紅登入狀態
    $this->kol = session('kol');
    if($this->kol){
      $this->data['kol_logined'] = 'true';
    }else{
      $this->data['kol_logined'] = 'false';
    }

    $index_online = DB::table('index_online')->where('id',1)->first();
    $this->data['index_online'] = CommonService::objectToArray($index_online);

    $index_excel = DB::table('index_excel')->orderBy('id','asc')->get();
    $this->index_excel = CommonService::objectToArray($index_excel);
    $this->data['index_excel'] = $this->index_excel;

    $seo = DB::table('seo')->get();
    $this->data['seo'] = CommonService::objectToArray($seo);

    $stronghold = DB::table('stronghold')->select('id')->orderByRaw('order_id asc, id desc')->first();
    $stronghold = CommonService::objectToArray($stronghold);
    if(!$stronghold){
      $stronghold = ['id'=>0];
    }
    $this->data['stronghold'] = $stronghold;

    $closeAdSide = session('closeAdSide');
    $this->data['closeAdSide'] = $closeAdSide;

    // 選單有顯示的分館
    $recommend_product_nav = $this->getProductMenu("home");
    //dump($recommend_product_nav);exit;
    $this->data['recommend_product_nav'] = $recommend_product_nav;
  }

  public function getProductMenu($action="product", $id="", $distributor_id=0) {
    $action = $action ? $action : "product";
    $id = $id ? $id : "";

    if($action=="product" || $action=="home" || ($action=="typeinfo" && $id=="") ){ /*第一層是分館*/
      $productMenu = DB::table('product')
                        ->select('title', 'id', 'recommend', 'pic_icon as pic')
                        ->where('online', 1)
                        ->where('distributor_id', $distributor_id)
                        ->orderbyRaw('order_id asc, id asc');
      if($action=="home"){
        $productMenu = $productMenu->where('show_on_nav', 1);
      }
      if($id!=""){
        $productMenu = $productMenu->where('id',$id);
      }
      $productMenu = $productMenu->get();
    }
    else{ /*第一層是分類*/
      $productMenu = DB::table('typeinfo')
                        ->select('title', 'id', 'pic')
                        ->orderbyRaw('order_id asc, id asc')
                        ->where('online', 1)
                        ->where('id',$id)
                        ->get();
    }
    $productMenu = CommonService::objectToArray($productMenu);

    foreach ($productMenu as $key => $vo) {
      $productMenu[$key]['action'] = $action;

      $recommend_product = "";
      if(isset($vo['recommend'])){
        $recommend_product = $vo['recommend'];
      }

      if($recommend_product!=""){ /*顯示商品*/
        $productMenu[$key]['subType'] = DB::table('productinfo as p')
                                          ->select('p.title', 'p.id')
                                          ->leftJoin('productinfo_orders AS po', 'po.prod_id','p.id')
                                          ->whereRaw("p.id in (".$recommend_product.") AND po.prev_id='".$vo['id']."'")
                                          ->where('online', 1)
                                          ->where('distributor_id', $distributor_id)
                                          ->orderbyRaw('po.order_id asc, p.id desc')
                                          //->limit(4)
                                          ->get();
        $productMenu = CommonService::objectToArray($productMenu);
        foreach ($productMenu[$key]['subType'] as $key2 => $vo2) {
          $productMenu[$key]['subType'][$key2]["action"] = 'productinfo';
          $productMenu[$key]['subType'][$key2]['subType'] = []; /*沒有下一階層*/
        }
      }
      else{ /*顯示分類*/
        if($action=='product' || $action=="home"){
          $parent_where = "parent_id='".$vo['id']. "' AND branch_id = 0 ";
        }else{
          $parent_where = "branch_id='".$vo['id']."' ";
        }

        $productMenu[$key]['subType'] =DB::table('typeinfo')
                                          ->select('title', 'id', 'pic')
                                          ->whereRaw($parent_where."
                                                    AND (
                                                      end <= 0
                                                      OR
                                                      (start < " . time() . " AND end > " . time() . ")
                                                    )")
                                          ->where('online',1)
                                          ->where('distributor_id', $distributor_id)
                                          ->orderByRaw('order_id')
                                          //->limit(4)
                                          ->get();
        $productMenu = CommonService::objectToArray($productMenu);														
        foreach ($productMenu[$key]['subType'] as $key2 => $vo2) {
          if(mb_strlen($productMenu[$key]['subType'][$key2]["title"], 'utf8') > 15){
            $productMenu[$key]['subType'][$key2]["title"] = mb_substr($productMenu[$key]['subType'][$key2]["title"],0,15,'utf8').'…';
          }
          $productMenu[$key]['subType'][$key2]["action"] = 'typeinfo';

          $productMenu[$key]['subType'][$key2]['subType'] = DB::table('typeinfo')
                                                              ->select('title', 'id','pic')
                                                              ->whereRaw('branch_id = ' . $vo2['id'] . "
                                                                          AND online = 1
                                                                          AND (
                                                                            end <= 0
                                                                            OR
                                                                            (start < " . time() . " AND end > " . time() . ")
                                                                          )")
                                                              ->where('online', 1)
                                                              ->where('distributor_id', $distributor_id)
                                                              ->orderByRaw('order_id')
                                                              ->get();
            
        }
      }
    }
    $productMenu = CommonService::objectToArray($productMenu);
    // dd($productMenu);
    return $productMenu;
  }


  public function dumpException(\Exception $e) {
    if(config('app.debug')){
      $this->data['waitSecond'] = 5;
      $this->error(Lang::get('操作失敗').'：'.$e->getMessage());
    }else{
      $this->data['waitSecond'] = 1;
      $this->error(Lang::get('操作失敗'));
    }
  }

  public function dumpError($errorMessage) {
    if(config('app.debug')){
      $this->data['waitSecond'] = 5;
    }else{
      $this->data['waitSecond'] = 1;
    }
    $this->error(Lang::get('操作失敗').'：'.$errorMessage);
  }

  public function product_species($id) {
    $expiring_product = DB::table('expiring_product')->select('product_id')->where("product_id",$id)->count();
    $hot_product = DB::table('hot_product')->select('product_id')->where("product_id",$id)->count();
    $recommend_product = DB::table('recommend_product')->select('product_id')->where("product_id",$id)->count();
    $spec_product = DB::table('spe_price_product')->select('product_id')->where("product_id",$id)->count();

    $expiring_product_name='';
    $expiring_product_type=0;
    $hot_product_name='';
    $hot_product_type=0;
    $recommend_product_name='';
    $recommend_product_type=0;
    $spec_product_name='';
    $spec_product_type=0;

    //設定標籤名稱
    $tag = DB::table('frontend_data_name')->where('show_type',"tag")->orderByRaw('id asc')->get();
    $tag = CommonService::objectToArray($tag);

    // 人氣商品
    if($hot_product>0){
      $hot_product_name='<div class="act_hot"><div class="cancel_crown"></div>'.$tag[0]['name'].'</div>';
      $hot_product_type=1;
    }

    // 店長推薦
    if($recommend_product>0){
      $recommend_product_name='<div class="act_rec"><div class="cancel_crown"></div>'.$tag[1]['name'].'</div>';
      $recommend_product_type=1;
    }

    // 即期良品
    if($expiring_product>0){
      $expiring_product_name='<div class="act_exp"><div class="cancel_crown"></div>'.$tag[2]['name'].'</div>';
      $expiring_product_type=1;
    }

    // 特價商品
    if($spec_product>0){
      $spec_product_name='<div class="act_exp"><div class="cancel_crown"></div>'.$tag[3]['name'].'</div>';
      $spec_product_type=1;
    }

    $this->data['hot_product'] = $hot_product_name;
    $this->data['hot_product_type'] =  $hot_product_type;
    $this->data['recommend_product'] = $recommend_product_name;
    $this->data['recommend_product_type'] = $recommend_product_type;
    $this->data['expiring_product'] = $expiring_product_name;
    $this->data['expiring_product_type'] = $expiring_product_type;
    $this->data['spec_product'] = $spec_product_name;
    $this->data['spec_product_type'] = $spec_product_type;
  }

  public function isMobileCheck(){
    //Detect special conditions devices
    $iPod = stripos(request()->server('HTTP_USER_AGENT'),"iPod");
    $iPhone = stripos(request()->server('HTTP_USER_AGENT'),"iPhone");
    $iPad = stripos(request()->server('HTTP_USER_AGENT'),"iPad");

    if(stripos(request()->server('HTTP_USER_AGENT'),"Android") && stripos(request()->server('HTTP_USER_AGENT'),"mobile")){
      $Android = true;
    }else if(stripos(request()->server('HTTP_USER_AGENT'),"Android")){
      $Android = false;
      $AndroidTablet = true;
    }else{
      $Android = false;
      $AndroidTablet = false;
    }

    $webOS = stripos(request()->server('HTTP_USER_AGENT'),"webOS");
    $BlackBerry = stripos(request()->server('HTTP_USER_AGENT'),"BlackBerry");
    $RimTablet= stripos(request()->server('HTTP_USER_AGENT'),"RIM Tablet");

    //do something with this information
    if( $iPod || $iPhone || $iPad || $Android || $AndroidTablet || $webOS || $BlackBerry || $RimTablet){
      return true;
    }else{
      return false;
    }
  }

  // 文字星號隱藏
  public function hidestr($string, $start = 0, $length = 0, $re = '*') {
    return HelperService::hidestr($string, $start, $length, $re);
  }

  /*主機發送請求*/
  static public function send_requset($url, $data=null, $headers=null){
    return HelperService::http_request($url, $data, $headers);
  }

  public static function set_visitor_record(Request $request, $product_id = 0) {
    $ip = $request->ip();
    $today = now()->toDateString();

    $alreadyLogged = DB::connection('main_db')->table('visitor_logs')->where('ip', $ip)->where('product_id', $product_id)->whereDate('created_at', $today)->exists();

    if (!$alreadyLogged) {
      DB::connection('main_db')->table('visitor_logs')->insert([
        'product_id' => $product_id,
        'ip' => $ip,
        'created_at' => now(),
      ]);
    }
  }
}
