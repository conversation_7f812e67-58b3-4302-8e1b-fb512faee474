<?php
namespace App\Http\Controllers\home;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\PointRecords;

class Points extends PublicController{
  public function __construct() {
    parent::__construct(request());
    $this->data['mebermenu_active'] = 'points';

    $consent = DB::table('consent')->where("id",1)->first();
    $this->data['consent_other'] = $consent->other;

    if($this->user["id"]==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};
  }

  public function set_point_expire() {
    $PointRecords = new PointRecords($this->user['id']);
    $PointRecords->set_point_expire();
  }

  public function points() {
    if($this->user == null){
      $this->redirect(url('Index/index'));
    }
    
    $PointRecords = new PointRecords($this->user['id']);
    $records = $PointRecords->get_user_records();
    $this->data['records'] =  $records;

    $expiring_points = $PointRecords->get_expiring_points();
    $this->data['expiring_points'] = $expiring_points;

    $current_points = $PointRecords->get_current_points();
    $this->data['current_points'] = $current_points;

    $consent = DB::table('consent')->select('point')->first();
    $this->data['consent']=$consent->point;

    return view('home.points.points',['data'=> $this->data]);
  }
}