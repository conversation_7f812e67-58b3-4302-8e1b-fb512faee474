<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use chillerlan\QRCode\QRCode;

//Photonic Class
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;
use App\Services\pattern\MemberInstance;

class Member extends PublicController
{
  private $DBFileConnecter;

  public function __construct(){
    $request = request();
    parent::__construct($request);
    $this->data['mebermenu_active'] = 'member';

    $consent = DB::table('consent')->whereRaw("id=1")->first();
    $this->data['consent_other'] = $consent->other;
    // dd($this->user);
    if($this->user['id']==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};
  }

  public function member(){
    $MemberInstance = new MemberInstance(session()->get('user.id'));
    $userD = $MemberInstance->get_user_data($addr_change="split");
    $this->data['userD'] = $userD;

    $account_column = $MemberInstance::$account_column;
    $this->data['account_column'] = $account_column;

    $city = DB::table('city')->get();
    $this->data['city'] = CommonService::objectToArray($city);

    return view('home.member.member',['data'=> $this->data]);
  }

  /*修改會員密碼*/
  public function chpwd(){
    $id = request()->post('id');
    $updateData = [
      'password' => request()->post('password'),
      'passwordB' => request()->post('passwordB'),
    ];

    $MemberInstance = new MemberInstance($id);
    $returnData = $MemberInstance->update_user_data($updateData);
    if($returnData['code'] == 0){ $this->error($returnData['msg']); }
    session()->put('user', $returnData['data']);

    $this->success(Lang::get('操作成功'));
  }
  /*修改會員資料*/
  public function chdata(){
    $data = request()->post();

    $MemberInstance = new MemberInstance($this->user['id']);

    $returnData = $MemberInstance->update_user_data($data);
    if($returnData['code'] == 0){ $this->error($returnData['msg']); }
    session()->put('user', $returnData['data']);

    $this->success(Lang::get('操作成功'));
  }

  /*我的註冊商品*/
  public function reg_product(){
    if(!empty(config('control.close_function_current')['註冊商品回函'])){
      $this->error(Lang::get('查無此頁'));
    }
    $this->data['mebermenu_active'] = 'reg_product';
    $userD = DB::table('excel')
                ->where("account_number",$this->user['id'])
                ->orderByRaw('id desc')
                ->paginate(10);
    $this->data['userD'] = $userD;

    return view('home.member.reg_product',['data'=> $this->data]);
  }

  /* 我的收藏商品 */
  public function product_store(){
    $this->data['mebermenu_active'] = 'product_store';
    $products = DB::table('product_store as ps')
                  ->select('p.*')
                  ->leftJoin('productinfo as p', 'p.id', '=', 'ps.product_id')
                  ->whereRaw("ps.user_id ='".$this->user['id']."'")
                  ->whereRaw("p.online !=2")	/*不是關閉的*/
                  ->orderByRaw('ps.id desc')
                  ->paginate(20);
    if (empty($products) == false) {
      foreach ($products->items() as $item) {
        $item->pic = json_decode($item->pic, true)[0];
      }
    }
    $this->data['products'] = $products;

    return view('home.member.product_store',['data'=> $this->data]);
  }

  /*招募EDM管理*/
  public function share_content(){
    if(!config('control.control_down_line')){
      $this->error(Lang::get('查無此頁'));
    }
    $this->data['mebermenu_active'] = 'share_content';

    $userD = DB::connection('main_db')->table('account')->find($this->user['id']);
    $userD = CommonService::objectToArray($userD);
    $userD['share_link'] = url('Login/signup').'?' . http_build_query(['recommend' => $userD['number']]);
    $userD['share_link_qrcode'] = (new QRCode)->render($userD['share_link']);
    $this->data['userD'] = $userD;
    // dd($userD);

    return view('home.member.share_content',['data'=> $this->data]);
  }
  public function update_share_content(){
    $recommend_content = request()->post('recommend_content');
    $config = \HTMLPurifier_Config::createDefault();
    $purifier = new \HTMLPurifier($config);
    $recommend_content = $purifier->purify($recommend_content);
    $data = [
      'recommend_content' => $recommend_content,
      'share_title' => htmlspecialchars(request()->post('share_title')),
      'share_text' => htmlspecialchars(request()->post('share_text')),
    ];
    // dd($data);
    $share_pic = request()->file('share_pic');
    if($share_pic){
      $this->DBFileConnecter = DBFileConnecter::withTableName('account');
      $share_pic = $this->DBFileConnecter->fixedFileUp($share_pic, 'account_' . 'share_pic_' . $this->user['id']);
      if($share_pic) $data['share_pic'] = "/public/static/index" . $share_pic;
    }

    try{
      DB::connection('main_db')->table('account')->whereRaw('id='.$this->user['id'])->update($data);			
    } catch (\Exception $e){dd($e->getMessage());
      $this->error(Lang::get('操作失敗'));
    }

    $this->success(Lang::get('操作成功'));
  }

  /*招募會員列表*/
  public function down_line(){
    if(!config('control.control_down_line')){
      $this->error(Lang::get('查無此頁'));
    }
    $this->data['mebermenu_active'] = 'down_line';

    $type = !empty(request()->get('type')) ? request()->get('type') : 'all';
    $search = !empty(request()->get('search')) ? request()->get('search') : '';

    $down_lines = DB::connection('main_db')->table('account')
                                          ->select('id','name','number','createtime','vip_type')
                                          ->whereRaw('upline_user='.$this->user['id'])
                                          ->whereRaw('name like "%'.$search.'%"');
    if($type=='normal'){
      $down_lines = $down_lines->whereRaw('vip_type=0');
    }else if($type=='vip'){
      $down_lines = $down_lines->whereRaw('vip_type=1');
    }
    // else if($type=='vip_ed'){
    //   $down_lines = $down_lines->where('(vip_type=1 and vip_end_time < "'.date('Y-m-d').'") or
    //     vip_type=0 and vip_end_time != ""
    //   ');
    // }
    $MemberInstance = new MemberInstance(0);
    $down_lines = $down_lines->orderByRaw('id desc')
                            ->paginate(25)
                            ->appends([
                              'type' => $type,
                              'search' => $search,
                            ]);
    if (empty($down_lines) == false) {
      foreach ($down_lines->items() as $key => $item) {
        $MemberInstance->change_user_id($item->id);
        $userD = $MemberInstance->get_user_data($addr_change="split");
        $down_lines[$key] = $userD;
      }
    }
    $this->data['down_lines'] = $down_lines;
    $this->data['type'] = $type;
    $this->data['search'] = $search;
    // dump($down_lines);

    return view('home.member.down_line',['data'=> $this->data]);
  }
}
