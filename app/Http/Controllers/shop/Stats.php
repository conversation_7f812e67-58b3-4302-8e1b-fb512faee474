<?php

namespace App\Http\Controllers\shop;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\admin\MainController;

// Photonic Class
use App\Services\CommonService;

class Stats extends MainController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        return view('shop.stats.index', ['data' => $this->data]);
    }

    public function get_data(Request $request)
    {
        $response = [
            'status' => 200,
            'message' => '資料更新成功',
            'data' => [],
        ];

        try {
            if (empty($request->all()) == true) {
                throw new \Exception('未輸入條件');
            }

            if (array_key_exists('scale', $request->all()) == false) {
                throw new \Exception('條件錯誤');
            }

            $scale = intval($request->post('scale'));
            $table_rank_length = intval($request->post('table_rank_length', 5));

            if (in_array($scale, [0, 1, 7, 30]) == false) {
                throw new \Exception('條件錯誤');
            }

            $response['data'] = [
                'chart' => [
                    'data_setting' => [
                        'labels' => [],
                    ],
                    'raw_data' => [
                        'order_total_main' => [],
                        'order_total_sub' => [],
                        'order_count_main' => [],
                        'order_count_sub' => [],
                        'visitors_main' => [],
                        'visitors_sub' => [],
                        'new_members_main' => [],
                        'new_members_sub' => [],
                    ],
                ],
                'table' => [
                    'raw_data' => [
                        'sale_table' => [],
                        'view_table' => [],
                    ],
                ],
            ];

            if ($scale == 0) {
                $now = new \DateTime();
                $now->modify('-1 hour');
                $end_hour = intval($now->format('H'));

                for ($i = 0; $i <= $end_hour; $i++) {
                    $current_hour = str_pad($i, 2, '0', STR_PAD_LEFT);

                    $response['data']['chart']['data_setting']['labels'][] = $current_hour . ':00';

                    $response['data']['chart']['raw_data']['order_total_main'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d') . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d') . ' ' . $current_hour . ':59:59')])->sum('total');
                    $response['data']['chart']['raw_data']['order_total_sub'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59')])->sum('total');

                    $response['data']['chart']['raw_data']['order_count_main'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d') . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d') . ' ' . $current_hour . ':59:59')])->count();
                    $response['data']['chart']['raw_data']['order_count_sub'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59')])->count();

                    $response['data']['chart']['raw_data']['visitors_main'][] = DB::connection('main_db')->table('visitor_logs')->whereBetween('created_at', [date('Y-m-d') . ' ' . $current_hour . ':00:00', date('Y-m-d') . ' ' . $current_hour . ':59:59'])->where('product_id', 0)->count();
                    $response['data']['chart']['raw_data']['visitors_sub'][] = DB::connection('main_db')->table('visitor_logs')->whereBetween('created_at', [date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00', date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59'])->where('product_id', 0)->count();

                    $response['data']['chart']['raw_data']['new_members_main'][] = DB::connection('main_db')->table('account')->whereBetween('createtime', [strtotime(date('Y-m-d') . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d') . ' ' . $current_hour . ':59:59')])->count();
                    $response['data']['chart']['raw_data']['new_members_sub'][] = DB::connection('main_db')->table('account')->whereBetween('createtime', [strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59')])->count();
                }

                $ranking = $this->get_ranking($scale, $table_rank_length);

                for ($i = 0; $i < $table_rank_length; $i++) {
                    $response['data']['table']['raw_data']['sale_table'][] = [$i + 1, '<img class="ranking" src="' . $ranking['sale'][$i]['image_path'] . '">', str_replace('\\"', '"', $ranking['sale'][$i]['title']), $ranking['sale'][$i]['amount']];
                    $response['data']['table']['raw_data']['view_table'][] = [$i + 1, '<img class="ranking" src="' . $ranking['view'][$i]['image_path'] . '">', str_replace('\\"', '"', $ranking['view'][$i]['title']), $ranking['view'][$i]['amount']];
                }
            } else if ($scale == 1) {
                $end_hour = 23;

                for ($i = 0; $i <= $end_hour; $i++) {
                    $current_hour = str_pad($i, 2, '0', STR_PAD_LEFT);

                    $response['data']['chart']['data_setting']['labels'][] = $current_hour . ':00';

                    $response['data']['chart']['raw_data']['order_total_main'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59')])->sum('total');
                    $response['data']['chart']['raw_data']['order_total_sub'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':59:59')])->sum('total');

                    $response['data']['chart']['raw_data']['order_count_main'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59')])->count();
                    $response['data']['chart']['raw_data']['order_count_sub'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':59:59')])->count();

                    $response['data']['chart']['raw_data']['visitors_main'][] = DB::connection('main_db')->table('visitor_logs')->whereBetween('created_at', [date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00', date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59'])->where('product_id', 0)->count();
                    $response['data']['chart']['raw_data']['visitors_sub'][] = DB::connection('main_db')->table('visitor_logs')->whereBetween('created_at', [date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':00:00', date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':59:59'])->where('product_id', 0)->count();

                    $response['data']['chart']['raw_data']['new_members_main'][] = DB::connection('main_db')->table('account')->whereBetween('createtime', [strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-1 day')) . ' ' . $current_hour . ':59:59')])->count();
                    $response['data']['chart']['raw_data']['new_members_sub'][] = DB::connection('main_db')->table('account')->whereBetween('createtime', [strtotime(date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':00:00'), strtotime(date('Y-m-d', strtotime('-2 day')) . ' ' . $current_hour . ':59:59')])->count();
                }

                $ranking = $this->get_ranking($scale, $table_rank_length);

                for ($i = 0; $i < $table_rank_length; $i++) {
                    $response['data']['table']['raw_data']['sale_table'][] = [$i + 1, '<img class="ranking" src="' . $ranking['sale'][$i]['image_path'] . '">', str_replace('\\"', '"', $ranking['sale'][$i]['title']), $ranking['sale'][$i]['amount']];
                    $response['data']['table']['raw_data']['view_table'][] = [$i + 1, '<img class="ranking" src="' . $ranking['view'][$i]['image_path'] . '">', str_replace('\\"', '"', $ranking['view'][$i]['title']), $ranking['view'][$i]['amount']];
                }
            } else {
                for ($i = $scale; $i >= 1; $i--) {
                    $current_date = date('Y-m-d', strtotime("-$i day"));
                    $date_arr = explode('-', $current_date);
                    $current_year = intval($date_arr[0]);
                    $current_month = intval($date_arr[1]);
                    $current_day = intval($date_arr[2]);

                    $response['data']['chart']['data_setting']['labels'][] = $current_date;

                    $response['data']['chart']['raw_data']['order_total_main'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime($current_date . ' 00:00:00'), strtotime($current_date . ' 23:59:59')])->sum('total');
                    $response['data']['chart']['raw_data']['order_count_main'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime($current_date . ' 00:00:00'), strtotime($current_date . ' 23:59:59')])->count();
                    $response['data']['chart']['raw_data']['visitors_main'][] = DB::connection('main_db')->table('customer_report')->where('year', $current_year)->where('month', $current_month)->where('day', $current_day)->where('product_id', 0)->sum('amount');
                    $response['data']['chart']['raw_data']['new_members_main'][] = DB::connection('main_db')->table('account')->whereBetween('createtime', [strtotime($current_date . ' 00:00:00'), strtotime($current_date . ' 23:59:59')])->count();
                }

                for ($i = $scale * 2; $i >= $scale + 1; $i--) {
                    $current_date = date('Y-m-d', strtotime("-$i day"));
                    $date_arr = explode('-', $current_date);
                    $current_year = intval($date_arr[0]);
                    $current_month = intval($date_arr[1]);
                    $current_day = intval($date_arr[2]);

                    $response['data']['chart']['raw_data']['order_total_sub'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime($current_date . ' 00:00:00'), strtotime($current_date . ' 23:59:59')])->sum('total');
                    $response['data']['chart']['raw_data']['order_count_sub'][] = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime($current_date . ' 00:00:00'), strtotime($current_date . ' 23:59:59')])->count();
                    $response['data']['chart']['raw_data']['visitors_sub'][] = DB::connection('main_db')->table('customer_report')->where('year', $current_year)->where('month', $current_month)->where('day', $current_day)->where('product_id', 0)->sum('amount');
                    $response['data']['chart']['raw_data']['new_members_sub'][] = DB::connection('main_db')->table('account')->whereBetween('createtime', [strtotime($current_date . ' 00:00:00'), strtotime($current_date . ' 23:59:59')])->count();
                }

                $ranking = $this->get_ranking($scale, $table_rank_length);

                for ($i = 0; $i < $table_rank_length; $i++) {
                    $response['data']['table']['raw_data']['sale_table'][] = [$i + 1, '<img class="ranking" src="' . $ranking['sale'][$i]['image_path'] . '">', str_replace('\\"', '"', $ranking['sale'][$i]['title']), $ranking['sale'][$i]['amount']];
                    $response['data']['table']['raw_data']['view_table'][] = [$i + 1, '<img class="ranking" src="' . $ranking['view'][$i]['image_path'] . '">', str_replace('\\"', '"', $ranking['view'][$i]['title']), $ranking['view'][$i]['amount']];
                }
            }
        } catch (\Exception $e) {
            $response['status'] = 400;
            $response['message'] = $e->getMessage();

            return $response;
        }

        return $response;
    }

    public function get_ranking($scale, $table_rank_length)
    {
        $ranking = [
            'sale' => [],
            'view' => [],
        ];

        $item = ['image_path' => '', 'title' => '', 'amount' => 0];

        for ($i = 0; $i < $table_rank_length; $i++) {
            $ranking['sale'][$i] = $item;
            $ranking['view'][$i] = $item;
        }

        $ranking = $this->get_sale_ranking($scale, $table_rank_length, $ranking);
        $ranking = $this->get_view_ranking($scale, $table_rank_length, $ranking);

        return $ranking;
    }

    public function get_sale_ranking($scale, $table_rank_length, $ranking)
    {
        if ($scale == 0) {
            $orders = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d')), strtotime(date('Y-m-d') . ' 23:59:59')])->get();
            $orders = CommonService::objectToArray($orders);
        } else if ($scale == 1) {
            $orders = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime('-1 day'))), strtotime(date('Y-m-d', strtotime('-1 day')) . ' 23:59:59')])->get();
            $orders = CommonService::objectToArray($orders);
        } else if ($scale > 1) {
            $orders = DB::connection('main_db')->table('orderform')->where('distributor_id', 0)->whereNotIn('status', ['Cancel', 'Return'])->whereBetween('create_time', [strtotime(date('Y-m-d', strtotime("-$scale day"))), strtotime(date('Y-m-d', strtotime("-1 day")) . ' 23:59:59')])->get();
            $orders = CommonService::objectToArray($orders);
        }

        if (empty($orders) == false) {
            $ordered_products = [];

            foreach ($orders as $order) {
                $products = json_decode($order['product'], true);

                if (isset($products[0]['info_id']) == false) {
                    continue;
                }

                $first_product_box_info = DB::table('productinfo')->select(['is_box', 'is_customized_box'])->where('id', $products[0]['info_id'])->first();
                $first_product_box_info = CommonService::objectToArray($first_product_box_info);

                if (empty($first_product_box_info) == false) {
                    if ($first_product_box_info['is_box'] == 1 && $first_product_box_info['is_customized_box'] == 1) {
                        if (array_key_exists($products[0]['type_id'], $ordered_products) == false) {
                            $ordered_products[$products[0]['type_id']] = $products[0]['num'];
                        } else {
                            $ordered_products[$products[0]['type_id']] += $products[0]['num'];
                        }
                    } else {
                        foreach ($products as $product) {
                            if (isset($product['info_id'])) {
                                if (array_key_exists($product['type_id'], $ordered_products) == false) {
                                    $ordered_products[$product['type_id']] = $product['num'];
                                } else {
                                    $ordered_products[$product['type_id']] += $product['num'];
                                }
                            }
                        }
                    }
                }
            }

            arsort($ordered_products);

            $index = 0;

            foreach ($ordered_products as $type_id => $amount) {
                $productinfo_type = DB::table('productinfo_type')
                    ->select(['pic_index', 'productinfo_type.title AS productinfo_type_title', 'pic', 'productinfo.title AS productinfo_title'])
                    ->rightJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
                    ->where('productinfo_type.id', $type_id)->first();
                $productinfo_type = CommonService::objectToArray($productinfo_type);

                if (empty($productinfo_type) == false) {
                    $ranking['sale'][$index] = [
                        'image_path' => '/public/static/index/' . json_decode($productinfo_type['pic'], true)[$productinfo_type['pic_index'] - 1],
                        'title' => $productinfo_type['productinfo_type_title'] ? $productinfo_type['productinfo_title'] . '-' . $productinfo_type['productinfo_type_title'] : $productinfo_type['productinfo_title'],
                        'amount' => $amount,
                    ];

                    $index++;

                    if ($index >= $table_rank_length) {
                        break;
                    }
                }
            }
        }

        return $ranking;
    }

    public function get_view_ranking($scale, $table_rank_length, $ranking)
    {
        if ($scale == 0) {
            $product_views = DB::connection('main_db')->table('visitor_logs')->select(['product_id', DB::raw('count(*) as count')])->whereBetween('created_at', [date('Y-m-d 00:00:00'), date('Y-m-d') . ' 23:59:59'])->where('product_id', '!=', 0)->groupBy('product_id')->get();
            $product_views = CommonService::objectToArray($product_views);
        } else if ($scale == 1) {
            $product_views = DB::connection('main_db')->table('visitor_logs')->select(['product_id', DB::raw('count(*) as count')])->whereBetween('created_at', [date('Y-m-d 00:00:00', strtotime('-1 day')), date('Y-m-d', strtotime('-1 day')) . ' 23:59:59'])->where('product_id', '!=', 0)->groupBy('product_id')->get();
            $product_views = CommonService::objectToArray($product_views);
        } else if ($scale > 1) {
            $product_views = DB::connection('main_db')->table('customer_report')->select(['product_id', DB::raw('sum(amount) as count')])->whereBetween('created_at', [date('Y-m-d 00:00:00', strtotime("-" . ($scale - 1) . " day")), date('Y-m-d') . ' 23:59:59'])->where('product_id', '!=', 0)->groupBy('product_id')->get();
            $product_views = CommonService::objectToArray($product_views);
        }

        if (empty($product_views) == false) {
            $ordered_views = [];

            foreach ($product_views as $item) {
                $ordered_views[$item['product_id']] = $item['count'];
            }

            arsort($ordered_views);

            $index = 0;

            foreach ($ordered_views as $product_id => $amount) {
                $productinfo = DB::table('productinfo')->select(['pic', 'title'])->where('id', $product_id)->first();
                $productinfo = CommonService::objectToArray($productinfo);

                if (empty($productinfo) == false) {
                    $ranking['view'][$index] = [
                        'image_path' => '/public/static/index/' . json_decode($productinfo['pic'], true)[0],
                        'title' => $productinfo['title'],
                        'amount' => $amount,
                    ];

                    $index++;

                    if ($index >= $table_rank_length) {
                        break;
                    }
                }
            }
        }

        return $ranking;
    }
}
