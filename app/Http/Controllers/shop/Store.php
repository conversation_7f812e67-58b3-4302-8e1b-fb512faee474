<?php

namespace App\Http\Controllers\shop;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\admin\MainController;
use App\Services\CommonService;
// use App\Services\pattern\MemberInstance;
use App\Services\pattern\PointRecords;
use App\Services\pattern\OrderHelper;

class Store extends MainController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        return view('shop.store.index', ['data' => $this->data]);
    }

    public function search_memeber(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['account']) === true) {
                throw new \Exception('請輸入帳號');
            }

            $regex = '/^09[0-9]{8}$/';

            if (!preg_match($regex, $request_data['account'])) {
                throw new \Exception('手機格式錯誤');
            }

            // 帳號：MemberInstance::$account_column
            $member_number = DB::connection('main_db')->table('account')->where([
                ['phone', '=', $request_data['account']],
                ['status', '=', 1]
            ])->value('number');

            if (empty($member_number) == true) {
                throw new \Exception('查無此帳號');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->redirect('/shop/store/input' . '?' . http_build_query([
            'member_number' => $member_number
        ]));
    }

    public function input(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['member_number']) === true) {
                throw new \Exception('查無會員');
            }

            $columns = ['id', 'name', 'number', 'phone', 'point'];

            $db_result = DB::connection('main_db')->table('account')->select($columns)->where([
                ['number', '=', $request_data['member_number']],
                ['status', '=', 1],
            ])->first();

            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result) === true) {
                throw new \Exception('查無會員');
            }

            $this->data['member'] = $db_result;

            $this->data['member']['total'] = DB::connection('main_db')->table('orderform')->where([
                ['user_id', '=', $this->data['member']['id']],
            ])->whereNotIn('status', ['Cancel', 'Return'])->sum('total');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return view('shop.store.input', ['data' => $this->data]);
    }

    public function check(Request $request)
    {
        $member_number = '';
        $real_amount = 0;
        $add_points = 0;
        $uniform_number = '';
        $invoice_number = '';

        try {
            $request_data = $request->all();

            if (empty($request_data['member_id']) === true) {
                throw new \Exception('查無會員');
            }

            if (empty($request_data['mode']) == true) {
                throw new \Exception('請選擇發票模式');
            }

            if (in_array($request_data['mode'], ['einvoice', 'printout']) == false) {
                throw new \Exception('發票模式錯誤，請重新選擇');
            }

            if ($request_data['mode'] == 'printout') {
                if (empty($request_data['invoice']) == true) {
                    throw new \Exception('請輸入發票左側二維條碼');
                }

                $qrcode_content = $request_data['invoice'];
                $invoice_number = substr($qrcode_content, 0, 10);
                $invoice_date = substr($qrcode_content, 10, 7);
                $invoice_random_number = substr($qrcode_content, 17, 4);
                $uniform_number = substr($qrcode_content, 21, 8) == '00000000' ? '' : substr($qrcode_content, 21, 8);
                $real_amount = intval(hexdec(substr($qrcode_content, 29, 8)));

                if ($invoice_date != (date('Y') - 1911) . date('md')) {
                    throw new \Exception('發票已過期，請重新輸入');
                }

                if (preg_match('/^[0-9]{4}$/', $invoice_random_number) == false) {
                    throw new \Exception('發票隨機碼格式錯誤，請重新輸入');
                }
            } else {
                if (empty($request_data['invoice_number']) == true) {
                    throw new \Exception('請輸入發票號碼');
                }

                $invoice_number = $request_data['invoice_number'];
                $invoice_random_number = null;

                if (empty($request_data['invoice_uniform_number']) == false) {
                    if (preg_match('/^[0-9]{8}$/', $request_data['invoice_uniform_number']) == false) {
                        throw new \Exception('統一編號格式錯誤');
                    }
                }

                $uniform_number = strval($request_data['invoice_uniform_number']);

                if (empty($request_data['invoice_amount']) == true) {
                    throw new \Exception('請輸入發票金額');
                }

                $real_amount = intval($request_data['invoice_amount']);
            }

            if (preg_match('/^[A-Z]{2}[0-9]{8}$/', $invoice_number) == false) {
                throw new \Exception('發票號碼格式錯誤，請重新輸入');
            }

            if ($real_amount < 0) {
                throw new \Exception('發票金額不得小於0');
            }

            $db_result = DB::connection('main_db')->table('orderform')->where('InvoiceNo', $invoice_number)->first();
            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result) === false) {
                throw new \Exception('發票重覆，請重新輸入');
            }

            if (intval($request_data['point']) < 0) {
                throw new \Exception('使用點數不得小於0');
            }

            $member_data = DB::connection('main_db')->table('account')
                ->select(['point', 'phone', 'email', 'number'])
                ->where([
                    ['id', '=', $request_data['member_id']],
                    ['status', '=', 1],
                ])->first();
            $member_data = CommonService::objectToArray($member_data);

            $member_number = $member_data['number'];

            if ($request_data['point'] > $member_data['point']) {
                throw new \Exception('點數不足');
            }

            if ($request_data['point'] >= $real_amount) {
                $use_points = $real_amount;
            } else {
                $use_points = $request_data['point'];
            }

            $PointRecords = new PointRecords($request_data['member_id']);
            $PointRecords->add_records([
                'points' => (-1) * $use_points,
                'msg' => '使用會員點數(' . $use_points . '點)',
                'belongs_time' => time(),
            ]);

            $add_points = floor($real_amount / floatval(DB::table('points_setting')->where('id', 3)->value('value')));

            DB::connection('main_db')->table('orderform')->insert([
                'distributor_id' => session()->get($this->admin_type)['id'],
                'user_id' => $request_data['member_id'],
                'order_number' => OrderHelper::create_order_number(),
                'create_time' => time(),
                'over_time' => OrderHelper::get_eff_dateline(),
                'payment' => 1,
                'transport' => '現場購物',
                'transport_location' => '現場購物',
                'transport_location_name' => '自用',
                'transport_location_phone' => $member_data['phone'],
                'transport_email' => $member_data['email'],
                'add_point' => $add_points,
                'total' => $real_amount,
                'discount' => '[]',
                'receipts_state' => 1,
                'transport_state' => 1,
                'transport_date' => date('Y-m-d'),
                'need_arrival_date' => 1,
                'arrival_date' => date('Y-m-d'),
                'uniform_numbers' => strval($uniform_number),
                'InvoiceNo' => $invoice_number,
                'RandomNumber' => strval($invoice_random_number),
                'Print' => 1,
                'status' => 'Complete',
                'stock_status' => 0,
                'cancel_date' => '0000-00-00 00:00:00',
                'product' => json_encode([
                    [
                        'name' => '現場購物',
                        'price' => $real_amount,
                        'num' => 1,
                        'total' => $real_amount,
                        'key_type' => 'normal',
                        'Author' => '份',
                    ],
                    [
                        'name' => '運費',
                        'price' => 0,
                        'num' => 1,
                        'total' => 0,
                    ],
                ], JSON_UNESCAPED_UNICODE),
            ]);

            $PointRecords->add_records([
                'points' => $add_points,
                'msg' => '購物獲得點數(' . intval($add_points) . '點)',
                'belongs_time' => time(),
            ]);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('交易成功', '/shop/store/final' . '?' . http_build_query([
            'member_number' => $member_number,
            'amount' => $real_amount,
            'point' => $add_points,
            'uniform_number' => $uniform_number,
            'invoice' => $invoice_number,
        ]));
    }

    public function final(Request $request)
    {
        try {
            $request_data = $request->all();

            $db_result = DB::connection('main_db')->table('account')->select(['name', 'point'])->where([
                ['number', '=', $request_data['member_number']],
                ['status', '=', 1],
            ])->first();
            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result) === true) {
                throw new \Exception('查無會員');
            }

            $this->data['member'] = $db_result;
            $this->data['amount'] =  $request_data['amount'];
            $this->data['point'] =   $request_data['point'];
            $this->data['uniform_number'] =  $request_data['uniform_number'];
            $this->data['invoice'] = $request_data['invoice'];
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return view('shop.store.final', ['data' => $this->data]);
    }
}
