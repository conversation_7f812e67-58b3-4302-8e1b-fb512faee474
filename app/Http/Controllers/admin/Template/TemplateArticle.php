<?php

namespace App\Http\Controllers\admin\Template;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Http\Controllers\admin\MainController;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class TemplateArticle extends MainController
{
    const PER_PAGE_ROWS = 10;
    const SIMPLE_MODE_PAGINATE = false;
    public $controllerName;
    public $DBTextConnecter;
    public $resTableName;
    public $pic_width;
    public $pic_height;
    private $has_column_user_id;

    //this resources's frontend use Single Page Web, some CURD method is useless
    public function edit(Request $request) {}

    public function create(Request $request) {}

    public function __construct($controller_name, $table_name, $pic_width = 700, $pic_height = 475)
    {
        parent::__construct();
        $this->controllerName = $controller_name;
        $this->DBTextConnecter = DBTextConnecter::withTableName($table_name);
        $this->resTableName = $table_name;
        $this->pic_width = $pic_width;
        $this->pic_height = $pic_height;

        /*測試是否有user_id欄位*/
        try {
            Db::table($this->resTableName)->column('user_id');
            $this->has_column_user_id = true;
        } catch (\Exception $e) {
            $this->has_column_user_id = false;
        }
    }

    public function index(Request $request)
    {
        $this->data['table_name'] = $this->resTableName;

        $searchKey = $request->get('searchKey') ?? '';
        $this->data['searchKey'] = trim($searchKey);

        $db_result = Db::table($this->resTableName);

        if ($this->has_column_user_id) {
            $db_result = $db_result->whereRaw($this->distributor_id_where_sql);
        }

        $this->data[$this->resTableName] = $db_result->orderBy('orders', 'asc')->orderBy('id', 'asc')->paginate(self::PER_PAGE_ROWS)->appends(['searchKey' => $searchKey]);

        return view("admin." . $this->resTableName . ".index", ['data' => $this->data]);
    }

    public function doCreate(Request $request, $finish = true)
    {
        try {
            $request_data = $request->all();

            $newData = ['online' => 1];

            /*創建資料*/
            $this->DBTextConnecter->setDataArray($newData);
            $id = $this->DBTextConnecter->createTextRow();

            $this->mainUpdate($id, $request_data);
        } catch (\Exception $e) {
            ob_clean();
            echo ($e->getMessage());

            die();
        }

        if (!$finish) {
            return $id;
        } else {
            ob_clean();
            echo ('<h1>上傳成功</h1>');

            die();
        }
    }

    public function update(Request $request, $finish = true)
    {
        try {
            $request_data = $request->all();

            if (parent::check_controll($this->resTableName, $request_data['id']) == false) {
                throw new \Exception("您無法編輯此項目", 1);
            }

            $this->mainUpdate($request_data['id'], $request_data);
        } catch (\Exception $e) {
            ob_clean();
            echo ($e->getMessage());

            die();
        }

        if (!$finish) {
            return $request_data['id'];
        } else {
            ob_clean();
            echo ('<h1>上傳成功</h1>');

            die();
        }
    }

    public function mainUpdate($id, $data)
    {
        $newData = ['id' => $id];

        foreach (['title', 'content', 'distributor_id', 'pic', 'url', 'description'] as $column) {
            switch ($column) {
                // 供應商設定
                case 'distribution_id':
                    if ($this->admin_type == 'distribution') {
                        $newData[$column] = session()->get($this->admin_type . '.id');
                    }

                    break;
                // 處理圖片
                case 'pic':
                    if (empty($data['image']) == false) {
                        $DBFileConnecter = new DBFileConnecter();
                        $newData[$column] = $DBFileConnecter->fixedFileUp($data['image'], $this->resTableName . '_' . $id, $this->pic_width, $this->pic_height);
                    }

                    break;
                case 'title':
                    if (isset($data['title'])) {
                        if (empty($data['title']) == false) {
                            $newData['title'] = $data['title'];
                        } else {
                            $newData['title'] = '未命名';
                            $newData['online'] = 0;
                        }
                    }

                    break;
                default:
                    if (empty($data[$column]) == false) {
                        $newData[$column] = $data[$column];
                    }

                    break;
            }
        }

        if (count(array_keys($newData)) > 1) {
            $this->DBTextConnecter->setDataArray($newData);
            $this->DBTextConnecter->upTextRow();
        }

        // 自動更新排序
        if (empty($data['orders']) == false) {
            parent::auto_change_orders($this->resTableName, 'orders', $data['orders'], 'id', $id);
        }
    }

    public function delete(Request $request)
    {
        try {
            $id = $request->get('id');

            if (!parent::check_controll($this->resTableName, $id)) {
                throw new \Exception("您無法編輯此項目");
            }

            if (empty($id) == true) {
                throw new \Exception('請選擇刪除項目');
            }

            Db::table($this->resTableName)->delete($id);
        } catch (\Exception $e) {
            $this->dumpException($e);
        }

        $this->success('刪除成功');
    }

    public function multiDelete(Request $request)
    {
        try {
            $idList = $request->post('id');

            if (empty($idList) == true) {
                throw new \Exception('請選擇刪除項目');
            }

            $idList = json_decode($idList);

            foreach ($idList as $id) {
                if (!parent::check_controll($this->resTableName, $id)) {
                    throw new \Exception("您無法編輯此項目");
                }
            }

            Db::table($this->resTableName)->whereIn('id', $idList)->delete();
        } catch (\Exception $e) {
            $this->dumpException($e);
        }

        $this->success('刪除成功');
    }

    /*AJAX*/
    public function cellCtrl(Request $request)
    {
        $outputData = ['status' => true, 'message' => 'success'];

        try {
            $updateData = $request->post();

            if (parent::check_controll($this->resTableName, $updateData['id']) == false) {
                throw new \Exception("您無法編輯此項目");
            }

            if (isset($updateData['online'])) {
                if ($updateData['online'] == 1) {
                    $updateData['time'] = date('Y-m-d H:i:s');
                }
            }

            $this->DBTextConnecter->setDataArray($updateData);
            $this->DBTextConnecter->upTextRow();
        } catch (\Exception $e) {
            $outputData = ['status' => false, 'message' => $e->getMessage()];
        }

        return $outputData;
    }
}
