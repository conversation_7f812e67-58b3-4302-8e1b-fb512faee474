<?php

namespace App\Http\Controllers\admin;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class Giftwithpurchase extends MainController
{
    public function index()
    {
        $this->data['today'] = date('Y-m-d');

        return view('admin.giftwithpurchase.index', ['data' => $this->data]);
    }

    public function get_items(Request $request)
    {
        $response = [
            'code' => 1,
            'msg' => '取得成功',
            'data' => [
                'search' => [
                    'keyword' => '',
                    'date_start' => '',
                    'date_end' => '',
                    'type' => 0,
                ],
                'items' => [],
            ],
        ];

        try {
            $request_data = $request->post();

            $conditions = [
                ['removed', '=', 0]
            ];

            if (empty($request_data['date_start']) == false) {
                $response['data']['search']['date_start'] = $request_data['date_start'];
                $conditions[] = ['date_start', '>=', $request_data['date_start']];
            }

            if (empty($request_data['date_end']) == false) {
                $response['data']['search']['date_end'] = $request_data['date_end'];
                $conditions[] = ['date_end', '<=', $request_data['date_end']];
            }

            if (empty($request_data['keyword']) == false) {
                $response['data']['search']['keyword'] = $request_data['keyword'];
                $conditions[] = ['name', 'like', '%' . $request_data['keyword'] . '%'];
            }

            if (in_array($request_data['type'], [0, 1, 2, 3]) == false) {
                throw new \Exception('狀態錯誤');
            }

            $response['data']['search']['type'] = $request_data['type'];

            $db_result =  DB::table('gift_with_purchase')
                ->select(['id', 'name', 'date_start', 'date_end', 'min_purchase_amount', 'status'])
                ->where($conditions)
                ->orderBy('min_purchase_amount', 'desc')
                ->orderBy('id');

            if ($request_data['type'] == 1) {
                $db_result = $db_result->whereNotNull('date_end')->where('date_end', '<', date('Y-m-d'));
            } else if ($request_data['type'] == 3) {
                $db_result = $db_result->whereNotNull('date_start')->where('date_start', '>', date('Y-m-d'));
            } else if ($request_data['type'] == 2) {
                $db_result = $db_result->where(function ($query) {
                    $query->where(function ($q) {
                        $q->whereNull('date_start')->whereNull('date_end');
                    })->orWhere(function ($q) {
                        $q->whereNotNull('date_start')->whereNull('date_end')
                            ->where('date_start', '<=', date('Y-m-d'));
                    })->orWhere(function ($q) {
                        $q->whereNull('date_start')->whereNotNull('date_end')
                            ->where('date_end', '>=', date('Y-m-d'));
                    })->orWhere(function ($q) {
                        $q->whereNotNull('date_start')->whereNotNull('date_end')
                            ->where('date_start', '<=', date('Y-m-d'))->where('date_end', '>=', date('Y-m-d'));
                    });
                });
            }

            $db_result = $db_result->get();

            if (empty($db_result) == false) {
                $db_result = CommonService::objectToArray($db_result);

                array_walk($db_result, function (&$item) {
                    $item['status'] = $item['status'] == 1 ? true : false;

                    // 首尾皆無，即進行中
                    if (isset($item['date_start']) == false && isset($item['date_end']) == false) {
                        $item['type'] = 2;
                    } else if (isset($item['date_start']) == true && isset($item['date_end']) == false) {
                        // 僅設定開始日期
                        if (strtotime($item['date_start']) <= strtotime(date('Y-m-d'))) {
                            $item['type'] = 2; // 進行中
                        } else {
                            $item['type'] = 3; // 未開始
                        }
                    } else if (isset($item['date_start']) == false && isset($item['date_end']) == true) {
                        // 僅設定結束日期
                        if (strtotime($item['date_end']) < strtotime(date('Y-m-d'))) {
                            $item['type'] = 1; // 已結束
                        } else {
                            $item['type'] = 2; // 進行中
                        }
                    } else {
                        if (strtotime($item['date_end']) < strtotime(date('Y-m-d'))) {
                            $item['type'] = 1; // 已結束
                        } else if (strtotime($item['date_start']) > strtotime(date('Y-m-d'))) {
                            $item['type'] = 3; // 未開始
                        } else {
                            $item['type'] = 2; // 進行中
                        }
                    }
                });

                $response['data']['items'] = $db_result;
            }
        } catch (\Exception $e) {
            $response['code'] = 0;
            $response['msg'] = $e->getMessage();
        }

        return $response;
    }

    public function update_item(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_array($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (in_array($request_data['column'], ['status']) == false) {
                throw new \Exception('欄位錯誤');
            }

            if (in_array($request_data['value'], [0, 1]) == false) {
                throw new \Exception('數值錯誤');
            }

            $db_result = DB::table('gift_with_purchase')
                ->whereIn('id', $request_data['id'])
                ->update([
                    $request_data['column'] => $request_data['value'],
                ]);

            if ($db_result === false) {
                throw new \Exception('更新失敗');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }

    public function delete_item(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['ids']) || is_array($request_data['ids']) == false) {
                throw new \Exception('ID錯誤');
            }

            $db_result = DB::table('gift_with_purchase')
                ->whereIn('id', $request_data['ids'])
                ->update(['removed' => 1]);

            if ($db_result === false) {
                throw new \Exception('刪除失敗');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('刪除成功');
    }

    public function add_item(Request $request)
    {
        $response = [
            'code' => 1,
            'msg' => '新增成功',
            'data' => [
                'id' => null,
            ],
        ];

        try {
            $request_data = $request->all();

            if (empty($request_data['name'])) {
                throw new \Exception('名稱不能為空');
            }

            if (empty($request_data['min_purchase_amount']) || is_numeric($request_data['min_purchase_amount']) == false) {
                throw new \Exception('達成金額錯誤');
            }

            if (empty($request_data['date_start']) == false && strtotime($request_data['date_start']) === false) {
                throw new \Exception('開始日期錯誤');
            }

            if (strtotime($request_data['date_start']) < strtotime(date('Y-m-d'))) {
                throw new \Exception('開始日期不能小於今天');
            }

            if (empty($request_data['date_end']) == false) {
                if (strtotime($request_data['date_end']) < strtotime($request_data['date_start'])) {
                    throw new \Exception('結束日期不能小於開始日期');
                }
            }

            $db_result = DB::table('gift_with_purchase')->insertGetId([
                'name' => $request_data['name'],
                'description' => $request_data['description'] ?? null,
                'min_purchase_amount' => $request_data['min_purchase_amount'],
                'date_start' => $request_data['date_start'],
                'date_end' => $request_data['date_end'] ?? null,
                'status' => 0,
            ]);

            if ($db_result === false) {
                throw new \Exception('新增失敗');
            }

            $response['data']['id'] = $db_result;
        } catch (\Exception $e) {
            $response['code'] = 0;
            $response['msg'] = $e->getMessage();
        }

        return $response;
    }

    public function  edit(Request $request)
    {
        try {
            $id = $request->get('id');

            if (is_numeric($id) == false || empty($id) == true) {
                throw new \Exception('ID錯誤');
            }

            $db_result = DB::table('gift_with_purchase')->select(['id', 'name', 'description', 'min_purchase_amount', 'date_start', 'date_end'])->where('id', $id)->where('removed', 0)->first();
            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result) == true) {
                throw new \Exception('滿額贈不存在或已被刪除');
            }

            $this->data['item'] = $db_result;
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return view('admin.giftwithpurchase.edit', ['data' => $this->data]);
    }

    public function update_item_basic(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['name'])) {
                throw new \Exception('名稱不能為空');
            }

            if (empty($request_data['min_purchase_amount']) || is_numeric($request_data['min_purchase_amount']) == false || $request_data['min_purchase_amount'] < 0) {
                throw new \Exception('達成金額錯誤');
            }

            if (empty($request_data['date_start']) == false && strtotime($request_data['date_start']) === false) {
                throw new \Exception('開始日期錯誤');
            }

            if ($request_data['date_start'] != DB::table('gift_with_purchase')->where('id', $request_data['id'])->value('date_start')) {
                if (strtotime($request_data['date_start']) < strtotime(date('Y-m-d'))) {
                    throw new \Exception('開始日期不能小於今天');
                }
            }

            if (empty($request_data['date_end']) == false) {
                if (strtotime($request_data['date_end']) < strtotime($request_data['date_start'])) {
                    throw new \Exception('結束日期不能小於開始日期');
                }
            }

            $db_result = DB::table('gift_with_purchase')
                ->where('id', $request_data['id'])
                ->update([
                    'name' => $request_data['name'],
                    'description' => $request_data['description'] ?? null,
                    'min_purchase_amount' => $request_data['min_purchase_amount'],
                    'date_start' => $request_data['date_start'],
                    'date_end' => $request_data['date_end'] ?? null,
                ]);

            if ($db_result === false) {
                throw new \Exception('更新失敗');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }

    public function get_gift_with_purchase(Request $request)
    {
        $response = [
            'code' => 1,
            'msg' => '取得成功',
            'data' => [],
        ];

        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['tab'])) {
                throw new \Exception('標籤錯誤');
            }

            if (in_array($request_data['tab'], ['rule', 'content']) == false) {
                throw new \Exception('標籤錯誤');
            }

            $existed_productinfo_type_ids = DB::table('gift_with_purchase_' . $request_data['tab'])->where('gift_with_purchase_id', $request_data['id'])->where('removed', 0)->pluck('productinfo_type_id');
            $existed_productinfo_type_ids = CommonService::objectToArray($existed_productinfo_type_ids);

            $data = [];

            foreach ($existed_productinfo_type_ids as $id) {
                $item = [
                    'id' => $id,
                ];

                if ($request_data['tab'] == 'content') {
                    $item['num'] = intval(DB::table('gift_with_purchase_content')
                        ->where('gift_with_purchase_id', $request_data['id'])
                        ->where('productinfo_type_id', $id)
                        ->value('num'));
                }

                $db_result = DB::table('productinfo_type')
                    ->select(['productinfo_type.id', 'productinfo_type.title as type_title', 'productinfo.title', 'pic_index', 'pic'])
                    ->leftJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
                    ->where('productinfo_type.id', $id)
                    ->where('productinfo_type.online', 1)
                    ->where('productinfo.online', 1)
                    ->first();
                $db_result = CommonService::objectToArray($db_result);

                if (empty($db_result) == true) {
                    continue;
                }

                $item['title'] = str_replace('\"', '"', $db_result['title']);

                if (empty($db_result['type_title']) == false) {
                    $item['title'] .= ' - ' . str_replace('\"', '"', $db_result['type_title']);
                }

                $item['pic'] = json_decode($db_result['pic'], true)[$db_result['pic_index'] - 1] ?: '';

                $data[] = $item;
            }

            $response['data'] = $data;
        } catch (\Exception $e) {
            $response['code'] = 0;
            $response['msg'] = $e->getMessage();
        }

        return $response;
    }

    public function update_rule_item(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['item_ids'])) {
                throw new \Exception('品項ID不能為空');
            }

            foreach ($request_data['item_ids'] as $item_id) {
                if (is_numeric($item_id) == false) {
                    throw new \Exception('品項ID錯誤');
                }

                $db_result = DB::table('gift_with_purchase_rule')
                    ->where('gift_with_purchase_id', $request_data['id'])
                    ->where('productinfo_type_id', $item_id)
                    ->first();
                $db_result = CommonService::objectToArray($db_result);

                if (empty($db_result) == false) {
                    $db_result = DB::table('gift_with_purchase_rule')
                        ->where('gift_with_purchase_id', $request_data['id'])
                        ->where('productinfo_type_id', $item_id)
                        ->update([
                            'removed' => 0,
                        ]);
                } else {
                    $db_result = DB::table('gift_with_purchase_rule')
                        ->insert([
                            'gift_with_purchase_id' => $request_data['id'],
                            'productinfo_type_id' => $item_id,
                            'removed' => 0,
                        ]);
                }

                if ($db_result === false) {
                    throw new \Exception('更新失敗');
                }
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }

    public function update_content_item(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['item_ids'])) {
                throw new \Exception('品項ID不能為空');
            }

            foreach ($request_data['item_ids'] as $item_id) {
                if (is_numeric($item_id) == false) {
                    throw new \Exception('品項ID錯誤');
                }

                $db_result = DB::table('gift_with_purchase_content')
                    ->where('gift_with_purchase_id', $request_data['id'])
                    ->where('productinfo_type_id', $item_id)
                    ->first();
                $db_result = CommonService::objectToArray($db_result);

                if (empty($db_result) == false) {
                    $db_result = DB::table('gift_with_purchase_content')
                        ->where('gift_with_purchase_id', $request_data['id'])
                        ->where('productinfo_type_id', $item_id)
                        ->update([
                            'removed' => 0,
                        ]);
                } else {
                    $db_result = DB::table('gift_with_purchase_content')
                        ->insert([
                            'gift_with_purchase_id' => $request_data['id'],
                            'productinfo_type_id' => $item_id,
                            'num' => 1,
                            'removed' => 0,
                        ]);
                }

                if ($db_result === false) {
                    throw new \Exception('更新失敗');
                }
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }

    public function delete_rule_item(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['item_ids'])) {
                throw new \Exception('品項ID不能為空');
            }

            foreach ($request_data['item_ids'] as $item_id) {
                if (is_numeric($item_id) == false) {
                    throw new \Exception('品項ID錯誤');
                }

                $db_result = DB::table('gift_with_purchase_rule')
                    ->where('gift_with_purchase_id', $request_data['id'])
                    ->where('productinfo_type_id', $item_id)
                    ->update([
                        'removed' => 1,
                    ]);

                if ($db_result === false) {
                    throw new \Exception('更新失敗');
                }
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }

    public function delete_content_item(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['item_ids'])) {
                throw new \Exception('品項ID不能為空');
            }

            foreach ($request_data['item_ids'] as $item_id) {
                if (is_numeric($item_id) == false) {
                    throw new \Exception('品項ID錯誤');
                }

                $db_result = DB::table('gift_with_purchase_content')
                    ->where('gift_with_purchase_id', $request_data['id'])
                    ->where('productinfo_type_id', $item_id)
                    ->update([
                        'removed' => 1,
                    ]);

                if ($db_result === false) {
                    throw new \Exception('更新失敗');
                }
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }

    public function get_branches(Request $request)
    {
        $response = [
            'code' => 1,
            'msg' => '取得成功',
            'data' => [],
        ];

        try {
            $data = [];

            $branch_result = DB::table('product')->select(['id', 'title'])->where('online', 1)->orderBy('order_id')->get();
            $branch_result = CommonService::objectToArray($branch_result);

            if (empty($branch_result) == true) {
                throw new \Exception('無分館');
            }

            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['tab'])) {
                throw new \Exception('標籤錯誤');
            }

            if (in_array($request_data['tab'], ['rule', 'content']) == false) {
                throw new \Exception('標籤錯誤');
            }

            $existed_productinfo_type_ids = DB::table('gift_with_purchase_' . $request_data['tab'])->where('gift_with_purchase_id', $request_data['id'])->where('removed', 0)->pluck('productinfo_type_id');
            $existed_productinfo_type_ids = CommonService::objectToArray($existed_productinfo_type_ids);

            foreach ($branch_result as $branch) {
                $data[] = [
                    'id' => $branch['id'],
                    'title' => $branch['title'],
                    'products' => [],
                    'layers' => [],
                ];

                $productinfo_result = DB::table('productinfo')->select(['id', 'title', 'pic'])->where('final_array', 'like', '%"prev_id":"' . $branch['id'] . '"%"parent_id":"0"%')->where('online', 1)->where('is_customized_box', 0)->orderBy('order_id')->get();
                $productinfo_result = CommonService::objectToArray($productinfo_result);

                if (empty($productinfo_result) == false) {
                    array_walk($productinfo_result, function (&$item) use ($existed_productinfo_type_ids) {
                        $item['title'] = str_replace('\"', '"', $item['title']);
                        $item['pic'] = json_decode($item['pic'], true);

                        $item['productinfo_type'] = DB::table('productinfo_type')->select(['id', 'title', 'pic_index'])->whereNotIn('id', $existed_productinfo_type_ids)->where('product_id', $item['id'])->where('online', 1)->orderBy('order_id')->get();
                        $item['productinfo_type'] = CommonService::objectToArray($item['productinfo_type']);

                        if (empty($item['productinfo_type']) == false) {
                            array_walk($item['productinfo_type'], function (&$type) use ($item) {
                                $type['title'] = str_replace('\"', '"', $type['title']);
                                $type['pic'] = $item['pic'][$type['pic_index'] - 1] ?: '';

                                unset($type['pic_index']);
                            });
                        }

                        unset($item['pic']);
                    });

                    $data[count($data) - 1]['products'] = $productinfo_result;
                }

                $typeinfo_result = DB::table('typeinfo')->select(['id', 'title'])->where('parent_id', $branch['id'])->where('online', 1)->orderBy('order_id')->get();
                $typeinfo_result = CommonService::objectToArray($typeinfo_result);

                if (empty($typeinfo_result) == true) {
                    continue;
                }

                array_walk($typeinfo_result, function (&$item) use ($existed_productinfo_type_ids) {
                    $products = DB::table('productinfo')->select(['id', 'title', 'pic'])->where('final_array', 'like', '%"parent_id":"' . $item['id'] . '"%')->where('online', 1)->where('is_customized_box', 0)->orderBy('order_id')->get();
                    $products = CommonService::objectToArray($products);

                    if (empty($products) == false) {
                        array_walk($products, function (&$prod) use ($existed_productinfo_type_ids) {
                            $prod['title'] = str_replace('\"', '"', $prod['title']);
                            $prod['pic'] = json_decode($prod['pic'], true);

                            $prod['productinfo_type'] = DB::table('productinfo_type')->select(['id', 'title', 'pic_index'])->whereNotIn('id', $existed_productinfo_type_ids)->where('product_id', $prod['id'])->where('online', 1)->orderBy('order_id')->get();
                            $prod['productinfo_type'] = CommonService::objectToArray($prod['productinfo_type']);

                            if (empty($prod['productinfo_type']) == false) {
                                array_walk($prod['productinfo_type'], function (&$type) use ($prod) {
                                    $type['title'] = str_replace('\"', '"', $type['title']);
                                    $type['pic'] = $prod['pic'][$type['pic_index'] - 1] ?: '';

                                    unset($type['pic_index']);
                                });
                            }

                            unset($prod['pic']);
                        });
                    }

                    $item['products'] = $products;
                });

                $data[count($data) - 1]['layers'] = $typeinfo_result;
            }

            $response['data'] = $data;
        } catch (\Exception $e) {
            $response['code'] = 0;
            $response['msg'] = $e->getMessage();
        }

        return $response;
    }

    public function save_content_num(Request $request)
    {
        try {
            $request_data = $request->all();

            if (empty($request_data['id']) || is_numeric($request_data['id']) == false) {
                throw new \Exception('ID錯誤');
            }

            if (empty($request_data['productinfo_type_id']) || is_numeric($request_data['productinfo_type_id']) == false) {
                throw new \Exception('品項ID錯誤');
            }

            if (empty($request_data['num']) || is_numeric($request_data['num']) == false) {
                throw new \Exception('數量錯誤');
            }

            $db_result = DB::table('gift_with_purchase_content')
                ->where('gift_with_purchase_id', $request_data['id'])
                ->where('productinfo_type_id', $request_data['productinfo_type_id'])
                ->update([
                    'num' => $request_data['num'],
                ]);

            if ($db_result === false) {
                throw new \Exception('更新失敗');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }
}
