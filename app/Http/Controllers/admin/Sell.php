<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\MemberInstance;

class Sell extends MainController{
  private $DBTextConnecter;
  private $DBFileConnecter;
  private $tableName;

  public function __construct() {
    parent::__construct();
  }
  
  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $this->data['searchKey'] = $searchKey;

    if($searchKey != ''){
      $ISBN = Db::table('productinfo')->where('ISBN',$searchKey)->first();
      $ISBN = CommonService::objectToArray($ISBN);
      $productinfo_type  = Db::table('productinfo_type')->where('product_id',$ISBN['id'])->orderByRaw('count')->get();
      
      $select_position =[];
      //dump($productinfo_type);
      foreach($productinfo_type as $k => $v){
        $productinfo_type[$k]['portion'] =[];
        $position_portion  = Db::table('position_portion as pp')->
                  select("pp.*", "pp.id as pp_id" ,"pos.*")->
                  whereRaw("productinfo_type = '".$v['id']."'")->
                  join('position pos','pp.position_id', '=', 'pos.id')->
                  select();
        foreach($position_portion as $pk => $pv){
          $productinfo_type[$k]['portion'][$pk] = array('id'=>$pv['pp_id'],'name'=>$pv['name'],'number'=>$pv['position_number'],'num'=>$pv['num']);
          //$sell_array[$v['id']] = $productinfo_type[$k]['portion'][$pk];
          
          $select_position[$pv['position_id']] = $pv['name'];
        }
        //dump($position_portion);
      }
      //dump($sell_array);
      //dump($productinfo_type);exit;
      $this->data['productinfo_type'] = $productinfo_type;
    }
    $this->data['id'] = $ISBN['id'] ?? '';
    $this->data['select_position'] = $select_position ?? '';
    $this->data['repeat'] = $ISBN['r_repeat'] ?? '';
    $this->data['productinfo_type'] = $productinfo_type ?? '';
    //exit;
    return view('admin.sell.sell',['data'=>$this->data]);
  }

  public function search(Request $request){
    $stock = Db::table('position_portion')
                ->whereRaw("product_id = '".$request->post('product_id')."' AND 
                    position_id = '".$request->post('position_id')."' AND 
                    position_number = '".$request->post('position_number')."'")
                ->first();
        //{"productinfo_type":958,"position_portion":4,"num":1}
    return array(['productinfo_type'=>$stock['productinfo_type'],'position_portion'=>$stock['id']]);
  }

  public function update(Request $request) {
    // 先清空購物車
    session()->put('cart',[]);
    $sell_array = json_decode($request->post("sell_array"),true);
    $searchKey = $request->post("searchKey");
    $user_id = $request->post("user_id"); 
    // dump($sell_array);

    $total=[];
    foreach($sell_array as $k => $v){//依productinfo_type將數量總和在一起
      $pt_id =$v['pt_id'];
      if(empty($total[$pt_id])){
        $total[$pt_id]= $v;	
      }else{
        if( empty( $total[$pt_id]['num'] ) )  $total[$pt_id]['num']=0;
        $total[$pt_id]['num'] += $v['num'];
      }
    }		
    // dump($total);
    // exit;

    foreach($total as $k => $v){
      // 商品加入購物車
      $result = OrderHelper::cartCtrl($k, $v['num'], 'increase', 'cart', $user_id);
      if(!$result['code']){
        $this->error($result['msg']);
      }
    }
    // 建立現場銷售訂單
    $OrderData = [
      "distributor_id" => session()->get($this->admin_type)['id'],
      "pay_way" => "1",
      "send_way" => "到店取貨",
      "transport_location_name" => "現場購物",
      "transport_email" => "",
      "transport_location_phone" => "",
      "transport_location_tele" => "",
      "addrC" => "現場購物",
      "id" => "",
      "discount" => "none_discount",
      "point" => "",
      "uniform_numbers" => "",
      "company_title" => "",
      "transport_location_textarea" => "",
      "tygwtk" => "",
      "selectPlace" => [],
      "receipts_state" => 1,
      "transport_state" => 1,
      "status" => "Complete",
      "ps" => "",
      "invoice_style" => 1,
    ];
    try {
      $orderData = OrderHelper::createOrder($OrderData, 'offline', $user_id); // 建立訂單(線下購買方式)，過程中扣除線上可購買數量
    } catch (\Throwable $th) {
      // dump($th->getMessage());exit;
      $this->error($th->getMessage());
    }

    //扣除實體庫存
    foreach($sell_array as $k => $v){	
      Db::table('position_portion')->where('id',$v['pp_id'])->decrement('num',$v['num']); // 扣除被撿走或的編碼數量

      Db::table('position_portion')->where('num',0)->delete(); // 編碼剩餘數為0則刪除紀錄
      Db::table('position_portion')->where('product_id',0)->delete(); // 編碼無對應商品則刪除紀錄
    }

    $this->success('更新成功',url('sell/index').'?searchKey='.$searchKey);
    //dump($total);
  }

  public function edit(Request $request) {	
    $type = $request->post('type');
    $id = $request->post('id');

    switch ($type) {
      case "add":
        $data['name'] = $request->post('name');
        $data['number'] = $request->post('number');
        if(Db::table($this->tableName)->insert($data)){
          echo 'success';
        }else{
          echo '新增失敗';
        }
        break;
      case "delete":
        if(Db::table($this->tableName)->where('id',$id)->delete()){
          echo 'success';
        }else{
          echo '失敗';
        }
            break;
      case "update":
        $data['number'] = $request->post('number');
        if(Db::table($this->tableName)->where('id',$id)->update($data)){
          echo 'success';
        }else{
          echo '新增失敗';
        }
        break;
    }
    
  }

  public function search_bar_mul(Request $request){
    $bar = trim($request->post('bar'));
    $sell_array = $request->post('sell_array');
    $sell_array = json_decode($sell_array, true);
    $goods = Db::table('productinfo as p')->select('p.title as name',
                          'pp.id as pp_id',
                          'pt.id as pt_id', 
                          'pt.product_id',
                          'pt.position',
                          'pt.price',
                          'pt.count',
                          'pp.num',
                          'pt.title',
                          'p.r_repeat',
                          'pos.name as p_name',
                          'pos.number as  p_number',
                          'pos.max as  p_max',
                          'pp.position_number'
                        )
        ->whereRaw("p.ISBN = '".$bar."'")
        ->whereRaw('p.'.$this->distributor_id_where_sql)
        ->leftJoin('productinfo_type as pt','p.id','=','pt.product_id')
        ->join('position_portion as pp','pp.productinfo_type','=','pt.id')
        ->join('position as pos','pp.position_id','=','pos.id')
        ->orderByRaw('pt.order_id asc, pt.id asc, pp.position_number asc')
        ->get();
    $goods = CommonService::objectToArray($goods);

    if(empty($goods)){
      return 'no';
      exit;
    }

    //dump($goods);
    $re = '
      <h5>商品名稱：'.$goods[0]['name'].'</h5>
      <table>
        <tr>
          <th style="width:30%">市價</th>
          <th style="width:30%">品項</th>
          <th style="width:15%">售價</th>
          <th style="width:100px">位置編碼</th>
        </tr>';
    foreach($goods as $k => $v){//數量不夠得跳過
      $pass = 0;
      if($v['num'] == 0){continue;}

      foreach($sell_array as $sk => $sv){
        if($sv['pp_id'] == $v['pp_id']){
          if($v['num'] < $sv['num']+1){
            $pass = 1;
            break;
          }
        }
      }
      if($pass == 1)
        continue;

      $p_code = $v['p_max'] == "1" ? $v['p_name'].$v['position_number'] : $v['p_name'].str_pad($v['position_number'], strlen($v['p_number']), "0", STR_PAD_LEFT);
      $re .= '<tr onclick="search_bar('.$v['pp_id'].','.$v['product_id'].')" >';
      $re .= '<td>'.$v['price'].'</td>';
      $re .= '<td>'.$v['title'].'</td>';
      $re .= '<td>'.$v['count'].'</td>';
      $re .= '<td>'.$p_code.'</td>';
      $re .= '</tr>';
    }
    $re .= '</table>';
    
    echo $re;
    /*
    $goods = array_values($goods);
    return $goods
    */
  }

  public function search_bar(Request $request){
    $pp_id = $request->post('pp_id');
    $product_id = $request->post('product_id');
    $sell_array = $request->post('sell_array');
    $sell_array = json_decode($sell_array,true);
    $goods = Db::table('productinfo as p')
                ->select('p.title',
                  'pt.product_id',
                  'pt.position',
                  'pt.price','pt.count','pt.num',
                  'pt.title as dis',
                  'pt.id as pt_id', 
                  'pp.id as pp_id', 
                  'pos.name as p_name',
                  'pos.number as p_number',
                  'pos.max as p_max',
                  'pp.position_number')
                ->whereRaw("pp.id = '".$pp_id."' and p.id='".$product_id."'")
                ->leftJoin('productinfo_type as pt','p.id','=','pt.product_id')
                ->join('position_portion as pp','pp.productinfo_type','=','pt.id')
                ->join('position as pos','pp.position_id', '=', 'pos.id')
                ->orderByRaw('pt.order_id asc, pt.id asc, pp.position_number asc')
                ->get();
    $goods = CommonService::objectToArray($goods);
    if(!empty($goods)){
      $goods[0]['num'] = 1;
      $goods[0]['title'] = $goods[0]['title'];
      $goods[0]['p_code'] = $goods[0]['p_max'] == "1" ? $goods[0]['p_name'].$goods[0]['position_number'] : $goods[0]['p_name'].str_pad($goods[0]['position_number'], strlen($goods[0]['p_number']), "0", STR_PAD_LEFT);
    }else{
      return 'no';
      exit;
    }

    $ck = 0;
    $pos = 0;
    foreach($sell_array as $k => $v){
      if($v['pp_id'] == $goods[0]['pp_id']){
        $sell_array[$k]['num']++;
        $ck = 1;
      }
      $pos = $k;
    }
    
    if($ck == 0){
      $sell_array[++$pos] = $goods[0];
    }

    $sell_array = array_values($sell_array);
    return json_encode($sell_array, JSON_UNESCAPED_UNICODE);
  }

  public function search_user(Request $request){
    $user_keyword = $request->post('user_keyword') ? $request->post('user_keyword') : "";
    $user_keyword = trim($user_keyword);

    /*會員資料 比對會員編號*/
    $MemberInstance = new MemberInstance(0);
    $rowData = $MemberInstance->get_user_data($addr_change="combine", $cond=[
      ['a.number', '=', $user_keyword],
    ]);

    if(!$rowData){
      /*會員資料 比對會員帳號*/
      $rowData = $MemberInstance->get_user_data($addr_change="combine", $cond=[
        ['a.email', '=', $user_keyword],
      ]);
    }

    // dump($rowData);exit;
    if($rowData){
      return [
        'id'=> $rowData['id'],
        'name'=> $rowData['name'],
      ];
    }else{
      return [
        'id'=> '0',
        'name'=> '遊客',
      ];
    }
  }
}