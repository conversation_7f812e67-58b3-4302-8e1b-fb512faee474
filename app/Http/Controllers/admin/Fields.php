<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\ExaminationHelper;

class Fields extends MainController{
  const PER_PAGE_ROWS = 20;
  const SIMPLE_MODE_PAGINATE = false;

  private $fieldsTable;
  private $commentsTable;
  public function __construct()
  {
    parent::__construct();
    $this->fieldsTable = 'fields_set';
    $this->commentsTable = 'comments_set';
  }

  /*常用欄位管理*/
    /*列表*/
    public function fields_set(Request $request){
      return view('admin.fields.fields_set',['data'=>$this->data]);
    }
    /*取得欄位與基本設定資料*/
    public function get_fields(Request $request){
      $get = $request->query();
      $return_data = [];
      
      /*基本設定資料*/
      $need_setting_data = $get['need_setting_data'] ?? 0;
      if($need_setting_data!=0){
        $get_fields_type_par = ExaminationHelper::get_fields_type_par();
        foreach ($get_fields_type_par as $key => $value) {
          $return_data[$key] = $value;
        }
        
        /*常用註記詞*/
        $comments_set = Db::table($this->commentsTable)
                          ->whereRaw($this->distributor_id_where_sql)
                          ->where('online', 1)
                          ->orderByRaw('order_id asc, id desc')->get();
        $return_data['comments_set'] = CommonService::objectToArray($comments_set);
      }

      /*篩選參數*/
      $searchOnline = isset($get['searchOnline']) ? $get['searchOnline'] : '-1';
      $searchKey = isset($get['searchKey']) ? trim($get['searchKey']) : '';
      $page = isset($get['page']) ? trim($get['page']) : 1;
      $return_data['num_pre_count'] = ($page-1)*self::PER_PAGE_ROWS;
      /*撈取資料*/
      $datas = Db::table($this->fieldsTable);
      if($searchOnline!='-1'){
        $datas = $datas->where('online', $searchOnline);
      }
      if($searchKey!=''){
        $datas = $datas->where(function ($query) use ($searchKey) {
          $query->where('title', 'like', "%".$searchKey."%")
                ->orWhere('discription', 'like', "%".$searchKey."%");
        });
      }
      $datas = $datas->whereRaw($this->distributor_id_where_sql)
                    ->orderByRaw('order_id asc, id desc')
                    ->paginate(
                      self::PER_PAGE_ROWS
                    )->appends([
                      'searchOnline' => $searchOnline,
                      'searchKey' => $searchKey,
                    ]);
      if(empty($datas->items()) == false) {
        foreach($datas->items() as $key => $item) {
          $datas->items()[$key]->options = $item->options ? json_decode($item->options) : [];
        }
      }
      $return_data['datas'] = $datas;
      $return_data['datas_items'] = $datas->items();
      $return_data['datas_links'] = "{$datas->links('pagination::bootstrap-5')}";
      // dump($return_data);exit;
      return $return_data;
    }
    /*新增、修改*/
    public function fields_set_save(Request $request){
      $post = $request->post();
      $data = [
        'title' => isset($post['title']) ? $post['title'] : "",
        'type' => isset($post['type']) ? $post['type'] : "",
        'required' => isset($post['required']) ? $post['required'] : 1,
        'special' => isset($post['special']) ? $post['special'] : 1,
        'limit' => isset($post['limit']) ? $post['limit'] : "",
        'discription' => isset($post['discription']) ? $post['discription'] : "",
        'options' => isset($post['options']) ? json_encode($post['options'], JSON_UNESCAPED_UNICODE) : "[]",
        'online' => isset($post['online']) ? $post['online'] : 1,
      ];
      if(isset($post['order_id'])){
        $data['order_id'] = $post['order_id']===null ? 0 : $post['order_id'];
      }

      if(!isset($post['id'])){$this->error('儲存失敗');}
      $id = $post['id'];

      if(!$data['type']){$this->error('請選擇資料類型');}
      if(!$data['title']){$this->error('請輸入標題');}

      if($id=='0'){ /*新增*/
        if($this->admin_type=='distribution'){
          $data['distributor_id'] = session()->get($this->admin_type)['id'];
        }
        $id = DB::table($this->fieldsTable)->insertGetId($data);
      }
      else{ /*編輯*/
        if(!parent::check_controll($this->fieldsTable, $id)){
          $this->error('您無法操作此項目');
        }
        DB::table($this->fieldsTable)->where('id', $id)->update($data);
      }
      if(isset($data['order_id'])){
        // 自動更新排序
        $table = $this->fieldsTable;
        $column = 'order_id';
        $order_num = $data['order_id'];
        $primary_key = 'id';
        $primary_value = $id;
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $this->distributor_id_where_sql);
      }

      $this->success(Lang::get('儲存成功'));
    }
    /*刪除*/
    public function fields_set_delete(Request $request){
      $post = $request->post();

      if(!parent::check_controll($this->fieldsTable, $post['id'])){
        $this->error('您無法操作此項目');
      }
      if(!isset($post['id'])){$this->error('刪除失敗');}
      if( in_array($post['id'], [1,2,3]) ){$this->error('此欄位不可刪除');}

      DB::table($this->fieldsTable)->where('id', $post['id'])->delete();
      $this->success('刪除成功');
    }


  /*常用註記詞管理*/
    /*列表*/
    public function comments_set(Request $request){
      $get = $request->query();
      $searchOnline = isset($get['searchOnline']) ? $get['searchOnline'] : '-1';
      $this->data['searchOnline'] = $searchOnline;
      $searchKey = isset($get['searchKey']) ? trim($get['searchKey']) : '';
      $this->data['searchKey'] = $searchKey;
      $page = isset($get['page']) ? trim($get['page']) : 1;
      $this->data['num_pre_count'] = ($page-1)*self::PER_PAGE_ROWS;

      $datas = Db::table($this->commentsTable);
      if($searchOnline!='-1'){
        $datas = $datas->where('online', $searchOnline);
      }
      if($searchKey!=''){
        $datas = $datas->where(function ($query) use ($searchKey) {
          $query->where('title', 'like', "%".$searchKey."%")
                ->orWhere('content', 'like', "%".$searchKey."%");
        });
      }

      $datas = $datas->whereRaw($this->distributor_id_where_sql)
                    ->orderByRaw('order_id asc, id desc')
                    ->paginate(self::PER_PAGE_ROWS)
                    ->appends([
                      'searchOnline' => $searchOnline,
                      'searchKey' => $searchKey,
                    ]);

      $this->data['datas'] = $datas;
      
      return view('admin.fields.comments_set',['data'=>$this->data]);
    }
    /*新增、修改*/
    public function comments_set_save(Request $request){
      $post = $request->post();
      $data = [
        'title' => isset($post['title']) ? $post['title'] : "",
        'content' => isset($post['content']) ? $post['content'] : "",
        'online' => isset($post['online']) ? $post['online'] : 1,
      ];
      if(isset($post['order_id'])){
        $data['order_id'] = $post['order_id']===null ? 0 : $post['order_id'];
      }

      if(!isset($post['id'])){$this->error('儲存失敗');}
      $id = $post['id'];

      if(!$data['title']){$this->error('請輸入標題');}

      if($id=='0'){ /*新增*/
        if($this->admin_type=='distribution'){
          $data['distributor_id'] = session()->get($this->admin_type)['id'];
        }
        $id = DB::table($this->commentsTable)->insertGetId($data);
      }
      else{ /*編輯*/
        if(!parent::check_controll($this->commentsTable, $id)){
          $this->error('您無法操作此項目');
        }
        DB::table($this->commentsTable)->where('id', $id)->update($data);
      }
      if(isset($data['order_id'])){
        // 自動更新排序
        $table = $this->commentsTable;
        $column = 'order_id';
        $order_num = $data['order_id'];
        $primary_key = 'id';
        $primary_value = $id;
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $this->distributor_id_where_sql);
      }

      $this->success('儲存成功');
    }
    /*刪除*/
    public function comments_set_delete(Request $request){
      $post = $request->post();
      if(!parent::check_controll($this->commentsTable, $post['id'])){
        $this->error('您無法操作此項目');
      }
      if(!isset($post['id'])){$this->error('刪除失敗');}
      DB::table($this->commentsTable)->where('id', $post['id'])->delete();
      $this->success('刪除成功');
    }
}