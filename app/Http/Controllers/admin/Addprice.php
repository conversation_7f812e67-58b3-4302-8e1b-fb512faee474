<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\AddpriceHelper;

class Addprice extends MainController
{
  private $table;
  private $actProdTable;
  private $ruleProdTable;

  public function __construct() {
    parent::__construct();
    $this->table = 'addprice';
    $this->actProdTable = 'addprice_product';
    $this->ruleProdTable = 'addprice_rule';
  }

  /*PAGE---------------------------------*/
  public function index(Request $request) {
    $searchtype = !empty($request->get('searchtype')) ? $request->get('searchtype') : 0;
    $this->data['searchtype'] = $searchtype;
    return view('admin.addprice.index',['data'=>$this->data]);
  }
  public function edit(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->table, $id)){
      $this->error(Lang::get('您無法編輯此項目'));
    }
    return view('admin.addprice.edit',['data'=>$this->data]);
  }

  /*API---------------------------------*/
  /*新增加價購*/
  public function doCreate(Request $request) {
    $newData = $request->post();
    $newData['title'] = $newData['title'] ?? '';
    if(!$newData['title']){ $this->error(Lang::get('請輸入標題')); }
    
    $newData['discount'] = $newData['discount'] ?? '';
    if(!$newData['discount']){ $this->error(Lang::get('請輸入加購折扣')); }
    if($newData['discount']<0 || $newData['discount']>1){ $this->error(Lang::get('優惠折扣請輸入0~1之間的數')); }
    
    $newData['start_time'] = $newData['start_time'] ?? '';
    if(!$newData['start_time']){ $this->error(Lang::get('請選擇開始時間')); }

    $newData['noEndTime'] = $newData['noEndTime'] ?? 'false';
    if($newData['noEndTime']=='true'){ $newData['end_time'] = ''; }
    $newData['end_time'] = $newData['end_time'] ?? '';
    if(!$newData['end_time'] && $newData['noEndTime']=='false' ){ $this->error(Lang::get('請選擇結束時間')); }
    unset($newData['noEndTime']);

    if($this->admin_type=='distribution'){
      $newData['distributor_id'] = session()->get($this->admin_type)['id'];
    }

    $new_id = Db::table($this->table)->insertGetId($newData);
    $this->success(Lang::get('操作成功'), url('Addprice/edit').'?id='.$new_id);
  }
  /*更新加價購資料*/
  public function doUpdate(Request $request){
    $actData = $request->post();
    $actInfo = $actData['act'];
    // dump($actData);

    if(!parent::check_controll($this->table, $actInfo['id'])){
      $this->error(Lang::get('您無法編輯此項目'));
    }

    if(!$actInfo['title']){ $this->error(Lang::get('請輸入標題')); }
    if(!$actInfo['discount']){ $this->error(Lang::get('請輸入加購折扣')); }
    if($actInfo['discount']<0 || $actInfo['discount']>1){ $this->error(Lang::get('優惠折扣請輸入0~1之間的數')); }
    if(!$actInfo['start_time']){ $this->error(Lang::get('請選擇開始時間')); }
    if(!$actInfo['end_time'] && $actInfo['noEndTime']=='false' ){ $this->error(Lang::get('請選擇結束時間')); }
    $actInfo['end_time'] = $actInfo['noEndTime']=='true' ? '' : $actInfo['end_time'];
    unset($actInfo['noEndTime']);
    
    // dump($actInfo);exit;
    DB::table($this->table)->where('id',$actInfo['id'])->update($actInfo);
    $this->success(Lang::get('操作成功'));
  }
  /*更新狀態*/
  public function changeOnline(Request $request){
    $itemData = $request->post();
    if(!parent::check_controll($this->table, $itemData['id'])){
      $this->error(Lang::get('您無法編輯此項目'));
    }

    $online =  ($itemData['online'] == 'true') ? 1 : 0;
    $result = Db::table($this->table)->where('id',$itemData['id'])->update(['online' => $online]);

    $this->success(Lang::get('操作成功'));
  }
  /*更改可購買上限*/
  public function updatelimitNum(Request $request){
    $id = $request->post('adpp_id');

    $result = Db::table($this->actProdTable)->whereRaw('id ="'.$id.'"')->first();
    if(!$result){ $this->error(Lang::get('無資料')); }
    if(!parent::check_controll($this->table, $result->addprice_id)){
      $this->error(Lang::get('您無法編輯此項目'));
    }

    $result = Db::table($this->actProdTable)->whereRaw('id ="'.$id.'"')->update(['limit_num'=>$request->post('limitNum')]);
    if($result){
      $this->success(Lang::get('操作成功'));
    }else{
      $this->error(Lang::get('無資料需要修改'));
    }
  }

  /*依條件取得加價購*/
  public function getList(Request $request){
    $type = $request->post('type');
    $searchKey = $request->post('searchKey');
    $start = $request->post('start');
    $end = $request->post('end');
    $searchtype = $request->post('searchtype');

    if($type == 'keyword' && !empty($searchKey)){
      $searchKey = trim($searchKey);
      $list = Db::table($this->table)->whereRaw('title like "%'.$searchKey.'%"');
      $search = $searchKey;
    }else{
      $list = Db::table($this->table);
      $search = "";
    }
    $list = $list->whereRaw($this->distributor_id_where_sql)->get();

    if($type == 'date'){
      $list = AddpriceHelper::getListByDate($start, $end)->whereRaw($this->distributor_id_where_sql)->get();
      $search = $start."~".$start;
    }
    $list = CommonService::objectToArray($list);
    /*計算各加價購展出商品數量*/
    foreach ($list as $key => $value) {
      $list[$key]['count'] = Db::table($this->actProdTable)->where('addprice_id',$value['id'])->count();
      if($value['end_time']=="0000-00-00 00:00:00") $list[$key]['end_time'] = Lang::get('無時間');
    }

    $retData = ['actList' => $list, 'search'=>$search];
    return $retData;
  }

  /*刪除加價購*/
  public function doDel(Request $request){
    if(!parent::check_controll($this->table, $request->post('id'))){
      $this->error(Lang::get('您無法編輯此項目'));
    }

    /*刪除加價購*/
    Db::table($this->table)->where('id',$request->post('id'))->delete();

    /*刪除加價購的加購商品紀錄*/
    Db::table($this->actProdTable)->where('addprice_id',$request->post('id'))->delete();

    /*刪除加價購的條件商品紀錄*/
    Db::table($this->ruleProdTable)->where('addprice_id',$request->post('id'))->delete();

    $this->success(Lang::get('操作成功'));
  }

  /*取得加價購內容及已套用商品*/
  public function getDetail(Request $request){
    $actId = $request->post('actId');

    if(!parent::check_controll($this->table, $actId)){
      $retData = [
        'status'  => 200,
        'actInfo' => [],
        'ruleProd'=> [],
        'actProd' => [],
      ];
      return $retData;
    }

    $actInfo = Db::table($this->table)->whereRaw('id ="'.$actId.'"')->first();
    $actInfo = CommonService::objectToArray($actInfo);
    $actInfo['noEndTime'] = $actInfo['end_time'] == '0000-00-00 00:00:00' ? true : false;

    $ruleProd = Db::table($this->ruleProdTable .' as adrp')
                  ->select('pt.*','pi.title as pi_title','pi.pic','adrp.id as adrp_id')
                  ->rightJoin('productinfo_type as pt', 'pt.id','=','adrp.product_type_id')
                  ->join('productinfo as pi','pi.id','=','pt.product_id')
                  ->whereRaw('adrp.addprice_id = "'.$actId.'" AND pt.online=1')
                  ->get();
    $ruleProd = CommonService::objectToArray($ruleProd);
    foreach($ruleProd as $k => $v){
      $pic1 = json_decode($v['pic'],true);
      if($pic1==null){
        $ruleProd[$k]['pic1'] = "";
      }else{
        $ruleProd[$k]['pic1'] = $pic1[0];
      }
    }

    $actProd = Db::table($this->actProdTable . ' as adpp')
                  ->select('pt.*','pi.title as pi_title','pi.pic','adpp.id as adpp_id','adpp.limit_num')
                  ->rightJoin('productinfo_type as pt', 'pt.id','=','adpp.product_type_id')
                  ->join('productinfo as pi','pi.id','=','pt.product_id')
                  ->whereRaw('adpp.addprice_id = "'.$actId.'" AND pt.online=1')
                  ->get();
    $actProd = CommonService::objectToArray($actProd);
    foreach($actProd as $k => $v){
      $pic1 = json_decode($v['pic'],true);
      if($pic1==null){
        $actProd[$k]['pic1'] = "";
      }else{
        $actProd[$k]['pic1'] = $pic1[0];
      }
    }

    $retData = [
      'status'  => 200,
      'actInfo' => $actInfo,
      'ruleProd'=> $ruleProd,
      'actProd' => $actProd,
    ];
    return $retData;
  }

  /*添加商品*/
  public function insertActProd(Request $request){
    $tab = $request->get('tab') ?? '';
    if($tab=='RuleProd'){ /*添加條件商品*/
      return $this->insertActProd_withtype($request, $this->ruleProdTable);
    }
    else if($tab=='ActProd'){ /*添加活動商品*/
      return $this->insertActProd_withtype($request, $this->actProdTable);
    }
  }
  private function insertActProd_withtype($request, $targetTable){
    $actData = $request->post();
    $actId 	 = $actData['actId'];
    if(!parent::check_controll($this->table, $actId)){
      $this->error(Lang::get('您無法編輯此項目'));
    }

    /*處理添加商品資料*/
    $addProd = [];
    foreach ($actData['cateProd'] as $k_p => $v_p){
      if (isset($v_p['select'])){
        if($v_p['select'] == 'true'){
          $productinfo_type = Db::table('productinfo_type')->find($v_p['id']);
          if(empty($productinfo_type)){ continue; }
          $productinfo = Db::table('productinfo')->find($productinfo_type->product_id);
          $productinfo = CommonService::objectToArray($productinfo);
          if(empty($productinfo)){ continue; }
          $is_yours = false;
          if($this->admin_type=='distribution'){
            if($productinfo['distributor_id']==session()->get($this->admin_type)['id']){
              $is_yours = true;
            }
          }else{
            if($productinfo['distributor_id']==0){
              $is_yours = true;
            }
          }
          if($is_yours){
            array_push($addProd, [
              'addprice_id'    => $actId ,
              'product_type_id'=> $v_p['id']
            ]);
          }
        }
      }
    }
    // dd($addProd);
    Db::table($targetTable)->insert($addProd);
    $this->success(Lang::get('操作成功'));
  }

  /*刪除商品*/
  public function delActProd(Request $request){
    $tab = $request->get('tab') ?? '';
    if($tab=='RuleProd'){ /*刪除條件商品*/
      return $this->delActProd_withtype($request, $this->ruleProdTable);
    }
    else if($tab=='ActProd'){ /*刪除活動商品*/
      return $this->delActProd_withtype($request, $this->actProdTable);
    }
  }
  /*刪除加價購商品*/
  private function delActProd_withtype($request, $targetTable){
    $postData = $request->post();
    $actId = $postData['actId'];
    if(!parent::check_controll($this->table, $actId)){
      $this->error(Lang::get('您無法編輯此項目'));
    }

    $delProd = $request->post('cateProd');
    foreach($delProd as $d_k => $d_v){
      if (isset($d_v['select'])) {
        if ($d_v['select'] == 'true') {
          Db::table($targetTable)->where('product_type_id',$d_v['id'])->where('addprice_id',$actId)->delete();
        }
      }
    }
    $this->success(Lang::get('操作成功'));
  }

  /*取得允許添加的商品清單*/
  public function getAddableProd(Request $request){
    $tab = $request->get('tab') ?? '';
    if($tab=='RuleProd'){ /*取得允許添加的條件商品清單*/
      return $this->getAddableProd_withtype($request, $this->ruleProdTable);
    }
    else if($tab=='ActProd'){ /*取得允許添加的活動商品清單*/
      return $this->getAddableProd_withtype($request, $this->actProdTable);
    }
  }
  private function getAddableProd_withtype($request, $targetTable, $useCate=true){
    $id = $request->post('cateId');
    $first = $request->post('first');
    $actId = $request->post('actId');
    // dump($id,$first,$actId);
    // exit;

    /*取得此加價購資料*/
    $actInfo = Db::table($this->table)->where('id',$actId)->first();

    /*找已加入此加價購的商品*/
    $usedProduct = Db::table($targetTable)->where('addprice_id',$actId)->get();
    $usedProduct = CommonService::objectToArray($usedProduct);
    $usedProduct_where = [];
    array_walk($usedProduct, function($item)use(&$usedProduct_where){
      array_push($usedProduct_where, $item['product_type_id']);
    });
    $usedProduct_where = $usedProduct_where ? ' AND pt.id not in ('. implode(',', $usedProduct_where).') ' : ' AND true ';
    // dump($usedProduct_where);

    /*找出未勾選的品項*/
    $productinfo = Db::table('productinfo as pi')->select('pt.*','pi.title as pi_title','pi.pic')
                                                ->rightJoin('productinfo_type as pt', 'pt.product_id','=','pi.id');
    if($useCate){
      /*階層搜尋*/
      $productinfo = $productinfo->leftJoin('productinfo_orders as po','po.prod_id','=','pi.id')
                                ->whereRaw('true '.$usedProduct_where);
      if($first){
        //是第一階(分館)
        if($id){
          $productinfo = $productinfo->whereRaw("final_array like '%\"prev_id\":\"".$id."\"%\"parent_id\":\"0\"%'");
          $productinfo_orders_where = 'po.prev_id='.$id.' AND po.branch_id=0';
        }else{
          $productinfo_orders_where = '1=1';
        }
        $order_sql = "po.order_id asc, pi.id desc";
      }else{
        //其他(分類)
        if($id){
          $productinfo = $productinfo->whereRaw("final_array like '%\"branch_id\":\"".$id."\"%' ");
          $productinfo_orders_where = 'po.branch_id='.$id;
        }else{
          $productinfo_orders_where = '1=1';
        }
        $order_sql = "po.order_id asc, pi.id desc";
      }

      $productinfo = $productinfo->whereRaw($productinfo_orders_where)->orderByRaw($order_sql);
    }
    else{
      /*無階層搜尋*/
      $productinfo = $productinfo->whereRaw('true '.$usedProduct_where);
    }

    $productinfo = $productinfo->whereRaw('pt.online=1')
                              ->whereRaw($this->distributor_id_where_sql)
                              ->orderByRaw('pt.order_id asc')->orderByRaw('pt.id asc')
                              ->groupBy('pt.id')
                              ->get();
    $productinfo = CommonService::objectToArray($productinfo);
    foreach($productinfo as $k => $v){
      $pic1 = json_decode($v['pic'],true);
      if($pic1==null){
        $productinfo[$k]['pic1'] = "";
      }else{
        $productinfo[$k]['pic1'] = $pic1[0];
      }
    }
    // dump($productinfo);
    // exit;
      
    //echo Db::table('productinfo')->getLastSql();
    $this->success(['cateId' => $id,'productinfo' => $productinfo]);
  }
}
