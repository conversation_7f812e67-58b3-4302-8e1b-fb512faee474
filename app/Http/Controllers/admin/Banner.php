<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\admin\Template\TemplateArticle;

class Banner extends TemplateArticle
{
    public function __construct() {
        $this->controllerName = "Banner";
        $this->resTableName = "frontend_menu_name";

        parent::__construct($this->controllerName, $this->resTableName, 1920, 616);
    }

    public function index(Request $request){
        $this->data['table_name'] = $this->resTableName;

        $db_result = Db::table($this->resTableName);

        $functions = [
            '關於我們' => 1,
            '新增商品' => 2,
            '有感體驗' => 3,
            '活動專區' => 4,
            '常見問題' => 5,
            '經銷據點' => 6,
            '最新消息' => 7,
            '找貨回函' => 8,
            '消費功能' => 9,
        ];

        // 預設排除：消費功能
        $not_in = [9];

        foreach(config('control.close_function_current') as $key => $value) {
            if (in_array($key, array_keys($functions))) {
                array_push($not_in, $functions[$key]);
            }
        }

        if (!empty($not_in)) {
            $db_result = $db_result->whereNotIn('id', $not_in);
        }

        $searchKey = $request->get('searchKey') ?? '';
        $this->data['searchKey'] = trim($searchKey);

        if ($searchKey) {
            $db_result = $db_result->where('name', 'like', "%$searchKey%");
        }

        $db_result = $db_result->orderBy('id', 'asc')->get();

        $this->data[$this->resTableName] = $db_result;

        return view('admin.banner.index',['data'=>$this->data]);
    }

    public function cellCtrl(Request $request) {
        $request_data = $request->all();

        if (empty($request_data['name']) == false) {
            $frontend_menu_name = Db::table($this->resTableName)->where('id', $request_data['id'])->pluck('backstage_menu_second_id')->first();

            if (empty($frontend_menu_name) == false) {
                Db::table('backstage_menu_second')->where('id', $frontend_menu_name)->update(['show_name' => $request_data['name']]);
            }
        }

        return parent::cellCtrl($request);
    }
}