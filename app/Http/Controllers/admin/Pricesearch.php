<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\admin\Template\TemplateArticle;
use App\Services\CommonService;

class Pricesearch extends TemplateArticle
{
    public function __construct() {
        $this->controllerName = "Pricesearch";
        $this->resTableName = "product_price_search";

        parent::__construct($this->controllerName, $this->resTableName, 0, 0);
    }

    public function index(Request $request) {
        $this->data['table_name'] = $this->resTableName;

        $searchKey = $request->get('searchKey') ?? '';
        $this->data['searchKey'] = trim($searchKey);

        $db_result = Db::table($this->resTableName)->whereRaw($this->distributor_id_where_sql);

        if ($searchKey) {
            $db_result = $db_result->where('title', 'like', "%$searchKey%");
        }

        $db_result = $db_result->orderBy('orders', 'asc')->orderBy('id', 'asc')->get();
        
        $this->data[$this->controllerName] = CommonService::objectToArray($db_result);

        return view('admin.pricesearch.index', ['data'=>$this->data]);
    }
}