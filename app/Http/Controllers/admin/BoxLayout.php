<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Services\CommonService;

class BoxLayout extends MainController
{
    const PER_PAGE_ROWS = 20;
    private $tableName = 'box_layout_cate';
    private $subTableName = 'box_layout';

    public function index(Request $request) {
        $this->data['searchKey'] = $request->get('searchKey') ?? '';

        $box_layout_cate = DB::table('box_layout_cate');

        if ($this->data['searchKey']) {
            $box_layout_cate = $box_layout_cate->where('name', 'like', '%' . $this->data['searchKey'] . '%');
        }

        $box_layout_cate = $box_layout_cate->where('removed', 0)
        ->orderBy('order')
        ->orderBy('id')
        ->paginate(self::PER_PAGE_ROWS)
        ->appends([
            'searchKey' => $this->data['searchKey']
        ]);

        foreach ($box_layout_cate->items() as $key => $value) {
            $box_layout_cate->items()[$key]->count = DB::table($this->subTableName)->where('cate_id', $value->id)->where('removed', 0)->count();
        }

        $this->data['box_layout_cate'] = $box_layout_cate;

        return view('admin.boxlayout.index', ['data' => $this->data]);
    }

    private function basic_check($request) {
        if (empty($request['id'])) {
            throw new \Exception('找不到此版型分類');
        }

        $db_result = DB::table('box_layout_cate')->where('id', $request['id'])->first();
        $db_result = CommonService::objectToArray($db_result);

        if (empty($db_result)) {
            throw new \Exception('找不到此版型分類');
        }

        return $db_result;
    }

    public function edit_layout(Request $request) {
        try {
            $layout = $this->basic_check($request->all());
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->data['layout'] = $layout;

        return view('admin.boxlayout.new', ['data' => $this->data]);
    }

    public function check_box_content_cate() {
        return DB::table('box_content_cate')->where('removed', 0)->count();
    }

    public function create_and_update(Request $request) {
        try {
            if (empty($request->post('id'))) {
                throw new \Exception('找不到此版型分類');
            }

            if (empty($request->post('name'))) {
                throw new \Exception('請輸入版型分類名稱');
            }

            $response = [
                'code' => 1,
                'msg' => '',
                'id' => 0,
            ];

            if ($request->post('id') == -1) {
                if (DB::table($this->tableName)->where('name', $request->post('name'))->count() > 0) {
                    throw new \Exception('分類名稱已存在');
                }

                $id = DB::table($this->tableName)->insertGetId([
                    'name' => $request->post('name'),
                    'order' => $request->post('order') ?? 0,
                ]);

                $response['msg'] = '新增成功';
                $response['id'] = $id;
            } else {
                $this->basic_check($request->all());

                $result = DB::table($this->tableName)->where('id', $request->post('id'))->update([
                    'name' => $request->post('name'),
                    'order' => $request->post('order') ?? 0,
                ]);

                if (empty($result)) {
                    throw new \Exception('更新失敗');
                }

                $response['msg'] = '更新成功';
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return $response;
    }

    public function change_online(Request $request) {
        try {
            $this->basic_check($request->all());

            if (DB::table('customized_box_info')->where('layouts_allowed', 'like', '%"' . $request->post('id') . '"%')->count() > 0) {
                throw new \Exception('尚有商品正在使用此版型，無法下架');
            }

            $online_original = DB::table($this->tableName)->where('id', $request->post('id'))->value('online');

            $result = DB::table($this->tableName)->where('id', $request->post('id'))->update([
                'online' => abs($online_original - 1),
            ]);

            if (empty($result)) {
                throw new \Exception('修改失敗');
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('修改成功');
    }

    public function change_order(Request $request) {
        try {
            $this->basic_check($request->all());

            if ($request->post('order') < 0) {
                throw new \Exception('排序不能小於0');
            }

            $result = DB::table($this->tableName)->where('id', $request->post('id'))->update([
                'order' => intval($request->post('order'))
            ]);

            if (empty($result)) {
                throw new \Exception('修改失敗');
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('修改成功');
    }

    public function delete_one(Request $request) {
        try {
            $this->basic_check($request->all());

            if (DB::table($this->subTableName)->where('cate_id', $request->post('id'))->where('removed', 0)->count() > 0) {
                throw new \Exception('此分類中尚有版型，無法刪除');
            }
    
            $result = DB::table($this->tableName)->where('id', $request->post('id'))->update([
                'removed' => 1,
            ]);

            if (empty($result)) {
                throw new \Exception('刪除失敗');
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    
        $this->success('刪除成功');
    }

    public function multi_operate(Request $request) {
        try {
            if (empty($request->post('ids'))) {
                throw new \Exception('請選擇要操作的版型');
            }

            $ids = $request->post('ids');

            if ($request->post('action') == 'delete') {
                foreach ($ids as $id) {
                    $layout_name = DB::table($this->tableName)->where('id', $id)->value('name');

                    if (DB::table($this->subTableName)->where('cate_id', $id)->where('removed', 0)->count() > 0) {
                        throw new \Exception('分類名稱 "' . $layout_name . '" 中尚有版型，無法刪除');
                    }
                }

                $result = DB::table($this->tableName)->whereIn('id', $ids)->update([
                    'removed' => 1,
                ]);

                if (empty($result)) {
                    throw new \Exception('刪除失敗');
                }
            } else if ($request->post('action') == 'online') {
                $result = DB::table($this->tableName)->whereIn('id', $ids)->update([
                    'online' => 1,
                ]);

                if (empty($result)) {
                    throw new \Exception('更新失敗');
                }
            } else if ($request->post('action') == 'offline') {
                $result = DB::table($this->tableName)->whereIn('id', $ids)->update([
                    'online' => 0,
                ]);

                if (empty($result)) {
                    throw new \Exception('更新失敗');
                }
            } else {
                throw new \Exception('不支援的操作');
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('操作成功');
    }

    public function get_layout(Request $request) {
        try {
            $this->basic_check($request->all());

            $layouts = DB::table($this->subTableName)->where('cate_id', $request->post('id'))->where('removed', 0)->get();
            $layouts = CommonService::objectToArray($layouts);

            $return_data = [];

            foreach ($layouts as $layout) {
                $insert = [];

                $insert['id'] = $layout['id'];
                $insert['image_path'] = $layout['image_path'];
                $insert['file_name'] = explode('/', $layout['image_path'])[count(explode('/', $layout['image_path'])) - 1];

                $blocks = DB::table('box_layout_block')->where('layout_id', $layout['id'])->get();
                $blocks = CommonService::objectToArray($blocks);

                $insert['blocks'] = $blocks;
                $insert['block_num'] = count($blocks);

                // block 中的 items 儲存格式為 json_encode(內容物分類 id 的 array, true)
                foreach ($insert['blocks'] as $block_key => $block) {
                    $items = json_decode($block['items'], true);

                    $insert['blocks'][$block_key]['items'] = [];

                    // 由數字(id)轉換為具有 id 及分類名稱的 array
                    foreach ($items as $item) {
                        $db_result = DB::table('box_content_cate')->select('id', 'name AS category_name')->where('id', $item)->where('removed', 0)->first();
                        $db_result = CommonService::objectToArray($db_result);

                        if (empty($db_result) == false) {
                            $insert['blocks'][$block_key]['items'][] = $db_result;
                        }
                    }
                }

                $return_data[] = $insert;
            }   
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return [
            'code' => 1,
            'msg' => '取得成功',
            'data' => $return_data,
        ];
    }

    public function get_modal_block_items() {
        try {
            $db_result = DB::table('box_content_cate')->select(['id', 'name AS category_name'])->where('removed', 0)->orderBy('order')->get();
            $db_result=  CommonService::ObjectToArray($db_result);

            if (empty($db_result)) {
                throw new \Exception('找不到內容物分類，請至 F商品管理區/禮盒內容物管理 中新增');
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return [
            'code' => 1,
            'msg' => '取得成功',
            'data' => $db_result,
        ];
    }

    public function store(Request $request) {
        try {
            $this->basic_check($request->all());

            $original_layouts = DB::table($this->subTableName)->where('cate_id', $request->post('id'))->pluck('id')->toArray();

            foreach ($request->post('layouts') as $layout) {
                // 移除現有layout或新增layout
                if (in_array($layout['id'], $original_layouts) == false) {
                    // 移除
                    if ($layout['id'] != 0) {
                        DB::table($this->subTableName)->where('id', $layout['id'])->update(['removed' => 1]);
                        DB::table('box_layout_block')->where('layout_id', $layout['id'])->update(['removed' => 1]);
                    // 新增
                    } else {
                        $id = DB::table($this->subTableName)->insertGetId([
                            'cate_id' => $request->post('id'),
                            'image_path' => $this->make_image($layout['file_name'], $layout['image_path']),
                        ]);

                        foreach ($layout['blocks'] as $block) {
                            $store_block_items = array_column($block['items'], 'id');
                            sort($store_block_items);

                            DB::table('box_layout_block')->insert([
                                'layout_id' => $id,
                                'items' => json_encode($store_block_items),
                                'width' => $block['width'],
                                'height' => $block['height'],
                                'left' => $block['left'],
                                'top' => $block['top'],
                            ]);
                        }
                    }
                // 編輯現有layout
                } else {
                    if (strpos($layout['image_path'], 'data:image') !== false) {
                        DB::table($this->subTableName)->where('id', $layout['id'])->update([
                            'image_path' => $this->make_image($layout['file_name'], $layout['image_path']),
                        ]);
                    }

                    $original_blocks = DB::table('box_layout_block')->where('layout_id', $layout['id'])->pluck('id')->toArray();

                    foreach ($layout['blocks'] as $block) {
                        // 編輯現有block
                        if (in_array($block['id'], $original_blocks)) {
                            $store_block_items = array_column($block['items'], 'id');
                            sort($store_block_items);

                            DB::table('box_layout_block')->where('id', $block['id'])->update([
                                'layout_id' => $layout['id'],
                                'items' => json_encode($store_block_items),
                                'width' => $block['width'],
                                'height' => $block['height'],
                                'left' => $block['left'],
                                'top' => $block['top'],
                            ]);
                        // 移除block或新增block
                        } else {
                            // 移除
                            if (isset($block['id'])) {
                                DB::table('box_layout_block')->where('id', $block['id'])->update(['removed' => 1]);
                            // 新增
                            } else {
                                $store_block_items = array_column($block['items'], 'id');
                                sort($store_block_items);

                                DB::table('box_layout_block')->insert([
                                    'layout_id' => $layout['id'],
                                    'items' => json_encode($store_block_items),
                                    'width' => $block['width'],
                                    'height' => $block['height'],
                                    'left' => $block['left'],
                                    'top' => $block['top'],
                                ]);
                            }
                        }
                    }
                }
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('操作成功');
    }

    private function make_image($file_name, $base64_image) {
        $store_dir = 'public' . DS . 'static' . DS . 'index' . DS . 'upload' . DS . 'box_layout';

        if (file_exists(ROOT_PATH . $store_dir) == false) {
            mkdir(ROOT_PATH . $store_dir);
        }

        $upload_path_raw = CommonService::uploadFile(DS . $store_dir, $base64_image, CommonService::randomkeys(4) . '_' . pathinfo($file_name, PATHINFO_FILENAME));

        return parse_url($upload_path_raw, PHP_URL_PATH);
    }

    public function delete_layout(Request $request) {
        try {
            if (empty($request['id'])) {
                throw new \Exception('找不到此版型');
            }
    
            $db_result = DB::table($this->subTableName)->where('id', $request['id'])->first();
            $db_result = CommonService::objectToArray($db_result);
    
            if (empty($db_result)) {
                throw new \Exception('找不到此版型');
            }

            if (DB::table('customized_box_info')->where('layouts_allowed', 'like', '%"' . $request['id'] . '"%')->where('removed', 0)->count() > 0) {
                throw new \Exception('尚有禮盒正在使用此版型，無法刪除');
            }

            $result = DB::table($this->subTableName)->where('id', $request['id'])->update(['removed' => 1]);

            if (empty($result)) {
                throw new \Exception('刪除失敗');
            }

            DB::table('box_layout_block')->where('layout_id', $request['id'])->update(['removed' => 1]);
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('刪除成功');
    }
}