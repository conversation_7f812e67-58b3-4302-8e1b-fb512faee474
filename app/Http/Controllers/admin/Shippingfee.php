<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\admin\CartMethod;
use App\Services\CommonService;

class Shippingfee extends CartMethod
{
    private static $weekday_map = [
        0 => '日',
        1 => '一',
        2 => '二',
        3 => '三',
        4 => '四',
        5 => '五',
        6 => '六',
    ];

    public function __construct()
    {
        parent::__construct('shipping_fee');
    }

    public function index(Request $request, $view_html = '')
    {
        return parent::index($request, 'admin.cartmethod.shippingfee');
    }

    public function get_exclude_weekday()
    {
        $this->success(DB::table('shipping_exclude_weekday')->first()->weekday);
    }

    public function update_exclude_weekday(Request $request)
    {
        try {
            if (isset($request['exclude_weekday']) == false) {
                throw new \Exception('請選擇星期');
            }

            if (in_array($request['exclude_weekday'], [0, 1, 2, 3, 4, 5, 6]) == false) {
                throw new \Exception('請選擇正確的星期');
            }

            if (DB::table('shipping_exclude_weekday')->first()->weekday == $request['exclude_weekday']) {
                throw new \Exception('星期未變更');
            }

            if (DB::table('shipping_exclude_weekday')->update(['weekday' => $request['exclude_weekday']]) === false) {
                throw new \Exception('更新失敗');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更新成功');
    }

    public function get_exclude_dates()
    {
        $return_data = [
            'code' => 1,
            'msg' => '',
            'data' => [],
        ];

        try {
            $tcat_exclude_dates = DB::table('tcat_exclude_dates')->where('date', '>=', date('Y-m-d'))->orderBy('date')->pluck('date');
            $tcat_exclude_dates = CommonService::objectToArray($tcat_exclude_dates);

            $exclude_dates = DB::table('shipping_exclude_dates')->where('date', '>=', date('Y-m-d'))->where('removed', 0)->orderBy('date')->pluck('date');
            $exclude_dates = CommonService::objectToArray($exclude_dates);

            $dates = array_merge($tcat_exclude_dates, $exclude_dates);
            sort($dates);
            array_walk($dates, function (&$date) {
                $date = $date . ' (' . self::$weekday_map[date('w', strtotime($date))] . ')';
            });

            $return_data['data'] = $dates;
        } catch (\Exception $e) {
            $return_data['code'] = 0;
            $return_data['msg'] = $e->getMessage();
        }

        return response()->json($return_data);
    }

    public function add_exclude_date(Request $request)
    {
        try {
            if (isset($request['exclude_date']) == false || empty($request['exclude_date'])) {
                throw new \Exception('請選擇排除日期');
            }

            if (strtotime($request['exclude_date'] . " 00:00:00") < strtotime(date('Y-m-d') . " 00:00:00")) {
                throw new \Exception('排除日期不能小於今天');
            }

            if (DB::table('tcat_exclude_dates')->where('date', $request['exclude_date'])->count() > 0) {
                throw new \Exception('已為黑貓宅配排除日期，不需再新增');
            }

            $db_result = DB::table('shipping_exclude_dates')->where('date', $request['exclude_date'])->first();

            if (empty($db_result) == false) {
                if ($db_result->removed == 0) {
                    throw new \Exception('此日期已經存在');
                }

                $db_result = DB::table('shipping_exclude_dates')->where('date', $request['exclude_date'])->update([
                    'removed' => 0,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            } else {
                $db_result = DB::table('shipping_exclude_dates')->insert([
                    'date' => $request['exclude_date'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            }

            if ($db_result === false) {
                throw new \Exception('增加失敗');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('增加成功');
    }

    public function remove_exclude_date(Request $request)
    {
        try {
            if (isset($request['exclude_date']) == false || empty($request['exclude_date'])) {
                throw new \Exception('請選擇排除日期');
            }

            if (DB::table('tcat_exclude_dates')->where('date', $request['exclude_date'])->count() > 0) {
                throw new \Exception('此日期為黑貓宅配排除日期，不可排除');
            }

            if (empty(DB::table('shipping_exclude_dates')->where('date', $request['exclude_date'])->first()) == true) {
                throw new \Exception('此日期不存在');
            }

            $db_result = DB::table('shipping_exclude_dates')->where('date', $request['exclude_date'])->update([
                'removed' => 1,
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            if ($db_result === false) {
                throw new \Exception('刪除失敗');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('刪除成功');
    }
}
