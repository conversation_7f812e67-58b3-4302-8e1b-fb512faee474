<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;
use App\Services\pattern\ProductHelpler;

class All extends MainController{
	const PER_PAGE_ROWS = 10;
	const SIMPLE_MODE_PAGINATE = false;
	public function __construct()
	{
		parent::__construct();
	}

	public function index(Request $request) {
		$searchBranch=$request->get('searchBranch');
		$searchPrev=$request->get('searchPrev');
		
		if(!empty($searchPrev)){
			$this->data['level'] = 'searchPrev='.$searchPrev;	
		}elseif(!empty($searchBranch)){
			$this->data['level'] = 'searchBranch='.$searchBranch;	
		}else{
			$this->data['level'] = '';
		}

		//dd($this->data);
		return view('admin.all.item-product',['data'=>$this->data]);
	}
	public function print(Request $request){
		$search_result = $this->search_product($request, $show_all=true);
		foreach ($search_result['productinfoItem'] as $key => $value) {
			$search_result['productinfoItem'][$key]['item'] = json_decode($value['item']);
		}
		// dump($search_result['productinfoItem']);
		$this->data['productinfoItem'] = $search_result['productinfoItem'];

		return view('admin.all.all-print',['data'=>$this->data]);
	}
	public function search_product_ajax(Request $request){
        return $this->search_product($request);
	}
	private function search_product(Request $request, $show_all=false){
		$search_data = $request->query();
		if($this->admin_type=='distribution'){ /*供應商限看自己的*/
			$search_data['distributor_id'] = $this->get_distributor_id();
		}
		else if($this->admin_type=='admin'){ /*若為管理者，可看指定供應商*/
			if(empty($request->get('distributor_id')) == false){
				if($request->get('distributor_id')!=='' && $request->get('distributor_id')!=='-1'){
					$search_data['distributor_id'] = $request->get('distributor_id');
				}
			}
		}
		$result = ProductHelpler::search_product($search_data, $show_all);
		return $result;
	}

	public function init_data_ajax(Request $request){
		/*網紅*/
		if($this->admin_type=='distribution'){
			$kol = [];
		}else{
			$kol = Db::table('kol')->orderByRaw('id desc')->get();
		}

		$position = Db::table('position');

		/*庫存位置*/
		$distributor_id_where = '';
		if($this->admin_type=='distribution'){ /*供應商限看自己的*/
			$distributor_id_where = $this->distributor_id_where_sql;
		}
		else if($this->admin_type=='admin'){ /*若為管理者，可看指定供應商*/
			if(empty($request->get('distributor_id')) == false){
				if($request->get('distributor_id')!=='' && $request->get('distributor_id')!=='-1'){
					$distributor_id_where = 'distributor_id="'.$request->get('distributor_id').'"';
				}
			}
		}
		if($distributor_id_where){
			$position = $position->whereRaw($distributor_id_where);
		}
		$position = $position->orderByRaw('name asc, id desc')->get();

		return [
			'kol' => CommonService::objectToArray($kol),
			'position' => CommonService::objectToArray($position),
		];
	}
}

