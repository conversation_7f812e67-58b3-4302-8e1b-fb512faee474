<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\pattern\MemberInstance;

class Excel extends MainController
{
  private $DBTextConnecter;
  private $DBFileConnecter;
  const PRODUCTINFO_NUMBAER_ROW_ID = 3;
  const PER_PAGE_ROWS = 50;
  const SIMPLE_MODE_PAGINATE = false;

  const REGISTER_CODE_TABLE= 'excel';
  const REGISTER_MAIL_TABLE= 'excel_reply';

  /*機身碼管理頁*/
  public function index(Request $request) {
    $page = $request->get('page') ?? 1;
    $searchKey = $request->get('searchKey') ?? '';
    $statusKey = $request->get('statusKey') ?? '';
    $brandKey = $request->get('brandKey') ?? '';
    $this->data['searchKey'] = $searchKey;
    $this->data['statusKey'] = $statusKey;
    $this->data['brandKey'] = $brandKey;

    $this->data['brand'] = Db::table(self::REGISTER_CODE_TABLE)->groupBy('product_brand')->select('product_brand')->get();
    //dump($brand);

    if (mb_strlen($searchKey, "UTF-8") == strlen($searchKey)) {
      $where = "product_brand  like  '%" . $brandKey . "%'    and  (product_name like  '%" . $searchKey . "%'  or product_code  like  '%" . $searchKey . "%'    or    account_number  like  '%" . $searchKey . "%'   or    createtime  like  '%" . $searchKey . "%') and status like '%".$statusKey."%'";
    } else {
      $where = "product_brand  like  '%" . $brandKey . "%'    and  (product_name like  '%" . $searchKey . "%'  or product_code  like  '%" . $searchKey . "%'    or    account_number  like  '%" . $searchKey . "%') and status like '%".$statusKey."%'";
    }

    $MemberInstance = new MemberInstance(0);
    $num = Db::table(self::REGISTER_CODE_TABLE)
        ->whereRaw($where)
        ->whereRaw($this->distributor_id_where_sql)
        ->orderByRaw('status desc,id asc')
        ->paginate(
          self::PER_PAGE_ROWS
        )->appends([
          'searchKey' => $searchKey,
          'statusKey' => $statusKey,
          'brandKey' => $brandKey,
        ]);
    if (empty($num->items()) == false) {
      foreach ($num->items() as $key => $value) {
        $MemberInstance->change_user_id($value->account_number);
        $number = $MemberInstance->get_user_data(); //根据ID查询相关其他信息
        if (empty($number) == true) {
          $num->items()[$key]->number = ""; //给数据集追加字段num并赋值
        } else {
          $num->items()[$key]->number = $number['number']; //给数据集追加字段num并赋值
        }
      }
    }
    //dump($num);

    $total = DB::table(self::REGISTER_CODE_TABLE)->whereRaw($where)->count();
    $regisetered = DB::table(self::REGISTER_CODE_TABLE)->whereRaw($where)->where('status','=',1)->count();
    $un_regisetered = DB::table(self::REGISTER_CODE_TABLE)->whereRaw($where)->where('status','=',0)->count();

    if($total != 0){
      $regiseter_rate = $regisetered/$total*100;
    }else{
      $regiseter_rate = 0;
    }

    $this->data['total'] = $total;
    $this->data['regisetered'] = $regisetered;
    $this->data['un_regisetered'] = $un_regisetered;

    $this->data['regiseter_rate'] = $regiseter_rate;
    $this->data['page'] = $page;

    $this->data['num'] = $num;
    return view('admin.excel.index',['data'=>$this->data]);
  }
  /*註冊商品回函頁*/
  public function reply(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $do = $request->get('do') ?? '';

    if($do == 1){
      $where = " (pro_id  like  '%" . $searchKey . "%' or product_code like '%".$searchKey."%') and status >= '".$do."'";
    }else{
      $where = " (pro_id  like  '%" . $searchKey . "%' or product_code like '%".$searchKey."%') and status like '%".$do."%'";
    }
    $this->data['searchKey'] = $searchKey;

    $MemberInstance = new MemberInstance(0);
    $qa = Db::table(self::REGISTER_MAIL_TABLE)
          ->whereRaw($where)
          ->whereRaw($this->distributor_id_where_sql)
          ->orderByRaw('pro_id desc')
          ->paginate(self::PER_PAGE_ROWS)
          ->appends([
            'searchKey' => $searchKey,
            'do' => $do,
          ]);
            
              /* ->each(function($item, $key)use($MemberInstance){
                $MemberInstance->change_user_id($item["account_number"]);
                $num = $MemberInstance->get_user_data()["number"]; //根据ID查询相关其他信息
                $item['account_name'] = $num; //给数据集追加字段num并赋值
                return $item;
              }); */


    foreach ($qa->items() as $key => $value) {
      $MemberInstance->change_user_id($value->account_number);
      $num = $MemberInstance->get_user_data()["number"]; //根据ID查询相关其他信息
      $qa->items()[$key]->account_name = $num; //给数据集追加字段num并赋值
    }
    //dump($qa);
    $this->data['qa'] = $qa;

    return view('admin.excel.reply',['data'=>$this->data]);
  }

  public function Import(Request $request) {
    //接收檔案
    $files = $request->file("file");
    $type = explode(".",$_FILES['file']['name']);

    if($type[1]== 'xlsx'){
      //儲存檔案
      $info = $files->move(ROOT_PATH . 'public' . DS . 'uploads' . DS . self::REGISTER_CODE_TABLE);dd($info);

      //檔案路徑
      $filename = ROOT_PATH.'public'.DS.'uploads'. DS . self::REGISTER_CODE_TABLE. DS .$info->getFilename();

      self::read_excel($filename);
    }else{
      echo "<script>alert('警告：格式錯誤'); location.href = '".url('Excel/index')."';</script>";
    }
  }
  private function read_excel($filename){
    //require_once 'Classes/PHPExcel/Reader/Excel5.php';
    $PHPReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader("Xlsx"); 
    $PHPExcel = $PHPReader->load($filename); 
    $sheet = $PHPExcel->getSheet(0); 
    $allRow = $sheet->getHighestRow(); //取得最大的行號 
    $allColumn = $sheet->getHighestColumn(); //取得最大的列號

    $repeat_code = [];
    if($allRow <= 10001){
      for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
        //獲取A列的值 
        $product_code = trim($PHPExcel->getActiveSheet()->getCell("A" . $currentRow)->getValue()); 
        $product_name = trim($PHPExcel->getActiveSheet()->getCell("B" . $currentRow)->getValue()); 
        $product_brand = trim($PHPExcel->getActiveSheet()->getCell("C" . $currentRow)->getValue()); 

        if($product_code!=""){
          $data['product_code'] = $product_code;
          $data['product_name'] = $product_name;
          $data['product_brand'] = $product_brand;
          $data['createtime'] = date('Y-m-d');
          if($this->admin_type=='distribution'){
            $distributor_id = session()->get($this->admin_type)['id'];
            $data['distributor_id'] = $distributor_id;
          }else{
            $distributor_id = 0;
          }

          /*重複檢查*/
          $repeat = Db::table(self::REGISTER_CODE_TABLE)->where('product_code', $product_code)
                                ->where('distributor_id', $distributor_id);
          if($product_code=='XXXXXXXXXXXXXX'){ /*若為任意輸入*/
            $repeat = $repeat->where('product_name', $product_name);
          }
          $repeat = $repeat->first();
          if($repeat){
            array_push($repeat_code, $product_name."：".$product_code);
          }else{
            Db::table(self::REGISTER_CODE_TABLE)->insert($data);
          }
        }
      }
      if(count($repeat_code)){
        $repeat_code = implode("<br>", $repeat_code);
        $this->success('匯入成功，但重複機身碼未被匯入：', url('Excel/index'), $repeat_code,  -1);
      }else{
        $this->success('匯入成功', url('Excel/index'));
      }
    }
    else{
      $this->error('匯入資料超過1萬筆', url('Excel/index'));
    }
  }

  /*更新註冊商品回函驗證狀態*/
  public function update(Request $request){
    $id = $request->post('id');
    if(!parent::check_controll(self::REGISTER_MAIL_TABLE, $id)){
      // $this->error('您無法操作此項目');
      return 0;
    }

    $val = $request->post('val');
    $user_id = $request->post('user_id');
    $user_name = $request->post('user_name');
    $pro_id = $request->post('pro_id');
    $name = $request->post('name');
    $product_code = $request->post('product_code');
    $pic = $request->post('pic');
    $regtime = $request->post('regtime');
    $tax_ID_number = $request->post('tax_ID_number');
    $buytime = $request->post('buytime');
    
    $MemberInstance = new MemberInstance($user_id);
    $email = $MemberInstance->get_user_data();
    $compare_code = Db::table(self::REGISTER_CODE_TABLE)->whereRaw("product_code = 'XXXXXXXXXXXXXX'")
                      ->whereRaw("product_name = '".$name."'")
                      ->whereRaw($this->distributor_id_where_sql)
                      ->get();
    $compare_code = CommonService::objectToArray($compare_code);
    if(empty($compare_code)){ // 當沒有特殊機身碼，要比對是否註冊過
      $had_registered = Db::table(self::REGISTER_CODE_TABLE)->whereRaw("product_code = '".$product_code."'")
                          // ->where("product_name = '".$name."'")
                          ->whereRaw("status != '0'")
                          ->whereRaw($this->distributor_id_where_sql)
                          ->get();
      if($had_registered && $val == 1){
        return 4;
        exit;
      }
    }

    /*更新註冊商品回函狀態*/
    $resault = Db::table(self::REGISTER_MAIL_TABLE)->whereRaw(" id = '".$id."'")->update(['status'=>$val]);
    if($resault){
      if($val == 1){ // 修改成成功
        if(!empty($compare_code)){ // 當有特殊機身碼，自動建立消費者輸入的資訊
          $data['product_code'] = $product_code;
          $data['product_name'] = $compare_code[0]['product_name'];
          $data['product_brand'] = $compare_code[0]['product_brand'];
          $data['createtime'] = date('Y-m-d');
          if($this->admin_type == 'distribution'){
            $data['distributor_id'] = session()->get($this->admin_type)['id'];
          }
          Db::table(self::REGISTER_CODE_TABLE)->insert($data);
        }

        if($this->admin_type == 'distribution'){
          $distributor_id = session()->get($this->admin_type)['id'];
        }else{
          $distributor_id = 0;
        }
        Db::table(self::REGISTER_CODE_TABLE)->whereRaw("product_code = '".$product_code."'")
                                            ->where("distributor_id", $distributor_id)
                                            ->update([
                                              'status'=>$val,
                                              'pic'=>$pic,
                                              'account_number'=>$user_id,
                                              'account_name'=>$user_name,
                                              'regtime'=>$regtime,
                                              'tax_ID_number'=>$tax_ID_number,
                                              'buytime'=>$buytime
                                            ]);
        $product_reg_letter_success = Lang::get('menu.商品註冊成功信');
        $product_reg_letter_success = str_replace("{name}", $name, $product_reg_letter_success);
        $product_reg_letter_success = str_replace("{product_code}", $product_code, $product_reg_letter_success);
        $mailBody = "
          <html>
            <head></head>
            <body>
              <div>
                ".$product_reg_letter_success."
              </div>
              <div style='color:red;'>
                ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
              </div>
            </body>
          </html>
        ";
      }
      else if($val == 2){ // 修改成失敗
        $product_reg_letter_error = Lang::get('menu.商品註冊失敗信');
        $product_reg_letter_error = str_replace("{name}", $name, $product_reg_letter_error);
        $product_reg_letter_error = str_replace("{product_code}", $product_code, $product_reg_letter_error);
        $mailBody = "
          <html>
            <head></head>
            <body>
              <div>
                ".$product_reg_letter_error."
              </div>
              <div style='color:red;'>
                ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
              </div>
            </body>
          </html>
        ";
      }

      $send_result = HelperService::Mail_Send($mailBody, 'client', $email['email'], '商品註冊通知', "");
      if ($send_result) {
        return 1;
      }else{
        return 2;
      }
    }else{
      return 0;
    }
  }
  /*刪除註冊商品回函*/
  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll(self::REGISTER_MAIL_TABLE, $id)){
      $this->error('您無法操作此項目');
    }
    try{
      Db::table(self::REGISTER_MAIL_TABLE)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
  /*刪除註冊商品回函(批次)*/
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll(self::REGISTER_MAIL_TABLE, $id)){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
        Db::table(self::REGISTER_MAIL_TABLE)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
  /*刪除機身碼(批次)*/
  public function multiDelete_or(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll(self::REGISTER_CODE_TABLE, $id)){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
        Db::table(self::REGISTER_CODE_TABLE)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
}