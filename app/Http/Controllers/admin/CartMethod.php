<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\pattern\Invoice;

class CartMethod extends MainController{
  private $DBTextConnecter;
  private $resTableName;
  private $target_table;
  const PER_PAGE_ROWS = 20;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct($table) {
    parent::__construct();
    $this->resTableName = $table;
    $this->target_table = $table;
    if($this->admin_type=='distribution'){
      $this->target_table .= '_distribution';
    }
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->target_table);
  }

  public function index(Request $request, $view_html='') {
    $searchKey = request()->get('searchKey') ?? '';
    $this->data['searchKey'] = $searchKey;

    $items = self::get_items($this->resTableName, $this->admin_type, $searchKey, $need_page=true, $this->data['my_distributor_id']);
    
    // dump($items->items());exit;
    $this->data[$this->resTableName] = $items;
    return view($view_html,['data'=>$this->data]);
  }
  public static function get_items($table, $admin_type, $searchKey='', $need_page=true, $distributor_id=0){
    $items = Db::table($table)
          ->whereRaw("name LIKE '%$searchKey%'")
          ->where('sys_status',1)
          ->orderBy('order_id');
    if($need_page){
      $items = $items->paginate(
            self::PER_PAGE_ROWS
          )->appends([
            'searchKey' => $searchKey
          ]);
    }else{
      $items = $items->get();
    }
    if($admin_type=='distribution'){
      if($need_page){
        $items = $items->each(function($item)use($table, $distributor_id){
          /*合併供應商設定資料*/
          return self::merge_distributor_setting($item, $table, $distributor_id);
        });
      }else{
        foreach ($items as $key => $item) {
          /*合併供應商設定資料*/
          $items[$key] = self::merge_distributor_setting($item, $table, $distributor_id);
        }
      }
    }
    return $items;
  }
  /*合併供應商設定資料*/
  public static function merge_distributor_setting($item, $table, $distributor_id){
    if($distributor_id!=0){
      $item[$table.'_id'] = $item['id'];
      $item['id'] = $item['id'].'_'.$item['id'];
      $relation = Db::table($table.'_distribution')
              ->where('distributor_id', $distributor_id)
              ->where($table.'_id', $item[$table.'_id'])
              ->first();
      $relation = CommonService::objectToArray($relation);
      if($relation){
        return array_merge($item, $relation);
      }
    }
    return $item;
  }

  public function delete(Request $request) {
    if($this->admin_type=='distribution'){
      $this->error('您無法操作此項目');
    }
    $id = request()->get('id');
    try{
      Db::table($this->target_table)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
  public function multiDelete(Request $request) {
    if($this->admin_type=='distribution'){
      $this->error('您無法操作此項目');
    }
    $idList = request()->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        Db::table($this->target_table)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  public function doCreate(Request $request) {
    if($this->admin_type=='distribution'){
      $this->error('您無法操作此項目');
    }
    $newData = request()->post();
    try{
      $this->code_check($newData);

      $this->DBTextConnecter->setDataArray($newData);
      $id = $this->DBTextConnecter->createTextRow();

      // 自動更新排序
      $table = $this->resTableName;
      $column = 'order_id';
      $order_num = 0;
      $primary_key = 'id';
      $primary_value = $id;
      parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);

      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
      return $outputData;
    }
    return $outputData;
  }

  public function update(Request $request) {
    $updateData = request()->post();
    $id_slice = explode('_', $updateData['id']);
    $updateData['id'] = $id_slice[0];

    if($this->admin_type=='distribution'){
      unset($updateData['name']);
      unset($updateData['order_id']);
      unset($updateData['sys_status']);
      
      if(count($id_slice)==1){
        $relation = Db::table($this->target_table)->where('id', $updateData['id']);
      }else{
        $relation = Db::table($this->target_table)->where($this->resTableName.'_id', $updateData['id']);
      }
      $relation = $relation->whereRaw($this->distributor_id_where_sql)->first();
      $relation = CommonService::objectToArray($relation);
      if(!$relation){ /*新增*/
        $updateData[$this->resTableName.'_id'] = explode('_', $updateData['id'])[0];
        $updateData['distributor_id'] =session()->get($this->admin_type)['id'];
        unset($updateData['id']);
        $this->DBTextConnecter->setDataArray($updateData);
        $id = $this->DBTextConnecter->createTextRow();
        $outputData = [
          'status' => true,
          'message' => 'success',
          'id' => $id,
        ];
        return $outputData;
      }else{
        if(!parent::check_controll($this->target_table, $updateData['id'])){
          throw new \Exception("您無法編輯此項目", 1);
        }
      }
    }
    try{
      $this->code_check($updateData);

      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success',
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
      return $outputData;
    }
    return $outputData;
  }
  /*AJAX*/
  public function cellCtrl(Request $request) {
    try{
      $updateData = request()->post();
      $id_slice = explode('_', $updateData['id']);
      $updateData['id'] = $id_slice[0];

      if($this->admin_type=='distribution'){
        if(count($id_slice)==1){
          $relation = Db::table($this->target_table)->where('id', $updateData['id']);
        }else{
          $relation = Db::table($this->target_table)->where($this->resTableName.'_id', $updateData['id']);
        }
        $relation = $relation->whereRaw($this->distributor_id_where_sql)->first();
        $relation = CommonService::objectToArray($relation);
        if(!$relation){ /*新增*/
          $updateData[$this->resTableName.'_id'] = explode('_', $updateData['id'])[0];
          $updateData['distributor_id'] = session()->get($this->admin_type)['id'];
          $main = Db::table($this->resTableName)->find($updateData[$this->resTableName.'_id']);
          unset($main['name']);
          unset($main['order_id']);
          unset($main['sys_status']);
          $updateData = array_merge($main, $updateData);
          unset($updateData['id']);
          $this->DBTextConnecter->setDataArray($updateData);
          $this->DBTextConnecter->createTextRow();
          $outputData = [
            'status' => true,
            'message' => 'success',
          ];
          return $outputData;
        }else{
          if(!parent::check_controll($this->target_table, $updateData['id'])){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
      }

      // 自動更新排序
      if( isset($updateData['order_id']) ){
        $table = $this->target_table;
        $column = 'order_id';
        $order_num = $updateData['order_id'];
        $primary_key = 'id';
        $primary_value = $updateData['id'];
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
        unset($updateData['order_id']);
      }
      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success',
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage(),
      ];
      return $outputData;
    }
    return $outputData;
  }

  private function code_check($newData) {
    if ($this->resTableName == 'lovecode') {
      $result = Invoice::instance()->check_love_code($newData['code']);
      if ($result['RtnCode'] != 1) {
        throw new \Exception($result['RtnMsg'], 1);
      }
    } else if ($this->resTableName == 'shipping_fee') {
      if (intval($newData['prepare_days']) < 1) {
        throw new \Exception('備貨天數需至少為1天');
      }
    }
  }
}