<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

// Photonic Class
use App\Services\CommonService;

class BoxContent extends MainController
{
    private $tableName = 'box_content_cate';

    public function cate() {
        $db_result = DB::table($this->tableName)->where('removed', 0)->orderBy('order')->orderBy('id')->get();
        $db_result = CommonService::objectToArray($db_result);

        foreach ($db_result as $key => $item) {
            $db_result[$key]['count'] = DB::table('box_content')->where('cate_id', $item['id'])->where('removed', 0)->count();
        }

        $this->data['category'] = $db_result;

        return view('admin.boxcontent.cate', ['data' => $this->data]);
    }

    private function basic_check($request) {
        if (empty($request['id'])) {
            throw new \Exception('找不到此類型');
        }

        $db_result = DB::table($this->tableName)->where('id', $request['id'])->first();
        $db_result = CommonService::objectToArray($db_result);

        if (empty($db_result)) {
            throw new \Exception('找不到此類型');
        }

        return $db_result;
    }

    public function content(Request $request) {
        try {
            $cate = $this->basic_check($request->all());
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->data['boxCateId'] = $cate['id'];
        $this->data['boxCateName'] = $cate['name'];

        return view('admin.boxcontent.content', ['data' => $this->data]);
    }

    public function create_and_update(Request $request) {
        try {
            if (empty($request->post('id'))) {
                throw new \Exception('找不到此類型');
            }

            if (empty($request->post('name'))) {
                throw new \Exception('請輸入名稱');
            }

            $response = [
                'code' => 1,
                'msg' => '',
                'id' => 0,
            ];

            if ($request->post('id') == -1) {
                if (DB::table($this->tableName)->where('name', $request->post('name'))->where('removed', 0)->count() > 0) {
                    throw new \Exception('名稱已存在');
                }

                $id = DB::table($this->tableName)->insertGetId([
                    'name' => $request->post('name'),
                    'order' => $request->post('order') ?? 0,
                ]);

                $response['msg'] = '新增成功';
                $response['id'] = $id;
            } else {
                $this->basic_check($request->all());

                $result = DB::table($this->tableName)->where('id', $request->post('id'))->update([
                    'name' => $request->post('name'),
                    'order' => $request->post('order') ?? 0,
                ]);

                if (empty($result)) {
                    throw new \Exception('更新失敗');
                }

                $response['msg'] = '更新成功';
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return $response;
    }

    public function change_order(Request $request) {
        try {
            $this->basic_check($request->all());

            if ($request->post('order') < 0) {
                throw new \Exception('排序不能小於0');
            }

            DB::table($this->tableName)->where('id', $request->post('id'))->update([
                'order' => intval($request->post('order'))
            ]);
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('修改成功');
    }

    public function delete_one(Request $request) {
        try {
            $this->basic_check($request->all());

            if (DB::table('box_layout_block')->where('items', 'like', '%"' . $request->post('id') . '"%')->count() > 0) {
                throw new \Exception('此類型尚有禮盒版型使用中，無法刪除');
            }

            DB::table($this->tableName)->where('id', $request->post('id'))->update([
                'removed' => 1,
            ]);

            DB::table('box_content')->where('cate_id', $request->post('id'))->update([
                'removed' => 1,
            ]);
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    
        $this->success('刪除成功');
    }

    public function get_all_productinfo() {
        try {
            $productinfo_type_had = DB::table('box_content')->where('removed', 0)->pluck('productinfo_type');
            $productinfo_type_had = CommonService::objectToArray($productinfo_type_had);

            $productinfo_other = DB::table('productinfo_type')
            ->select([
                'productinfo_type.product_id',
                'productinfo.title AS productinfo_title',
                'productinfo.pic',
            ])
            ->rightJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
            ->where('productinfo_type.online', 1)
            ->whereNotIn('productinfo_type.id', $productinfo_type_had)
            ->where('productinfo.is_box', 0)
            ->groupBy('productinfo_type.product_id')
            ->orderBy('productinfo.order_id')
            ->orderBy('productinfo.id')
            ->orderBy('productinfo_type.order_id')
            ->orderBy('productinfo_type.id')
            ->get();
            $productinfo_other = CommonService::objectToArray($productinfo_other);

            $data = [];

            foreach ($productinfo_other as $item) {
                $insert = [];

                $insert['product_title'] = $item['productinfo_title'];
                $insert['productinfo_type'] = [];

                $pictures = json_decode($item['pic'], true);

                $productinfo_types = DB::table('productinfo_type')->select([
                    'productinfo_type.id',
                    'productinfo_type.title AS productinfo_type_title',
                    'productinfo_type.pic_index',
                ])
                ->where('productinfo_type.product_id', $item['product_id'])
                ->where('productinfo_type.online', 1)
                ->whereNotIn('productinfo_type.id', $productinfo_type_had)
                ->orderBy('productinfo_type.order_id')
                ->orderBy('productinfo_type.id')
                ->get();
                $productinfo_types = CommonService::objectToArray($productinfo_types);

                if (empty($productinfo_types)) {
                    continue;
                }

                foreach ($productinfo_types as $item2) {
                    $insert2 = [];

                    $insert2['productinfo_type'] = $item2['id'];
                    $insert2['productinfo_type_title'] = $item2['productinfo_type_title'];

                    $pictures = json_decode($item['pic'], true);

                    $insert2['pic'] = '/public/static/index/' . $pictures[$item2['pic_index'] - 1];

                    $insert['productinfo_type'][] = $insert2;
                }

                $data[] = $insert;
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return [
            'code' => 1,
            'msg' => '取得成功',
            'data' => $data,
        ];
    }

    public function get_content(Request $request) {
        try {
            if (empty($request['cate_id'])) {
                throw new \Exception('找不到此類型');
            }

            $db_result = DB::table('box_content')
            ->select([
                'box_content.productinfo_type',
                'productinfo_type.title AS productinfo_type_title',
                'productinfo_type.pic_index',
                'productinfo_type.online',
                'productinfo.title AS productinfo_title',
                'productinfo.pic',
            ])
            ->rightJoin('productinfo_type', 'box_content.productinfo_type', '=', 'productinfo_type.id')
            ->rightJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
            ->where('box_content.cate_id', $request->post('cate_id'))
            ->where('box_content.removed', 0)
            ->orderBy('productinfo.id')
            ->orderBy('productinfo_type.id')
            ->get();
            $db_result = CommonService::objectToArray($db_result);

            $content = [];

            foreach ($db_result as $item) {
                $insert = [];

                if ($item['online'] == 0) {
                    DB::table('box_content')->where('productinfo_type', $item['productinfo_type'])->update([
                        'removed' => 1,
                    ]);

                    continue;
                }

                $insert['productinfo_type'] = $item['productinfo_type'];
                $insert['productinfo_type_title'] = $item['productinfo_type_title'];
                $insert['productinfo_title'] = $item['productinfo_title'];

                $pictures = json_decode($item['pic'], true);

                $insert['pic'] = '/public/static/index/' . $pictures[$item['pic_index'] - 1];

                $content[] = $insert;
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        return [
            'code' => 1,
            'msg' => '取得成功',
            'data' => $content,
        ];
    }

    public function add_to_content(Request $request) {
        try {
            if (empty($request['cate_id'])) {
                throw new \Exception('找不到此類型');
            }

            if (DB::table('box_content_cate')->where('id', $request['cate_id'])->where('removed', 0)->count() == 0) {
                throw new \Exception('找不到此類型');
            }

            if (empty($request['selected'])) {
                throw new \Exception('找不到此品項');
            }

            $selected = $request['selected'];
            sort($selected);

            foreach ($selected as $item) {
                $db_result = DB::table('box_content')->where('cate_id', $request['cate_id'])->where('productinfo_type', $item)->where('removed', 0)->first();
                $db_result = CommonService::objectToArray($db_result);

                if (empty($db_result) == false) {
                    continue;
                }

                $db_result = DB::table('productinfo_type')->select(['is_box', 'productinfo_type.online'])->rightJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')->where('productinfo_type.id', $item)->first();
                $db_result = CommonService::objectToArray($db_result);

                if (empty($db_result)) {
                    throw new \Exception('找不到此品項');
                }

                if ($db_result['online'] == 0) {
                    throw new \Exception('此品項已下架');
                }

                if ($db_result['is_box'] == 1) {
                    throw new \Exception('此品項為禮盒，無法加入');
                }

                $db_result = DB::table('box_content')->where('productinfo_type', $item)->where('removed', 1)->first();
                $db_result = CommonService::objectToArray($db_result);

                if (empty($db_result) == false) {
                    $result = DB::table('box_content')->where('productinfo_type', $item)->update([
                        'cate_id' => $request['cate_id'],
                        'removed' => 0,
                    ]);
                } else {
                    $result = DB::table('box_content')->insert([
                        'cate_id' => $request['cate_id'],
                        'productinfo_type' => $item,
                    ]);
                }

                if (empty($result)) {
                    throw new \Exception('加入失敗');
                }
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    
        $this->success('加入成功');
    }

    public function remove_content(Request $request) {
        try {
            if (empty($request['cate_id'])) {
                throw new \Exception('找不到此類型');
            }

            if (empty($request['productinfo_type'])) {
                throw new \Exception('找不到此品項');
            }

            $result = DB::table('box_content')->where('cate_id', $request['cate_id'])->where('productinfo_type', $request['productinfo_type'])->update([
                'removed' => 1,
            ]);

            if (empty($result)) {
                throw new \Exception('刪除失敗');
            }
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    
        $this->success('刪除成功');
    }
}