<?php
namespace App\Http\Controllers\admin;

use ReflectionClass;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;
use App\Services\pattern\ExaminationHelper;

class Examination extends MainController{
  private $DBTextConnecter;
  private $DBFileConnecter;
  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('examinee_info');
    $this->DBFileConnecter = DBFileConnecter::withTableName('examinee_info');

    // 是否啟用報名功能
    if(config('control.control_register')==0){$this->error('未啟用此功能');}
  }

  /*報名資料列表頁*/
  public function examinee_list(Request $request){
    $id = $request->get('id');
    // dump($this->data);exit;
    if(!parent::check_controll('productinfo', $id)){
      $this->error('您無法操作此項目');
    }
    // dump($id); exit;
    $this->data['id'] = $id;

    $title = Db::table('productinfo as info')->select('info.title as info_title')->find($id);
    $this->data['title'] = CommonService::objectToArray($title);

    $productinfo_type = Db::table('productinfo_type')->select('id','title')->whereRaw("product_id = '".$id."'")->get();
    $this->data['productinfo_type'] = CommonService::objectToArray($productinfo_type);
    if(count($productinfo_type)==0){
      $this->error('請先建立品項再來查看報名資料');
    }

    $this->data['types_need_limit'] = ExaminationHelper::$types_need_limit;
    $this->data['types_need_option'] = ExaminationHelper::$types_need_option;
    $this->data['types_need_checked'] = ExaminationHelper::$types_need_checked;

    $post_data['search_cond'] = $request->query(); /*設定篩選條件*/
    $post_data['search_cond']['prod_id'] = $id;
    $post_data['search_cond']['type_id'] = isset($post_data['search_cond']['type_id']) ? $post_data['search_cond']['type_id'] : "";
    $post_data['backstage'] = true; /*允許看全部*/
    // dump($post_data);exit;
    try {
      $examinee_list_tableView = ExaminationHelper::create_examinee_list_table_view($this->data, $productinfo_type[0]->id, $order_id=-1, true, $post_data);
    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }
    $reflection = new ReflectionClass($examinee_list_tableView);
    $property = $reflection->getProperty('data');
    $property->setAccessible(true);
    $examinee_list_tableView_data = $property->getValue($examinee_list_tableView);
    // dump($examinee_list_tableView_data);exit;
    $this->data['examinee_list_table'] = "{$examinee_list_tableView}";
    $this->data['examinees'] = $examinee_list_tableView_data['data']['examinees'];

    $register_fields_search = $this->register_fields_search($id, $post_data);
    $this->data['register_fields_search'] = $register_fields_search;

    try {
      $examinee_list_graphView = ExaminationHelper::create_examinee_list_graph_view($this->data, $productinfo_type[0]->id, $order_id=-1, true, $post_data);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $reflection2 = new ReflectionClass($examinee_list_graphView);
    $property = $reflection2->getProperty('data');
    $property->setAccessible(true);
    $examinee_list_graphView_data = $property->getValue($examinee_list_graphView);
    // dump($examinee_list_graphView_data);exit;
    $this->data['examinee_list_graph'] = "{$examinee_list_graphView}";
    $this->data['rand'] = $examinee_list_graphView_data['data']['rand'];
    $this->data['graph_datas'] = $examinee_list_graphView_data['data']['graph_datas'];
    // dd($this->data['graph_datas']);
    $post_data['search_cond']['reg_status'] = '0';
    $examinees_pre = ExaminationHelper::get_myself_type_examinee_info(0, $examinee_id=0, $order_id=-1, '', $post_data);
    $pre_group = [];
    foreach ($examinees_pre as $key => $value) {
      $pre_group_index = $value['order_id'];
      if(!isset($pre_group[$pre_group_index])){
        $pre_group[$pre_group_index] = [];
      }
      array_push($pre_group[$pre_group_index], $value);
    }
    ksort($pre_group); /*確保越早的訂單排越前面*/
    // dump($pre_group);exit;
    $this->data['pre_group'] = $pre_group;

    $post_data['search_cond']['cancel_status'] = '0';
    $post_data['search_cond']['reg_status'] = '-1';
    $examinees_on = ExaminationHelper::get_myself_type_examinee_info(0, $examinee_id=0, $order_id=-1, '', $post_data);
    $this->data['examinees_on_num'] = count($examinees_on);

    return view('admin.examination.examinee_list', ['data'=>$this->data]);
  }
  /*報名資料篩選輸入區*/
  private function register_fields_search($info_id, $post_data){
    $this->data['info_id'] = $info_id;
    $productinfo = Db::table('productinfo')->where("id", $info_id)->first();
    $this->data['productinfo'] = CommonService::objectToArray($productinfo);

    $productinfo_type = Db::table('productinfo_type')->select('id','title')->whereRaw("product_id = '".$info_id."'")->get();
    $this->data['productinfo_type'] = CommonService::objectToArray($productinfo_type);


    if(isset($post_data['search_cond'])){
      $register_data = isset($post_data['search_cond']['register_data']) ? $post_data['search_cond']['register_data'] : "[]";
    }else{
      $register_data = "[]";
    }
    $register_data = (Array)json_decode($register_data);

    /*取得商品報名欄位*/
    $fields_for_search = ExaminationHelper::get_fields_by_prod_id($info_id);
    foreach ($fields_for_search as $key => $value) {
      $fields_for_search[$key]['options'] = json_decode($value['options']);

      /*答案併入問題中*/
      $ans_key = 'field_id_'.$value['id'];
      if( isset($register_data[$ans_key]) ){ /*有紀錄的話，依紀錄*/
        if($register_data[$ans_key]!="" && $register_data[$ans_key]!=[]){
          $fields_for_search[$key]['ans'] = $register_data[$ans_key];
        }else{
          $fields_for_search[$key]['ans'] = ExaminationHelper::set_examinee_default_ans($value['type']);
        }
      }
      else{ /*沒紀錄的話，依預設*/
        $fields_for_search[$key]['ans'] = ExaminationHelper::set_examinee_default_ans($value['type']);
      }
    }
    // dump($fields_for_search);exit;
    $this->data['fields_for_search'] = $fields_for_search;
    return view('admin.examination.register_fields_search', ['data'=>$this->data]);
  }

  /* 取得報名者填寫頁面 */
  public function examinee_panel(Request $request){
    $post = $request->post();
    $post['backstage'] = true;
    return ExaminationHelper::create_examinee_panel_view($this->view, $post, 0);
  }
  public function examinee_save(Request $request){
    try {
      $data = ExaminationHelper::examinee_save(true, 0, $request->post());
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success(json_encode($data, JSON_UNESCAPED_UNICODE));
  }

  /*報名資料列匯出成excel*/
  public function excel(Request $request){
    $id = $request->get('id');
    if(!parent::check_controll('productinfo', $id)){
      $this->error('您無法操作此項目');
    }
    $title = Db::table('productinfo AS info')->select('info.title as info_title')->find($id);
    $this->data['title'] = CommonService::objectToArray($title);

    $productinfo_type = Db::table('productinfo_type')->select('id','title')->where("product_id", $id)->get();
    $this->data['productinfo_type'] = CommonService::objectToArray($productinfo_type);
    if(count($productinfo_type)==0){
      $this->error('請先建立品項再來查看報名資料');
    }

    $post_data['search_cond'] = $request->query(); /*設定篩選條件*/
    $post_data['backstage'] = true; /*允許看全部*/
    $examinees = ExaminationHelper::get_myself_type_examinee_info($type_id=0, $examinee_id=0, $order_id=-1, '', $post_data);

    $register_fields = ExaminationHelper::get_fields_by_prod_id($id);

    $title = Db::table('productinfo AS info')->select('info.title as info_title')->find($id);
    $title = CommonService::objectToArray($title);

    $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $objPHPExcel->setActiveSheetIndex(0);
    // $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, 1, "回傳值");
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, 1, "報名項目");
    $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(2, 1, "報名狀態");
    $get_fields_type_par = ExaminationHelper::get_fields_type_par();
    foreach ($register_fields as $key => $value) {
      $column_index = $key + 3;
      $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column_index, 1, $value['title']);

      $row = 2;
      foreach ($examinees as $exk => $exv) {
        $type_id = explode('_', $exv['type_id'])[0];
        $productinfo_type = Db::table('productinfo_type')->select('id','title')->whereRaw("id = '".$type_id."'")->first();
        $productinfo_type = CommonService::objectToArray($productinfo_type);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(0, $row, $productinfo_type['title'] ?? '');
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow(1, $row, $exv['cancel']=='1' ? '已取消' : '報名');

        $register_data = (Array)json_decode($exv['register_data']);
        if( !isset($register_data['field_id_'.$value['id']]) ){
          $register_data['field_id_'.$value['id']] = ExaminationHelper::set_examinee_default_ans($value['type']);
        }

        if(in_array($value['type'], $get_fields_type_par['types_need_checked'])){
          $ans = implode(",", $register_data['field_id_'.$value['id']]);
        }
        else if(in_array($value['type'], ['file'])){
          $ans = $register_data['field_id_'.$value['id']]->file_name."：".$register_data['field_id_'.$value['id']]->data;
        }
        else{
          $ans = str_replace("\\n", "\n", $register_data['field_id_'.$value['id']]);
        }
        $ans = str_replace('"', "''", $ans); /*取代「”」成兩個「’」，避免語法衝突*/
        if(is_numeric($ans)){ $ans = '="'.$ans.'"'; } /*將數字加入「=""」強制轉成文字格式(避免手機09的0被去除)*/
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($column_index, $row, $ans);

        $row++;
      }
    }

    $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
    $filename = $title['info_title']." 報名資料.xlsx";
    try {
      ob_end_clean();
    } catch (\Exception $e){
      // dump($e->getMessage());
    }
    ob_start();
    header("Content-type: application/force-download");
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $PHPExcelWriter->save('php://output');
  }

  /*依訂單編號設定入取候補*/
  public function select_pre_examinee(Request $request){
    $type_id = $request->post('type_id');
    $order_id = $request->post('order_id');
    // dump($type_id);
    // dump($order_id);
    $this->check_controll_productinfo($type_id);

    $post_data['search_cond']['cancel_status'] = '0';
    $post_data['search_cond']['type_id'] = explode('_', $type_id)[0];
    $post_data['backstage'] = true; /*允許看全部*/
    $examinees_on = ExaminationHelper::get_myself_type_examinee_info(0, $examinee_id=0, $order_id, '', $post_data);
    // dump($examinees_on);

    if(count($examinees_on)){
      foreach ($examinees_on as $key => $value) {
        Db::table('examinee_info')->where('id',$value['id'])->update(['reg_status'=>2]);
      }
      $this->success(count($examinees_on));
    }else{
      Db::table('examinee_info')->where('type_id',$type_id)->where('order_id',$order_id)->update(['reg_status'=>3]);
      $this->error("此批人員可能已取消訂單或已取消報名，無法錄取");
    }
  }


  /*取消活動(以品項id)*/
  public function close_type(Request $request){
    $type_id = $request->get('type_id');
    $this->check_controll_productinfo($type_id);

    // 提醒報名者活動取消
    $examinee_info = Db::table('examinee_info')->where('type_id', $type_id)->where('cancel', 0)->get();
    $examinee_info = CommonService::objectToArray($examinee_info);
    foreach ($examinee_info as $exk => $exv) {
      $this->mail_cancel_letter($exv['id']);
      Db::table('examinee_info')->where('id', $exv['id'])->update(['cancel'=>1]);
    }

    /*修改品項取消狀態、日期*/
    Db::table('productinfo_type')->where('id='.$type_id)->update(
      ['closed'=>1, 'closed_date'=>date('Y-m-d H:i:s')
    ]);
    $this->success("取消成功");
  }
  /*取消報名(以報名id)*/
  public function multiCancel(Request $request) {
    $post = $request->post();
    $id = isset($post['id']) ? $post['id'] : "[]";
    $id = json_decode($id);

    foreach ($id as $value) {
      $this->check_controll_examinee_info($value);
    }
    try{
      foreach ($id as $key => $value) {
        $this->mail_cancel_letter($id);
        Db::table('examinee_info')->where('id', $value)->update(['cancel'=>1]);
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('取消報名成功');
  }
  /*寄送取消報名信*/
  private static function mail_cancel_letter($ex_id){
    $examinee_info = Db::table('examinee_info')->where('cancel', 0)->find($ex_id);
    $examinee_info = CommonService::objectToArray($examinee_info);

    if($examinee_info){
      $globalMailData = HelperService::getMailData();

      // 找出報名的品項、商品
      $productinfo_type = ExaminationHelper::get_prodtype_and_prodtitle_by_type_id($examinee_info['type_id']);

      /*取得商品的所有欄位*/
      $fields = ExaminationHelper::get_fields_by_prod_id($examinee_info['prod_id']);
      $name_key = "name_key";
      foreach ($fields as $fk => $fv) {
        if($fv['fields_set_id']=='1'){ /*此欄是姓名*/
          $name_key = 'field_id_' . $fv['id'];
        }
      }

      $register_data = (Array)json_decode($examinee_info['register_data']);
      $ex_name = isset($register_data[$name_key]) ? $register_data[$name_key] : "報名者";
      if($examinee_info['email']){
        $mailBody = "
          <html>
            <head></head>
            <body>
              <div>
                ".$ex_name." 您好<br>
                您對 ".$productinfo_type['p_title']." ".$productinfo_type['title']." 的報名已被取消，造成不便請見諒<br>
              </div>
              <div>
                ". $globalMailData['system_email']['act_cancel'] ."
              </div>
              <div style='color:red;'>
                ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
              </div>
            </body>
          </html>
        ";
        $result = HelperService::Mail_Send($mailBody, 'client', $examinee_info['email'], "活動取消通知");
        $converted_res = $result ? 'true' : 'false';
        // dump($mailBody);
        // dump($converted_res." ".$examinee_info['email']);
        // exit;
      }
    }
  }


  /*修改點名資料*/
  public function update_examinee(Request $request){
    $data = $request->post();
    if(!isset($data['id'])){ $this->error("資訊不足"); }
    $this->check_controll_examinee_info($data['id']);

    $update_data = [];
    try{
      $update_data[ $data['col'] ] = $data['value'];
      if($data['col']=='roll_call') $update_data['roll_call_time'] = date('Y-m-d H:i');
      Db::table('examinee_info')->where(['id'=>$data['id']])->update($update_data);
    }catch (\Exception $e){
      $this->error($e->getMessage());
    }
    $this->success($update_data);
  }
  /*點名準備*/
  public function start_roll_call(Request $request){
    $code = $request->get('code');
    if(!$code){ $this->error('點名連結有誤，無法準備點名'); }
    $productinfo = Db::table('productinfo')->whereRaw("md5(product_id)='".$code."'")->first();
    $productinfo = CommonService::objectToArray($productinfo);
    if(!$productinfo){ $this->error('點名連結有誤，無法準備點名'); }

    session()->put('roll_call', $productinfo['id']);
    $this->success('點名準備已完成('.session()->get('roll_call').')，可以開始進行QR code掃描點名', $url='', $data='', $wait=-1);
  }
  /*QR code 點名*/
  public function qrcode_roll_call(Request $request){
    $ex_id=$request->get('ex_id');
    if(!$ex_id) $this->error('QR code有誤，無法點名');

    $examinee_info = Db::table('examinee_info');

    if(!session()->get('admin')){ /*未登入後台*/
      $prod_id = session()->get('roll_call') ? session()->get('roll_call') : "";
      if($prod_id){ /*檢查是否有使用點名連結*/
        if(!parent::check_controll('productinfo', $prod_id)){
          $this->error('您無法操作此項目');
        }
        $examinee_info = $examinee_info->where('prod_id', $prod_id);
      }else{
        $this->error('請先登入後台或取得點名網址，再進行點名', url('Login/index'));
      }
    }

    $examinee_info = $examinee_info->whereRaw('md5(CONCAT(prod_id, "_", id))="'.$ex_id.'"')->first();
    $examinee_info = CommonService::objectToArray($examinee_info);
    if(!$examinee_info) $this->error('未登入後台 或 使用錯誤點名連結，無法點名');
    if($examinee_info['roll_call']!='0'){
      $this->data['roll_call_status'] = '您已點過名了';
    }
    else{
      $this->data['roll_call_status'] = '點名成功';
      $update_data = [
        'roll_call' => 2,
        'roll_call_time' => date('Y-m-d H:i'),
      ];
      Db::table('examinee_info')->where('id', $examinee_info['id'])->update($update_data);
    }

    /*展示被點名者填寫的資料*/
    $post_data['examinee_id'] = $examinee_info['id'];
    $post_data['email'] = $examinee_info['email'];
    $post_data['type_id'] = $examinee_info['type_id'];
    $post_data['backstage'] = true; /*允許看全部*/
    return ExaminationHelper::create_examinee_panel_view(
      $this->data, 
      $post_data, 
      $user_id=0,
      $view_path='admin\examination\examinee_panel'
    );
  }

  private function check_controll_productinfo($type_id){
    $examinee_info = Db::table('examinee_info')->where('type_id', $type_id)->first();
    $examinee_info = CommonService::objectToArray($examinee_info);
    if(!$examinee_info){ $this->error('此項目不存在'); }
    if(!parent::check_controll('productinfo', $examinee_info['prod_id'])){
      $this->error('您無法操作此項目');
    }
  }
  private function check_controll_examinee_info($examinee_id){
    $examinee_info = Db::table('examinee_info')->where(['id'=>$examinee_id])->first();
    $examinee_info = CommonService::objectToArray($examinee_info);
    if(!$examinee_info){ $this->error("此項目不存在"); }
    if(!parent::check_controll('productinfo', $examinee_info['prod_id'])){
      $this->error('您無法操作此項目');
    }
  }

  /*AJAX取得某商品的欄位資料*/
  public function get_fields_by_prod_id(Request $request){
    $prod_id=$request->post('prod_id');
    $prod_fields = ExaminationHelper::get_fields_by_prod_id($prod_id);
    return $prod_fields;
  }
}