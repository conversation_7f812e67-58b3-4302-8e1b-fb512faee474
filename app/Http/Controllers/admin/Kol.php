<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Http\Controllers\home\Kol as KolFront;

class Kol extends MainController{
  private $DBTextConnecter;
  private $resTableName;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('kol');
    $this->resTableName = 'kol';
  }

  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $this->data['searchKey'] = $searchKey;
    $kol = Db::table($this->resTableName)
              ->whereRaw("
                email LIKE '%$searchKey%' OR 
                kol_name LIKE '%$searchKey%' OR 
                real_name LIKE '%$searchKey%' OR 
                english_name LIKE '%$searchKey%' OR 
                phone LIKE '%$searchKey%' OR 
                category LIKE '%$searchKey%'
              ")
              ->orderByRaw('id desc')
              ->paginate(KolFront::PER_PAGE_ROWS)
              ->appends([
                'searchKey' => $searchKey
              ]);
    $this->data['kol'] = $kol;
    return view('admin.kol.set',['data'=>$this->data]);
  }
  public function delete(Request $request) {
    $id = $request->get('id');
    try{
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        Db::table($this->resTableName)->whereIn('id',$idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  /*網紅主體資料檢查必填*/
  private function check_require($data){
    if($data['email']==''){
      $this->error('請輸入帳號(MAIL)');
    }else if($data['password']==''){
      $this->error('請輸入密碼');
    }else if($data['kol_name']==''){
      $this->error('請輸入網紅名');
    }else if($data['mobile']==''){
      $this->error('請輸入手機');
    }else if($data['bank_name']==''){
      $this->error('請輸入匯款銀行');
    }else if($data['bank_account']==''){
      $this->error('請輸入匯款帳號');
    }
  }
  public function doCreate(Request $request) {
    $newData = $request->post();

    // 檢查必填欄位
    $this->check_require($newData);

    $has_account = Db::table($this->resTableName)->whereRaw('email ="'.$newData['email'].'"')->get();
    if(empty($has_account)){
      $newData['creatdate'] = time();
      try{
        $this->DBTextConnecter->setDataArray($newData);
        $this->DBTextConnecter->createTextRow();
      }catch (\Exception $e){
        $this->error($e->getMessage());
      }
    }else{
      $this->error('帳號重複');
    }
    $this->success('操作成功');
  }
  public function update(Request $request) {
    $newData = $request->post();

    // 檢查必填欄位
    $this->check_require($newData);

    $has_account = Db::table($this->resTableName)->whereRaw('email ="'.$newData['email'].'" and id !='.$newData['id'])->get();
    $has_account = CommonService::objectToArray($has_account);
    if(empty($has_account)){
      try{
        $this->DBTextConnecter->setDataArray($newData);
        $this->DBTextConnecter->upTextRow();
      }catch (\Exception $e){
        $this->error($e->getMessage());
      }
    }else{
      $this->error('帳號重複');
    }
    $this->success('操作成功');
  }
  /*AJAX:建立合作期間資料*/
  public function create_period(Request $request){
    $post = $request->post();
    $kol_id = $post['kol_id'] ?? '0';
    $date_start = $post['date_start'] ?? '';
    $date_end = $post['date_end'] ?? '';
    $count_days = $post['count_days'] ?? '';
    if($kol_id=='' || $kol_id=='0'){ $this->error('無法對此網紅新增合作期間'); }
    if($date_start==''){ $this->error('請設定開始日期'); }
    if($date_end==''){ $this->error('請設定結束日期'); }
    if($date_end < $date_start){ $this->error('結束日期需大於開始日期'); }
    if($count_days=='' || $count_days < 1){ $this->error('請設定有效的結算周期(日)'); }

    $repeat_period = Db::table('kol_period')
                        ->where('kol_id', $kol_id)
                        ->whereRaw('
                          ( date_start <= "'.$date_start.'" AND "'.$date_start.'" <= date_end) OR 
                          ( date_start <= "'.$date_end.'" AND "'.$date_end.'" <= date_end) OR 
                          ( date_start <= "'.$date_start.'" AND "'.$date_end.'" <= date_end) OR 
                          ( date_start >= "'.$date_start.'" AND "'.$date_end.'" >= date_end)
                        ')
                        ->get();
    $repeat_period = CommonService::objectToArray($repeat_period);
    if($repeat_period){ $this->error('合作期間重疊'); }

    Db::beginTransaction();
    $kol_period_id = Db::table('kol_period')->insertGetId([
      'kol_id' => $kol_id,
      'date_start' => $date_start,
      'date_end' => $date_end,
      'count_days' => $count_days,
    ]);

    $insert_result = false;
    try{
      $terms = [];
      $terms_date_start = $date_start;
      while ($terms_date_start <= $date_end) {
        $terms_period_end = date('Y-m-d', strtotime($terms_date_start.' +'.($count_days-1).'Day'));
        $terms_period_end = $terms_period_end <= $date_end ? $terms_period_end : $date_end;
        array_push($terms, [
          'kol_period_id' => $kol_period_id,
          'period_start' => $terms_date_start,
          'period_end' => $terms_period_end,
        ]);
        $terms_date_start =  date('Y-m-d', strtotime($terms_period_end.' +1Day'));
      }
      if(count($terms)){
        $insert_result = Db::table('kol_period_term')->insert($terms);
      }
    } catch (\Exception $e) {
      Db::rollback();
      $this->error($e->getMessage());
    }
  
    if($insert_result){
      Db::commit();    
    }else{
      Db::rollback();
      $this->error('發生錯誤');
    }
    $this->success('操作成功');
  }

  /*對帳單列表(未結算、結算中、已結算)*/
  public function salelist(Request $request){
    if (!$request->get('id')) {
      $this->error('請選擇網紅');
    } else {
      $kol_id = $request->get('id');
      $this->data['kol_id'] = $kol_id;
    }
    $type = $request->get('type') ? $request->get('type') : '0'; // 預設未結算
    $this->data['type'] = $type;

    $page = $request->get('page') ? $request->get('page') : 1; // 預設第一頁
    $this->data['page'] = $page;
    
    $kol = Db::table($this->resTableName)->find($kol_id);
    $this->data['kol'] = CommonService::objectToArray($kol);
    
    // 取得符合搜尋區間及類型的訂單
    $start = $request->get('start');
    $end = $request->get('end');
    $this->data['start'] = $start;
    $this->data['end'] = $end;

    $KolFront = new KolFront();
    switch ($type) {
      case '0': // 未結算
        $return_data = $KolFront->get_target_order($kol_id, $type, $start, $end, $page); // 找出符合條件的訂單(依頁數)
        $this->data['orderform'] = $return_data['orderform'];
        $return_data_all = $KolFront->get_target_order($kol_id, $type, $start, $end); // 找出符合條件的訂單(全部)
        $this->data['totalpage'] = ceil(count($return_data_all['orderform'])/KolFront::PER_PAGE_ROWS);
        return view('admin.kol.salelist',['data'=>$this->data]);
        break;

      case '1' || '2': // 結算中or已結算
        $periods = $KolFront->get_target_periods($kol_id, $type, $start, $end, $page); // 找出符合條件的對帳單(依頁數)
        foreach ($periods as $key => $value) { /*各對帳單計算金額*/
          // 找出該對帳單的對應訂單
          $return_data = $KolFront->get_target_order($kol_id, $type, $value['period_start'], $value['period_end']);
          $total = $KolFront->count_orderform_total($kol_id, $return_data['orderform']);
          $periods[$key]['total'] = $total;
          $periods[$key]['confrim_content'] = json_decode($value['confrim_content'], true);
        }
        $this->data['period_sale'] = $periods;
        $periods_all = $KolFront->get_target_periods($kol_id, $type, $start, $end); // 找出符合條件的訂單(全部)
        $this->data['totalpage'] = ceil( count($periods_all)/KolFront::PER_PAGE_ROWS);

        return view('admin.kol.salelist_period',['data'=>$this->data]);
        break;
    }
  }

  /*對帳單詳細內容*/
  public function sale_detail(Request $request){
    if (!$request->get('id')) {
      $this->error('請選擇網紅');
    } else {
      $kol_id = $request->get('id');
    }
    $kol = Db::table($this->resTableName)->find($kol_id);
    $this->data['kol'] = CommonService::objectToArray($kol);
    if (!$request->get('period')) {
      $this->error('請選擇期數');
    } else {
      $period = $request->get('period'); // 所選期數
    }
    $this->data['period'] = $period;
    $kol_period_term = Db::table('kol_period_term')->whereRaw('id ='.$period)->first();
    $kol_period_term = CommonService::objectToArray($kol_period_term);
    $kol_period_term['confrim_content'] = json_decode($kol_period_term['confrim_content'], true);

    $KolFront = new KolFront();
    $type = $kol_period_term['confirm_date'] ? 2 : 1;
    $return_data = $KolFront->get_target_order($kol_id, $type, $kol_period_term['period_start'], $kol_period_term['period_end']);
    $this->data['orderform'] = $return_data['orderform'];

    $kol_period_term['total'] = $KolFront->count_orderform_total($kol_id, $return_data['orderform']);
    $this->data['kol_period_term'] = $kol_period_term;

    return view('admin.kol.sale_detail',['data'=>$this->data]);
  }
  /*AJAX:確認結算*/
  public function confirm_period(Request $request){
    $kol_id = $request->post('kol_id') ?? '';
    if(!$kol_id){ $this->error('沒選擇網紅'); }

    $period = $request->post('period') ?? '';
    $kol_period_term = Db::table('kol_period_term')->whereRaw('id ='.$period)->first();
    $kol_period_term = CommonService::objectToArray($kol_period_term);
    if(!$kol_period_term){ $this->error('期數有誤'); }
    if($kol_period_term['confirm_date']){ $this->error('此期數已核可過'); }

    $KolFront = new KolFront();
    $type = $kol_period_term['confirm_date'] ? 2 : 1;
    $return_data = $KolFront->get_target_order($kol_id, $type, $kol_period_term['period_start'], $kol_period_term['period_end']);

    Db::table('kol_period_term')->whereRaw('id ='.$period)->update([
      'confirm_date' => time(),                                                           /*記錄核可時間*/
      'confrim_content'=>json_encode($return_data['orderform'], JSON_UNESCAPED_UNICODE),  /*記錄當下訂單內容*/
    ]);
    $this->success('確認成功');
  }
}