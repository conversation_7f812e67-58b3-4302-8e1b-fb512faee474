<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\DB;

class Controller extends BaseController
{
	public $data = [];

    public function __construct() {
        $this->data = [];

        if(!defined('DS')) define('DS', '/');
        if(!defined('ROOT_PATH')) define('ROOT_PATH', base_path() . DS);
    }

    /**
     * 操作成功跳转的快捷方法
     * @access protected
     * @param  mixed     $msg 提示信息
     * @param  string    $url 跳转的URL地址
     * @param  mixed     $data 返回的数据
     * @param  integer   $wait 跳转等待时间
     * @param  array     $header 发送的Header信息
     * @return void
     */
    public function success($msg = '', $url = null, $data = '', $wait = 3, array $header = [])
    {
        if (is_null($url) && !is_null(request()->server('HTTP_REFERER'))) {
            $url = request()->server('HTTP_REFERER');
        } 
		// elseif ('' !== $url) {
        //     $url = (strpos($url, '://') || 0 === strpos($url, '/')) ? $url : $this->Container::get('url')->build($url);
        // }
        $admin_info = (array)Db::table('admin_info')->where('id', 1)->first();

        $result = [
            'code' => 1,
            'msg'  => $msg,
            'data' => $data,
            'url'  => $url,
            'wait' => $wait,
            'admin_info' => $admin_info,
        ];

        $type = $this->getResponseType();

        if ('html' == strtolower($type)) {
            $result = View::make(config()->get('view.dispatch_success_tmpl'), $result);
        }

        $response = Response::make($result, 200, $header);

        throw new HttpResponseException($response);
    }

    /**
     * 操作错误跳转的快捷方法
     * @access protected
     * @param  mixed     $msg 提示信息
     * @param  string    $url 跳转的URL地址
     * @param  mixed     $data 返回的数据
     * @param  integer   $wait 跳转等待时间
     * @param  array     $header 发送的Header信息
     * @return void
     */
    public function error($msg = '', $url = null, $data = '', $wait = 3, array $header = [])
    {
        if (is_null($url)) {
            $url = request()->ajax() ? '' : 'javascript:history.back(-1); setTimeout(function(){location.href=`/`}, 1000);';
        }
        // elseif ('' !== $url) {
        //     $url = (strpos($url, '://') || 0 === strpos($url, '/')) ? $url : app('url')->build($url);
        // }
        $admin_info = (array)Db::table('admin_info')->where('id', 1)->first();

        $result = [
            'code' => 0,
            'msg'  => $msg,
            'data' => $data,
            'url'  => $url,
            'wait' => $wait,
            'admin_info' => $admin_info,
        ];

        $type = $this->getResponseType();

        if ('html' == strtolower($type)) {
            $result = View::make(config()->get('view.dispatch_error_tmpl'), $result);
        }

        $response = Response::make($result, 200, $header);

        throw new HttpResponseException($response);
    }

    /**
     * URL重定向
     * @access protected
     * @param string         $url 跳转的URL表达式
     * @param array|integer  $params 其它URL参数
     * @param integer        $code http code
     * @param array          $with 隐式传参
     * @return void
     */
    public function redirect($url, $params = [], $code = 302, $with = [])
    {
        if (empty(session()->get('redirected')) == true) {
            session()->put('redirected', true);
        }

        $response = Redirect::away($url, $code, ['cache-control' => 'no-cache, must-revalidate']);

        throw new HttpResponseException($response);
    }

    /**
     * 获取当前的response 输出类型
     * @access protected
     * @return string
     */
    protected function getResponseType()
    {
        $isAjax = request()->ajax();
        return $isAjax ? 'json' : 'html';
    }

    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;
}
