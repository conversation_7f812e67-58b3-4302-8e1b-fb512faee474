<?php

namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
//photonicClass
use App\Services\CommonService;
use App\Services\pattern\simpleFactory\orderFactory\OrderFactory;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\PointRecords;
use App\Http\Controllers\ajax\Ecpaylogistic;
use App\Http\Controllers\ajax\Hctlogistic;
use App\Services\pattern\MemberInstance;
use App\Http\Controllers\admin\Payfee;

class OrderCtrl extends MainController
{
    const PER_PAGE_ROWS = 20;
    const SIMPLE_MODE_PAGINATE = false;
    const RECEIPTS_STATE = ['未收款', '已收款'];
    const TRANSPORT_STATE = ['未出貨', '已確認'];
    const ORDER_STATE = [
        'New' => '新進訂單',
        'Pickable' => '待列印訂單',
        'Picked' => '待出貨訂單',
        'Complete' => '完成訂單',
        'Cancel' => '取消訂單',
        'Return' => '退貨訂單'
    ];
    protected $style;
    private $Order;
    private $tableName;
    private $coupon_tableName;

    public function __construct(Request $request)
    {
        parent::__construct();
        $this->tableName = 'orderform';
        $this->coupon_tableName = 'coupon_pool';

        $id = $request->post('id');

        if ($id != '' && $id != null) {
            $this->Order = OrderFactory::createOrder($id, $this->tableName, $this->coupon_tableName);
        }
    }

    public function index(Request $request)
    {
        return $this->order_view($request, $request->get('state') ?? 'New');
    }

    public function trash(Request $request)
    {
        return $this->order_view($request, 'Trash');
    }

    private function order_view(Request $request, $state)
    {
        $this->data['distributor_id_search'] = 0;

        $rowData = $this->get_orders($request, $state);
        $this->data['rowData'] = $rowData;

        $rowDataItem = $rowData->items();
        $rowDataItem = array_map(function ($value) {
            $user = DB::connection('main_db')->table('account')->find($value->user_id);
            $value->user = CommonService::objectToArray($user);
            $value->product = json_decode($value->product);

            $value->product = array_map(function ($value) {
                return get_object_vars($value);
            }, $value->product);

            return $value;
        }, $rowDataItem);
        $this->data['rowDataItem'] = $rowDataItem;

        /*付款方式下拉選*/
        $payments = [];
        $pay_fee_dict = Payfee::get_pay_fee_dict();
        $this->data['pay_fee_dict'] = $pay_fee_dict;

        foreach ($pay_fee_dict as $key => $value) {
            if ($value['online'] == 1) {
                array_push($payments, ['id' => $value['id'], 'name' => $value['name'], 'sys_status' => $value['sys_status']]);
            }
        }
        $this->data['payments'] = $payments;

        /*運法下拉選*/
        $transports = DB::connection('main_db')->table($this->tableName)->select('transport')->groupBy('transport')->get();
        $this->data['transports'] = $transports;

        $this->data['state'] = $state;

        if ($state == 'Trash') {
            return view('order.order_ctrl.trash', ['data' => $this->data]);
        } else {
            return view('order.order_ctrl.order', ['data' => $this->data]);
        }
    }

    public function excel(Request $request)
    {
        $state = $request->get('state');
        $rowData = $this->get_orders($request, $state, $need_page = false);
        $rowData = CommonService::objectToArray($rowData);

        // 產生excel表頭
        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $objPHPExcel->setActiveSheetIndex(0);

        $columns = [
            ['excel_name' => '訂購日期', 'column_name' => 'create_time'],
            ['excel_name' => '訂單號碼', 'column_name' => 'order_number'],
            ['excel_name' => '訂購人名', 'column_name' => 'transport_location_name'],
            ['excel_name' => '電話', 'column_name' => 'transport_location_phone'],
            ['excel_name' => '地址', 'column_name' => 'transport_location'],
            ['excel_name' => '訂貨分店', 'column_name' => ''],
            ['excel_name' => '出貨日期', 'column_name' => 'arrival_date'],
            ['excel_name' => '配送代號', 'column_name' => 'transport'],
            ['excel_name' => '序號', 'column_name' => ''],
            ['excel_name' => '貨品編號', 'column_name' => ''],
            ['excel_name' => '貨品名稱', 'column_name' => 'name'],
            ['excel_name' => '早班量', 'column_name' => 'num'],
            ['excel_name' => '中班量', 'column_name' => ''],
            ['excel_name' => '晚班量', 'column_name' => ''],
            ['excel_name' => '合計數量', 'column_name' => 'num'],
            ['excel_name' => '單價', 'column_name' => 'price'],
            ['excel_name' => '小計', 'column_name' => 'total'],
            ['excel_name' => '收款註記', 'column_name' => 'payment'],
        ];

        foreach ($columns as $key => $column_info) {
            $objPHPExcel->getActiveSheet()->setCellValue(chr(65 + $key) . '1', $column_info['excel_name']);
        }

        // 放入資料
        $row_index = 2;

        foreach ($rowData as $row) {
            // 出貨日 = 指定到貨日-1；若遇星期天則-2。
            if (date('l', strtotime('-1 day', strtotime($row['arrival_date']))) === 'Sunday') {
                $shipping_date = date('Y/n/j', strtotime('-2 day', strtotime($row['arrival_date'])));
            } else {
                $shipping_date = date('Y/n/j', strtotime('-1 day', strtotime($row['arrival_date'])));
            }

            $insert = [
                0 => date('Y/n/j', $row['create_time']),
                1 => $row['order_number'],
                2 => $row['transport_location_name'],
                3 => $row['transport_location_phone'],
                4 => $row['transport_location'],
                5 => '001',
                6 => $shipping_date,
                7 => DB::table('shipping_fee')->where('name', $row['transport'])->value('id'),  // 運法改shipping_fee的id編號
                8 => 0,
                9 => '',
                10 => '',
                11 => '',
                12 => '',
                13 => '',
                14 => 0,
                15 => 0,
                16 => 0,
                17 => '',
            ];

            $products = json_decode($row['product'], true);

            $productinfo_type_count = [];
            $whole_productinfo_type = [];
            $box_notcostomized = [];

            foreach ($products as $product) {
                if (isset($product['info_id'])) {
                    $box_info = DB::table('productinfo')->select(['is_box', 'is_customized_box'])->where('id', $product['info_id'])->first();

                    if (empty($box_info) == true) {
                        continue;
                    }

                    // 是不是禮盒shows "is_box : 0||1"
                    if ($box_info->is_box == 1 && $box_info->is_customized_box == 0) {  // 是禮盒但不是客製化，即定型化禮盒
                        $box_content = DB::table('standard_box_info')->where('productinfo', $product['info_id'])->where('created_at', '<', date('Y-m-d H:i:s', $row['create_time']))->orderBy('created_at', 'desc')->first();
                        $box_content = CommonService::objectToArray($box_content);

                        // 曾發生過明明DB就有存到內容物，但這邊就是NULL，匯出excel表格就出錯了；後來去下一張同個商品的訂單，檢查也有相同的內容物、相同的資料結構、陣列，但貼複製給報錯訂單後就修復完成了。其原因未瞭，但PM要求不能報錯，故萬一未來再發生此情況，直接跳過該商品之匯出，待客戶詢問為何少某項，再回來此做檢查修復。
                        if (empty($box_content) == true) {
                            continue;
                        }

                        $box_content['content'] = json_decode($box_content['content'], true);

                        // 如果同筆訂單購買多個同一商品但不同品項的處理。混合主商品和內容物到key，後面(定型化禮盒特殊id形式處理)再拆出以查找詳細資料。
                        $productinfo_type_count[$product['info_id'] . '_' . $product['type_id']] = $product['num'];

                        // 取得定型化禮盒內容物各自的id
                        foreach ($box_content['content'] as $box_content_id => $num) {
                            $type_id = DB::table('box_content')->where('id', $box_content_id)->value('productinfo_type');
                            $box_notcostomized[] = $type_id;  // 記錄定型化禮盒內容物的id，後面修改金額要用

                            if (array_key_exists($product['info_id'] . '_' . $type_id, $productinfo_type_count)) {
                                $productinfo_type_count[$product['info_id'] . '_' . $type_id] += $product['num'] * $num;
                            } else {
                                $productinfo_type_count[$product['info_id'] . '_' . $type_id] = $product['num'] * $num;
                            }
                        }
                    } else {
                        if ($product['key_type'] == 'gift') {
                            if (array_key_exists($product['type_id'] . '_gift', $productinfo_type_count)) {
                                $productinfo_type_count[$product['type_id'] . '_gift'] += $product['num'];
                            } else {
                                $productinfo_type_count[$product['type_id'] . '_gift'] = $product['num'];
                            }
                        } else {
                            if (array_key_exists($product['type_id'], $productinfo_type_count)) {
                                $productinfo_type_count[$product['type_id']] += $product['num'];
                            } else {
                                $productinfo_type_count[$product['type_id']] = $product['num'];
                            }
                        }
                    }
                }
            }

            // ksort($productinfo_type_count);

            foreach ($productinfo_type_count as $type_id => $num) {
                $type_id_arr = explode('_', $type_id);

                // 滿額贈
                if (count($type_id_arr) == 2 && $type_id_arr[1] == 'gift') {
                    $db_result = DB::table('productinfo_type')
                        ->select(['productinfo_type.title AS productinfo_type_title', 'productinfo.title AS productinfo_title', 'productinfo_type.count'])
                        ->rightJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
                        ->where('productinfo_type.id', $type_id_arr[0])
                        ->first();

                    //  滿額贈商品之價格皆為 0
                    $whole_productinfo_type[] = [
                        'title' => $db_result->productinfo_type_title ? $db_result->productinfo_title . ' - ' . $db_result->productinfo_type_title : $db_result->productinfo_title,
                        'num' => $num,
                        'price' => 0,
                        'total' => 0,
                        'remark' => '滿額贈',
                    ];
                } else {
                    if (count($type_id_arr) == 1) { // 一般商品、客製化禮盒
                        $type_id = $type_id_arr[0];
                    } else { // 定型化禮盒內容物
                        $type_id = $type_id_arr[1];
                    }

                    $db_result = DB::table('productinfo_type')
                        ->select(['productinfo_type.title AS productinfo_type_title', 'productinfo.title AS productinfo_title', 'productinfo_type.count'])
                        ->rightJoin('productinfo', 'productinfo_type.product_id', '=', 'productinfo.id')
                        ->where('productinfo_type.id', $type_id)
                        ->first();

                    if (empty($db_result)) {
                        continue;
                    }

                    // 如果這個列表項目是屬於定型化禮盒的內容物，則價格都要歸零。
                    if (in_array($type_id, $box_notcostomized)) {
                        $whole_productinfo_type[] = [
                            'title' => $db_result->productinfo_type_title ? $db_result->productinfo_title . ' - ' . $db_result->productinfo_type_title : $db_result->productinfo_title,
                            'num' => $num,
                            'price' => 0,
                            'total' => 0,
                            'item_order' => '',
                            'remark' => '定型化禮盒內容物',
                        ];
                    } else { // 一般非屬定型化禮盒
                        $whole_productinfo_type[] = [
                            'title' => $db_result->productinfo_type_title ? $db_result->productinfo_title . ' - ' . $db_result->productinfo_type_title : $db_result->productinfo_title,
                            'num' => $num,
                            'price' => $db_result->count,
                            'total' => $db_result->count * $num,
                            'remark' => '',
                        ];
                    }
                }
            }

            $item_order = 2;

            foreach ($whole_productinfo_type as $item) {
                // $insert[8] = isset($item['item_order']) ? $item['item_order'] : str_pad($item_order - 1, 3, '0', STR_PAD_LEFT); // 序號
                $insert[8] = str_pad($item_order - 1, 3, '0', STR_PAD_LEFT); // 序號
                $insert[10] = $item['title'];
                $insert[11] = $item['num'];
                $insert[14] = $item['num'];
                $insert[15] = $item['price'];
                $insert[16] = $item['total'];
                $insert[17] = $item['remark'];

                foreach ($insert as $key => $value) {
                    $objPHPExcel->getActiveSheet()->setCellValue(chr(65 + $key) . $row_index, $value);
                }

                // 若是定型化禮盒的品項，不增加序號。
                // isset($item['item_order']) ? $item_order : $item_order++;
                $item_order++;
                $row_index++;
            }
        }

        // 下載檔案
        $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
        $filename = "訂單紀錄.xlsx";
        ob_end_clean();
        ob_start();
        header("Content-type: application/force-download");
        header("Content-Disposition: attachment; filename=\"$filename\"");
        $PHPExcelWriter->save('php://output');
    }

    public function group_excel(Request $request)
    {
        $state = $request->get('state');
        $distributors = [];
        $rowData = $this->get_orders($request, $state, $need_page = false);

        foreach ($rowData as $key => $value) {
            $key = 'k_' . $value['distributor_id'];

            if (!isset($distributors[$key])) {
                if ($value['distributor_id'] == 0) {
                    $info = ['id' => $value['distributor_id'], 'name' => '平台'];
                } else {
                    /*會員資料*/
                    $MemberInstance = new MemberInstance($value['distributor_id']);
                    $info = $MemberInstance->get_user_data();
                }

                $distributors[$key] = ['info' => $info, 'total' => 0];
            }

            $distributors[$key]['total'] += $value['total'];
        }

        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();;
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getActiveSheet()->setCellValue('A1', '名稱');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '訂單總額');

        // 放入資料
        $row = 2;

        foreach ($distributors as $value) {
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, $value['info']['name']);  // 下單日期	
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $value['total']);      // 訂單編號
            $row++;
        }

        // 下載檔案
        $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
        $filename = "訂單金額總計.xlsx";
        ob_end_clean();
        ob_start();
        header("Content-type: application/force-download");
        header("Content-Disposition: attachment; filename=\"$filename\"");
        $PHPExcelWriter->save('php://output');
    }

    public function get_orderforms(Request $request)
    {
        $state = $request->get('state');
        $rowData_all = $this->get_orders($request, $state, false);
        $data['total_orderform'] = $rowData_all;

        $rowData = $this->get_orders($request, $state, true);
        $data['CurrentPage'] = $rowData->currentPage();
        $data['listRows'] = $rowData->perPage();
        $data['lastPage'] = $rowData->lastPage();

        $rowDataItem = $rowData->items();
        $rowDataItem = array_map(function ($value) {
            $value->create_date = date('Y-m-d', $value->create_time);
            $user = DB::connection('main_db')->table('account')->select('name', 'number')->find($value->user_id);
            $value->user_name = CommonService::objectToArray($user);
            $value->product = json_decode($value->product);

            if (isset(config('control.invoice_style_text')[$value->InvoiceStyle])) {
                $value->InvoiceStyleText = config('control.invoice_style_text')[$value->InvoiceStyle];
            } else {
                $value->InvoiceStyleText = config('control.invoice_style_text')[1];
            }

            $value->payment_name = DB::table('pay_fee')->select('name')->find($value->payment)->name;

            $value->product = array_map(function ($value) {
                return str_replace('\\"', '"', get_object_vars($value));
            }, $value->product);

            $value->tcat_latest_status_translate = intval($value->tcat_latest_status) == 0 ? '-' : DB::connection('main_db')->table('tcat_order_status')->where('status', $value->tcat_latest_status)->value('description');

            return $value;
        }, $rowDataItem);

        $data['rowDataItem'] = $rowDataItem;

        return $data;
    }

    private function get_orders(Request $request, $state, $need_page = true)
    {
        $order_ship_status = $request->get('order_ship_status') ?? '';

        $order_sql = 'order.need_arrival_date desc, order.arrival_date asc, order.create_time desc';

        if ($state == 'Trash') { // 垃圾桶訂單
            $where = "( (order.status='Cancel') OR (order.status='Return') )";
            $order_sql = 'order.cancel_date desc';
        } else if ($state == 'New') { // 新進訂單
            if ($order_ship_status == '-1') { // 全部(非垃圾桶)
                $where = "( order.status NOT IN ('Cancel', 'Return') )";
            } else if ($order_ship_status == '1') { // 待處理
                $where = "( order.status='New' AND (order.receipts_state='0' AND order.payment!='1') )";
            } else if ($order_ship_status == '2') { // 可處理
                $where = "( order.status='New' AND NOT (order.receipts_state='0' AND order.payment!='1') )";
            } else if ($order_ship_status == '3') { // 待列印
                $where = "( order.status='Pickable' )";
            } else if ($order_ship_status == '4') { // 待出貨
                $where = "( order.status='Picked' )";
            } else if ($order_ship_status == '5') { // 已寄出
                $where = "( order.status='Complete' )";
            } else { // 其他
                $where = "( order.status='New' )";
            }
        } else if ($state == 'Complete') { // 完成訂單
            $where = "( order.status='Complete' )";
        } else {
            $where = "( order.status='" . $state . "' )";
        }

        $receipts_state = $request->get('receipts_state') ?? '';

        if ($receipts_state !== '') {
            $where .= " AND order.receipts_state='" . $receipts_state . "'";
        }

        $payment = $request->get('payment') ?? '';

        if ($payment !== '') {
            $where .= " AND order.payment='" . $payment . "'";
        }

        $transport = $request->get('transport') ?? '';

        if ($transport !== '') {
            $where .= " AND order.transport='" . $transport . "'";
        }

        $need_arrival_date = $request->get('need_arrival_date') ?? '';

        if ($need_arrival_date !== '') {
            $where .= " AND order.need_arrival_date='" . $need_arrival_date . "'";
        }

        $transport_location_name = $request->get('transport_location_name') ?? '';

        if ($transport_location_name !== '') {
            $where .= " AND order.transport_location_name='" . $transport_location_name . "'";
        }

        $transport_location_phone = $request->get('transport_location_phone') ?? '';

        if ($transport_location_phone !== '') {
            $where .= " AND order.transport_location_phone='" . $transport_location_phone . "'";
        }

        $stock_status = $request->get('stock_status') ?? '';

        if ($stock_status !== '') {
            $where .= " AND order.stock_status='" . $stock_status . "'";
        }

        $searchKey2 = $request->get('searchKey2') ?? '';
        $searchKey2 = trim($searchKey2);
        $searchKey2_exclude = $request->get('searchKey2_exclude') ?? '';

        if ($searchKey2 != '') {
            $like_condition = $searchKey2_exclude == '排除' ? ' NOT LIKE ' : ' LIKE ';
            $where .= " AND order.product " . $like_condition . " '%" . $searchKey2 . "%'";
        }

        $buy_date_st2 = $request->get('buy_date_st2') ?? '';
        $buy_date_en2 = $request->get('buy_date_en2') ?? '';

        if ($buy_date_st2 != '') {
            $where .= " AND order.create_time > '" . strtotime($buy_date_st2) . "'";
        }

        if ($buy_date_en2 != '') {
            $where .= " AND order.create_time <'" . (strtotime($buy_date_en2) + 86400) . "'";
        }

        $pre_buy = $request->get('pre_buy') ?? '';

        if ($pre_buy != '') {
            $where .= " AND order.product REGEXP '\"pre_buy\":\"1\"";
        }

        $where = str_replace('\\', '\\\\', $where);

        $rowData = DB::connection('main_db')->table($this->tableName . ' as order')->select('order.*', 'account.name', 'account.number')->leftJoin('account', 'account.id', '=', 'order.user_id')->whereRaw($where);

        $distributor_id = 0;
        $distributor_id_where = '';

        if ($this->admin_type == 'distribution') { /*供應商限看自己的*/
            $distributor_id_where = $this->distributor_id_where_sql;
        } else if ($this->admin_type == 'admin') { /*若為管理者，可看指定供應商*/
            // $distributor_id = $request->get('distributor_id') ?? $this->my_distributor_id;
            $distributor_id = '-1';

            if ($distributor_id !== '' && $distributor_id !== '-1') {
                $distributor_id_where = 'distributor_id="' . $distributor_id . '"';
            }
        }

        if ($distributor_id_where) {
            $rowData = $rowData->whereRaw($distributor_id_where);
        }

        $searchKey = $request->get('searchKey') ?? '';
        $searchKey = trim($searchKey);
        $rowData = $rowData->whereRaw("(order.order_number LIKE '%$searchKey%' OR order.transport_location_name LIKE '%$searchKey%' OR order.transport_location_phone LIKE '%$searchKey%')")->orderByRaw($order_sql);

        $page_count = $request->get('page_count') ?? self::PER_PAGE_ROWS;

        if (!$need_page) {
            $rowData = $rowData->get();
            $rowData = CommonService::objectToArray($rowData);
        } else {
            $rowData = $rowData->paginate($page_count)
                ->appends([
                    'order_ship_status' => $order_ship_status,
                    'state' => $state,
                    'receipts_state' => $receipts_state,
                    'payment' => $payment,
                    'transport' => $transport,
                    'transport_location_name' => $transport_location_name,
                    'transport_location_phone' => $transport_location_phone,
                    'stock_status' => $stock_status,
                    'searchKey2' => $searchKey2,
                    'searchKey2_exclude' => $searchKey2_exclude,
                    'buy_date_st2' => $buy_date_st2,
                    'buy_date_en2' => $buy_date_en2,
                    'pre_buy' => $pre_buy,
                    'distributor_id' => $distributor_id,
                    'searchKey' => $searchKey,
                    'page_count' => $page_count,
                ]);
        }

        return $rowData;
    }

    public function check_stock(Request $request)
    {
        /*更新訂單標記的實體庫存狀態(標記非「超額購買」的訂單標為「足夠」)*/
        OrderHelper::check_stock();
        $this->success(Lang::get('操作成功'));
    }

    public function show(Request $request)
    {
        $id = $request->get('id');

        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        $singleData = DB::connection('main_db')->table($this->tableName)->find($id);
        $singleData = CommonService::objectToArray($singleData);
        $config_db = 'A_sub';

        /*會員資料*/
        $MemberInstance = new MemberInstance($singleData['user_id']);
        $singleData['user'] = $MemberInstance->get_user_data();

        $singleData['receipts_state'] = self::RECEIPTS_STATE[$singleData['receipts_state']];
        $singleData['transport_state'] = self::TRANSPORT_STATE[$singleData['transport_state']];
        $singleData['statusName'] = self::ORDER_STATE[$singleData['status']];

        $product_ids = [];
        $singleData['product'] = OrderHelper::get_orderform_products([$singleData['id']]);

        foreach ($singleData['product'] as $k => $v) {
            if (isset($v['type_id']) || $v['name'] == '現場購物') {
                $singleData['product'][$k]['type_id_ori'] = explode('_', $v['type_id'])[0];
                $singleData['product'][$k]['url'] = str_replace('admin', $this->admin_type, $v['url']);
                array_push($product_ids, $v['info_id']);
            }
        }

        /*檢查是否包含「實體編碼」的商品*/
        $has_instance_product = DB::connection($config_db)->table('productinfo')
            ->where('r_repeat', 0)
            ->whereIn('id', $product_ids)
            ->count();
        $this->data['has_instance_product'] = $has_instance_product;

        if (!json_decode($singleData['discount'])) {
            $singleData['discount'] = [];
        } else {
            $singleData['discount'] = array_map(function ($value) {
                return get_object_vars($value);
            }, json_decode($singleData['discount']));
        }

        if (empty($singleData['product'][0]['url'])) {
            $singleData['product'][0]['url'] = 'none';
        }

        // 發票類型
        if (isset(config('control.invoice_style_text')[$singleData['InvoiceStyle']])) {
            $singleData['InvoiceStyleText'] = config('control.invoice_style_text')[$singleData['InvoiceStyle']];
        } else {
            $singleData['InvoiceStyleText'] = config('control.invoice_style_text')[1];
        }

        // 發票資料加密
        $singleData['captcha'] = hash('sha256', $singleData['id'] . '-photonic-' . date('Ymd') . 'abZLvDHTqrjCWFgs7e2w');
        // 捐贈碼查詢
        $singleData['LoveCodeText'] = '其他';

        if ($singleData['LoveCode'] != '') {
            $result = DB::table('lovecode')->where('code', $singleData['LoveCode'])->first();

            if (empty($result) == false) {
                $singleData['LoveCodeText'] = $result->name;
            }
        }

        $singleData['report_check_time'] = date('Y-m-d H:i', $singleData['report_check_time']);

        $this->data['singleData'] = $singleData;

        //物流單按鈕
        $transportPaperBtn = "";

        if (config('control.thirdpart_logistic') == 1) {
            if (in_array($singleData['transport'], config('extra.tcat.shippable'))) {
                if ($singleData['tcat_file_no'] != '') {
                    $transportPaperBtn = '<a class="btn sendbtn btn-sm text-white mr-3 NoPrint" target="_blank" href="/downloads/logistic/Tcat/' . $singleData['order_number'] . '.pdf" download>下載黑貓物流單</a>';
                } else {
                    if (empty($singleData['transport_date'])) {
                        $transportPaperBtn = '<span class="text-danger">請先於系統填寫出貨日期，方可產生物流單</span>';
                    } else {
                        // if (strpos($singleData['transport'], '冷藏') !== false) {
                        //     $transportPaperBtn = '<a class="btn sendbtn btn-sm text-white mr-3 NoPrint" target="_blank" href="' . url('ajax/Tcatlogistic/createTransportPaper') . '/' . $singleData['id'] . '?thermosphere=2" onclick="$(\'#transportPaperBtn_area\').html(\'(已生成物流單，請重新整理)\')">黑貓物流單</a>';
                        // } else if (strpos($singleData['transport'], '冷凍') !== false) {
                        //     $transportPaperBtn = '<a class="btn sendbtn btn-sm text-white mr-3 NoPrint" target="_blank" href="' . url('ajax/Tcatlogistic/createTransportPaper') . '/' . $singleData['id'] . '?thermosphere=3" onclick="$(\'#transportPaperBtn_area\').html(\'(已生成物流單，請重新整理)\')">黑貓物流單</a>';
                        // } else {
                        $transportPaperBtn = '<a class="btn sendbtn btn-sm text-white mr-3 NoPrint" target="_blank" href="' . url('ajax/Tcatlogistic/createTransportPaper') . '/' . $singleData['id'] . '?thermosphere=1" onclick="$(\'#transportPaperBtn_area\').html(\'(已生成物流單，請重新整理)\')">黑貓物流單</a>';
                        // }
                    }
                }
            } else if (in_array($singleData['transport'], config('extra.ecpay.shippable'))) {
                if ($singleData['AllPayLogisticsID'] != '') {
                    $Ecpaylogistic = new Ecpaylogistic();
                    $transportPaperBtn = $Ecpaylogistic->checkTransportPaper($id);
                } else {
                    $transportPaperBtn = '&nbsp;&nbsp;';

                    if ($singleData['transport'] == Lang::get("到店取貨")) { /*當配送方式為「到店取貨」*/
                        $transportPaperBtn = '';
                    } else {
                        $transportPaperBtn_template = '<a class="btn sendbtn btn-sm text-white mr-3 NoPrint" target="_blank" href="{send_url}" onclick="$(\'#transportPaperBtn_area\').html(\'(已生成物流單，請重新整理)\')">{send_name}</a>';

                        if ($singleData['transport'] == Lang::get("宅配")) { /*當配送方式為「宅配」*/
                            /*區分「黑貓」、「郵局」*/
                            $url1_btn = str_replace('{send_url}', url('ajax/Ecpaylogistic/createTransportPaper') . '/' . $singleData['id'] . '/TCAT', $transportPaperBtn_template);
                            $url1_btn = str_replace('{send_name}', '黑貓物流單', $url1_btn);
                            $transportPaperBtn .= $url1_btn;
                            $url2_btn = str_replace('{send_url}', url('ajax/Ecpaylogistic/createTransportPaper') . '/' . $singleData['id'] . '/POST', $transportPaperBtn_template);
                            $url2_btn = str_replace('{send_name}', '中華郵政物流單', $url2_btn);
                            $transportPaperBtn .= $url2_btn;
                        } else {
                            $url3_btn = str_replace('{send_url}', url('ajax/Ecpaylogistic/createTransportPaper') . '/' . $singleData['id'], $transportPaperBtn_template);
                            $url3_btn = str_replace('{send_name}', '產生物流單', $url3_btn);
                            $transportPaperBtn .= $url3_btn;
                        }
                    }
                }
            }
        }

        $this->data['transportPaperBtn'] = $transportPaperBtn;

        $this->data['pay_fee_dict'] = Payfee::get_pay_fee_dict();

        $this->data['NOT_PRODUCTINFO_PRODTYPE'] = Proposal::NOT_PRODUCTINFO_PRODTYPE;

        return view('order.order_ctrl.member-order-info', ['data' => $this->data]);
    }

    /*確認匯款*/
    public function checkReport(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $this->Order->setReportState();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更動成功');
    }

    /*更改收款*/
    public function setReceiptsState(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $this->Order->setReceiptsState($request->post('state'));
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更動成功');
    }

    /*更改出貨*/
    public function setTransportState(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $this->Order->setTransportState($request->post('state'));
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更動成功');
    }

    /*更新訂單備註*/
    public function setPS(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $ps = $request->post('ps');

            if ($ps) {
                // 訂單備註
                $this->Order->setPS($ps);
            } else {
                // 訂單備註(消費者查看)
                $id = $request->post('id');

                if ($id) {
                    DB::connection('main_db')->table($this->tableName)->where("id", $id)->update(['ps2' => $request->post('ps2')]);
                }
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('更動成功');
    }

    /*取消*/
    public function changeStatus2Cancel(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $this->Order->changeStatus2Cancel($request->post('reason'));
        } catch (\Exception $e) {
            $this->dumpException($e);
        }

        $this->success('取消轉換成功');
    }

    public function delete(Request $request)
    {
        if (!parent::check_controll($this->tableName, $this->Order->id(), 'main_db')) {
            $this->error('您無法操作此項目');
        }

        $this->Order->delete();
        $this->success('刪除成功');
    }

    /*恢復訂單*/
    public function changeStatus2Restore(Request $request)
    {
        $reason = $request->post();
        $id = $reason['id'];

        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        try {
            $this->Order->changeStatus2Restore();
        } catch (\Exception $e) {
            $this->dumpException($e);
        }

        $this->success('恢復轉換成功');
    }

    public function multiCancel(Request $request)
    {
        $idList = $request->post('idList');

        if ($idList) {
            try {
                $idList = json_decode($idList);

                foreach ($idList as $value) {
                    if (!parent::check_controll($this->tableName, $value, 'main_db')) {
                        throw new \Exception('您無法操作此項目', 1);
                    }
                }

                foreach ($idList as $value) {
                    $Order = OrderFactory::createOrder($value, $this->tableName, $this->coupon_tableName);
                    $Order->changeStatus2Cancel('');
                }
            } catch (\Exception $e) {
                $this->dumpException($e);
            }
        }

        if ($idList) {
            $this->success('複數取消成功', url('order/order_ctrl/trash'));
        }

        $this->error('請選擇要修改的項目');
    }

    public function multiPicked(Request $request)
    {
        $idList = $request->post('idList');

        if ($idList) {
            try {
                $idList = json_decode($idList);

                foreach ($idList as $value) {
                    if (!parent::check_controll($this->tableName, $value, 'main_db')) {
                        throw new \Exception('您無法操作此項目', 1);
                    }
                }

                foreach ($idList as $value) {
                    $Order = OrderFactory::createOrder($value, $this->tableName, $this->coupon_tableName);
                    $Order->changeStatus2Picked();
                }
            } catch (\Exception $e) {
                $this->dumpException($e);
            }
        }

        if ($idList) {
            $this->success('複數轉換成功');
        }

        $this->error('請選擇要修改的項目');
    }

    public function multiNext(Request $request)
    {
        $idList = $request->post('idList');

        if ($idList) {
            try {
                $idList = json_decode($idList);

                foreach ($idList as $value) {
                    if (!parent::check_controll($this->tableName, $value, 'main_db')) {
                        throw new \Exception('您無法操作此項目', 1);
                    }
                }

                foreach ($idList as $value) {
                    $Order = OrderFactory::createOrder($value, $this->tableName, $this->coupon_tableName);
                    $Order->changeStatus2Next();
                }
            } catch (\Exception $e) {
                $this->dumpException($e);
            }
        }

        if ($idList) {
            $this->success('複數轉換成功');
        }

        $this->error('請選擇要修改的項目');
    }
    public function multiDelete(Request $request)
    {
        $idList = $request->post('idList');

        if ($idList) {
            try {
                $idList = json_decode($idList);

                foreach ($idList as $value) {
                    if (!parent::check_controll($this->tableName, $value, 'main_db')) {
                        throw new \Exception('您無法操作此項目', 1);
                    }
                }

                foreach ($idList as $value) {
                    $Order = OrderFactory::createOrder($value, $this->tableName, $this->coupon_tableName);
                    $Order->delete();
                }
            } catch (\Exception $e) {
                $this->dumpException($e);
            }
        }

        if ($idList) {
            $this->success('複數刪除成功', url('order/order_ctrl/trash'));
        }

        $this->error('請選擇要修改的項目');
    }

    public function move_to_pick(Request $request)
    {
        /*POST傳入idList，內容為json格式的訂單id*/
        /*拋轉須排除包含依實體編碼的商品的訂單*/
        /*拋轉須需檢查商品數量是否足夠*/
        /*拋轉成功的商品需標記上「檢貨編號」，請利用 OrderCtrl 的 get_position (可將功能共用化再加以利用)*/
        $id_list = json_decode($request->get('idList'), true);

        $return_data = [
            'success' => [],        /*拋轉成功的訂單，內容為訂單編號(orderform_number)*/
            'out_of_stock' => [],   /*因庫存數量拋轉失敗的訂單，內容為訂單編號(orderform_number)*/
            'instance_code' => [],  /*因品項為實體編碼拋轉失敗的訂單，內容為訂單編號(orderform_number)*/
        ];

        /*更新訂單標記的實體庫存狀態(標記非「超額購買」的訂單標為「足夠」)*/
        // OrderHelper::check_stock();

        foreach ($id_list as $id) {
            $orderinfos = DB::connection('main_db')->table($this->tableName)->where('id', $id)->first();
            $orderinfos = CommonService::objectToArray($orderinfos);
            $order_number = $orderinfos['order_number'];
            // $products = OrderHelper::get_orderform_products([$orderinfos['id']]);

            /*檢查訂單是否有被標為「實體庫存足夠」*/
            // if ($orderinfos['stock_status'] != 1) {
            //     $return_data['out_of_stock'][] = $order_number;
            //     continue;
            // } else if ($orderinfos['status'] != 'New') {
            //     $return_data['out_of_stock'][] = $order_number;
            //     continue;
            // }

            /* 檢查訂單是否有包含「依實體編碼」的商品 */
            // $flag = 1; /* 預設可以 */
            // foreach ($products as $info) {
            //     if (isset($info['type_id']) == true && isset($info['info_id']) == true) {
            //         $productinfo = DB::table('productinfo')->select('r_repeat')->find($info['info_id']);
            //         $productinfo = CommonService::objectToArray($productinfo);
            //         if ($productinfo['r_repeat'] == '0') {
            //             $return_data['instance_code'][] = $order_number;
            //             $flag = 0;
            //             break;
            //         }
            //     }
            // }
            // if ($flag == 1) {
            $return_data['success'][] = $order_number;
            DB::connection('main_db')->table($this->tableName)->where('id', $id)->update(['status' => 'Pickable']);
            //     $this->write_history($orderinfos);
            // }
        }

        return $return_data;
    }

    /*AJAX 物流狀態*/
    public function ajax_logistic_status(Request $request)
    {
        $id = $request->get('id');

        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            return [];
        }

        $order = DB::connection('main_db')->table($this->tableName)->find($id);
        $order = CommonService::objectToArray($order);
        $AllPayLogisticsID = $order['AllPayLogisticsID'];

        $logisticsRecord = DB::connection('main_db')->table('logistics_record as lr')
            ->distinct()
            ->select('lr.time', 'lc.type', 'lc.message', 'lr.id')
            ->leftJoin('logistics_code as lc', function ($query) {
                $query->on('lr.RtnCode', '=', 'lc.code')
                    ->on('lr.LogisticsType', '=', 'lc.type');
            })
            ->where('lr.order_id', $id);

        if (empty($AllPayLogisticsID) == false) {
            $logisticsRecord = $logisticsRecord->where('lr.logistics_id', $AllPayLogisticsID);
        }

        $logisticsRecord = $logisticsRecord->orderBy('lr.id', 'asc')->get();
        $logisticsRecord = CommonService::objectToArray($logisticsRecord);

        return $logisticsRecord;
    }

    /* 取得商品品項庫存編碼 */
    public function get_position(Request $request)
    {
        if (!empty($request->post('order_number'))) {
            $order_number = $request->post('order_number');
        } else {
            $this->error('請提供訂單編號');
        }

        if (!empty($request->post('prod_id'))) {
            $prod_id = $request->post('prod_id');
        } else {
            $this->error('請提供商品ID');
        }

        if (!empty($request->post('type_id'))) {
            $type_id = $request->post('type_id');
        } else {
            $this->error('請提供品項ID');
        }

        $orderdata = DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->first();

        if (empty($orderdata) == true) {
            $this->error('無此項目');
        }

        if (!parent::check_controll($this->tableName, $orderdata->id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        // 處理type_id
        [$key_id, $key_type] = Proposal::get_prod_key_type($type_id);

        if (in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) {
            $this->error('此商品無須揀貨');
        }

        $config_db = OrderHelper::get_shop_db_config($order_number);

        $productinfo = DB::table('productinfo')->where('id', $prod_id)->get();
        $productinfo = CommonService::objectToArray($productinfo);

        if (empty($productinfo) == true) {
            $this->error('查無商品');
        }

        if (!parent::check_controll('productinfo', $prod_id, $config_db)) {
            $this->error('您無法操作此項目');
        }

        $position_portion = DB::connection($config_db)->table('position_portion as pp')
            ->select([
                'pos.name as p_name',
                'pos.number as p_number',
                'pos.max as p_max',
                'pp.position_id',
                'pp.position_number',
                'pp.id as pp_id',
                'pp.num'
            ])
            ->whereRaw("pp.productinfo_type = '" . $key_id . "'")
            ->leftJoin('position as pos', 'pos.id', '=', 'pp.position_id')
            ->orderByRaw('pp.position_number asc')
            ->get();
        $position_portion = CommonService::objectToArray($position_portion);
        array_walk($position_portion, function ($item, $key) use (&$position_portion) {
            $position_portion[$key]['p_code'] = $item['p_max'] == "1" ? $item['p_name'] . $item['position_number'] : $item['p_name'] . str_pad($item['position_number'], strlen($item['p_number']), "0", STR_PAD_LEFT);
        });

        if ($productinfo[0]['r_repeat'] == 0) { // 依實體編碼			
            return ['code' => 1, 'position_portion' => $position_portion];
        } else {  // 依品項編碼
            $orderform = DB::connection('main_db')->table($this->tableName)->where('order_number', $order_number)->get();
            $orderform = CommonService::objectToArray($orderform);

            if (empty($orderform) == true) {
                $this->error('查無此訂單');
            }

            $product = OrderHelper::get_orderform_products([$orderform[0]['id']]);

            foreach ($product as $k => $v) {
                if (!isset($v['type_id'])) {
                    continue;
                }

                if (!$v['type_id']) {
                    continue;
                }

                if ($v['type_id'] == $key_id && $v['key_type'] == $key_type) {
                    DB::table('position_portion')->whereRaw('product_id=' . $prod_id . ' AND productinfo_type=' . $key_id)->limit(1)->decrement('num', $v['num']); // 釋出庫存編碼、扣數量
                    DB::table('position_portion')->where('num', 0)->delete(); // 編碼剩餘數為0則刪除紀錄
                    DB::table('position_portion')->where('product_id', 0)->delete(); // 編碼無對應商品則刪除紀錄

                    $config_db = OrderHelper::get_shop_db_config($orderform[0]['order_number']);
                    Db::connection($config_db)->table('picked_history')
                        ->insert([
                            'order_id' => $orderform[0]['id'],
                            'order_time' => $orderform[0]['create_time'],
                            'p_code' => $position_portion[0]['p_code'],
                            'num' => $v['num'],
                            'product_id' => $prod_id,
                            'productinfo_type' => $key_id,
                            'position_id' => $position_portion[0]['position_id'],
                            'position_number' => $position_portion[0]['position_number'],
                            'deal_position' => 1,
                            'datetime' => date('Y-m-d H:i:s'),
                        ]);
                    DB::connection('main_db')->table('orderform_product')
                        ->where('orderform_id', $orderform[0]['id'])
                        ->where('type_id', $key_id)
                        ->where('key_type', $key_type)
                        ->update([
                            'deal_position' => 1,
                            'position_code' => $position_portion[0]['p_code'] . ":" . $v['num'] . '個',
                        ]);
                    $product[$k]['deal_position'] = 1;
                    $product[$k]['position_code'] = $position_portion[0]['p_code'] . ":" . $v['num'] . '個';

                    break;
                }
            }

            // 更新訂單狀態
            $order_update = ['product' => json_encode($product, JSON_UNESCAPED_UNICODE),];
            // 檢查是否有未撿貨的商品
            $has_not_picked = DB::connection('main_db')->table('orderform_product')
                ->where('orderform_id', $orderform[0]['id'])
                ->whereNotNull('key_type')
                ->where('deal_position', 0)
                ->count();

            if ($has_not_picked == 0) { // 無未撿貨商品
                $order_update['status'] = 'Picked'; // 更新訂單為待出貨
            }

            DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->update($order_update);

            return ['code' => 2];
        }
    }
    /* 釋出庫存編碼、扣數量 */
    public function release_position(Request $request)
    {
        if (!empty($request->post('order_number'))) {
            $order_number = $request->post('order_number');
        } else {
            $this->error('請提供訂單編號');
        }

        if (!empty($request->post('type_id'))) {
            $type_id = $request->post('type_id');
        } else {
            $this->error('請提供品項ID');
        }

        if (!empty($request->post('positions'))) {
            $positions = $request->post('positions');
        } else {
            $this->error('請提供庫存編碼ID');
        }

        $orderdata = DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->first();
        $orderdata = CommonService::objectToArray($orderdata);

        if (empty($orderdata) == true) {
            $this->error('無此項目');
        }

        if (!parent::check_controll($this->tableName, $orderdata['id'], 'main_db')) {
            $this->error('您無法操作此項目');
        }

        // 處理type_id
        [$key_id, $key_type] = Proposal::get_prod_key_type($type_id);

        if (in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) {
            $this->error('此商品無須揀貨');
        }

        $config_db = OrderHelper::get_shop_db_config($order_number);

        $productinfo_type = DB::connection($config_db)->table('productinfo_type')->where('id', $type_id)->first();

        if (empty($productinfo_type) == true) {
            $this->error('無此項目');
        }

        if (!parent::check_controll('productinfo', $productinfo_type->product_id, $config_db)) {
            $this->error('您無法操作此項目');
        }

        $position_code = [];
        $release_time = date('Y-m-d H:i:s');

        foreach ($positions as $k => $v) {
            DB::connection($config_db)->table('position_portion')->whereRaw('id=' . $v['pp_id'])->decrement('num', $v['num']); // 釋出庫存編碼、扣數量

            /*建立揀貨資料(依實體編碼)*/
            $r = Db::connection($config_db)->table('position_portion')->whereRaw('id=' . $v['pp_id'])->first();
            $r = CommonService::objectToArray($r);
            Db::connection($config_db)->table('picked_history')
                ->insert([
                    'order_id' => $orderdata['id'],
                    'order_time' => $orderdata['create_time'],
                    'p_code' => $v['p_code'],
                    'num' => $v['num'],
                    'product_id' => $r['product_id'],
                    'productinfo_type' => $r['productinfo_type'],
                    'position_id' => $r['position_id'],
                    'position_number' => $r['position_number'],
                    'deal_position' => 1,
                    'datetime' => $release_time,
                ]);
            array_push($position_code, $v['p_code'] . ":" . $v['num'] . '個');
        }

        DB::connection($config_db)->table('position_portion')->whereRaw('num<=0')->delete(); // 編碼剩餘數為0(貨更小)則刪除紀錄
        DB::connection($config_db)->table('position_portion')->where('product_id', 0)->delete(); // 編碼無對應商品則刪除紀錄

        $product = OrderHelper::get_orderform_products([$orderdata['id']]);

        foreach ($product as $k => $v) {
            if (!isset($v['type_id'])) {
                continue;
            }

            if (!$v['type_id']) {
                continue;
            }

            if ($v['type_id'] == $key_id && $v['key_type'] == $key_type) {
                $product[$k]['deal_position'] = 1;
                $product[$k]['position_code'] = join(',', $position_code);
                DB::connection('main_db')->table('orderform_product')
                    ->where('orderform_id', $orderdata['id'])
                    ->where('type_id', $key_id)
                    ->where('key_type', $key_type)
                    ->update([
                        'deal_position' => 1,
                        'position_code' => join(',', $position_code),
                    ]);
            }
        }

        // 更新訂單狀態
        $order_update = ['product' => json_encode($product, JSON_UNESCAPED_UNICODE),];
        // 檢查是否有未撿貨的商品
        $has_not_picked = DB::connection('main_db')->table('orderform_product')
            ->where('orderform_id', $orderdata['id'])
            ->whereNotNull('key_type')
            ->where('deal_position', 0)
            ->count();

        if ($has_not_picked == 0) { // 無未撿貨商品
            $order_update['status'] = 'Picked'; // 更新訂單為待出貨
        }

        DB::connection('main_db')->table($this->tableName)->whereRaw('order_number="' . $order_number . '"')->update($order_update);

        $this->success('揀貨成功');
    }

    /*修改訂單內容*/
    public function update(Request $request)
    {
        if (!$request->post()) {
            $this->error('請提供更新內容');
        }

        if (empty($request->post('id'))) {
            $this->error('請提供更新訂單');
        }

        $id = $request->post('id');

        if (!parent::check_controll($this->tableName, $id, 'main_db')) {
            $this->error('您無法操作此項目');
        }

        $data = [];
        $allow_change = ['transport_location', 'transport_location_name', 'transport_location_phone', 'GoodsWeight', 'InvoiceNo', 'InvoiceDate'];

        foreach ($allow_change as $key) {
            if (empty($request->post($key)) == false) {
                $data[$key] = $request->post($key);

                if ($key == 'InvoiceDate') {
                    $data[$key] = date('Y-m-d', strtotime($data[$key]));
                }
            }
        }

        DB::connection('main_db')->table($this->tableName)->whereRaw('id="' . $id . '"')->update($data);
        $this->success('修改成功');
    }

    /*待取庫存 - 確認*/
    public function move_to_picked(Request $request)
    {
        $input = $request->post();

        $pick_num = $input['pick_num'];

        $result = DB::table('picked_history')->select('*')
            ->where('deal_position', 0)
            ->orderBy('order_time', 'asc');

        if (empty(config('control.close_function_current')['存放位置管理'])) {
            $result = $result->where('p_code', $input['position_code']);
        } else {
            $result = $result->where('productinfo_type', $input['position_code']);
        }

        $result = $result->get();

        $pick_time = date('Y-m-d H:i:s');

        if (empty($result) == false) {
            $result = CommonService::objectToArray($result);

            foreach ($result as $info) {
                if ($pick_num == 0) {
                    break;
                }

                if ($info['num'] <= $pick_num) {
                    $pick_num -= $info['num'];
                    DB::table('picked_history')->whereRaw("id = '{$info['id']}'")->update(['deal_position' => 1, 'datetime' => $pick_time]);
                } else {
                    if (empty(config('control.close_function_current')['庫存警示']) == true) {
                        DB::table('picked_history')->whereRaw("id = '{$info['id']}'")->update(['num' => $info['num'] - $pick_num]);
                    }

                    DB::table('picked_history')->insert([
                        'order_id' => $info['order_id'],
                        'order_time' => $info['order_time'],
                        'p_code' => $info['p_code'],
                        'num' => $pick_num,
                        'product_id' => $info['product_id'],
                        'productinfo_type' => $info['productinfo_type'],
                        'position_id' => $info['position_id'],
                        'position_number' => $info['position_number'],
                        'deal_position' => 1,
                        'datetime' => $pick_time,
                    ]);

                    break;
                }
            }

            $this->check_orders(['position_id' => $info['position_id'], 'position_number' => $info['position_number']]);
        }
    }

    // 列印訂單
    public function print_order(Request $request)
    {
        $id = $request->get('id');
        $singleData = DB::connection('main_db')->table($this->tableName)->find($id);
        $singleData = CommonService::objectToArray($singleData);

        $singleData['subtotal'] = 0;
        $singleData['product'] = OrderHelper::get_orderform_products([$singleData['id']]);

        foreach ($singleData['product'] as $k => $arr) {
            $singleData['product'][$k]['name'] = str_replace('\\"', '"', $arr['name']);

            if (is_null($arr['type_id'])) {
                $singleData['shipping_fee'] = $arr['total'];
                unset($singleData['product'][$k]);
            } else {
                $singleData['subtotal'] += $arr['total'];
            }
        }

        // $singleData['discount']: [{"type":"使用紅利點數","name":"數量106","count":"扣$106","dis":106}]
        $discount_arr = json_decode($singleData['discount'], true);
        $discount_total = 0;

        foreach ($discount_arr as $k => $v) {
            if (preg_match('/打([0-9.]+)折/', $v['count'], $matches)) {
                $discount_total += $matches[1];
            }
            if (preg_match('/扣NT\$([0-9.]+)/', $v['count'], $matches)) {
                $discount_total += $matches[1];
            }
            $discount_total += intval($v['count']);
        }

        $singleData['discount'] = $discount_total;

        $payment_map = [
            '1' => '貨到付款',
            '2' => 'ATM轉帳\\匯款',
            '3' => '線上刷卡',
            '4' => '分期付款',
            '5' => 'LinePay',
        ];
        $singleData['payment'] = $payment_map[$singleData['payment']];

        if ($singleData['ps2'] == '') {
            $singleData['ps2'] = '無';
        }

        $singleData['company_name'] = config('extra.shop.company_name');
        $singleData['service_tel'] = config('extra.shop.service_tel');
        $singleData['service_email'] = config('extra.shop.service_email');
        $singleData['service_address'] = config('extra.shop.service_address');

        $singleData['icon'] = DB::table('index_excel')->orderBy('id')->value('data1');

        $singleData['layout_preview'] = $singleData['layout_preview'] ? json_decode($singleData['layout_preview'], true) : [];

        $this->data['singleData'] = $singleData;

        return view('order.order_ctrl.print_order', ['data' => $this->data]);
    }

    private function write_history($orderinfos)
    {
        $order_id = $orderinfos['id'];
        $config_db = OrderHelper::get_shop_db_config($orderinfos['order_number']);
        $product = OrderHelper::get_orderform_products([$orderinfos['id']]);

        foreach ($product as $k => $v) {
            if (!isset($v['type_id'])) {
                continue;
            }

            if (!$v['type_id']) {
                continue;
            }

            $num = $v['num'];
            $type_id = $v['type_id'];
            $product_id = $v['info_id'];
            $key_type = $v['key_type'];

            if (in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)) {
                $this->error('此商品無須揀貨');
            }

            $productinfo = DB::connection($config_db)->table('productinfo')->whereRaw("id = '$product_id'")->get();

            if (empty($productinfo) == true) {
                $this->error('查無商品');
            }

            if (!parent::check_controll('productinfo', $product_id, $config_db)) {
                $this->error('您無法操作此項目');
            }

            if (isset(config('control.close_function_current')['存放位置管理']) == true) {
                DB::connection($config_db)->table('picked_history')
                    ->insert([
                        'order_id' => $order_id,
                        'order_time' => $orderinfos['create_time'],
                        'num' => $num,
                        'product_id' => $product_id,
                        'productinfo_type' => $type_id,
                        'deal_position' => 0,
                    ]);
            } else {
                $position_portion = DB::connection($config_db)->table('position_portion as pp')
                    ->select(
                        'pos.id as p_id',
                        'pos.name as p_name',
                        'pos.number as  p_number',
                        'pos.max as p_max',
                        'pp.position_number',
                        'pp.id as pp_id'
                    )
                    ->whereRaw("pp.productinfo_type = '" . $type_id . "'")
                    ->leftJoin('position as pos', 'pos.id', '=', 'pp.position_id')
                    ->orderByRaw('pp.position_number asc')
                    ->get();
                $position_portion = CommonService::objectToArray($position_portion);

                array_walk($position_portion, function ($item, $key) use (&$position_portion) {
                    $position_portion[$key]['p_code'] = $item['p_max'] == "1" ? $item['p_name'] . $item['position_number'] : $item['p_name'] . str_pad($item['position_number'], strlen($item['p_number']), "0", STR_PAD_LEFT);
                });

                DB::connection($config_db)->table('picked_history')->insert([
                    'order_id' => $order_id,
                    'order_time' => $orderinfos['create_time'],
                    'p_code' => $position_portion[0]['p_code'],
                    'num' => $num,
                    'product_id' => $product_id,
                    'productinfo_type' => $type_id,
                    'position_id' => $position_portion[0]['p_id'],
                    'position_number' => $position_portion[0]['position_number'],
                    'deal_position' => 0,
                ]);
            }
        }
    }

    /*檢查訂單*/
    private function check_orders($check_info)
    {
        $orderforms = DB::connection('main_db')->table($this->tableName)->whereRaw('status = "Pickable"')->get();

        if (empty($orderforms) == false) {
            $orderforms = CommonService::objectToArray($orderforms);

            foreach ($orderforms as $orderform) {
                $order_id = $orderform['id'];
                $config_db = OrderHelper::get_shop_db_config($orderform['order_number']);
                $products = OrderHelper::get_orderform_products([$order_id]);

                $product_amount = count($products);
                $cc = 1;

                foreach ($products as $product) {
                    if (isset($product['type_id']) == true) {
                        $product_id = $product['info_id'];
                        $productinfotype = $product['type_id'];
                        $num = $product['num'];

                        if (isset(config('control.close_function_current')['存放位置管理']) == true) {
                            $history = DB::table('picked_history as ph')
                                ->selectRaw('SUM(ph.num) as count, ph.product_id, ph.productinfo_type')
                                ->whereRaw("ph.order_id = '{$order_id}' AND ph.product_id = '{$product_id}' AND ph.productinfo_type = '{$productinfotype}' AND ph.deal_position = '1'")
                                ->first();
                            $history = CommonService::objectToArray($history);

                            if (empty($history) == true) {
                                $this->error('揀貨列表無此項目');
                            }

                            if ($history['count'] == $num) {
                                $product['deal_position'] = 1;
                                $cc += 1;
                            }
                        } else {
                            $position_name = $product['position'];

                            $history = DB::table('picked_history as ph')
                                ->selectRaw('SUM(ph.num) as count, ph.product_id, ph.productinfo_type, ph.p_code, ph.position_id, ph.position_number, p.max as p_max')
                                ->leftJoin('position as p', 'p.id', '=', 'ph.position_id')
                                ->whereRaw("ph.order_id = '{$order_id}' AND p.name = '{$position_name}' AND ph.product_id = '{$product_id}' AND ph.productinfo_type = '{$productinfotype}' AND ph.deal_position = '1'")
                                ->first();
                            $history = CommonService::objectToArray($history);

                            if (empty($history) == true) {
                                $this->error('揀貨列表無此項目');
                            }

                            if ($history['count'] == $num) {
                                $product['deal_position'] = 1;
                                $product['position_code'] = $history['p_code'] . ':' . $num . '個';
                                $cc += 1;

                                if ($check_info['position_id'] == $history['position_id'] && $check_info['position_number'] == $history['position_number']) {
                                    DB::connection($config_db)->table('position_portion')->whereRaw("product_id = '{$history['product_id']}' AND productinfo_type = '{$history['productinfo_type']}'")->limit(1)->decrement('num', $num); // 釋出庫存編碼、扣數量
                                }

                                DB::connection($config_db)->table('position_portion')->where('num', 0)->delete(); // 編碼剩餘數為0則刪除紀錄
                                DB::connection($config_db)->table('position_portion')->where('product_id', 0)->delete(); // 編碼無對應商品則刪除紀錄

                                DB::connection('main_db')->table('orderform_product')
                                    ->where('orderform_id', $product['orderform_id'])
                                    ->where('type_id', $product['type_id'])
                                    ->update([
                                        'deal_position' => 1,
                                        'position_code' => $history['p_code'] . ':' . $num . '個',
                                    ]);
                            }
                        }
                    }
                }

                if ($product_amount == $cc) {
                    DB::connection('main_db')->table($this->tableName)->where('id', $order_id)->update([
                        'product' => json_encode($products, JSON_UNESCAPED_UNICODE),
                        'status' => 'Picked',
                    ]);
                }
            }
        }
    }
    public function change_transport_date(Request $request)
    {
        try {
            if (empty($request) == true) {
                throw new \Exception('請提供更新內容');
            }

            if (empty($request->post('id'))) {
                throw new \Exception('請提供更新訂單');
            }

            $id = $request->post('id');

            if (empty($request->post('date'))) {
                throw new \Exception('請提供更新日期');
            }

            $date = $request->post('date');

            if (strtotime($date) < strtotime(date('Y-m-d 00:00:00'))) {
                throw new \Exception('日期不能小於今天');
            }

            $db_result = DB::connection('main_db')->table($this->tableName)->where('id', $id)->count();
            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result) == true) {
                throw new \Exception('查無訂單');
            }

            $tcat_exclude_dates = DB::table('tcat_exclude_dates')->orderBy('date')->pluck('date');
            $tcat_exclude_dates = CommonService::objectToArray($tcat_exclude_dates);

            $exclude_dates = DB::table('shipping_exclude_dates')->where('removed', 0)->orderBy('date')->pluck('date');
            $exclude_dates = CommonService::objectToArray($exclude_dates);

            $dates = array_merge($tcat_exclude_dates, $exclude_dates);

            if (in_array($date, $dates)) {
                throw new \Exception('出貨日為排除出貨日，請擇日再進行出貨');
            }

            $db_result = DB::connection('main_db')->table($this->tableName)->where('id', $id)->update(['transport_date' => $date]);

            if ($db_result === false) {
                throw new \Exception('更新失敗，請稍後再試');
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('修改成功');
    }
}
