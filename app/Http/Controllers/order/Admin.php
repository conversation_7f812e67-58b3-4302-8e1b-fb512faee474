<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

use App\Services\CommonService;
use App\Services\pattern\MemberInstance;

class Admin extends MainController{
  public function member_discount(Request $request){
    $fisrt_buy = Db::connection('main_db')->table("vip_type")->whereRaw("id = 0")->first();
    $this->data['fisrt_buy'] = CommonService::objectToArray($fisrt_buy);

    $vip_type = MemberInstance::get_vip_types()['db_data'];
    $this->data['vip_type'] = $vip_type;

    return view('order.admin.member_discount',['data'=>$this->data]);
  }
  public function add_vip_type(Request $request){
    $insert = $request->post();
    unset($insert['_token']);
    $rule = [
      'type'  	=> 'required',
      'vip_name'	=> 'required',
      'discount' 	=> 'required',
    ];
    $msg = [
      'type.required' => '請選擇折扣方式',
      'vip_name.required' => '名稱不得為空',
      'discount.required' => '優惠值不得為空',
    ];
    $data = [
      'type'  	=> $insert['type'],
      'vip_name'  => $insert['vip_name'],
      'discount'	=> $insert['discount'],
    ];

    $validate=Validator::make($data ,$rule, $msg);

    if($validate->fails()){
      $this->error($validate->errors()->first());
    }

    try{
      Db::connection('main_db')->table("vip_type")->insert($insert);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('新增成功');
  }
  public function update_vip_type(Request $request){
    $id = $request->get('id');

    $update = $request->post();
    unset($update['id']);
    unset($update['_token']);
    $rule = [
      'type'  	=> 'required',
      'vip_name'	=> 'required',
      'discount' 	=> 'required',
    ];
    $msg = [
      'type.required' => '請選擇折扣方式',
      'vip_name.required' => '名稱不得為空',
      'discount.required' => '優惠值不得為空',
    ];
    $data = [
      'type'  	=> $update['type'],
      'vip_name'  => $update['vip_name'],
      'discount'	=> $update['discount'],
    ];
    
    $validate=Validator::make($data,$rule,$msg);

    if($validate->fails()){
      $this->error($validate->errors()->first());
    }

    try{
      Db::connection('main_db')->table("vip_type")->where("id", $id)->update($update);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('更新成功');
  }
  public function del_vip_type(Request $request){
    $id = $request->get('id');
    if($id=="0") $this->error("不可刪除");
    try{
      Db::connection('main_db')->table("vip_type")->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');	
  }
}