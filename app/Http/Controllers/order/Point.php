<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\PointRecords;

class Point extends MainController{
  public function points_record(Request $request){
    return view('order.point.points_record',['data'=> $this->data]);
  }
  public function get_point_data(Request $request){
    $params = $request->post();
    
    /*依需求搜尋紀錄*/
    $PointRecords = new PointRecords(0);
		$records_show = $PointRecords->get_records($params, true);
    
    /*找出符合搜尋紀錄的全部數量*/
    unset($params['count_of_items']);
		$records_all= $PointRecords->get_records($params, false);
    $records_total = count($records_all);

    return [
      'records_show' => $records_show,
      'records_total' => $records_total,
    ];
  }
}