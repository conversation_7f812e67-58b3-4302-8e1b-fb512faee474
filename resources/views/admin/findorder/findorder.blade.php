@extends('admin.Public.aside')
@section('title')G功能應用項目 > 找貨回函@endsection
@section('css')
    <style type="text/css">
        .info .small_pic{
            max-width: 100px;
            max-height: 100px;
            width: auto;
            height: auto;
        }

    </style>
@endsection

@section('content')
    <!-- 修改找貨回函開始 -->
    <a id="replayBoxView_btn" data-toggle="modal" data-target="#replayBoxView" class="d-none">回函內容</a>
    <div class="modal fade main-modal" id="replayBoxView" tabindex="-1" role="dialog" aria-labelledby="replayBoxViewLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="replayBoxView">
            <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                <div class="modal-header">
                    <h5 class="modal-title">回函內容</h5>
                    
                </div>
                <div class="modal-body">
                    <table>
                        <!-- 第一列 -->
                        <tr>
                            <td colspan="2" v-text="time"></td>
                        </tr>

                        <!-- 第二列 -->
                        <tr>
                            <td v-text="name"></td>
                            <td v-text="'手機：'+phone"></td>
                        </tr>
                        <!-- 第三列 -->
                        <tr>
                            <td colspan="2" v-text="'信箱：'+email"></td>
                        </tr>
                        <!-- 第五列 -->
                        <tr>
                            <td colspan="2" v-html="ask_text">
                            </td>
                        </tr>
                        <!-- 第六列 -->
                        <tr v-if="status">
                            <td colspan="2">客服處理紀錄：<br>
                                <textarea readonly v-model="remessage" class="form-control" rows="8"></textarea>
                            </td>
                        </tr>
                        <tr v-else>
                            <td colspan="2">客服處理紀錄：<br>
                                <textarea v-model="remessage" class="form-control" rows="8"></textarea>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="modal-footer" v-if="!status">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">等待處理</button>
                    <button type="button" class="btn btn-primary" @click="ajaxSubmit">處理完畢</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 修改找貨回函結束 -->

    <div id="content">
    
        <ul id="title" class="brand-menu">
            <li><a href="###">G功能應用項目</a></li>
            <li onclick="javascript:location.href='findorder'"><a href="###">找貨回函</a></li>
            <li><a href="###">
                @if($data['searchKey'] != '')
                    搜尋：{{$data['searchKey']}}
                @endif
            </a></li>
        </ul>
        <div class="searchbox">
            <form action="{{url('Findorder/findorder')}}" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1 text-center" placeholder="搜尋品名、單位、備註" style="width:300px" value="{{$data['searchKey_input']}}">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
            <div class="searchTimeBox flex-nowrap">
                <input class="date form-control mr-1" type="text" id="searchTimeInput" value="{{$data['searchDate_input']}}" style="font-size:14px; width: 180px;"/>
                <a class="btn sendbtn"  onclick="searchTime();">搜尋</a>
            </div>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <span class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a onclick="multiDelete();">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
                <!-- 編輯結束 -->
            </span>
            
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width: 1200px;">
                <thead>
                    <tr>
                        <th style="width:20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=contact_logCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th>日期</th>
                        <th>姓名</th>
                        <th>手機</th>
                        <th>信箱</th>
                        <th>狀態</th>
                        <th>詢問內容</th>
                        <th style="width:60px">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['contact_log'])==true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['contact_log'] as $vo)
                    <tr id="contact_log_{{$vo->id}}">
                        <td><input type="checkbox" class="contact_logCheckbox" alt="{{$vo->id}}"></td>
                        <td v-text="time"></td>
                        <td><a href="###" @click="openBox" v-text="name"></a></td>
                        <td><a href="###" @click="openBox" v-text="phone"></a></td>
                        <td><a href="###" @click="openBox" v-text="email"></a></td>
                        <td>
                            <a href="###" @click="openBox" v-if="status">處理完畢</a>
                            <a href="###" @click="openBox" v-else>待處理</a></td>
                        </td>
                        <td v-html="ask_text"></td>
                        <td>
                            <span class="bi bi-trash" 
                                  onclick="location.href = '{{url('Findorder/delete')}}?id={{$vo->id}}'"></span>
                        </td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
                
                
            </table>
        </div>
       
        <!--表格 結束-->
        <div class="text-center">
            {{$data['contact_log']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <!-- Include Required Prerequisites -->
    <script type="text/javascript" src="{{__PUBLIC__}}/js/moment.min.js"></script>
    <!-- Include Date Range Picker -->
    <script type="text/javascript" src="{{__PUBLIC__}}/js/daterangepicker.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>
    $(function() {
        $(document).click(function() {
            $('.edit-item').fadeOut();
        })
        $('.edit').click(function(event) {
            event.stopPropagation();
        })
    });
    </script>
    <script>
        $("input.date").daterangepicker({locale: {format: 'YYYY-MM-DD'}});
    </script>

    <script>
        function contact_status(Data) {
            $.ajax({
                url: "{{url('Findorder/status')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                data: Data,
                datatype: 'json',
                error: function(xhr) {
                    alert('Ajax request 發生錯誤');
                },
                success: function(response) {
                    if(response.status){
                        replayBoxVM.status = 1;
                        replayBoxVM.updateCallerData();
                        alert('更改成功');
                        $('#replayBoxView').modal('hide');
                    }else{
                        console.error(response);
                    }
                }
            });
        }

        var replayBoxModel = {
            id: 0, time:'', name: '', phone: '',  email: '',
            status: 0, ask_text: '', ask: [], remessage: '',
        }
        var replayBoxVM = new Vue({
            el: '#replayBoxView',
            data: replayBoxModel,
            methods: {
                ajaxSubmit: function () {
                    var Data = {
                        id: this.id,
                        status: 1,
                        resp: this.remessage.replace(/\n/g, '<br>')
                    }
                    contact_status(Data);
                },
                updateCallerData: function () {
                    this.caller.status = this.status;
                    this.caller.remessage = this.remessage;
                }
            }
        });

        ///////andy/////多行文字串//////
        function heredoc(fn) {
            return fn.toString().replace(/[\\]/g,"") + '\n'
        }
        /////////////////////////////////////////////

        @if(empty($data['contact_log'])==false)
        @foreach($data['contact_log'] as $vo)

            var tmpl = '';
            tmpl = heredoc(function(){
                `{!! $vo['ask_text'] !!}`
            });
            tmpl = tmpl.split('`');
            delete tmpl[0];
            var lastnum = tmpl.length -1;
            delete tmpl[lastnum];

            var contact_log_{{$vo->id}} = {
                id: "{{$vo->id}}",
                name: "{{$vo->user_name}}",
                phone: "{{$vo->user_phone}}",
                email: "{{$vo->user_email}}",
                status: Number("{{$vo->status}}"),
                ask_text: tmpl.join(''),
                remessage: "<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $vo->resp); ?>",
                time: "{{$vo->createdate}}",
            }
            var contact_log_{{$vo->id}}_VM = new Vue({
                el: '#contact_log_{{$vo->id}}',
                data: contact_log_{{$vo->id}},
                methods: {
                    openBox: function () {
                        replayBoxVM.id = this.id;
                        replayBoxVM.name = this.name;
                        replayBoxVM.phone = this.phone;
                        replayBoxVM.email = this.email;
                        replayBoxVM.status = this.status;
                        replayBoxVM.ask_text = this.ask_text;
                        replayBoxVM.remessage = this.remessage;
                        replayBoxVM.time = this.time;
                        replayBoxVM.caller = this;
                        $('#replayBoxView_btn').click();
                    }
                }
            });
        @endforeach
        @endif

        function multiDelete() {
            var form = document.createElement("form");
            form.action = "{{url('Findorder/multiDelete')}}";
            form.method = "post";
            form.className = 'd-none';

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            multiId.name = "id";
            form.appendChild(multiId);
            document.body.appendChild(form);

            $('#block_block').show();
            form.submit();
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.contact_logCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function searchTime() {
            var form = document.createElement("form");
            form.action = "{{url('Findorder/findorder')}}";
            form.method = "get";
            form.className = 'd-none';

            $timeRange = $('#searchTimeInput').val().split(" - ");

            start = document.createElement("input");
            start.value = $timeRange[0];
            start.name = "start";
            form.appendChild(start);

            end = document.createElement("input");
            end.value = $timeRange[1];
            end.name = "end";
            form.appendChild(end);
            document.body.appendChild(form);

            $('#block_block').show();
            form.submit();
        }

    </script>
@endsection
