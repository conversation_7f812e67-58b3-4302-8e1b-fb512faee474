@extends('admin.Public.aside')
@section('title')G功能應用項目 > 運法管理@endsection
@section('css')
<style>
    .date-tag-bg {
        background-color: white;
        border-radius: 5px;
        gap: 5px;
        padding: 5px;
    }

    .date-tag {
        background-color: #dee2e6;
        border-radius: 5px;
        width: 155px;
        display: inline-block;
        border: 1px solid #000000;
    }
</style>
@endsection
@section('content')
    <!-- 新增修改運法開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">運法內容</h5>
                </div>
                <div class="modal-body">
                    @if($data['admin_type']!='distribution')
                        <p>運法名稱：<br>
                            <input v-model="name" type="text" placeholder="請輸入名稱" class="form-control">
                        </p>
                    @endif
                    <p>運法費用：<br>
                        <input v-model="price" type="number" placeholder="請輸入金額" class="form-control">
                    </p>
                    <p>滿額免運：<span class="text-danger">(若不希望免運，請設定金額為:999999999)</span><br>
                        <input v-model="free_rule" type="number" placeholder="請輸入金額" class="form-control">
                        <input v-model="online" type="hidden">
                    </p>
                    <p>備貨天數：<br>
                        <input v-model="prepare_days" type="number" placeholder="請輸入金額" class="form-control" min="1">
                    </p>
                    <p class="text-danger remark mb-0">備註：</p>
                    <ul class="text-danger remark">
                        @if(config('control.thirdpart_logistic')==1)
                            <li>若想使用綠界物流功能，請將運法名稱輸入成 「宅配」、「7-11取貨」、「全家取貨」、「萊爾富取貨」</li>
                            <li>若運法名稱輸入成「OKmart取貨」，則會顯示門市查詢按鈕，供消費者查詢門市</li>
                        @else
                            <li>若運法名稱輸入成 「7-11取貨」、「全家取貨」、「萊爾富取貨」、「OKmart取貨」，則會顯示門市查詢按鈕，供消費者查詢門市</li>
                        @endif
                        <li>「到店取貨」則一律運法為0元，即便設定其他數值也一樣</li>
                        <li>備貨天數將會影響客戶購買時的防呆判斷</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增修改運法結束 -->

    <div id="content">
    
        <ul id="title" class="brand-menu" onclick="javascript:location.href='index'">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">運法管理</a></li>
            @if($data['searchKey'] != '')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>

        <div id="shipping_setting" class="d-flex mt-3 flex-column">
            <div class="d-flex flex-row align-items-center">
                <div>排除特定星期：</div>
                <select id="exclude_weekday" class="form-control w-25" v-model="exclude_weekday">
                    <option value="0">星期日</option>
                    <option value="1">星期一</option>
                    <option value="2">星期二</option>
                    <option value="3">星期三</option>
                    <option value="4">星期四</option>
                    <option value="5">星期五</option>
                    <option value="6">星期六</option>
                </select>
                <div class="btn sendbtn ml-2" @click="update_exclude_weekday">送出</div>
            </div>
            <div class="mt-2 d-flex flex-row align-items-center">
                <div>排除特定日期：</div>
                <input type="date" id="exclude_date" v-model="exclude_date" class="form-control w-25" placeholder="請選擇日期">
                <button class="btn sendbtn ml-2" @click="add_exclude_date()">新增</button>
            </div>
            <div class="w-100 mt-2 p-2 date-tag-bg d-flex flex-wrap">
                <template v-for="item in exclude_dates">
                    <div class="p-1 mt-1 date-tag">
                        <span v-text="item"></span>
                        <i class="ml-1 bi bi-x-circle cursor-pointer" @click="remove_exclude_date(item)"></i>
                    </div>
                </template>
            </div>
        </div>

        <hr>

        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1 text-center" placeholder="請輸入運法名稱">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame d-flex flex-wrap">
            <div>
                @if($data['admin_type']!='distribution')
                    <a href="###" class="btn clearbtn" onclick="newBlock();"><span class="bi bi-plus-lg add" ></span>新增</a>
                @endif

                <span class="d-inline-block position-relative">
                    <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                    <!-- 編輯開始 -->
                    <div class="edit-item none">
                        <a onclick="multiOnline();">
                            <p class="mb-0">上架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled checked><span class="slider round"></span>
                            </label>
                        </a>
                       
                        <a onclick="multiOffline();">
                            <p class="mb-0">下架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled><span class="slider round"></span>
                            </label>
                        </a>
                        @if($data['admin_type']!='distribution')
                            <a onclick="multiDelete();" class="border-top">
                                刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                            </a>
                        @endif
                    </div>
                    <!-- 編輯結束 -->
                </span>
            </div>

            @if(empty(config('control.close_function_current')['運法標籤管理']))
                <div class="text-danger remark ml-2">
                    「特規運費管理」功能已啟用，運費金額為各商品套用的特規運費金額*數量的合計，而非此處設定之運法費用
                    <br>如未曾自行設定過，則依平台設定
                </div>
            @endif
        </div>

        <!--表格 開始-->
        <div class="edit_form" >
            <table class="table table-rwd" style="min-width:768px;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=shipping_feeCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th>ID</th>
                        @if($data['admin_type']!='distribution')
                            <th>排序</th>
                        @endif
                        <th  style="width: 80px;">上下架</th>
                        <th>運法名稱</th>
                        <th>運法費用</th>
                        <th>滿額免運</th>
                        <th>備貨天數</th>
                        @if($data['admin_type']!='distribution')
                            <th>刪除</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['shipping_fee'])==true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['shipping_fee'] as $vo)
                    <tr id="shipping_fee_{{$vo->id}}">
                        <td><input type="checkbox" class="shipping_feeCheckbox" alt="{{$vo->id}}"></td>
                        <td v-text="id"></td>
                        @if($data['admin_type']!='distribution')
                            <td><input type="number" v-model = "order_id" @blur="change_order()"></td>
                        @endif
                        <td>
                            <label class="switch">
                                <input type="checkbox" v-model="online">
                                <span class="slider round"></span>
                            </label>
                        </td>
                        <td><a href="###" @click="openBox" v-text="name"></a></td>
                        <td v-text="price"></td>
                        <td v-text="free_rule"></td>
                        <td v-text="prepare_days"></td>
                        @if($data['admin_type']!='distribution')
                            <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                        @endif
                    </tr>
                    @endforeach
                    @endif
                </tbody>
                
                
            </table>
        </div>
       
        <div class="text-center">
            {{$data['shipping_fee']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh-tw.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('shippingfee/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                        location.reload();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.createShipping_fee = function (Data) {
            $.ajax({
                url: "{{url('shippingfee/doCreate')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        location.reload();
                    }else{
                        alert('新增失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('新增失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.updateShipping_fee = function (Data) {
            $.ajax({
                url: "{{url('shippingfee/update')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        alert('更改成功');
                        BoxVM.updateCallerData();
                        location.reload();
                    }else{
                        alert(`更改失敗，${response.message}`);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {name: "", price: 0, free_rule:999999999, prepare_days:1, online:1, id: 0, caller: null}
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
            },
            methods: {
                ajaxSubmit: function () {
                    var Data = {
                        name: this.name,
                        price: this.price,
                        free_rule: this.free_rule,
                        prepare_days: this.prepare_days,
                        online: this.online,
                    }
                    if(this.caller == 'new'){
                        this.createShipping_fee(Data);
                    }else{
                        Data.id = this.id;
                        this.updateShipping_fee(Data);
                    }
                },
                updateCallerData: function () {
                    this.caller.name = this.name;
                    this.caller.price = this.price;
                    this.caller.free_rule = this.free_rule;
                    this.caller.prepare_days = this.prepare_days;
                    this.caller.online = this.online;
                    $('#functionModal').modal('hide');
                }
            }
        });

        @if(empty($data['shipping_fee'])==false)
        @foreach($data['shipping_fee'] as $vo)
            var shipping_fee_{{$vo->id}} = {
                id: "{{$vo->id}}",
                order_id: "{{$vo->order_id}}",
                name: "{{$vo->name}}",
                price: "{{$vo->price}}",
                free_rule: "{{$vo->free_rule}}",
                prepare_days: "{{$vo->prepare_days}}",
                online: +"{{$vo->online}}"
            }
            var shipping_fee_{{$vo->id}}_VM = new Vue({
                el: '#shipping_fee_{{$vo->id}}',
                data: shipping_fee_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    },
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.name = this.name;
                        BoxVM.price = this.price;
                        BoxVM.free_rule = this.free_rule;
                        BoxVM.prepare_days = this.prepare_days;
                        BoxVM.online = this.online;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    },
                    change_order: function () {
                        blockData = {
                            id: this.id,
                            order_id: this.order_id
                        }
                        this.blockCtrl(blockData);
                    },
                }
            });
        @endforeach
        @endif

        const shippingSettingVM = new Vue({
            el: '#shipping_setting',
            data: {
                exclude_weekday: -1,
                exclude_date: '',
                exclude_dates: [],
            },
            methods: {
                init: async function () {
                    self = this;

                    await self.get_exclude_weekday();
                    await self.get_exclude_dates();

                    if (self.exclude_dates.length == 0) {
                        $('.date-tag-bg').hide();
                    } else {
                        $('.date-tag-bg').show();
                    }

                    const disableDates = [
                        function(date) {
                            return date.getDay() === self.exclude_weekday;
                        },
                    ]

                    self.exclude_dates.forEach(function (item) {
                        disableDates.push(new Date(item));
                    });

                    $("#exclude_date").flatpickr(
                        {
                            "disable": disableDates,
                            locale: "zh_tw",
                            dateFormat: "Y-m-d",
                            minDate: "today",
                        }
                    );
                },
                get_exclude_weekday: function() {
                    $.ajax({
                        url: "{{url('shippingfee/get_exclude_weekday')}}",
                        type: 'POST',
                        async: false,
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.code == 1) {
                                self.exclude_weekday = response.msg;
                            } else {
                                alert('操作失敗，請重新整理頁面');
                            }
                        },
                        error: function(xhr) {
                        }
                    });
                },
                get_exclude_dates: function() {
                    $.ajax({
                        url: "{{url('shippingfee/get_exclude_dates')}}",
                        type: 'POST',
                        async: false,
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.code == 1) {
                                self.exclude_dates = response.data;
                            } else {
                                alert('操作失敗，請重新整理頁面');
                            }
                        },
                        error: function(xhr) {
                        }
                    });
                },
                update_exclude_weekday: function () {
                    $.ajax({
                        url: "{{url('shippingfee/update_exclude_weekday')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: {
                            exclude_weekday: self.exclude_weekday
                        },
                        success: function(response) {
                            if (response.code == 1) {
                                alert(response.msg);
                                self.init();
                            } else {
                                alert(response.msg);
                                console.log(response.msg);
                            }
                        },
                        error: function(xhr) {
                            alert('更改失敗');
                            console.log(xhr);
                        }
                    });
                },
                add_exclude_date: function () {
                    if (!self.exclude_date) {
                        alert('請選擇日期');
                        return;
                    }

                    $.ajax({
                        url: "{{url('shippingfee/add_exclude_date')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: {
                            exclude_date: self.exclude_date
                        },
                        success: function(response) {
                            if (response.code == 1) {
                                alert(response.msg);
                                self.init();
                            } else {
                                alert(response.msg);
                                console.log(response.msg);
                            }

                            self.exclude_date = '';
                        },
                        error: function(xhr) {
                            alert('更改失敗');
                            console.log(xhr);
                        }
                    });
                },
                remove_exclude_date: function (exclude_date) {
                    if (!confirm('確認刪除?')) {
                        return;
                    }

                    $.ajax({
                        url: "{{url('shippingfee/remove_exclude_date')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: {
                            exclude_date: exclude_date
                        },
                        success: function(response) {
                            if (response.code == 1) {
                                alert(response.msg);
                                self.init();
                            } else {
                                alert(response.msg);
                                console.log(response.msg);
                            }
                        },
                        error: function(xhr) {
                            alert('更改失敗');
                            console.log(xhr);
                        }
                    });
                },
            }
        });

        shippingSettingVM.init();

        function newBlock(){
            BoxVM.id = 0;
            BoxVM.name = "";
            BoxVM.price = "";
            BoxVM.free_rule = 999999999;
            BoxVM.prepare_days = 1;
            BoxVM.online = 1;
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.shipping_feeCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id){
            if(confirm('確認刪除?')){
                location.href = "{{url('shippingfee/delete')}}?id="+id;
            }
        }
        function multiDelete() {
            if(confirm("確定刪除?")){
                var form = document.createElement("form");
                form.action = "{{url('shippingfee/multiDelete')}}";
                form.method = "post";
                form.className = 'd-none';

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";
                form.appendChild(multiId);
                document.body.appendChild(form);
                
                $('#block_block').show();
                form.submit();
                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('shipping_fee_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('shipping_fee_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }
    </script>
@endsection