@extends('admin.Public.aside')
@section('title')G功能應用項目 > 捐贈碼管理@endsection

@section('content')
    <!-- 新增修改捐贈碼開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">捐贈碼內容</h5>
                </div>
                <div class="modal-body">
                    @if($data['admin_type']!='distribution')
                        <p>捐贈單位名稱：<br>
                            <input v-model="name" type="text" placeholder="請輸入單位名稱" class="form-control">
                        </p>
                    @endif
                    <p>捐贈碼：<br>
                        <input v-model="code" type="text" placeholder="請輸入捐贈碼" class="form-control">
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增修改捐贈碼結束 -->

    <div id="content">
    
        <ul id="title" class="brand-menu" onclick="javascript:location.href='index'">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">捐贈碼管理</a></li>
            @if($data['searchKey'] != '')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>
        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1 text-center" placeholder="請輸入捐贈單位名稱">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame d-flex flex-wrap">
            <div>
                @if($data['admin_type']!='distribution')
                    <a href="###" class="btn clearbtn" onclick="newBlock();"><span class="bi bi-plus-lg add" ></span>新增</a>
                @endif

                <span class="d-inline-block position-relative">
                    <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                    <!-- 編輯開始 -->
                    <div class="edit-item none">
                        <a onclick="multiOnline();">
                            <p class="mb-0">上架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled checked><span class="slider round"></span>
                            </label>
                        </a>
                       
                        <a onclick="multiOffline();">
                            <p class="mb-0">下架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled><span class="slider round"></span>
                            </label>
                        </a>
                        @if($data['admin_type']!='distribution')
                            <a onclick="multiDelete();" class="border-top">
                                刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                            </a>
                        @endif
                    </div>
                    <!-- 編輯結束 -->
                </span>
            </div>
        </div>

        <!--表格 開始-->
        <div class="edit_form" >
            <table class="table table-rwd" style="min-width:768px;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=love_codeCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        @if($data['admin_type']!='distribution')
                            <th>排序</th>
                        @endif
                        <th  style="width: 80px;">上下架</th>
                        <th>捐贈單位名稱</th>
                        <th>捐贈碼</th>
                        @if($data['admin_type']!='distribution')
                            <th>刪除</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['lovecode'])==true)
                        <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['lovecode'] as $vo)
                        <tr id="love_code_{{$vo->id}}">
                            <td><input type="checkbox" class="love_codeCheckbox" alt="{{$vo->id}}"></td>
                            @if($data['admin_type']!='distribution')
                                <td><input type="number" v-model = "order_id" @blur="change_order()"></td>
                            @endif
                            <td>
                                <label class="switch">
                                    <input type="checkbox" v-model="online">
                                    <span class="slider round"></span>
                                </label>
                            </td>
                            <td><a href="###" @click="openBox" v-text="name"></a></td>
                            <td v-text="code"></td>
                            @if($data['admin_type']!='distribution')
                                <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                            @endif
                        </tr>
                    @endforeach
                    @endif
                </tbody>
                
                
            </table>
        </div>
       
        <div class="text-center">
            {{$data['lovecode']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('lovecode/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        location.reload();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.createlove_code = function (Data) {
            $.ajax({
                url: "{{url('lovecode/doCreate')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        location.reload();
                    }else{
                        alert('新增失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('新增失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.updatelove_code = function (Data) {
            $.ajax({
                url: "{{url('lovecode/update')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        alert('更改成功');
                        BoxVM.updateCallerData();
                        if(response.id){
                            location.reload();
                        }
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {name: "", code: 0, online:1, id: 0, caller: null}
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
            },
            methods: {
                ajaxSubmit: function () {
                    var Data = {
                        name: this.name,
                        code: this.code,
                        online: this.online,
                    }
                    if(this.caller == 'new'){
                        this.createlove_code(Data);
                    }else{
                        Data.id = this.id;
                        this.updatelove_code(Data);
                    }
                },
                updateCallerData: function () {
                    this.caller.name = this.name;
                    this.caller.code = this.code;
                    this.caller.online = this.online;
                    $('#functionModal').modal('hide');
                }
            }
        });

        @if(empty($data['lovecode'])==false)
        @foreach($data['lovecode'] as $vo)
            var love_code_{{$vo->id}} = {
                id: "{{$vo->id}}",
                order_id: "{{$vo->order_id}}",
                name: "{{$vo->name}}",
                code: "{{$vo->code}}",
                online: +"{{$vo->online}}"
            }
            var love_code_{{$vo->id}}_VM = new Vue({
                el: '#love_code_{{$vo->id}}',
                data: love_code_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    },
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.name = this.name;
                        BoxVM.code = this.code;
                        BoxVM.online = this.online;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    },
                    change_order: function () {
                        blockData = {
                            id: this.id,
                            order_id: this.order_id
                        }
                        this.blockCtrl(blockData);
                    },
                }
            });
        @endforeach
        @endif

        function newBlock(){
            BoxVM.id = 0;
            BoxVM.name = "";
            BoxVM.code = "";
            BoxVM.online = 1;
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.love_codeCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id){
            if(confirm('確認刪除?')){
                location.href = `{{url('lovecode/delete')}}?id=`+id;
            }
        }
        function multiDelete() {
            if(confirm("確定刪除?")){
                var form = document.createElement("form");
                form.action = "{{url('lovecode/multiDelete')}}";
                form.method = "post";
                form.className = 'd-none';

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";
                form.appendChild(multiId);
                document.body.appendChild(form);

                $('#block_block').show();
                form.submit();
                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('love_code_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('love_code_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }
    </script>
@endsection