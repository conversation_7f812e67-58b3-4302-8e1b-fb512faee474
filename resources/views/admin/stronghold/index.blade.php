@extends('admin.Public.aside')
@section('title')E圖文編輯項目 > 經營據點@endsection

@section('content')
    <iframe id="boxFormIframe" name="boxFormIframe" style="display: none;"></iframe>

    <!-- 新增修改經銷據點開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box" style="max-width: 1024px;">
            <form name="boxForm" :action="action" method="post" target="boxFormIframe" enctype="multipart/form-data">
                @csrf
                <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-header">
                        <h5 class="modal-title">據點內容</h5>                        
                    </div>
                    <div class="modal-body">
                            <div class="row">
                                <div class="col-12 col-md-6">
                                    <div class="img-box" style="text-align: center;">
                                        <span class="bi bi-image"></span>
                                        <input type='file' ref="img" class="upl" name="image" accept="image/*" @change="previewImg">
                                        <img class="preview" :src="src"/>
                                    </div>
                                    <p class="remark mb-0">建議大小：700*700</p>
                                </div>
                                <div class="col-12 col-md-6 mt-4">
                                    <span class="remark">標題：</span>
                                    <input type="text" name="title" v-model="title" class="form-control mb-2">
                                    <span class="remark">排序：</span>
                                    <input type="text" name="orders" v-model="orders" class="form-control mb-2">
                                    <span class="remark">內文：</span>
                                    <textarea v-model="content" class="form-control mb-2"></textarea>
                                    <input name="content" type="hidden" v-model="contentNl2br">        
                                    <span class="remark">URL：</span>
                                    <input type="text" name="url" v-model="url" class="form-control mb-2">
                                    <!-- <a class="btn sendbtn mb-2" href="###" @click="add_pic">新增附圖</a> -->
                                    <div v-for="(pic, index) in sub_pics" class="sub_pic_item">
                                        <img v-if="pic.slice(0,7)==='/upload'" :src="'/public/static/index' + pic" >
                                        <img v-if="pic.slice(0,7)!='/upload' && pic!=' '" :src="pic" >
                                        <input type='file' ref="sub_pic" name="sub_pics[]" accept="image/*" @change="previewSubImg(index)">
                                        <a class="btn clearbtn" href="###" @click="delSubImg(index)">刪除</a>
                                        <p class="remark">附圖建議尺寸:36*36</p>
                                    </div>
                                   
                                </div>
                                
                            </div>
                        
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="id" v-model="id">    
                        <input type="hidden" name="parent_id" value="{{$data['parent_id']}}">    
                        <input type="hidden" name="json_sub_pics" :value="json_sub_pics">
                        <button type="button" class="btn sendbtn" @click="formSubmit">儲存</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 新增修改經銷據點結束 -->

    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">E圖文編輯項目</a></li>
            <li><a href="###" onclick="goback()">經營據點</a></li>
            @if($data['distrtitle']!='')
                <li><a href="###">{{$data['distrtitle']}}</a></li>
            @endif
            @if($data['searchKey'] !='')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul> 
        <div class="searchbox">
            <div class="searchKeyBox">
                <div class="label">新增分類：</div>
                <input class="form-control mr-1" type="text"  autofocus>
                <a class="btn sendbtn" onclick="addProdect(this)" class="btn sendbtn">儲存</a>
            </div>
           
        </div>


     

        <!-- 新增分類 -->
    
        <!-- 切換分類 -->
        <div class="list-items d-flex flex-wrap">
            <span class="w-100">操作分類：</span>
            <ul class="list-item">
                @if(count($data['stronghold'])==0)
                無分類，請先建立分類
                @else
                @foreach($data['stronghold'] as $vo)
                <li class="position-relative {{App\Services\CommonService::compare_return($data['parent_id'], $vo['id'], 'border-dark')}}">
                    <a href="{{url('Stronghold/index')}}?id={{$vo['id']}}">
                        {{$vo['title']}}
                        <!-- <span class="bi bi-eye"></span> -->
                    </a>
                    <a href="javascript:$('.change-title').hide();Show('.change-title_{{$vo['id']}}')">
                        <i class="bi bi-pencil-square"></i>               
                    </a>
                    <a href="javascript:del_stronghold('{{$vo['id']}}')">
                        <i class="bi bi-trash"></i>
                    </a>
                    <!-- 更改標題 -->
                    <form action="{{url('Stronghold/updatetitle')}}" name="productForm_{{$vo['id']}}" class="position-absolute" style="z-index: 5;  left: 0;top: 100%;" method="post" enctype="multipart/form-data" >
                        @csrf
                        <div class="change-title change-title_{{$vo['id']}} none border bg-white p-3" style="width: 180px">
                            <p class="remark mb-0">分類名稱</p>
                            <input id="item-name" name="title" type="text" value="{{$vo['title']}}" class="w-100 mb-2">
                            <input id="item-id" name="id" type="hidden" value="{{$vo['id']}}" class="w-100 mb-2">
                            <div class="d-flex">
                                <a class="btn sendbtn" onclick="productForm_{{$vo['id']}}.submit();">儲存</a>
                                <a class="btn clearbtn" onclick="Show('.change-title_{{$vo['id']}}')">取消</a>
                            </div>
                        </div>
                    </form>
                    <!-- 更改標題 結束 -->
                </li>
                @endforeach
                @endif
            </ul>
         
        </div>
        <hr>

        @if($data['parent_id']!='')
            <div class="searchbox">
                <form action="" name="searchForm" method="get"  class="searchKeyBox">
                    @csrf
                    <input type="text" name="searchKey" class="form-control mr-1" placeholder="搜尋據點">
                    <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
                </form>
            </div>

            <!--新增與編輯-->
            <div class="frame">
                <a href="###" class="btn clearbtn"  onclick="newBlock();"><i class="bi bi-plus-lg add small"></i>  新增</a>
                
                <div class="d-inline-block position-relative">
                    <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                    <!-- 編輯開始 -->
                    <div class="edit-item none">
                        <a onclick="multiOnline();">
                            <p class="mb-0">上架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled checked><span class="slider round"></span>
                            </label>
                        </a>
                        <a onclick="multiOffline();">
                            <p class="mb-0">下架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled><span class="slider round"></span>
                            </label>
                        </a>
                        <a onclick="multiDelete();" class="mt-2 btn border-top">
                            刪除 <span style="margin-left: 15px;" class="bi  bi-trash"></span>
                        </a>
                    </div>
                    <!-- 編輯結束 -->
                </div>
            
            </div>
            <!--表格 開始-->
            <div class="edit_form">
                <table class="table-rwd table table-striped" style="min-width:992px;">
                    <thead>
                        <tr>
                            <th style="width: 20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=typeinfo_strCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                            <th style="width: 100px">上下架</th>
                            <th style="width: 200px">預覽圖片</th>
                            <th>標題</th>
                            <th style="width: 200px">排序</th>
                            <th style="width: 60px">刪除</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(count($data['typeinfo_str'])==0)
                        <tr><td colspan='20'>沒有數據</td></tr>
                        @else
                        @foreach($data['typeinfo_str'] as $vo)
                        <tr id="typeinfo_str_{{$vo->id}}">
                            <td><input type="checkbox" class="typeinfo_strCheckbox" alt="{{$vo->id}}"></td>
                            <td>
                                <label class="switch">
                                    <input type="checkbox" v-model="online">
                                    <span class="slider round"></span>
                                </label>
                            </td>
                            <td>
                                <div class="img-box" @click="openBox" style="cursor:pointer;">
                                    <img class="preview_r" :src="src"/>
                                </div>
                            </td>
                            <td><a href="###" @click="openBox">{{$vo->title}}</a></td>
                            <td>{{$vo->orders}}</td>
                            <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}','{{$vo->parent_id}}')"></span></td>
                        </tr>
                        @endforeach
                        @endif
                    </tbody>
                    
                </table>
            </div>

            <div class="text-center">
                {{$data['typeinfo_str']->links('pagination.customize')}}
            </div>
        @endif
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
            $('.edit-item').click(function(event) {
                event.stopPropagation();
            })
        });
        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Stronghold/cellCtrl')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                        location.href = `{{url('stronghold/index')}}`
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };
        var Box = {
            title: "", src: "", content: "", id: 0,
            action: "", url: "", sub_pics: [], orders:0,
            caller: null
        }
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
                contentNl2br: function () {
                    return this.content.replace(/\n/g, '<br>');
                },
                json_sub_pics: function(){
                    return JSON.stringify(this.sub_pics);
                },
            },
            methods: {
                formSubmit: function () {
                    $('#block_block').show();
                    setTimeout(function(){ document.boxForm.submit(); }, 50);
                },
                previewImg: function () {
                    // console.log(this.$refs.img.files);
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        // console.log(e.target.result)
                        Box.src = e.target.result;
                    }
                    reader.readAsDataURL(this.$refs.img.files[0]);
                },
                updateCallerData: function () {
                    this.caller.src = this.src;
                    this.caller.title = this.title;
                    this.caller.sub_pics = this.sub_pics;
                    this.caller.url = this.url;
                    this.caller.content = this.content;
                    $('#functionModal_btn').click();
                },
                add_pic: function(){
                    this.sub_pics.push(" ");
                },
                previewSubImg: function(index){
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        // 以此方式設定陣列的值vue才能偵測到資料更新，然後渲染畫面
                        BoxVM.$set(BoxVM.sub_pics, index, e.target.result);
                    }
                    reader.readAsDataURL(this.$refs.sub_pic[index].files[0]);
                },
                delSubImg: function(index){
                    this.sub_pics.splice(index, 1);
                },
            }
        });
        $('#boxFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                // alert("上傳成功");
                // if(BoxVM.caller == 'new'){
                //     location.reload();
                // }else{
                //     BoxVM.updateCallerData();
                // }
                location.reload();
            }else{
                alert("上傳失敗");
                console.log($(this).contents().find('body').text());
            }
            $('#block_block').hide();
        });

        @if (!empty($data['typeinfo_str']))
        @foreach($data['typeinfo_str'] as $vo)
            var typeinfo_str_{{$vo->id}} = {
                id: "{{$vo->id}}",
                title: "{{$vo->title}}",
                sub_pics: "{{$vo->sub_pics}}",
                src: "{{__UPLOAD__}}{{$vo->pic}}",
                content: "<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $vo->content); ?>",
                online: +"{{$vo->online}}",
                orders: "{{$vo->orders}}",
                action: "{{url('Stronghold/update')}}",
            }
            var typeinfo_str_{{$vo->id}}_VM = new Vue({
                el: "#typeinfo_str_{{$vo->id}}",
                data: typeinfo_str_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    }
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.title = this.title;
                        BoxVM.sub_pics = this.sub_pics;
                        BoxVM.src = this.src;
                        BoxVM.content = this.content;
                        BoxVM.url = this.url;
    					BoxVM.orders = this.orders;
                        BoxVM.action = this.action;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    }
                }
            });
            typeinfo_str_{{$vo->id}}_VM.sub_pics = typeinfo_str_{{$vo->id}}_VM.sub_pics ? JSON.parse(typeinfo_str_{{$vo->id}}_VM.sub_pics): [];
        @endforeach
        @endif

        function newBlock(){
            BoxVM.id = "";
            BoxVM.title = "";
            BoxVM.sub_pics = [];
            BoxVM.src = "";
            BoxVM.url = "";
            BoxVM.content = "";
    		BoxVM.orders = 0;
            BoxVM.action = "{{url('Stronghold/doCreate')}}";
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.typeinfo_strCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id, parent_id){
            if(confirm('確認刪除?')){
                 location.href = "{{url('Stronghold/delete')}}?id="+id+'&parent_id='+parent_id;
            }
        }
        function multiDelete() {
            if(confirm('確認刪除?')){
                var form = document.createElement("form");
                form.action = "{{url('Stronghold/multiDelete')}}";
                form.method = "post";
                form.className = 'd-none';

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";

                form.appendChild(multiId);
                document.body.appendChild(form);

                $('#block_block').show();
                form.submit();
                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('typeinfo_str_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('typeinfo_str_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }
    	
    	/*andy*/
    	function goback(){
    		/*抓取網址 andy*/
    			var checkurl = location.href.split('?')[0];
    			if(checkurl){
    				location.href=checkurl;
    			}
    	}
    </script>
    <script type="text/javascript">
        //新增分館、經銷據點名稱
        function addProdect(item) {
            var input = $(item).prev();
            if(input.val() == '') return alert("請輸入名稱");
            post_url = "{{url('stronghold/create')}}"
            $.ajax({
                url: post_url,
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                datatype: 'json',
                data: {
                title: input.val()
                },
                error: function(xhr) {
                    //alert('Ajax request 發生錯誤');
                    console.error(xhr);
                },
                success: function(response) {
                    if(response.status){
                        //console.log(response);
                        location.reload();
                    }else{
                        console.error(response);
                    }
                }
            });
        }

        function del_stronghold(id=''){
            if(!id){
                return;
            }

            if(confirm("確定刪除?")){
                if("{{$data['parent_id']}}"==id){
                    url = "{{url('stronghold/index')}}";
                }else{
                    url = location.href;
                }
                location.href = "{{url('stronghold/dodelete')}}?id="+ id +'&url='+ url;
            }
        } 
    </script>
@endsection