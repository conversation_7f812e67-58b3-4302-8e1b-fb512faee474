@extends('admin.Public.aside')
@section('title')G功能應用項目 > 禮盒內容物管理@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu" onclick="location.reload()">
            <li><a>G功能應用項目</a></li>
            <li><a>禮盒內容物管理</a></li>
        </ul>

        <!--新增-->
        <div class="d-flex flex-wrap pt-2 pb-2">
            <a class="btn clearbtn" onclick="show_modal('new')">
                <i class="bi bi-plus-lg add"></i>
                <span class="ml-1">新增</span>
            </a>
        </div>

        <!--表格-->
        <div class="edit_form">
            <table class="table table-rwd" style="min-width:768px;">
                <thead>
                    <tr>
                        <th style="width: 250px;">排序</th>
                        <th>內容物分類名稱</th>
                        <th>內容物種類數</th>
                        <th>刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['category']) == true)
                        <tr><td colspan='10'>沒有資料</td></tr>
                    @else
                    @foreach($data['category'] as $vo)
                        <tr>
                            <td><input type="number" min="0" value="{{$vo['order']}}" onblur="change_order('{{$vo['id']}}')" data-id="{{$vo['id']}}"></td>
                            <td><a href="{{url('boxcontent/content')}}?id={{$vo['id']}}">{{$vo['name']}}</a><i class="ml-3 bi bi-pencil-square cursor-pointer" onclick="show_modal('edit', '{{$vo['id']}}', '{{$vo['name']}}', '{{$vo['order']}}');"></i></td>
                            <td><span>{{$vo['count']}}</span></td>
                            <td><span class="bi bi-trash" onclick="delete_one('{{$vo['id']}}')"></span></td>
                        </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
        </div>
    </div>

    <!-- 新增/編輯Modal -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title"></h5>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="id" value="-1">
                    <p>請輸入內容物分類名稱：<br>
                        <input name="name" type="text" class="form-control">
                    </p>
                    <p>請輸入排序：<br>
                        <input name="order" type="number" min="0" class="form-control">
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" onclick="submit_modal()"></button>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('ownJS')
<script src="{{__PUBLIC__}}/js/action.js"></script>
<script>
    function show_modal(mode, id, name, order) {
        if (mode == 'new') {
            $('input[name="name"]').val('');
            $('input[name="order"]').val('0');
            $('input[name="id"]').val(-1);
            $('#functionModal .modal-title').text('新增內容物分類名稱');
            $('.sendbtn').text('新增');
        } else {
            $('input[name="name"]').val(name);
            $('input[name="order"]').val(order);
            $('input[name="id"]').val(id);
            $('#functionModal .modal-title').text('編輯內容物分類名稱');
            $('.sendbtn').text('編輯');
        }

        $('#functionModal_btn').click();
    }

    function submit_modal() {
        const ID = $('input[name="id"]').val();

        $.ajax({
            url: "{{url('boxcontent/create_and_update')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                id: ID,
                name: $('input[name="name"]').val(),
                order: $('input[name="order"]').val(),
            },
            success: function(response) {console.log(response);
                alert(response.msg);
                $('#functionModal').modal('hide');

                if (ID == -1) {
                    location.href = "{{url('boxcontent/content')}}?id=" + response.id;
                } else {
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('新增失敗');
                console.log(xhr);
            }
        });
    }

    function change_order(id) {
        var order = $(`input[data-id="${id}"]`).val();

        $.ajax({
            url: "{{url('boxcontent/change_order')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                id: id,
                order: order
            },
            success: function(response) {
                alert(response.msg);
                location.reload();
            },
            error: function(xhr) {
                alert('更改失敗');
                console.log(xhr);
            }
        });
    }

    function delete_one(id){
        if (!confirm('確認刪除?')) {
            return;
        }

        $.ajax({
            url: "{{url('boxcontent/delete_one')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                alert(response.msg);
                location.reload();
            },
            error: function(xhr) {
                alert('刪除失敗');
                console.log(xhr);
            }
        });
    }
</script>
@endsection