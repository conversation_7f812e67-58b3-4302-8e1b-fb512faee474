@extends('admin.Public.aside')
@section('title')G功能應用項目 > 禮盒內容物管理@endsection

@section('content')
<div id="main">
    <ul id="title" class="brand-menu">
        <li><a>G功能應用項目</a></li>
        <li><a href="{{url('boxcontent/cate')}}">禮盒內容物管理</a></li>
        <li><a>{{$data['boxCateName']}}</a></li>
    </ul>

    <!-- 添加內容物 -->
    <div class="frame" id="addBtn">
        <a href="{{url('boxcontent/cate')}}" class="btn sendbtn">
            <i class="bi bi-arrow-left"></i>
            <span class="ml-1">回上一頁</span>
        </a>
        <template v-if="all_productinfo.length > 0">
            <a class="btn clearbtn" data-target="#contentSelectModal" data-toggle="modal" @click="get_all_productinfo()">
                <i class="bi bi-plus-lg add"></i>
                <span class="ml-1">添加</span>
            </a>
        </template>
        <template v-else>
            <h3 class="mt-4">目前沒有任何品項可供選擇</h3>
        </template>
    </div>

    <!-- 內容物清單 -->
    <div class="admin-content" v-if="content.length > 0">
        <div class="row selected-items">
            <div class="col-lg-2 col-md-4 col-sm-6" v-for="item in content">
                <div class="item">
                    <div>
                        <span v-text="item.productinfo_title"></span><span v-if="item.productinfo_type_title"> - </span><span v-if="item.productinfo_type_title" v-text="item.productinfo_type_title"></span>
                    </div>
                    <div class="img" :style="{backgroundImage:`url(${item.pic})`}"></div>
                    <button type="button" class="btn-sm sendbtn" @click="remove_content(item.productinfo_type)">刪除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加內容物modal -->
    <div class="modal boxModal fade" id="contentSelectModal" tabindex="-1" aria-labelledby="contentSelectModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contentSelectModalLabel">添加內容物</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" style="overflow-x: hidden; overflow-y: auto; max-height: 70vh;">
                    <div class="admin-content modal-admin-content">
                        <template v-for="item in all_productinfo">
                            <div class="mb-4">
                                <h4 v-text="item.product_title"></h4>
                                <div class="row selected-items">
                                    <template v-for="subItem in item.productinfo_type">
                                        <div class="col-md-2 col-sm-6">
                                            <div class="item" @click="toggle_add_to_selected(subItem.productinfo_type)">
                                                <div class="d-flex align-items-center">
                                                    <input type="checkbox" :id="subItem.productinfo_type" :value="subItem.productinfo_type" v-model="selected">
                                                    <label class="mb-0 ml-2" :for="subItem.productinfo_type" v-text="subItem.productinfo_type_title"></label>
                                                </div>
                                                <div class="img" :style="{backgroundImage:`url(${subItem.pic})`}"></div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                    <button type="button" class="btn-sm sendbtn float-right" @click="add_to_content()">加入</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('ownJS')
<script src="{{__PUBLIC__}}/js/action.js"></script>
<script type="text/javascript">
const boxCateId = "{{$data['boxCateId']}}";

const toastDuration = 1500;

let mainVM = new Vue({
    el: '#main',
    data: {
        all_productinfo: [{}],
        content: [],
        selected: [],
    },
    methods: {
        initial: function() {
            let self = this;

            self.selected = [];

            $.ajax({
                url: '{{url("boxcontent/get_content")}}',
                type: 'POST',
                dataType: 'json',
                async: false,
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                data: {
                    cate_id: boxCateId,
                },
                success: function(response) {
                    if (response.code == 1) {
                        self.content = response.data;
                    } else {
                        Vue.toasted.show(response.msg, {duration:toastDuration, className: ["toasted-primary", "bg-danger"]});
                        setTimeout(function() {
                            history.back();
                        }, toastDuration);
                    }
                },
            });
        },
        get_all_productinfo: function() {
            let self = this;

            self.all_productinfo = [{}];

            $.ajax({
                url: '{{url("boxcontent/get_all_productinfo")}}',
                type: 'POST',
                dataType: 'json',
                async: false,
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                success: function(response) {
                    if (response.code == 1) {
                        self.all_productinfo = response.data;
                    } else {
                        Vue.toasted.show(response.msg, {duration:toastDuration, className: ["toasted-primary", "bg-danger"]});
                        setTimeout(function() {
                            history.back();
                        }, toastDuration);
                    }
                },
            });
        },
        modalBodyScrollToTop: function() {
            $('.modal-body').scrollTop(0);
        },
        toggle_add_to_selected: function(productinfo_type) {
            if (this.selected.includes(productinfo_type)) {
                this.selected.splice(this.selected.indexOf(productinfo_type), 1);
            } else {
                this.selected.push(productinfo_type);
            }
        },
        add_to_content: function() {
            let self = this;

            $.ajax({
                url: '{{url("boxcontent/add_to_content")}}',
                type: 'POST',
                dataType: 'json',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                data: {
                    cate_id: boxCateId,
                    selected: self.selected,
                },
                success: function(response) {
                    if (response.code == 1) {
                        Vue.toasted.show(response.msg, {duration:1500, className: ["toasted-primary", "bg-success"]});
                    } else {
                        Vue.toasted.show(response.msg, {duration:1500, className: ["toasted-primary", "bg-danger"]});
                    }

                    $('#contentSelectModal').modal('hide');

                    self.initial();
                },
            });
        },
        remove_content: function(productinfo_type) {
            let self = this;

            $.ajax({
                url: '{{url("boxcontent/remove_content")}}',
                type: 'POST',
                dataType: 'json',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                data: {
                    cate_id: boxCateId,
                    productinfo_type: productinfo_type,
                },
                success: function(response) {
                    if (response.code == 1) {
                        Vue.toasted.show(response.msg, {duration:1500, className: ["toasted-primary", "bg-success"]});
                    } else {
                        Vue.toasted.show(response.msg, {duration:1500, className: ["toasted-primary", "bg-danger"]});
                    }

                    self.initial();
                },
            });
        },
    },
    mounted: function() {
        this.initial();
    },
});

$('#contentSelectModal').on('shown.bs.modal', function () {
    mainVM.modalBodyScrollToTop();
});
</script>
@endsection