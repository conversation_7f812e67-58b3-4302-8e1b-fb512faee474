<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2, user-scalable=0">
    
    <link rel="stylesheet" href="{{__UPLOAD__}}/css/reset.css"> <!-- 初始化樣式 -->
        
    <!-- jquery -->
    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <!-- <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script> -->
    
    <!-- bootstrap -->
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js"></script> <!--輔助bootstrap rwd-->
    <script src="//stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.js"></script>
    <link rel="stylesheet" href="{{__UPLOAD__}}/css/bootstrap-icons.css">
    <link rel="stylesheet" href="{{__UPLOAD__}}/css/iconstyle.css">
    <!-- <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.0/js/bootstrap.min.js"></script> -->

    <!-- 字體 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <!-- <link href="//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700,800&display=swap" rel="stylesheet">-->

    <!-- 自訂樣式 -->
    <link rel="stylesheet" href="{{__UPLOAD__}}/css/style.css?20241113">
    <link rel="stylesheet" href="{{__UPLOAD__}}/css/style2021.css?20241113">
    <link rel="stylesheet" href="{{__UPLOAD__}}/css/body_block.css?20241113">

    <title>{{Lang::get('點名')}} {{$data['title']}}</title>
</head>
<body>
    <div class="container mt-2">
        <div class="row">
            <div class="col-12 mb-2">
                <h5 class="text-center">{{$data['examinee_info']['name']}}</h5>
                <h5 class="text-center">{{Lang::get('點名時間')}}:{{$data['examinee_info']['roll_call_time']}}</h5>
                <hr class="mb-0">
            </div>
            <div class="col-12">
                {{Lang::get('報名資料')}}:
            </div>
			@foreach($data['register_fields'] as $vo)
				<div class="form-group col-12 mb-2">
			        <label class="col-form-label">{{$vo['title']}}</label>
			        @if($vo['required']==1)
			        	<span class="smallText">必填</span>
			        @endif

			        <!-- 輸入設定 -->
                    @switch($vo['type'])
                        @case('text')
                        @case('number')
                        @case('date')
                            <input type="{{$vo['type']}}" class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}"
                                   value="{{$vo['ans']}}" disabled>
                            @break
                        @case('textarea')
                            <textarea class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}" disabled>{{$vo['ans']}}</textarea>
                            @break
                        @case('radio')
                        @case('checkbox')
                            <div class="w-100">
                            @foreach($vo['options'] as $o_k=>$option)
                                <div class="d-inline-block mr-2">
                                    <input type="{{$vo['type']}}" id="field_id_{{$vo['id']}}_{{$o_k}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}" value="{{$option}}"
                                           @if(in_array($option, $vo['ans'])) checked @endif disabled
                                    >
                                    <label for="field_id_{{$vo['id']}}_{{$o_k}}">{{$option}}</label>
                                </div>
                            @endforeach
                            </div>
                            @break
                        @case('select')
                            <select class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}" disabled>
                                <option value="">請選擇</option>
                                @foreach($vo['options'] as $o_k=>$option)
                                    <option value="{{$option}}" @if($option==$vo['ans']) selected @endif>{{$option}}</option>
                                @endforeach
                            </select>
                            @break
                        @case('file')
                            <input type="{{$vo['type']}}" class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}" accept="{{$vo['limit']}}" disabled>
                            已上傳檔案：
                                @if($vo['ans']->data)
                                    <a id="field_id_{{$vo['id']}}_link" href="{{$vo['ans']->data}}" target="_blank">
                                        {{$vo['ans']->file_name}}
                                    </a>
                                @endif
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <!-- <a href="javascript:cancel_file('field_id_{{$vo['id']}}')">取消上傳</a> -->
                            @break
                        @default
                            @break
                    @endswitch
			    	
			    	@if(strip_tags($vo['discription']))
			    		<label class="col-form-label">{{$vo['discription']}}</label>
			    	@endif
			    </div>
			@endforeach
		</div>
	</div>
    @if($data['roll_call_status']??'')
        <script>
            alert("{{$data['roll_call_status']}}");
        </script>
    @endif
</body>