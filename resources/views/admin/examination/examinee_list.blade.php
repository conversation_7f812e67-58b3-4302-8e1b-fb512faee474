@extends('admin.Public.aside')

@section('title')報名資料 > {{$data['title']['info_title']}}  @endsection

@section('css')
    <script src="/public/static/jq-plugin/echarts.js"></script> <!--繪圖-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.0/jquery-confirm.min.css">
    @endsection

@section('content')
    <div id="content">
        <p id="title">
            報名資料 / {{$data['title']['info_title']}}
        </p>

        <div class="searchbox mb-2">
            <form action="" name="searchForm" method="get" class="search-All">
                @csrf
                <input type="hidden" name="id" value="{{$data['id']}}">

                {!! $data['register_fields_search'] !!}

                <a class="btn sendbtn" onclick="send_searchForm();">搜尋</a>
				<a class="btn clearbtn" href="{{url('examination/examinee_list') . '?id=' . $data['id']}}">清除</a>
            </form>
		
        </div>

		<div>
            <a class="btn btn-sm whitebtn mr-1" onclick="show_graph();">顯示圖表</a>
		    <a class="btn btn-sm whitebtn" onclick="send_excelForm();">匯出名單</a>
			<!-- <a class="btn btn-success btn-sm text-white mt-5" href="{{url('Examination/examinee_grade') . '?prod_id' . $data['id']}}">以匯入編輯名單</a> -->
		</div>
        <!-- 
            <form action="{{url('examination/Import') . '?id=' . $data['id']}}" name="excelForm2" method="post"  enctype="multipart/form-data">
                @csrf
                <div class="frame">
                    <input type="file" name="file" id="file_excel"/>
                    <a class="button" onclick="newBlock();">匯入檔案</a>
                </div>
            </form>
        -->

        <!--新增與編輯-->
        <div class="frame d-flex justify-content-between">
            <div class="d-flex align-items-center flex-wrap" style="width: 50%">
                <div class="edit mr-4" onclick="multiCancel();">
                    <a class="d-inline-block"><span class="bi bi-trash"></span> 取消</a>
                </div>
                @if(config('control.control_pre_buy')==1 && empty(config('control.close_function_current')['庫存警示']))
                @if($data['productinfo']['pre_buy'])
                    <a class="btn btn-success btn-sm text-white mr-3" data-toggle="modal" data-target="#pre_group">選擇錄取候補</a>    
                @endif
                @endif
            </div>
            <span class="text-danger remark">
                1.雙擊列表可進入編輯畫面。<br>
                2.可利用左側核選方塊取消報名。
            </span>
        </div>

        <!--表格 開始-->
       
        <div class="edit_form">
            <table class="table table-rwd" style="min-width:1200px;">
                {!! $data['examinee_list_table'] !!}
            </table>
        </div>
        <!--表格 結束-->
    </div>

    <!-- 錄取候補 -->
    <div class="modal fade main-modal" id="pre_group" tabindex="-1" role="dialog" aria-labelledby="pre_group" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">選擇錄取候補</h5>
                    
                </div>
                <div class="modal-body">
                    <h5>
                        目前人數：<span id="examinees_on_num">{{$data['examinees_on_num']}}</span>
                        <!-- 剩餘人數：<span></span> -->
                    </h5>
                    <table class="table table-rwd">
                        <tr>
                            <td class="text-left">名單</td>
                            <td class="text-right" style="width:100px">人數</td>
                            <td style="width:75px">操作</td>
                        </tr>
                        @foreach($data['pre_group'] as $group)
                        <tr id="pre_group_{{$group[0]['order_id']}}">
                            <td class="text-left">
                                @foreach($group as $ex_k => $ex_v)
                                    <a href="javascript:open_examinee_panel('examinee{{$ex_v['id']}}_VM', '{{$ex_v['id']}}', '{{$ex_v['type_id']}}')">{{$ex_v['name']}}</a>
                                    @if(count($group) > $ex_k+1)、@endif
                                @endforeach
                            </td>
                            <td class="text-right">{{count($group)}}</td>
                            <td>
                                <a class="btn btn-primary text-white" href="javascript:select_pre_examinee('{{$group[0]['type_id']}}', '{{$group[0]['order_id']}}')">錄取</a>
                            </td>
                        </tr>
                        @endforeach
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <a id="model_btn" class="visibility:hidden" data-toggle="modal" data-target="#model"></a>
    <div class="modal large_modal main-modal fade shoppingCart" id="model" tabindex="-1" role="dialog" aria-labelledby="modelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="modelTitle">修改資料</h5>
                </div>
                <div class="modal-body" id="boxModel">
                </div>
            </div>
        </div>
    </div>

    <!-- 統計圖表 -->
    <a class="d-none" id="praph_btn" data-toggle="modal" data-target="#praph"></a>
    <div class="modal large_modal fade service_unit" id="praph" tabindex="-1" role="dialog" aria-labelledby="praph" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">統計圖表</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 圖表 -->
                    {!! $data['examinee_list_graph'] !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.0/jquery-confirm.min.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>
	<script src="{{__PUBLIC__}}/js/moment.min.js"></script>
    <!-- <script src="/public/static/index/js/check_input.js"></script> -->
    <script>
        function getMultiId() {
            var multiIdArray = [];
            $('.productinfoCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });

            return multiIdArray;
        }

        function multiCancel() {
            if(confirm('確定取消?')){
                var form = document.createElement("form");
                form.action = "{{url('Examination/multiCancel')}}";
                form.method = "post";
                form.className = 'd-none';

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";
                form.appendChild(multiId);
                document.body.appendChild(form);

                $('#block_block').show();
                form.submit();
                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function newBlock(){
            if($("#file_excel").val() != ''){
               excelForm2.submit();
            }else{
                alert('請選擇檔案');
            }
        }

        /*送出搜尋表單*/
        var fields = [];
        $.ajax({
            url: '{{ url('Examination/get_fields_by_prod_id') }}',
            type: 'post',
            datatype: 'json',
            headers: {
                'X-CSRF-TOKEN': csrf_token
            },
            data: {prod_id: '{{$data['id']}}'},
            success: function(res){
                fields = res;
            }
        });
        function send_searchForm(){
            var types_need_checked = JSON.parse(`{{json_encode($data['types_need_checked'])}}`.replace(/&quot;/g, '"').trim());
            var register_data = {};
            /*組織答案列*/
            for (var i = 0; i < fields.length; i++) {
                if(types_need_checked.indexOf(fields[i]['type']) != -1){ /*選項類型*/
                    var checkexd = $('[name="search_' + fields[i]['name'] + '"]:checked');
                    var ans = [];
                    for (var x = 0; x < checkexd.length; x++) {
                        ans.push($(checkexd[x]).attr('value'));
                    }
                }
                else if(['file'].indexOf(fields[i]['type']) != -1){ /*檔案類型*/
                    var ans = {file_name: $('[name="search_' + fields[i]['name'] + '"]').val()};
                }
                else{ /*文字類型*/
                    var ans = $('[name="search_' + fields[i]['name'] + '"]').val();
                }

                register_data[fields[i]['name']] = ans;
            }
            $('#register_data').val(JSON.stringify(register_data))

            searchForm.submit();
        }
        /*送出匯出名單excel*/
        function send_excelForm(){
            $('[name="searchForm"]').attr("action", "{{url('Examination/excel') . '?id=' . $data['id']}}");
            send_searchForm();
            $('[name="searchForm"]').attr("action", "");
        }
    </script>
    <script>
        @if(empty($data['examinees']) == false)
        @foreach($data['examinees'] as $vo_e)
            examinee{{$vo_e['id']}}_VM = window['examinee{{$vo_e["id"]}}_VM_init']();
        @endforeach
        @endif
    </script>
    <script type="text/javascript">
        function show_graph(){
            clean_graph();
            $('#praph_btn')[0].click();

            $(window).on('resize', function(){
                init_graph();
            });
            function init_graph(){
                @if(!empty($data['graph_datas']))
                    @foreach($data['graph_datas'] as $graph_k => $graph)
                        create_graph(
                            target="{{$graph_k}}_{{$data['rand']}}",
                            name="{{$graph['title']}}",
                            column={!! ($graph['options'] ?? '[]') !!},
                            data=JSON.parse(`{!! json_encode($graph['dates'] ?? []) !!}`.replace(/&quot;/g, '"').trim()),
                            y_max={{count($data['examinees'])}},
                        )
                    @endforeach
                @endif
                //
            }
            setTimeout(function(){
                init_graph();
            }, 200);
        }
    </script>

    <script type="text/javascript">
        const examinees_on_num = $('#examinees_on_num');
        function select_pre_examinee(type_id, order_id){
            if(order_id){
                $.ajax({
                    type: "post",
                    dataType: "json",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    url : "{{url('examination/select_pre_examinee')}}",
                    data: {
                        type_id:type_id,
                        order_id:order_id,
                    },
                    success: function(res){
                        console.log(res);
                        if(res.code){
                            examinees_on_num.html(Number(examinees_on_num.html()) + res.msg);
                            Vue.toasted.show('錄取成功', {duration:1500, className:'bg-success'});
                        }else{
                            Vue.toasted.show(res.msg, {duration:1500, className:'bg-danger'});
                        }
                        $('#pre_group_'+order_id).remove();
                    }
                });
            }
        }
    </script>
@endsection

