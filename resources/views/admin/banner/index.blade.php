@extends('admin.Public.aside')

@section('title')E圖文編輯項目 > 次頁形象圖@endsection

@section('content')
    <iframe id="boxFormIframe" name="boxFormIframe" style="display: none;"></iframe>

    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">E圖文編輯項目</a></li>
            <li><a href="###" onclick="javascript:location.href='index'">次頁形象圖</a></li>
            @if($data['searchKey'] != '')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>
        <div class="searchbox">
            <form action="" name="searchForm" method="get"  class="searchKeyBox">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1" placeholder="搜尋">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div> 

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width:1200px;">
                <thead>
                    <tr>
                        <th style="width: 300px;">預覽圖片<span class="remark d-inline">(建議尺寸：1920*616)</span></th>
                        <th style="width: 300px;">標題</th>
                        <!-- <th>英文標題</th> -->
                        <th>標題顏色<span class="remark d-inline">(請輸入含#的六位色碼，或使用顏色選擇器)</span></th>
                        <!-- <th>子選單設定</th> -->
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['frontend_menu_name'])==true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['frontend_menu_name'] as $vo)
                        <tr id="news_{{$vo->id}}">
                            <td>
                                <div style="display:flex">
                                    <form ref="boxForm" :action="action" method="post" target="boxFormIframe" enctype="multipart/form-data">
                                        @csrf
                                        <div class="img-box text-center" style="min-width: 185px;">
                                            <span class="bi bi-image"></span>
                                            <input type='file' ref="img" class="upl" name="image" accept="image/*" @change="save_img">
                                            <img class="preview" :src="src"/>
                                        </div>
                                        <input type="hidden" name="id" v-model="id">
                                    </form>
                                    <button @click="cancel_img" class="btn sendbtn ml-2" style="height:40px">刪除圖片</button>
                                </div>
                            </td>
                            <td><input type="text" class="form-control" v-model="name" @blur="save_data('name')"></td>
                            <!-- <td><input type="text" v-model="en_name" @blur="save_data('en_name')"></td> -->
                            <td>
                                <div class="d-flex align-items-center">
                                    <input type="color" v-model="text_color" @blur="save_data('text_color')">
                                    <input type="text" v-model="text_color" class="form-control ml-2"  @blur="save_data('text_color')">
                                </div>
                                
                            </td>
                            <!-- <td><a @click="openBox">設定</a></td> -->
                        </tr>
                    @endforeach
                    @endif
                </tbody>
                
    
                
            </table>
        </div>
        <!--表格 結束-->
    </div>
@endsection

@section('ownJS')
    <!-- <script src="/public/static/admin/js/action.js"></script>
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        KindEditor.ready(function (K) {
            editor = K.create('#editor', {
                afterBlur: function () { this.sync(); },
                langType: 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright'
                ],
                width: '100%',
                height: '300px',
                resizeType: 0
            });
        });
    </script> -->
    <script>  
        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Banner/cellCtrl')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功',{ duration:1500, className: ["toasted-primary", 'bg-success']});
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }

                    location.reload();
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        $('#boxFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                // alert("上傳成功");
                // if(BoxVM.caller == 'new'){
                //     location.reload();
                // }else{
                //     BoxVM.updateCallerData();
                // }
                location.reload();
            }else{
                alert("上傳失敗");
                $('#block_block').hide();
                console.log($(this).contents().find('body').text());
            }
        });

        @if(empty($data['frontend_menu_name'])==false)
        @foreach($data['frontend_menu_name'] as $vo)
            var news_{{$vo->id}} = {
                id: "{{$vo->id}}",
                name: "{{$vo->name}}",
                en_name: "{{$vo->en_name}}",
                src: "{{$vo->pic ? __UPLOAD__.$vo->pic : ''}}",
                text_color: "{{$vo->text_color}}",
                action: "{{url('Banner/update')}}",
            }
            var news_{{$vo->id}}_VM = new Vue({
                el: '#news_{{$vo->id}}',
                data: news_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    }
                },
                methods: {
                    save_data: function(column){
                        blockData = {
                            id: this.id,
                        };
                        blockData[column] = this[column];
                        this.blockCtrl(blockData);
                    },
                    cancel_img: function(){
                        blockData = {
                            id: this.id,
                            pic: "",
                        };
                        this.src = "";
                        this.blockCtrl(blockData);
                    },
                    save_img: function(column){
                        self = this;
                        console.log(self.$refs.img.files);
                        var reader = new FileReader();
                        reader.onload = function (e) {
                            self.src = e.target.result;

                            $('#block_block').show();
                            setTimeout(function(){ self.$refs.boxForm.submit(); }, 50);
                        }
                        reader.readAsDataURL(self.$refs.img.files[0]);
                    },
                }
            });
        @endforeach
        @endif
    </script>

@endsection