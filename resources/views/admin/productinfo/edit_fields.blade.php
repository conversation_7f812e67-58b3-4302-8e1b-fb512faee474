@extends('admin.Public.aside')
@section('title')報名欄位設定 > 商品管理區@endsection
@section('css')@endsection

@section('content')
    <!-- 新增/編輯開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal main-modal fade" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">欄位內容</h5>
                    
                </div>
                <div class="modal-body">
                    <p>資料類型：
                        <select v-model="aeModel.type" @change="change_type" :disabled="aeModel.fields_set_id!=0">
                            <option v-for="key in Object.keys(data_types)" :value="key" v-text="data_types[key]"></option>
                        </select>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        資料來源：
                        <span v-if="aeModel.fields_set_id!=0">常用欄位</span>
                        <span v-if="aeModel.fields_set_id==0">自訂</span>
                    </p>
                    <div class="mb-3">
                        標題：<input type="text" v-model="aeModel.title" :disabled="aeModel.fields_set_id!=0"/>
                        &nbsp;&nbsp;
                        必填：<input type="checkbox" v-model="aeModel.required" true-value="1" false-value="0"
                                    :disabled="[1,2,3].indexOf(aeModel.fields_set_id)!=-1"/>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <div class="mt-1">
                            特殊欄位：<input type="checkbox" v-model="aeModel.special" true-value="1" false-value="0"/>
                            <span class="text-danger  remark">(勾選後，若使用者有填寫此欄位之內容，將通知管理者)</span>
                        </div>
                    </div>
                    <p>
                        排序：<input type="number" v-model="aeModel.order_id"/>
                        &nbsp;&nbsp;
                        <!-- 狀態：
                        <select v-model="aeModel.online" :class="[aeModel.online== 0 ? 'color_red' : 'color_green']">
                            <option class="color_green" value="1">啟用</option>
                            <option class="color_red" value="0">停用</option>
                        </select> -->
                    </p>
                    <div v-if="types_need_option.indexOf(aeModel.type) != -1">
                        <table style="width: 100%">
                            <tr>
                                <td>選項內容</td>
                                <td>操作
                                    <span class="bi bi-plus-lg" @click="add_option()" style="float:right" v-if="aeModel.fields_set_id==0"></span>
                                </td>
                            </tr>
                            <tr v-for="(item, index) in aeModel.options">
                                <td>
                                    <input type="text" v-model="aeModel.options[index]" :disabled="aeModel.fields_set_id!=0">
                                </td>
                                <td>
                                    <span @click="del_option(index)" class="bi bi-trash" v-if="aeModel.fields_set_id==0"></span>
                                </td>
                            </tr>
                        </table>
                        <br>
                    </div>
                    <div v-if="types_need_limit.indexOf(aeModel.type) != -1" class="format">
                        限定格式：<input type="text" v-model="aeModel.limit"/ style="width: 100%;" :disabled="aeModel.fields_set_id!=0"><br>
                        <ul class="p-1">
                            <li>文字類型資料請輸入「正規表達式」，可參考<a href="https://ihateregex.io" target="_blank">此網站</a>，用於檢查輸入內容是否符合格式</li>
                            <li>檔案資料則請參考<a href="https://blog.gtwang.org/web-development/html-input-accept-attribute-tutorial/" target="_blank">此網頁的「指定副檔名」格式</a>來輸入，用於設定允許上傳的檔案類型</li>
                        </ul>
                    </div>
                    <p>
                        欄位說明：
                        <select @change="set_field_discription" v-model="select_discription" :disabled="aeModel.fields_set_id!=0">
                            <option value="-1">請選擇</option>
                            <option v-for="(comment, index) in comments_set" :value="index" v-text="comment.title"></option>
                        </select>
                        <br>
                        <textarea id="editor" :disabled="aeModel.fields_set_id!=0"></textarea>
                        <input type="hidden" v-model="aeModel.discription"/>
                    </p>
                </div>
                <div class="modal-footer flex-wrap justify-content-center">
                    <button type="button" class="btn sendbtn" 
                            v-if="aeModel.id!=1 && aeModel.id!=2"
                            @click="ajaxSubmit"
                    >儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增/編輯結束 -->


    <div id="content" >
    
        <ul id="title" class="brand-menu" >
            <li><a href="###" onclick="javascript:location.href='/admin/all/index'">F商品管理區</a></li>
            <li><a href="###" onclick="javascript:location.href='/admin/productinfo/edit?id={{$data['productinfo']['id']}}'">{{$data['productinfo']['title']}}</a></li>
            <li><a href="###">報名欄位設定</a></li>
        </ul>

        <!--新增與編輯-->
        <div class="edit_fields mb-2" >
            <div>
                <div class="btn sendbtn mr-1" @click="newBlock" style="width: fit-content;" data-toggle="modal" data-target="#functionModal">添加欄位</div>
                <div class="btn sendbtn" data-toggle="modal" data-target="#time_tool" style="width: fit-content;">建立時間</div>
            </div>
            <ul class="text-danger remark">
                <li>點擊「套用常用欄位」的選項可以套用欄位，或點擊「添加欄位」自行設定</li>
                <li>「常用欄位」只可控制是否必填、是否為特殊欄位、排序、是否使用(不使用請刪除)，其餘設定不可修改</li>
                <li>姓名、手機為特殊常用欄位，必定使用，且必定必填</li>
                <li>所有時間選欄位會被統一組織成時間表格(不論問題排序)，顯示於最後使用者勾選</li>
            </ul>
        </div>

        <div class="fields_info">
            <!-- 常用欄位 -->
            <div class="casual_use">
                <h4 class="main-title p-2">套用常用欄位</h4>
                <table class="table table-rwd">
                    <thead>
                        <tr>
                            <th>欄位名稱</th>
                            <th>資料類型</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(field, index) in fields_set" @click="add_to_use_fields(index)" class="cursor-pointer">
                            <td v-text="field.title"></td>
                            <td v-text="data_types[field.type]"></td>
                        </tr>
                    </tbody>
                    
                </table>
            </div>

            <!--表格 開始-->
            <div style="width:100%">
                <h4 class="main-title p-2">使用中欄位</h4>
                <table class="table table-rwd">
                    <thead>
                        <tr>
                            <th style="width:5%">
                                <!-- <input type="checkbox" style="cursor:pointer;" v-model="select_all" @click="selectAll()"> -->
                                編號
                            </th>
                            <!-- <th style="width:5%">狀態</th> -->
                            <th style="width:15%">標題</th>
                            <th style="width:10%">資料類型</th>
                            <th style="width:10%">資料來源</th>
                            <th style="width:40%">說明</th>
                            <th style="width:10%">排序</th>
                            <th style="width:10%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(vo, index) in datas">
                            <td th-data="編號">
                                <!-- <input type="checkbox" v-model="vo.select" v-if="vo.fields_set_id!=1 && vo.fields_set_id!=2"> -->
                                <span v-text="index + 1"></span>
                            </td>
                            <!-- <td th-data="狀態">
                                <select v-if="vo.fields_set_id!=1 && vo.fields_set_id!=2"
                                        v-model="vo.online" :class="[vo.online== 0 ? 'color_red' : 'color_green']" @change="save_one(index)">
                                    <option class="color_green" value="1">啟用</option>
                                    <option class="color_red" value="0">停用</option>
                                </select>
    
                                <span v-if="vo.fields_set_id==1 || vo.fields_set_id==2" class="color_green">啟用</span>
                            </td> -->
                            <td th-data="標題">
                                <a href="###" @click="openBox(index)" v-text="vo.title"></a>
                                <span style="color:red;" v-if="vo.required==1">*</span>
                                <span v-if="vo.special!=0">(特殊)</span>
                            </td>
                            <td th-data="資料類型" v-html="data_types[vo.type]"></td>
                            <td th-data="資料來源">
                                <span v-if="vo.fields_set_id!=0">常用欄位</span>
                                <span v-if="vo.fields_set_id==0">自訂</span>
                            </td>
                            <td th-data="說明" v-html="vo.discription"></td>
                            <td th-data="排序"><input value="number" v-model="vo.order_id" @change="save_one(index)"></td>
                            <td th-data="操作">
                                <button v-if="[1,2,3].indexOf(vo.fields_set_id)==-1"
                                        type="button" @click="del(vo.id)"  class="btn-sm"
                                        style=" background:#03a6ff; color:#fff; border:none; padding:3px 5px ; cursor:pointer; font-size:16px">刪除</button>
                            </td>              
                        </tr>
                    </tbody>
                    
                </table>
            </div>
        </div>

    </div>

    <!-- 建立時間 -->
    <div class="modal main-modal fade" id="time_tool" tabindex="-1" role="dialog" aria-labelledby="time_tool" aria-modal="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="time_toolTitle">建立時間</h5>
                    
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <label class="text-danger remark"><em>點擊「確認新增」後視窗不會關閉，可修改部分內容再次新增時間選</em></label>
                            
                            <div class="row">
                                <div class="form-group col-md-12 mb-3">
                                    <label class="form-label">選擇日期：</label>
                                    <span class="d-inline-flex align-items-center">
                                        <input class="form-control" type="date" v-model="time_tool.date_s">&nbsp;~&nbsp;
                                        <input class="form-control" type="date" v-model="time_tool.date_e">
                                    </span>
                                </div>
                                <div class="col-md-12 mb-3">
                                    選擇星期：<br>
                                    <span class="form-check d-inline pl-4 pr-2" v-for="option in time_tool.weekday_option">
                                        <input class="form-check-input" type="checkbox" name="weekday"
                                               :id="'wd_'+option.val" :value="option.val" v-model="time_tool.weekday">
                                        <label :for="'wd_'+option.val" class="form-check-label cursor-pointer" v-text="option.name"></label>
                                    </span>
                                    <br>
                                    <label class="text-danger"><em>全不選視為全選</em></label>
                                </div>
                                <div class="form-group col-md-12 mb-3">
                                    <label class="form-label">起訖時間：</label>
                                    <span class="d-inline-flex align-items-center">
                                        <select class="form-control" v-model="time_tool.hour_s">
                                            <option v-for="option in time_tool.hour_option" :value="option" v-text="option"></option>
                                        </select>：
                                        <select class="form-control" v-model="time_tool.minute_s">
                                            <option v-for="option in time_tool.minute_option" :value="option" v-text="option"></option>
                                        </select>
                                    </span>
                                    &nbsp;~&nbsp;
                                    <span class="d-inline-flex align-items-center">
                                        <select class="form-control" v-model="time_tool.hour_e">
                                            <option v-for="option in time_tool.hour_option" :value="option" v-text="option"></option>
                                        </select>：
                                        <select class="form-control" v-model="time_tool.minute_e">
                                            <option v-for="option in time_tool.minute_option" :value="option" v-text="option"></option>
                                        </select>
                                    </span>
                                </div>
                                <div class="col-md-12">
                                    持續時間：
                                    <input class="form-control d-inline-block text-right mr-2" type="number" min="0" step="1" v-model="time_tool.duration" style="width: 200px;">分鐘(系統會以30分鐘為單位計算)
                                </div>
                             </div>
                        </div>

                        <div class="col-12 text-center mt-3">
                            <button class="btn clearbtn" @click="clean_time_tool">清空設定</button>
                            <button class="btn sendbtn" @click="add_time_option">確認新增</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="/public/static/admin/js/action.js"></script>

    <script charset="utf-8" src="/public/static/admin/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/lang/zh_TW.js"></script>
    <script type="text/javascript">
        /*初始化編輯器*/
        var editor = KindEditor.ready(function(K) {
            editor = K.create('#editor', {
                langType : 'zh_TW',
                items:['source', '|', 'hr','|','emoticons','|','forecolor','bold', 'italic', 'underline','link', 'unlink',],
                width:'100%',
                height:'200px',
                resizeType:0
            });
        });
    </script>

    <script>
        function paddingLeft(str, length){
            if(str.length >= length){
                return str;
            }else{
                return paddingLeft("0" +str, length);
            }
        }
        var c_date = new Date();
        c_date_format = paddingLeft(c_date.getFullYear().toString(), 4) + "-" +
                        paddingLeft((c_date.getMonth()+1).toString(), 2) + "-" +
                        paddingLeft(c_date.getDate().toString(), 2)
        var clean_time_tool_data = {
            // date_s:"2022-05-13",
            // date_e:"2022-05-27",
            // weekday:[2, 4],
            // hour_s:"14",
            // minute_s:"00",
            // hour_e:"17",
            // minute_e:"00",
            // duration:"100",
            date_s: c_date_format,
            date_e: c_date_format,
            weekday:[],
            hour_s:"09",
            minute_s:"00",
            hour_e:"09",
            minute_e:"00",
            duration:"",
            weekday_option: [
                {val:1, name:"一"},
                {val:2, name:"二"},
                {val:3, name:"三"},
                {val:4, name:"四"},
                {val:5, name:"五"},
                {val:6, name:"六"},
                {val:0, name:"日"},
            ],
            hour_option:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"],
            minute_option:["00","30"],
        };
        var aeModel_empty = { 
            id: 0, fields_set_id:0, prod_id:'{{$data['productinfo']['id']}}', title: "", type: "text", 
            required: 0, special: 0, limit: "", discription: "", 
            options:[], order_id: 0, online: 1,
        }
        // console.log(datas);
        var types_need_option_data= {!! json_encode($data['types_need_option'], JSON_UNESCAPED_UNICODE) !!};
        var types_need_limit_data = {!! json_encode($data['types_need_limit'], JSON_UNESCAPED_UNICODE) !!};
        var content_area_data = {
            types_need_option: types_need_option_data,
            types_need_limit: types_need_limit_data,

            prod_id: '{{$data['productinfo']['id']}}',
            select_all: false,
            datas: [],
            aeModel: JSON.parse(JSON.stringify(aeModel_empty)),
            data_types:{
                text:"單行文字",
                textarea: "多行文字",
                radio: "單選題",
                // radio_box: "單選題_開視窗",
                checkbox: "多選題",
                checkbox_time: "時間選",
                // checkbox_box: "多選題_開視窗",
                select: "下拉選單",
                number: "數字題",
                file: "檔案上傳",
                date: "日期",
            },
            select_discription: -1,
            comments_set: [],

            fields_set: [],

            time_tool: JSON.parse(JSON.stringify(clean_time_tool_data)),
        };
        var content_areaVM = new Vue({
            el: "#content_area",
            data: content_area_data,
            methods: {
                /*切換資料類型*/
                change_type: function(){
                    if(this.types_need_limit.indexOf(this.aeModel.type) == -1){ /*切換至不須限定格式的類型*/
                        this.aeModel.limit = "";
                    }
                },
                /*開啟新增畫面*/
                newBlock: function (){
                    self = this;
                    console.info(aeModel_empty);
                    this.aeModel = JSON.parse(JSON.stringify(aeModel_empty));
                    console.info(this.aeModel);
                    this.aeModel.order_id = Number(self.datas[self.datas.length-1].order_id) + 1;
                    editor.html("");
                    editor.readonly(false);
                    console.info($('#functionModal_btn'));
                    $('#functionModal_btn').click();
                },
                /*開啟編輯畫面*/
                openBox: function(index){
                    self = this;
                    self.aeModel = Object.assign({}, self.datas[index]);
                    // console.log(self.aeModel)
                    editor.html(self.aeModel.discription);
                    if(self.aeModel.fields_set_id!=0){
                        editor.readonly(true);
                    }
                    $('#functionModal_btn').click();
                },
                /*新增、編輯欄位*/
                ajaxSubmit: function(){
                    self = this;
                    self.aeModel.discription = editor.html();
                    self.ajax_save_data(self.aeModel);
                },
                /*儲存單個欄位*/
                save_one: function(index){
                    self = this;
                    self.ajax_save_data(self.datas[index]);
                },
                /*送出儲存資料請求*/
                ajax_save_data: function(post_data, reload=true){
                    clear_option = [];
                    for (var i = 0; i < post_data.options.length; i++) {
                        if( $.trim(post_data.options[i])!="" ){
                            clear_option.push(post_data.options[i]);
                        }
                    }
                    post_data.options = clear_option;

                    return $.ajax({
                        type:'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        data: post_data,
                        url:"{{url('Productinfo/fields_set_save')}}",
                        success:function(res){
                            console.info(res.code);
                            let bg_class = res.code=='1' ? "bg-success" : "bg-danger";
                            if(res.code=='1'){
                                Vue.toasted.show(res.msg, {duration:1500, className:bg_class});
                                location.reload();
                                if(reload){
                                    
                                    // self.aeModel = JSON.parse(JSON.stringify(aeModel_empty));
                                    // self.get_data();
                                }
                            }else{
                                Vue.toasted.show(res.msg, {duration:1500, className:bg_class});
                            }
                        }
                    });
                },

                /*刪除欄位*/
                del: function(id){
                    $.ajax({
                        type:'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        data: {id: id},
                        url:"{{url('Productinfo/fields_set_delete')}}",
                        success:function(res){
                            let bg_class = res.code=='1' ? "bg-success" : "bg-danger";
                            Vue.toasted.show(res.msg, {duration:1500, className:bg_class});
                            if(res.code=='1'){
                                self.get_data();
                            }
                        }
                    });
                },
                
                /*列表操作*/
                    selectAll: function(){
                        self = this;
                        var select_type = self.select_all ? false : true;
                        for (var i = 0; i < self.datas.length; i++) {
                            if( [1,2,3].indexOf(self.datas[i]['fields_set_id']) == -1 ){
                                self.datas[i]['select'] = select_type;
                            }
                        }
                    },
                    multiOnline: function(){
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.datas[i]['online'] = 1;
                                self.ajax_save_data(self.datas[i], false);
                            }
                        }
                        Vue.toasted.show("修改成功", {duration:1500, className:"bg-success"});
                        setTimeout(function(){self.get_data();}, 300);
                    },
                    multiOffline: function(){
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.datas[i]['online'] = 0;
                                self.ajax_save_data(self.datas[i], false);
                            }
                        }
                        setTimeout(function(){self.get_data();}, 300);
                    },
                    multiDelete: function(){
                        if(!confirm('確認批次刪除？')){return}
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.del(self.datas[i]['id']);
                            }
                        }
                        setTimeout(function(){self.get_data();}, 300);
                    },

                /*添加選項*/
                add_option: function(){
                    self = this;
                    self.aeModel.options.push("");
                },
                /*刪除選項*/
                del_option: function(index){
                    self = this;
                    self.aeModel.options.splice(index, 1);
                },
                /*套用常用註記詞*/
                set_field_discription: function(){
                    self = this;
                    if(self.select_discription==-1){ return; }
                    
                    html = self.comments_set[self.select_discription].content;
                    self.aeModel.discription = html;
                    editor.html(html);
                    self.select_discription = -1;
                },

                /*添加到使用中欄位*/
                add_to_use_fields: function(index){
                    self = this;
                    add_field = Object.assign({}, self.fields_set[index]);
                    self.aeModel = Object.assign({}, add_field)
                    self.aeModel.id = 0;
                    self.aeModel.fields_set_id = add_field.id;
                    self.aeModel.prod_id = self.prod_id;
                    self.aeModel.order_id = Number(self.datas[self.datas.length-1].order_id) + 1;
                    self.ajax_save_data(self.aeModel);
                },

                get_data: async function(){
                    self = this;
                    $.ajax({
                        type:'GET',
                        dataType: "json",
                        url: "{{url('Productinfo/get_prod_fields')}}?id={{$data['productinfo']['id']}}",
                        success:function(res){
                            self.datas = res.datas;
                            self.fields_set = res.fields_set;
                            self.comments_set = res.comments_set;
                        }
                    });
                },
                clean_time_tool: function(){
                    self = this;
                    self.time_tool = JSON.parse(JSON.stringify(clean_time_tool_data));
                    // self.$forceUpdate();
                },
                add_time_option: function(){
                    $('#block_block').show();
                    one_day = 86400000;
                    self = this;
                    // console.log(self.time_tool);

                    // 計算差異天數
                    _date_time_s = new Date(self.time_tool.date_s + " 00:00:00");
                    _date_time_s = Date.parse(_date_time_s);
                    // console.log(_date_time_s);
                    _date_time_e = new Date(self.time_tool.date_e + " 00:00:00");
                    _date_time_e = Date.parse(_date_time_e) + one_day;
                    // console.log(_date_time_e);
                    if( _date_time_s >= _date_time_e){
                        Vue.toasted.show("結束日期需大於等於開始日期", {duration:1500, className:"bg-warning"});
                        $('#block_block').hide(); return;
                    }
                    diff_day = (_date_time_e - _date_time_s) / one_day;
                    // console.log(diff_day);
                    
                    // 計算差異時間
                    from_min = Number(self.time_tool.hour_s) * 60 + Number(self.time_tool.minute_s);
                    // console.log(from_min);
                    end_min = Number(self.time_tool.hour_e) * 60 + Number(self.time_tool.minute_e);
                    // console.log(end_min);
                    if( from_min >= end_min){
                        Vue.toasted.show("結束時間需大於等於開始時間", {duration:1500, className:"bg-warning"});
                        $('#block_block').hide(); return;
                    }
                    diff_min = end_min - from_min;
                    // console.log(diff_min);

                    // 計算會議區間
                    _duration = Math.round(self.time_tool.duration / 30) * 30;
                    if(_duration<=0){
                        Vue.toasted.show("會議時間設定有誤", {duration:1500, className:"bg-warning"});
                        $('#block_block').hide(); return;
                    }
                    self.time_tool.duration = _duration;
                    // console.log(_duration);


                    // 計算每日選項
                    _options = [];
                    _time_loop = Math.floor(diff_min / _duration);
                    if(_time_loop<=0){
                        Vue.toasted.show("無時間選項需建立，請調整「起訖時間」與「會議時間」", {duration:1500, className:"bg-warning"});
                        $('#block_block').hide(); return;
                    }
                    // console.log(_time_loop);
                    for (var i = 0; i < _time_loop; i++) {
                        // HH:mm~HH:mm
                        option_time_s = _date_time_s + ( from_min + i*_duration ) * 60 * 1000;
                        option_time_s_date = new Date(option_time_s);
                        option_time_e = _date_time_s + ( from_min + (i+1)*_duration ) * 60 * 1000;
                        option_time_e_date = new Date(option_time_e);
                        _options.push(
                            paddingLeft(option_time_s_date.getHours().toString(), 2) + ":" + paddingLeft(option_time_s_date.getMinutes().toString(), 2) 
                            + "~" +
                            paddingLeft(option_time_e_date.getHours().toString(), 2) + ":" + paddingLeft(option_time_e_date.getMinutes().toString(), 2)
                        );
                    }
                    console.log(_options);

                    // 計算新增問題
                    var _questions = [];
                    for (var i = 0; i < diff_day; i++) {
                        // YYYY/MM/DD
                        _question_date = _date_time_s + i * one_day;
                        _question_date_date = new Date(_question_date);
                        _weekday = _question_date_date.getDay();
                        if(self.time_tool.weekday.indexOf(_weekday)!=-1 || self.time_tool.weekday.length==0){
                            _questions.push(
                                paddingLeft(_question_date_date.getFullYear().toString(), 4) + "/" +
                                paddingLeft((_question_date_date.getMonth()+1).toString(), 2) + "/" +
                                paddingLeft(_question_date_date.getDate().toString(), 2)
                            );
                        }
                    }
                    if(_questions.length==0){
                        Vue.toasted.show("無時間選項需建立，請調整「選擇日期」與「選擇星期」", {duration:1500, className:"bg-warning"});
                        $('#block_block').hide(); return;
                    }
                    console.log(_questions);

                    // 新增&編輯問題
                    var _res_count = 0;
                    for (var i = 0; i < _questions.length; i++) {
                        var _target_index = -1;
                        for (var x = 0; x < self.datas.length; x++) {
                            if(self.datas[x].title == _questions[i]){
                                _target_index = x;
                                break;
                            }
                        }
                        if(_target_index==-1){ /*新增欄位*/
                            _new_order_id = self.datas.length!=0 ? Number(self.datas[self.datas.length-1].order_id) + 1 : 0;
                            _new_data = { 
                                id: 0, 
                                fields_set_id: 0,
                                prod_id: self.prod_id,
                                title: _questions[i], 
                                type: "checkbox_time", 
                                required: 0, 
                                special: 1, 
                                limit: "", 
                                discription: "", 
                                options: _options, 
                                options_cate:[], 
                                order_id: _new_order_id, 
                                online: 1,
                            }
                            self.aeModel = _new_data;
                            self.datas.push(_new_data);
                        }else{ /*編輯欄位*/
                            self.aeModel = Object.assign({}, self.datas[x]);
                            _renew_options = self.aeModel.options.slice();
                            for (var o = 0; o < _options.length; o++) {
                                if(self.aeModel.options.indexOf(_options[o])==-1){
                                    _renew_options.push(_options[o]);
                                }
                            }
                            self.aeModel.options = _renew_options.sort();
                            self.datas[x] = Object.assign({}, self.aeModel);
                        }
                        console.log(self.aeModel);
                        self.ajax_save_data(self.aeModel, false).then(function(){
                            _res_count++;
                        });
                    }

                    var _timer = setInterval(async function(){
                        if(_res_count==_questions.length){
                            clearInterval(_timer);
                            Vue.toasted.show("資料已儲存", {duration:1500, className:"bg-success"});
                            // self.get_data();
                            await self.get_data();
                            $('#block_block').hide();
                        }
                    }, 1000);
                },
            },
        });
        content_areaVM.get_data();
    </script>
@endsection