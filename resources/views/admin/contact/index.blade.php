@extends('admin.Public.aside')
@section('title')D關於我們 > 客戶來函@endsection

@section('content')

    <!-- 新增分類開始 -->
    <a id="cateModal_btn" data-toggle="modal" data-target="#cateModal" class="d-none">修改分類</a>
    <div class="modal main-modal fade" id="cateModal" tabindex="-1" role="dialog" aria-labelledby="cateModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">修改分類</h5>
                  
                </div>
                <div class="modal-body">
                    <textarea id="contact_typeTextarea" class="form-control" rows="8" autofocus>{{$data['contact_type']}}</textarea>
                    <p class="remark">注意：類別間用英文逗號區隔(,)</p>
                </div>
                
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn sendbtn w-110" onclick="contact_type()">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增分類結束 -->

    <!-- 修改回函表開始 -->
    <a id="contactModal_btn" data-toggle="modal" data-target="#contactModal" class="d-none">回函內容</a>
    <div class="modal fade main-modal" id="contactModal" tabindex="-1" role="dialog" aria-labelledby="contactModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="replayBoxView">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">回函內容</h5>
                    
                </div>
                <div class="modal-body">
                    <div style="overflow-y: hidden;">
                        <table class="table border table-rwd">
                            <thead>
                                <!-- 第一列 -->
                                <tr>
                                    <th v-text="time"></th>
                                    <th v-text="type"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 第二列 -->
                                <tr>
                                    <td v-text="name"></td>
                                    <td v-text="'信箱：'+email"></td>
                                </tr>
                                <!-- 第三列 -->
                                <tr>
                                    <td v-text="'手機：'+phone"></td>
                                    <td v-text="'聯絡電話：'+homephone"></td>
                                </tr>
                                <!-- 第四列 -->
                                <tr>
                                    <td colspan="2" v-text="'聯絡時間：'+freeTime"></td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <span v-if="order_number" v-text="'訂單編號：'+order_number"></span>
                                        <span v-if="prod_id" v-text="'商品ID或名稱：'+prod_id"></span>
                                    </td>
                                </tr>
                                <!-- 第五列 -->
                                <tr>
                                    <td colspan="2">
                                        <textarea readonly v-model="content" class="form-control" rows="4"></textarea>
                                    </td>
                                </tr>
                                <!-- 第六列 -->
                                <tr>
                                    <td colspan="2">
                                        客服處理歷程：<br>
                                        <table class="w-100">
                                            <tr v-for="record in records">
                                                <td v-text="record.datetime_format"></td>
                                                <td v-html="record.message"></td>
                                                <td v-text="record.admin_name"></td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr v-if="!status">
                                    <td colspan="2">留言：<br>
                                        <textarea v-model="remessage_input" class="form-control" rows="4"></textarea>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer" v-if="!status">
                    <button type="button" class="btn clearbtn w-110" @click="send_record">紀錄歷程</button>
                    <button type="button" class="btn sendbtn w-110" @click="send_complete">處理完畢</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 修改回函表結束 -->

    <div id="content">
        <ul id="title" class="brand-menu" onclick="javascript:location.href='index'">
            <li><a href="###">D關於我們</a></li>
            <li><a href="###">客戶來函</a></li>
        </ul>
        <div class="searchbox">
            <form action="{{url('Contact/index')}}" name="searchForm" method="get" class="searchKeyBox flex-nowrap align-items-center">
                @csrf
                <input type="text" class="form-control mr-3" name="searchKey" value="{{$_GET['searchKey']??''}}" style="width:275px" placeholder="搜尋分類/姓名/電話/手機/信箱/訂單">
                建立時間：
                <input type="date" class="date form-control mr-1" name="start" value="{{$_GET['start']??''}}" style="width:130px;"/>
                <input type="date" class="date form-control mr-1" name="end" value="{{$_GET['end']??''}}" style="width:130px;"/>

                <select class="form-control mr-1" name="status">
                    <option value="">處理狀態</option>
                    <option value="0" {{App\Services\CommonService::compare_return($_GET['status']??'', '0', 'selected')}}>待處理</option>
                    <option value="1" {{App\Services\CommonService::compare_return($_GET['status']??'', '1', 'selected')}}>處理完畢</option>
                </select>
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <span class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a onclick="multiDelete();">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
            </span>
            <!-- 編輯結束 -->
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width: 1400px;">
                <thead>
                    <tr>
                        <th style="width: 20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=contact_logCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 12.5%;">日期</th>
                        <th>
                            <p class="cursor-pointer mb-0 d-flex align-items-center" onclick="$('#cateModal_btn').click();">
                                分類 
                                <span class="addbtn" onclick="$('#cateModal_btn').click();">
                                    <i class="bi bi-plus-lg add" onclick="$('#cateModal_btn').click();"></i>
                                </span>
                            </p>
                        </th>
                        <th style="width: 150px;">姓名</th>
                        <th style="width: 80px">狀態</th>
                        <th>聯絡信箱</th>
                        <th>手機</th>
                        <th style="width: 60px">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['contact_log']) == true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['contact_log'] as $vo)
                        <tr id="contact_log_{{$vo->id}}">
                            <td><input type="checkbox" class="contact_logCheckbox" alt="{{$vo->id}}"></td>
                            <td>{{$vo->time}}</td>
                            <td><a @click="openBox" href="###">{{$vo->type}}
                                    <span v-if="order_number">：{{$vo->order_number}}</span>
                                    <span v-if="prod_id">：{{$vo->prod_id}}</span>
                                </a>
                            </td>
                            <td><a @click="openBox" href="###">{{$vo->name}}</a></td>
                            <td><a @click="openBox" href="###">{{$vo->status ? '處理完畢' : '待處理'}}</a></td>
                            <td><a @click="openBox" href="###">{{$vo->email}}</a></td>
                            <td><a @click="openBox" href="###">{{$vo->phone}}</a></td>
                            <td><span class="bi bi-trash" onclick="location.href = '{{url('Contact/delete')}}?id={{$vo->id}}'"></span></td>
                        </tr>
                    @endforeach
                    @endif
                </tbody>
                
                
            </table>
        </div>
        
        <!--表格 結束-->
        <div class="text-center">
            {{$data['contact_log']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <!-- Include Required Prerequisites -->
    <script type="text/javascript" src="{{__PUBLIC__}}/js/moment.min.js"></script>
    <!-- Include Date Range Picker -->
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>
    $(function() {
        $(document).click(function() {
            $('.edit-item').fadeOut();
        })
        $('.edit').click(function(event) {
            event.stopPropagation();
        })
    });
    </script>
    <script>
        function contact_type() {
            $.ajax({
                url: "{{url('Contact/type')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                data:{
                    id: 1,
                    contact_type: $('#contact_typeTextarea').val()
                },
                datatype: 'json',
                error: function(xhr) {
                    alert('Ajax request 發生錯誤');
                },
                success: function(response) {
                    console.log(response);
                    if(response.status){
                        Vue.toasted.show(response.message,{duration:1500, className: ["toasted-primary", "bg-success"]});
                        $("#cateModal").modal('hide');
                    }else{
                        // console.error(response);
                        if(response.message){
                            Vue.toasted.show(response.message,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    }
                }
            });
        }
        function contact_status(Data) {
            $.ajax({
                url: "{{url('Contact/status')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                data: Data,
                datatype: 'json',
                error: function(xhr) {
                },
                success: function(response) {
                    if(response.code){
                        replayBoxVM.status = Data.status;
                        replayBoxVM.remessage_input = '';
                        replayBoxVM.updateCallerData();
                        Vue.toasted.show(response.msg,{duration:1500, className: ["toasted-primary", "bg-success"]});
                        $('#contactModal_btn').click();
                    }else{
                        // console.error(response);
                        if(response.msg){
                            Vue.toasted.show(response.msg,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    }
                }
            });
        }

        var replayBoxModel = {
            id: 0, name: '', phone: '', homephone: '', email: '',
            type: '', freeTime: '', content: '',
            status: 0, remessage: '', remessage_input:'', time: '', order_number:'', prod_id:'', records:[],
        }

        var replayBoxVM = new Vue({
            el: '#replayBoxView',
            data: replayBoxModel,
            methods: {
                send_record: function(){
                    this.ajaxSubmit(0);
                },
                send_complete: function(){
                    this.ajaxSubmit(1);
                },
                ajaxSubmit: function (status) {
                    var Data = {
                        id: this.id,
                        status: status,
                        remessage: this.remessage_input.replace(/\n/g, '<br>')
                    }
                    contact_status(Data);
                },
                updateCallerData: function () {
                    this.caller.status = this.status;
                    this.caller.remessage = this.remessage;
                }
            }
        });

        ///////andy/////多行文字串//////
        function heredoc(fn) {
            return fn.toString().replace(/[\\]/g,"") + '\n'
        }
        /////////////////////////////////////////////

        @if(empty($data['contact_log']) == false)
        @foreach($data['contact_log'] as $vo)
            var tmpl = '';
            tmpl = heredoc(function(){
                `<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $vo->content); ?>`
            });
            tmpl = tmpl.split('`');
            delete tmpl[0];
            var lastnum = tmpl.length -1;
            delete tmpl[lastnum];

            var contact_log_{{$vo->id}} = {
                id: "{{$vo->id}}",
                name: "{{$vo->name}}",
                phone: "{{$vo->phone}}",
                homephone: "{{$vo->homephone}}",
                email: "{{$vo->email}}",
                type: "{{$vo->type}}",
                status: Number("{{$vo->status}}"),
                freeTime: "{{$vo->freeTime}}",
                content: tmpl.join(''),
                remessage: "<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $vo->remessage); ?>",
                remessage_input: '',
                time: "{{$vo->time}}",
                order_number: "{{$vo->order_number}}",
                prod_id: "{{$vo->prod_id}}",
                records: [],
            }
            var contact_log_{{$vo->id}}_VM = new Vue({
                el: '#contact_log_{{$vo->id}}',
                data: contact_log_{{$vo->id}},
                methods: {
                    openBox: async function () {
                        replayBoxVM.id = this.id;
                        replayBoxVM.name = this.name;
                        replayBoxVM.phone = this.phone;
                        replayBoxVM.homephone = this.homephone;
                        replayBoxVM.email = this.email;
                        replayBoxVM.type = this.type;
                        replayBoxVM.status = this.status;
                        replayBoxVM.freeTime = this.freeTime;
                        replayBoxVM.content = this.content;
                        replayBoxVM.remessage = this.remessage;
                        replayBoxVM.time = this.time;
                        replayBoxVM.order_number = this.order_number;
                        replayBoxVM.prod_id = this.prod_id;
                        replayBoxVM.caller = this;
                        replayBoxVM.records = await this.get_contact_log_record(replayBoxVM.id);
                        $('#contactModal_btn').click();
                    },
                    get_contact_log_record: function (contact_log_id) {
                        return $.ajax({
                            url: "{{url('Contact/contact_log_record')}}",
                            type: 'POST',
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            data: {
                                contact_log_id: contact_log_id,
                            },
                            datatype: 'json',
                            error: function(xhr) {
                                return [];
                            },
                            success: function(response) {
                                return response.records;
                            }
                        });
                    },
                }
            });
        @endforeach
        @endif

        function multiDelete() {
            var form = document.createElement("form");
            form.action = "{{url('Contact/multiDelete')}}";
            form.method = "post";
            form.className = 'd-none';

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            multiId.name = "id";
            form.appendChild(multiId);
            document.body.appendChild(form);

            $('#block_block').show();
            form.submit();
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.contact_logCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }
    </script>
@endsection
