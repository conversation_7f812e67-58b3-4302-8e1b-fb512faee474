@extends('admin.Public.aside')
@section('title')參數設定 > 常用註記詞管理@endsection
@section('css')
@endsection

@section('content')
    <div id="block" onclick="CloseAdd('.aeModel')"></div>

    <!-- 新增/編輯開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal main-modal fade" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">註記詞內容</h5>
                </div>
                <div class="modal-body">
                    <p>
                        標題：<input type="text" v-model="aeModel.title"/>
                    </p>
                    <p>
                        排序：<input type="number" v-model="aeModel.order_id"/>
                    </p>
                    <p>
                        狀態：
                        <select v-model="aeModel.online" :class="[aeModel.online== 0 ? 'color_red' : 'color_green']">
                            <option class="color_green" value="1">啟用</option>
                            <option class="color_red" value="0">停用</option>
                        </select>
                    </p>
                    <p>
                        內容：<br>
                        <textarea id="editor"></textarea>
                        <input type="hidden" v-model="aeModel.content"/>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增/編輯結束 -->

    <div id="content">
      
        <ul id="title" class="brand-menu" >
            <li onclick="javascript:location.href='index'"><a href="###">K其它功能</a></li>
            <li><a href="###">常用註記詞管理</a></li>
        </ul>

        <div class="searchbox">
            <form action="" name="searchForm" method="get"  class="searchKeyBox">
                @csrf
                <select name="searchOnline" v-model="search.searchOnline" class="form-control mr-1 mb-1">
                    <option value="-1">全部</option>
                    <option value="1">啟用</option>
                    <option value="0">停用</option>
                </select>
                <input class="form-control mr-1 mb-1 text-center"  name="searchKey" type="text" v-model="search.searchKey"
                      placeholder="請輸入標題/內容">

                <a class="sendbtn btn mr-1 mb-1" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn mb-1" onclick="location.href='{{url('fields/comments_set')}}'">清除搜尋</a>
            </form>
        </div>


        <!--新增與編輯-->
        <div class="frame  d-flex align-items-center">
            <a href="###" class="btn clearbtn mr-1" @click="newBlock"><i class="bi bi-plus-lg add small"></i>  新增</a>
            <span class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                <div class="edit-item none">
                    <a @click="multiOnline()">
                        <p class="mb-0">開啟&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled="" checked=""><span class="slider round"></span>
                        </label>
                    </a>
                    <a @click="multiOffline()">
                        <p class="mb-0">關閉&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled=""><span class="slider round"></span>
                        </label>
                    </a>
                    <a @click="multiDelete()" class="border-top">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
            </span>
            
        </div>

        <!--表格 開始-->
            <table class="table table-rwd table-mobile">
                <thead>
                    <tr>
                        <th style="width:80px">
                            <input type="checkbox" style="cursor:pointer;" v-model="select_all" @click="selectAll()">
                            編號
                        </th>
                        <th style="width:80px">狀態</th>
                        <th>標題</th>
                        <th>內容</th>
                        <th style="width:80px">排序</th>
                        <th style="width:90px">操作</td>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(vo, index) in datas">
                        <td data-th="編號">
                            <input type="checkbox" v-model="vo.select">
                            <span v-text="index + 1 + {{$data['num_pre_count']}}"></span>
                        </td>
                        <td data-th="狀態">
                            <select v-model="vo.online" :class="[vo.online== 0 ? 'color_red' : 'color_green']" @change="save_one(index, false)">
                                <option class="color_green" value="1">啟用</option>
                                <option class="color_red" value="0">停用</option>
                            </select>
                        </td>
                        <td data-th="標題"><a href="###" @click="openBox(index)" v-text="vo.title"></a></td>
                        <td data-th="內容" v-html="vo.content"></td>
                        <td data-th="排序"><input value="number" v-model="vo.order_id" @change="save_one(index)"></td>
                        <td data-th="操作">
                            <button type="button" @click="del(vo.id)" class="btn sendbtn" >刪除</button>
                        </td>              
                    </tr>
                </tbody>
                
            </table>
        
        <!--表格 結束-->
        <div class="text-center">
            {{$data['datas']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script type="text/javascript">
        /*初始化編輯器*/
        var editor = KindEditor.ready(function(K) {
            editor = K.create('#editor', {
                langType : 'zh_TW',
                items:['source', '|', 'hr','|','emoticons','|','forecolor','bold', 'italic', 'underline','link', 'unlink',],
                width:'100%',
                height:'300px',
                resizeType:0
            });
        });
    </script>

    <script>
        var datas = `{{json_encode($data['datas']->items())}}` ? JSON.parse(`{{json_encode($data['datas']->items())}}`.replace(/&quot;/g,'"').trim()) : [];
        for (var i = 0; i < datas.length; i++) {
            datas[i]['content'] = datas[i]['content'].replaceAll('\"', "'");
            datas[i]['select'] = false;
        }
        // console.log(datas);
        var content_area_data = {
            select_all: false,
            search: {searchOnline:{{$data['searchOnline']}}, searchKey: `{{$data['searchKey']}}`},
            datas: datas,
            aeModel: { id: 0, title: "", content: "", order_id: 0, online: 1, },
        };
        var content_areaVM = new Vue({
            el: "#content_area",
            data: content_area_data,
            methods: {
                newBlock: function (){
                    this.aeModel.id = 0;
                    this.aeModel.title = "";
                    this.aeModel.content = "";
                    this.aeModel.order_id = 0;
                    this.aeModel.online = 1;
                    editor.html("");
                    $('#functionModal_btn').click();
                },
                openBox: function(index){
                    self = this;
                    self.aeModel = Object.assign({}, self.datas[index]);
                    editor.html(self.aeModel.content)
                    $('#functionModal_btn').click();
                },
                ajaxSubmit: function(){
                    self = this;
                    self.aeModel.content = editor.html();
                    self.ajax_save_data(self.aeModel);
                },
                save_one: function(index, reload=true){
                    self = this;
                    self.ajax_save_data(self.datas[index], reload);
                },
                ajax_save_data: function(post_data, reload=true){
                    return $.ajax({
                        type:'post',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: post_data,
                        url:"{{url('Fields/comments_set_save')}}",
                        success:function(res){
                            if(res.code=='1'){
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(res.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            if(reload){ setTimeout(()=>{ location.reload(); }, 300); }
                        }
                    });
                },
                del: function(id){
                    $.ajax({
                        type:'post',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: {id: id},
                        url:"{{url('Fields/comments_set_delete')}}",
                        success:function(res){
                            if(res.code=='1'){
                                bg_class = 'bg-success';
                                setTimeout(()=>{ location.reload(); }, 300);
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(res.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                        }
                    });
                },

                selectAll: function(){
                    self = this;
                    var select_type = self.select_all ? false : true;
                    for (var i = 0; i < self.datas.length; i++) {
                        self.datas[i]['select'] = select_type;
                    }
                },
                multiOnline: async function(){
                    self = this;
                    for (var i = 0; i < self.datas.length; i++) {
                        if(self.datas[i]['select']){
                            self.datas[i]['online'] = 1;
                            await self.ajax_save_data(self.datas[i], false);
                        }
                    }
                    setTimeout(function(){location.reload();}, 300);
                },
                multiOffline: async function(){
                    self = this;
                    for (var i = 0; i < self.datas.length; i++) {
                        if(self.datas[i]['select']){
                            self.datas[i]['online'] = 0;
                            await self.ajax_save_data(self.datas[i], false);
                        }
                    }
                    setTimeout(function(){location.reload();}, 300);
                },
                multiDelete: function(){
                    if(!confirm('確認批次刪除？')){return}
                    self = this;
                    for (var i = 0; i < self.datas.length; i++) {
                        if(self.datas[i]['select']){
                            self.del(self.datas[i]['id']);
                        }
                    }
                    setTimeout(function(){location.reload();}, 300);
                },
            },
        });
    </script>
@endsection