@extends('admin.Public.aside')

@section('title')
G功能應用項目 > 特規運費管理
@endsection

@section('content')
    <!-- 新增修改運費標籤開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">運費標籤內容</h5>
                    
                </div>
                <div class="modal-body">
                    <p>運費標籤名稱：<br>
                        <input v-model="name" type="text" placeholder="請輸入名稱" class="form-control">
                    </p>
                    <p>運費標籤金額：<br>
                        <input v-model="price" type="number" placeholder="請輸入金額" class="form-control">
                    </p>
                </div>
                <div class="modal-footer ">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增修改運費標籤結束 -->

    <div id="content">
  
        <ul id="title" class="brand-menu">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">特規運費管理</a></li>
            @if($data['searchKey'] !='')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>
        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1 text-center" placeholder="請輸入運費標籤名稱">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn" onclick="newBlock();"><i class="bi bi-plus-lg add small"></i>  新增</a>
            <div class="edit" onclick="multiDelete();">
                刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
            </div>

            <span class="text-danger remark d-inline">當商品找不到對應的運費標籤時，一律視為免運費</span>
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table table-rwd" style="min-width: 992x;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=shipping_feeCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th>排序</th>
                        <th>運費標籤名稱</th>
                        <th>運費標籤金額</th>
                        <th style="width: 60px;">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(count($data['shipping_fee'])==0)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @endif
                    @foreach($data['shipping_fee'] as $vo)
                    <tr id="shipping_fee_{{$vo->id}}">
                        <td><input type="checkbox" class="shipping_feeCheckbox" alt="{{$vo->id}}"></td>
                        <td><input type="number" v-model = "order_id" @blur="change_order()"></td>
                        <td><a href="###" @click="openBox" v-text="name">{{$vo->name}}</a></td>
                        <td v-text="price">{{$vo->price}}</td>
                        <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
       
        <div class="text-center">
            {{$data['shipping_fee']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="/public/static/admin/js/action.js"></script>

    <script>
    
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('shippingfeetag/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                    },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                        location.reload();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.createShipping_fee = function (Data) {
            $.ajax({
                url: "{{url('shippingfeetag/doCreate')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                    },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        location.reload();
                    }else{
                        alert('新增失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('新增失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.updateShipping_fee = function (Data) {
            $.ajax({
                url: "{{url('shippingfeetag/update')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                    },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        alert('更改成功');
                        BoxVM.updateCallerData();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {name: "", price: 0, id: 0, caller: null}
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
            },
            methods: {
                ajaxSubmit: function () {
                    var Data = {
                        name: this.name,
                        price: this.price,
                    }
                    if(this.caller == 'new'){
                        this.createShipping_fee(Data);
                    }else{
                        Data.id = this.id;
                        this.updateShipping_fee(Data);
                    }
                },
                updateCallerData: function () {
                    this.caller.name = this.name;
                    this.caller.price = this.price;
                    $('#functionModal').modal('hide');
                }
            }
        });

        @foreach($data['shipping_fee'] as $vo)
            var shipping_fee_{{$vo->id}} = {
                id: "{{$vo->id}}",
                order_id: "{{$vo->order_id}}",
                name: "{{$vo->name}}",
                price: "{{$vo->price}}",
            }
            var shipping_fee_{{$vo->id}}_VM = new Vue({
                el: '#shipping_fee_{{$vo->id}}',
                data: shipping_fee_{{$vo->id}},
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.name = this.name;
                        BoxVM.price = this.price;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    },
                    change_order: function () {
                        blockData = {
                            id: this.id,
                            order_id: this.order_id
                        }
                        this.blockCtrl(blockData);
                    },
                }
            });
        @endforeach

        function newBlock(){
            BoxVM.id = 0;
            BoxVM.name = "";
            BoxVM.price = "";
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.shipping_feeCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id){
            if(confirm('確認刪除?')){
                location.href = "{{url('shippingfeetag/delete')}}?id="+id;
            }
        }
        function multiDelete() {
            if(confirm("確定刪除?")){
                var form = document.createElement("form");
                form.action = "{{url('shippingfeetag/multiDelete')}}";
                form.method = "post";
                form.className = 'd-none';

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";

                form.appendChild(multiId);
                document.body.appendChild(form);

                $('#block_block').show();
                form.submit();
                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }
    </script>
@endsection