@extends('admin.Public.aside')
@section('title')H行銷項目 > 消費累積兌換領取名單@endsection
@section('css')
@endsection

@section('content')
    <div id="content">
 
        <ul id="title" class="brand-menu">
            <li><a href="###">H行銷項目</a></li>
            <li><a href="###">消費累積兌換</a></li>
        </ul>

        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchForm">
                @csrf
                <div class="item">
                    <select name="exchange_id"  v-model="search.exchange_id">
                        <option value="">全部</option>
                        @foreach($data['consumption_exchanges'] as $vo)
                            <option value="{{$vo['id']}}">{{$vo['name']}}</option>
                        @endforeach
                    </select>
                    <input  name="searchKey" type="text" v-model="search.searchKey"
                          clsss="form-control w-100  text-center" placeholder="請輸入贈品說明,領取人姓名,會員編號">
                </div>
                <div class="item">
                    領取日期區間：<input type="date"  name="s_ex_date" v-model="search.s_ex_date">~
                             <input type="date"  name="e_ex_date" v-model="search.e_ex_date">
                </div>
                <a class="btn sendbtn m-1 btn-sm" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn m-1 btn-sm" onclick="location.href='{{url('Consumption/exchange_list')}}'">清除搜尋</a>
            </form>
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table table-rwd" style="min-width: 1200px;">
                <thead>
                    <tr>
                        <th style="width:5%">編號</th>
                        <th style="width:5%">贈品圖片</th>
                        <th style="width:15%">贈品說明</th>
                        <th style="width:15%">領取人姓名</th>
                        <th style="width:15%">會員編號</th>
                        <th style="width:15%">領取日期</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['datas']->items() as $vo)
                    <tr>
                        <td th-data="編號">{{$vo->index}}</td>
                        <td th-data="贈品圖片">@if($vo->pic)<img src="{{__UPLOAD__ . '/' . $vo->pic}}">@endif</td>
                        <td th-data="贈品說明">{{$vo->name}}</td>
                        <td th-data="領取人姓名"><a href="/order/index/edit?id={{$vo->user['id']}}" target="_blank">{{$vo->user['name']}}</a></td>
                        <td th-data="會員編號">{{$vo->user['number']}}</td>
                        <td th-data="領取日期">{{$vo->ex_date}}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!--表格 結束-->

        <div class="text-center">
            {{$data['datas']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script>
        // console.log(datas);
        var content_area_data = {
            search: {
                exchange_id:"{{$data['exchange_id']}}", searchKey:"{{$data['searchKey']}}",
                s_ex_date: "{{$data['s_ex_date']}}", e_ex_date: "{{$data['e_ex_date']}}",
            }
        };
        var content_areaVM = new Vue({
            el: "#content_area",
            data: content_area_data,
            methods: {
            },
        });
    </script>
@endsection