@extends('admin.Public.aside')
@section('title'){{Lang::get('H行銷項目')}} - 滿額贈設定@endsection
@section('css')
<link rel="stylesheet" type="text/css" href="{{__PUBLIC__}}/css/daterangepicker.css" />
@endsection
@section('content')
<div id="content">
    <ul id="title" class="brand-menu">
        <li>{{Lang::get('H行銷項目')}}</li>
        <li><a href="{{url('Giftwithpurchase/index')}}">滿額贈設定</a></li>
        <li v-if="search.keyword"><a href="###" v-text="search.keyword"></a></li>
    </ul>
    <div class="searchbox">
        <form action="{{url('Giftwithpurchase/index')}}" name="searchForm" method="get" class="d-flex flex-nowrap align-items-center">
            @csrf
            <div class="d-flex flex-column">
                <div class="d-flex flex-nowrap mx-1 align-items-center col-12">
                    <label for="date_start" class="my-0 col-6" style="width: 180px;">搜尋起始日期：</label>
                    <input type="date" class="form-control text-center col-6" name="date_start" id="date_start" v-model="search.date_start">
                </div>
                <div class="d-flex flex-nowrap mx-1 mt-2 align-items-center col-12">
                    <label for="date_start" class="my-0 col-6" style="width: 180px;">搜尋結束日期：</label>
                    <input type="date" class="form-control text-center col-6" name="date_end" id="date_end" v-model="search.date_end">
                </div>
            </div>
            <select class="form-control mr-1 col-12 col-sm-1" name="type" v-model="search.type">
                <option v-for="type in types" :value="type.value" v-text="type.text"></option>
            </select>
            <input type="text" class="form-control mr-1 text-center col-12 col-sm-1" style="width: 3000px;" name="keyword" placeholder="{{Lang::get('名稱')}}" v-model="search.keyword">
            <a class="btn sendbtn" @click="getItems()">{{Lang::get('搜尋')}}</a>
            <a class="btn btn-light ml-2" style="background-color: #ffffff; border: 1px solid var(--border);" @click="clearSearch()">{{Lang::get('清除搜尋')}}</a>
        </form>
    </div>
    <!-- Buttons -->
    <div class="frame d-flex flex-row">
        <button type="button" class="btn clearbtn" data-toggle="modal" data-target="#modalNew">
            <i class="bi bi-plus-lg add small"></i>
            {{Lang::get('新增')}}
        </button>
        <div class="dropdown ml-2">
            <button class="btn btn-light dropdown-toggle" style="background-color: #ffffff; border: 1px solid var(--border);" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                編輯
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item d-flex justify-content-between align-items-center" @click="multiItemOn">
                    <span>{{Lang::get('上架')}}</span>
                    <label class="switch">
                        <input type="checkbox" disabled checked><span class="slider round"></span>
                    </label>
                </a>
                <a class="dropdown-item d-flex justify-content-between align-items-center" @click="multiItemOff">
                    <span>{{Lang::get('下架')}}</span>
                    <label class="switch">
                        <input type="checkbox" disabled><span class="slider round"></span>
                    </label>
                </a>
                <a class="dropdown-item d-flex justify-content-between align-items-center" @click="multiItemDel">
                    <span>刪除</span>
                    <i class="bi bi-trash ml-2"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="edit_form">
        <table class="table table-rwd" style="min-width: 992px;">
            <thead>
                <tr>
                    <th style="width: 20px"><input type="checkbox" class="box_all m-0" onclick="checkAll()"></th>
                    <th style="width: 80px">{{Lang::get('上下架')}}</th>
                    <th>{{Lang::get('名稱')}}</th>
                    <th>{{Lang::get('達成金額')}}</th>
                    <th>{{Lang::get('狀態')}}</th>
                    <th>{{Lang::get('開始日期')}}</th>
                    <th>{{Lang::get('結束日期')}}</th>
                    <th style="width: 60px">{{Lang::get('刪除')}}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-if="!items || items.length === 0">
                    <td colspan="10" class="text-center">{{Lang::get('無資料')}}</td>
                </tr>
                <tr v-else v-for="item in items" :key="item.id">
                    <td>
                        <input type="checkbox" class="box m-0" :name="'box_' + item.id" :data-id="item.id" onclick="checkOne()">
                    </td>
                    <td>
                        <label class="switch my-0">
                            <input type="checkbox" @change="oneItemSwitch(item.id, $event.target.checked)" :checked="item.status">
                            <span class="slider round"></span>
                        </label>
                    </td>
                    <td>
                        <a :href="'/admin/giftwithpurchase/edit?id=' + item.id">
                            <span v-text="item.name"></span>
                        </a>
                    </td>
                    <td>
                        <span v-text="item.min_purchase_amount"></span>
                    </td>
                    <td>
                        <span v-text="types.find(type => type.value === item.type).text"></span>
                    </td>
                    <td>
                        <template v-if="item.date_start">
                            <span v-text="item.date_start"></span>
                        </template>
                        <template v-else>
                            <span>無</span>
                        </template>
                    </td>
                    <td>
                        <template v-if="item.date_end">
                            <span v-text="item.date_end"></span>
                        </template>
                        <template v-else>
                            <span>無</span>
                        </template>
                    </td>
                    <td>
                        <i class="bi bi-trash" @click="deleteItem(item.id)"></i>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<!-- 新增滿額贈 -->
<div class="modal fade in main-modal" id="modalNew" tabindex="-1" role="dialog" aria-labelledby="modalNewLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">×</span>
            </button>
            <div class="modal-header">
                <h5 class="modal-title" id="addModalLabel" style="display: inline-block;">
                    新增滿額贈
                </h5>
            </div>
            <div class="modal-body row" id="boxModel">
                <div class="form-group col-sm-12 col-12">
                    <label class="col-form-label">{{Lang::get('名稱')}}<span class="text-danger">*</span></label>
                    <input type="text" class="form-control" v-model="new_modal.name" placeholder="請輸入名稱">
                </div>
                <div class="form-group col-sm-12 col-12">
                    <label class="col-form-label">簡介</label>
                    <textarea rows="5" class="form-control" v-model="new_modal.description"></textarea>
                </div>
                <div class="form-group col-12">
                    <label class="col-form-label">達成金額<span class="text-danger">*</span></label>
                    <input type="number" class="form-control" v-model="new_modal.min_purchase_amount" min="0">
                </div>
                <div class="form-group col-sm-6 col-12">
                    <label class="col-form-label">{{Lang::get('開始日期')}}<span class="text-danger">*</span></label>
                    <input type="date" class="form-control" v-model="new_modal.date_start" min="{{date('Y-m-d')}}">
                </div>
                <div class="form-group col-sm-6 col-12">
                    <label class="col-form-label">{{Lang::get('結束日期')}}</label>
                    <input type="date" class="form-control" v-model="new_modal.date_end">
                </div>
            </div>
            <div class="modal-footer flex-wrap justify-content-center">
                <button class="btn sendbtn" @click="addItem">{{Lang::get('新增')}}</button>
            </div>
        </div>
    </div>
</div>
@endsection
@section('ownJS')
<script type="text/javascript">
    function checkAll() {
        if ($(".box_all").prop("checked")) {
            $(".box").prop("checked", true);
        } else {
            $(".box").prop("checked", false);
        }
    }

    function checkOne() {
        if ($(".box:checked").length == $(".box").length) {
            $(".box_all").prop("checked", true);
            $(".box_all").prop('indeterminate', false);
        } else if ($(".box:checked").length == 0) {
            $(".box_all").prop("checked", false);
            $(".box_all").prop('indeterminate', false);
        } else {
            $(".box_all").prop('indeterminate', true);
        }
    }

    const empty_new_modal = {
        name: '',
        description: '',
        min_purchase_amount: 0,
        productinfo_type_id: '',
        product_count: 1,
        date_start: '',
        date_end: '',
    };

    const empty_search = {
        keyword: '',
        date_start: '',
        date_end: '',
        type: 0,
    };

    const content_areaVM = new Vue({
        el: '#content_area',
        data: {
            types: [{
                    value: 0,
                    text: '全部'
                },
                {
                    value: 1,
                    text: '已過期'
                },
                {
                    value: 2,
                    text: '進行中'
                },
                {
                    value: 3,
                    text: '未開始'
                },
            ],
            search: {
                ...empty_search
            },
            new_modal: {
                name: '',
                description: '',
                min_purchase_amount: 0,
                date_start: '',
                date_end: '',
            },
            items: [],
        },
        mounted: function() {
            this.getItems();
        },
        methods: {
            getItems: function() {
                $('.box').prop("checked", false);
                $('.box_all').prop("checked", false);
                $('.box_all').prop('indeterminate', false);

                self = this;

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    url: "{{url('Giftwithpurchase/getItems')}}",
                    data: self.search,
                    success: function(response) {
                        if (response.code != 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });

                            return false;
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });

                            self.search = response.data.search;
                            self.items = response.data.items;
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error fetching items:", error);
                    }
                });
            },
            clearSearch: function() {
                this.search = {
                    ...empty_search
                };

                this.getItems();
            },
            addItem: function() {
                self = this;

                try {
                    if (!self.new_modal.name) {
                        throw new Error("{{Lang::get('請輸入名稱')}}");
                    }

                    if (parseInt(self.new_modal.min_purchase_amount) <= 0) {
                        throw new Error('請輸入達成金額');
                    }

                    if (!self.new_modal.date_start) {
                        throw new Error("{{Lang::get('請選擇開始日期')}}");
                    }

                    if (new Date(self.new_modal.date_start) < new Date("{{date('Y-m-d')}}")) {
                        throw new Error("{{Lang::get('開始日期不能小於今天')}}");
                    }

                    if (self.new_modal.date_end) {
                        if (new Date(self.new_modal.date_start) > new Date(self.new_modal.date_end)) {
                            throw new Error("{{Lang::get('開始日期不能大於結束日期')}}");
                        }
                    }
                } catch (e) {
                    Vue.toasted.show(e, {
                        duration: 1500,
                        className: ["toasted-primary", 'bg-warning']
                    });

                    return false;
                }

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    url: "{{url('Giftwithpurchase/addItem')}}",
                    data: self.new_modal,
                    success: function(response) {
                        if (response.code != 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });

                            setTimeout(function() {
                                window.location.href = "{{url('Giftwithpurchase/edit')}}?id=" + response.data.id;
                            }, 1000);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error adding item:", error);
                    }
                });
            },
            getCheckedIds: function() {
                return $('.box:checked').map(function() {
                    return $(this).data('id');
                }).get();
            },
            multiItemOn: function() {
                this.updateAction(this.getCheckedIds(), 'status', 1);
            },
            multiItemOff: function() {
                this.updateAction(this.getCheckedIds(), 'status', 0);
            },
            multiItemDel: function() {
                this.deleteAction(this.getCheckedIds());
            },
            oneItemSwitch: function(id, checked) {
                this.updateAction([id], 'status', checked ? 1 : 0);
            },
            updateAction: function(id, column, value) {
                self = this;

                if (id.length == 0) {
                    Vue.toasted.show("{{Lang::get('請至少選擇一項')}}", {
                        duration: 1500,
                        className: ["toasted-primary", 'bg-warning']
                    });

                    return;
                };

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    url: "{{url('Giftwithpurchase/updateItem')}}",
                    data: {
                        id: id,
                        column: column,
                        value: value
                    },
                    success: function(response) {
                        if (response.code != 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });
                        }

                        self.getItems();
                    },
                    error: function(xhr, status, error) {
                        console.error("Error fetching items:", error);
                    }
                });
            },
            deleteItem: function(id) {
                this.deleteAction([id]);
            },
            deleteAction: function(ids) {
                self = this;

                if (!confirm("{{Lang::get('確定刪除嗎')}}")) {
                    return;
                }

                if (ids.length == 0) {
                    Vue.toasted.show("{{Lang::get('請至少選擇一項')}}", {
                        duration: 1500,
                        className: ["toasted-primary", 'bg-warning']
                    });

                    return;
                };

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    url: "{{url('Giftwithpurchase/deleteItem')}}",
                    data: {
                        ids: ids
                    },
                    success: function(response) {
                        if (response.code != 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });
                        }

                        self.getItems();
                    },
                    error: function(xhr, status, error) {
                        console.error("Error deleting item:", error);
                    }
                });
            },
        }
    });
</script>
@endsection