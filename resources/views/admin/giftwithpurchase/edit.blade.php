@extends('admin.Public.aside')
@section('title'){{Lang::get('H行銷項目')}} - 滿額贈設定@endsection
@section('css')
@endsection
@section('content')
<div id="content">
    <ul id="title" class="brand-menu">
        <li>{{Lang::get('H行銷項目')}}</li>
        <li><a href="{{url('Giftwithpurchase/index')}}">滿額贈設定</a></li>
        <li>{{Lang::get('編輯')}}</li>
    </ul>
    <a class="btn sendbtn px-2 py-1" href="{{url('Giftwithpurchase/index')}}">
        <span class="bi bi-arrow-left"></span>
        <span class="ml-2">回上一頁</span>
    </a>
    <div class="admin-content act">
        <div class="col-lg-12">
            <div class="item border-bottom">
                <div>{{Lang::get('名稱')}}<span class="text-danger ml-1">*</span>：</div>
                <input type="text" v-model="basic.name" placeholder="必填" class="border-0">
            </div>
            <div class="item border-bottom">
                <div>{{Lang::get('達成金額')}}<span class="text-danger ml-1">*</span>：</div>
                <input type="number" min="0" v-model="basic.min_purchase_amount" placeholder="必填" class="border-0">
            </div>
            <div class="item">
                <div>{{Lang::get('開始日期')}}<span class="text-danger ml-1">*</span>：
                    <input type="date" v-model="basic.date_start" id="date_start" placeholder="必填" class="border-0" min="{{date('Y-m-d')}}">
                </div>
                <div class="ml-4">{{Lang::get('結束日期')}}：
                    <input type="date" v-model="basic.date_end" id="date_end" placeholder="必填" class="border-0">
                </div>
            </div>
            <div class="item">
                <div>{{Lang::get('簡介')}}</div>
                <textarea rows="5" class="form-control mt-2" v-model="basic.description" placeholder="請輸入簡介"></textarea>
            </div>
        </div>
        <a class="btn sendbtn m-auto btn-sm" @click="updateItemBasic()">{{Lang::get('更新基本設定')}}</a>
    </div>
    <div class="product_layer_select m-auto">
        <div id="tabs" class="product_layer_tabs">
            <ul>
                <li><a href="#rule" @click="changeTab('rule')" class="btn border-0 btn-sm" style="cursor: pointer;" v-text="tabs[0].title"></a></li>
                <li><a href="#content" @click="changeTab('content')" class="btn border-0 btn-sm" style="cursor: pointer;" v-text="tabs[1].title"></a></li>
            </ul>
            <template v-for="type in tabs">
                <div :id="type.var">
                    <div class="container-fluid product_layer_select" v-if="existed_data.length > 0">
                        <span>{{Lang::get('已套用商品')}}：</span>
                        <span class="ml-1">
                            <a href="###" class="btn sendbtn btn-sm" @click="existedAddAll()">{{Lang::get('全選商品')}}</a>
                            <a href="###" class="btn sendbtn btn-sm" @click="existedRemoveAll()">清空所有勾選商品</a>
                        </span>
                        <div class="row mt-2" ref="existed_data">
                            <div v-for="item in existed_data" class="col-12 col-md-4 col-lg-3 product_item" :key="item.id">
                                <div>
                                    <input type="checkbox" v-model="exist_selected_info_types" :value="item.id">
                                    <span @click="addToExistSelected(item.id)" v-text="item.title"></span>
                                </div>
                                <img :src="'/public/static/index' + item.pic" @click="addToExistSelected(item.id)">
                                <div v-if="current.tab == 'content'" class="mt-2">
                                    {{Lang::get('贈送數量')}}：<input class="w-50" v-model="item.num" @blur="saveContentNum(item.id, item.num)">
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-center">
                            <button @click="deleteGiftWithPurchaseItms()" class="btn width-50 sendbtn">{{Lang::get('刪除套用商品')}}</button>
                        </div>
                    </div>
                    <span v-else>{{Lang::get('尚無套用商品')}}</span>
                </div>
            </template>
        </div>
    </div>
    <br>
    <div class="admin-content product_layer_select mb-4">
        <div class="mb-3">
            <div class="mb-1">
                <a href="###" class="btn sendbtn btn-sm" @click="resetBranches()">{{Lang::get('重置階層')}}</a>
            </div>
            <div class="mb-2 d-flex flex-wrap">
                <div class="w-100 mb-2">
                    <span class="name">{{Lang::get('所在階層')}}：/</span>
                    <a href="###" class="ml-1 layer-item" @click="showLayers(current.branch.id)" v-if="current.branch.title" v-text="current.branch.title"></a>
                    <span class="mx-1" v-if="current.layer.title">/</span>
                    <a href="###" class="layer-item" v-if="current.layer.title" v-text="current.layer.title"></a>
                </div>
                <div class="w-100 mb-2" v-if="current.branch.id == 0 || (current.branch.id != 0 && current.branch.layers.length > 0)">
                    <span class="name">{{Lang::get('子階層')}}：</span>
                    <template v-if="current.branch.id == 0">
                        <span v-for="item in branches">
                            <a href="###" class="item_tree" @click="showBranchProducts(item.id)" v-text="item.title"></a>
                        </span>
                    </template>
                    <template v-else>
                        <span v-for="item in current.branch.layers">
                            <a href="###" class="item_tree" @click="showLayerProucts(item.id)" v-text="item.title"></a>
                        </span>
                    </template>
                </div>
                <div class="w-100" v-if="items_to_be_selected_length > 0">
                    <span>
                        <a href="###" class="item_tree" @click="addAll()">{{Lang::get('全選商品')}}</a>
                        <a href="###" class="item_tree" @click="removeAll()">清空所有勾選商品</a>
                    </span>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <div class="container-fluid">
                <div class="row" ref="items_to_be_selected">
                    <template v-if="current.branch.id == 0 && current.layer.id == 0">
                        <template v-for="branch in branches">
                            <template v-for="product in branch.products">
                                <template v-for="item in product.productinfo_type">
                                    <div class="col-12 col-md-4 col-lg-3 product_item">
                                        <div>
                                            <input type="checkbox" v-model="selected_info_types" :value="item.id">
                                            <span @click="addToSelected(item.id)">
                                                <span v-text="product.title"></span>
                                                <span v-if="item.title">-</span>
                                                <span v-if="item.title" v-text="item.title"></span>
                                            </span>
                                        </div>
                                        <img :src="'/public/static/index' + item.pic" @click="addToSelected(item.id)">
                                    </div>
                                </template>
                            </template>
                            <template v-for="layer in branch.layers">
                                <template v-for="product in layer.products">
                                    <template v-for="item in product.productinfo_type">
                                        <div class="col-12 col-md-4 col-lg-3 product_item">
                                            <div>
                                                <input type="checkbox" v-model="selected_info_types" :value="item.id">
                                                <span @click="addToSelected(item.id)">
                                                    <span v-text="product.title"></span>
                                                    <span v-if="item.title">-</span>
                                                    <span v-if="item.title" v-text="item.title"></span>
                                                </span>
                                            </div>
                                            <img :src="'/public/static/index' + item.pic" @click="addToSelected(item.id)">
                                        </div>
                                    </template>
                                </template>
                            </template>
                        </template>
                    </template>
                    <template v-else-if="current.branch.id != 0 && current.layer.id == 0">
                        <template v-for="product in current.branch.products">
                            <template v-for="item in product.productinfo_type">
                                <div class="col-12 col-md-4 col-lg-3 product_item">
                                    <div>
                                        <input type="checkbox" v-model="selected_info_types" :value="item.id">
                                        <span @click="addToSelected(item.id)">
                                            <span v-text="product.title"></span>
                                            <span v-if="item.title">-</span>
                                            <span v-if="item.title" v-text="item.title"></span>
                                        </span>
                                    </div>
                                    <img :src="'/public/static/index' + item.pic" @click="addToSelected(item.id)">
                                </div>
                            </template>
                        </template>
                    </template>
                    <template v-else>
                        <template v-for="product in current.layer.products">
                            <template v-for="item in product.productinfo_type">
                                <div class="col-12 col-md-4 col-lg-3 product_item">
                                    <div>
                                        <input type="checkbox" v-model="selected_info_types" :value="item.id">
                                        <span @click="addToSelected(item.id)">
                                            <span v-text="product.title"></span>
                                            <span v-if="item.title">-</span>
                                            <span v-if="item.title" v-text="item.title"></span>
                                        </span>
                                    </div>
                                    <img :src="'/public/static/index' + item.pic" @click="addToSelected(item.id)">
                                </div>
                            </template>
                        </template>
                    </template>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-center">
            <button class="btn width-50 sendbtn" @click="updateGiftWithPurchase()">{{Lang::get('加入套用商品')}}</button>
        </div>
    </div>
</div>
@endsection
@section('ownJS')
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script type="text/javascript">
    $(function() {
        $("#tabs").tabs();
        $("#exampleTabs").tabs();
    });

    const ID = "{{$data['item']['id']}}";

    const empty_branch = {
        id: 0,
        title: '',
        products: [],
        layers: [],
    };

    const empty_layer = {
        id: 0,
        title: '',
        products: [],
    };

    const content_areaVM = new Vue({
        el: '#content_area',
        data: {
            basic: {
                id: ID,
                name: "{{$data['item']['name']}}",
                description: `{{$data['item']['description']}}`,
                min_purchase_amount: "{{$data['item']['min_purchase_amount']}}",
                date_start: "{{$data['item']['date_start']}}",
                date_end: "{{$data['item']['date_end']}}",
            },
            tabs: [{
                    var: 'rule',
                    title: "{{Lang::get('條件商品')}}"
                },
                {
                    var: 'content',
                    title: "{{Lang::get('滿額贈商品')}}"
                }
            ],
            current: {
                tab: '',
                branch: {
                    ...empty_branch
                },
                layer: {
                    ...empty_layer
                },
            },
            branches: [],
            items_to_be_selected_length: 0,
            selected_info_types: [],
            existed_data: [],
            exist_selected_info_types: [],
        },
        mounted: function() {
            this.current.tab = this.tabs[0].var;
            this.getGiftWithPurchase();
            this.getBranches();
            this.resetBranches();
        },
        methods: {
            updateItemBasic: function() {
                self = this;

                try {
                    if (!self.basic.name) {
                        throw new Error("{{Lang::get('請輸入名稱')}}");
                    }

                    if (parseInt(self.basic.min_purchase_amount) <= 0) {
                        throw new Error('請輸入正確的達成金額');
                    }

                    if (!self.basic.date_start) {
                        throw new Error("{{Lang::get('請選擇開始日期')}}");
                    }

                    // if (new Date(self.basic.date_start) < new Date("{{date('Y-m-d')}}")) {
                    //     throw new Error("{{Lang::get('開始日期不能小於今天')}}");
                    // }

                    if (self.basic.date_end) {
                        if (new Date(self.basic.date_start) > new Date(self.basic.date_end)) {
                            throw new Error("{{Lang::get('開始日期不能大於結束日期')}}");
                        }
                    }
                } catch (e) {
                    Vue.toasted.show(e, {
                        duration: 1500,
                        className: ["toasted-primary", 'bg-warning']
                    });

                    return false;
                }

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    url: "{{url('Giftwithpurchase/updateItemBasic')}}",
                    data: self.basic,
                    success: function(response) {
                        if (response.code != 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error updating item basic:", error);
                    }
                });
            },
            changeTab: function(tab) {
                if (!this.tabs.find(t => t.var === tab)) {
                    alert('不存在的標籤');

                    return;
                }

                this.current.tab = tab;
                this.getGiftWithPurchase();
                this.getBranches();
            },
            getGiftWithPurchase: function() {
                self = this;

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    async: false,
                    url: "{{url('Giftwithpurchase/getGiftWithPurchase')}}",
                    data: {
                        id: self.basic.id,
                        tab: self.current.tab,
                    },
                    success: function(response) {
                        if (response.code == 1) {
                            self.existed_data = response.data;
                            console.log(self.existed_data);
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error fetching exist selected info types:", error);
                    }
                });

                this.$nextTick(() => {
                    if (!this.$refs.existed_data.children) {
                        this.existed_data_length = 0;
                    } else {
                        this.existed_data_length = this.$refs.existed_data.children.length;
                    }
                });
            },
            updateGiftWithPurchase: function() {
                self = this;

                const updateUrl = this.current.tab === 'rule' ? "{{url('Giftwithpurchase/updateRuleItem')}}" : "{{url('Giftwithpurchase/updateContentItem')}}";

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    async: false,
                    url: updateUrl,
                    data: {
                        id: self.basic.id,
                        item_ids: self.selected_info_types,
                    },
                    success: function(response) {
                        if (response.code != 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error updating item basic:", error);
                    }
                });

                this.getGiftWithPurchase();
                this.getBranches();
            },
            deleteGiftWithPurchaseItms: function() {
                self = this;

                const deleteUrl = this.current.tab === 'rule' ? "{{url('Giftwithpurchase/deleteRuleItem')}}" : "{{url('Giftwithpurchase/deleteContentItem')}}";

                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    async: false,
                    url: deleteUrl,
                    data: {
                        id: self.basic.id,
                        item_ids: self.exist_selected_info_types,
                    },
                    success: function(response) {
                        if (response.code != 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error updating item basic:", error);
                    }
                });

                this.exist_selected_info_types = [];
                this.getGiftWithPurchase();
                this.getBranches();
            },
            getBranches: function() {
                self = this;

                $.ajax({
                    type: "post",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    async: false,
                    url: "{{url('Giftwithpurchase/getBranches')}}",
                    data: {
                        id: self.basic.id,
                        tab: self.current.tab,
                    },
                    success: function(response) {
                        if (response.code == 1) {
                            self.branches = response.data;
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });

                            return false;
                        }
                    }
                });

                this.selected_info_types = [];
            },
            resetBranches: function() {
                this.current.branch = {
                    ...empty_branch
                };

                this.current.layer = {
                    ...empty_layer
                };

                this.$nextTick(() => {
                    if (!this.$refs.items_to_be_selected.children) {
                        this.items_to_be_selected_length = 0;
                    } else {
                        this.items_to_be_selected_length = this.$refs.items_to_be_selected.children.length;
                    }
                });
            },
            showLayers: function(branch_id) {
                this.current.layer = {
                    ...empty_layer
                };
                this.showBranchProducts(branch_id);
            },
            showBranchProducts: function(branch_id) {
                const branch_data = this.branches.find(branch => branch.id === branch_id);

                this.current.branch = {
                    ...branch_data
                };

                this.$nextTick(() => {
                    if (!this.$refs.items_to_be_selected.children) {
                        this.items_to_be_selected_length = 0;
                    } else {
                        this.items_to_be_selected_length = this.$refs.items_to_be_selected.children.length;
                    }
                });
            },
            showLayerProucts: function(layer_id) {
                const layer_data = this.current.branch.layers.find(layer => layer.id === layer_id);

                this.current.layer = {
                    ...layer_data
                };

                this.$nextTick(() => {
                    if (!this.$refs.items_to_be_selected.children) {
                        this.items_to_be_selected_length = 0;
                    } else {
                        this.items_to_be_selected_length = this.$refs.items_to_be_selected.children.length;
                    }
                });
            },
            addToExistSelected: function(item_id) {
                if (!this.exist_selected_info_types.includes(item_id)) {
                    this.exist_selected_info_types.push(item_id);
                } else {
                    const index = this.exist_selected_info_types.indexOf(item_id);

                    if (index > -1) {
                        this.exist_selected_info_types.splice(index, 1);
                    }
                }
            },
            addToSelected: function(item_id) {
                if (!this.selected_info_types.includes(item_id)) {
                    this.selected_info_types.push(item_id);
                } else {
                    const index = this.selected_info_types.indexOf(item_id);

                    if (index > -1) {
                        this.selected_info_types.splice(index, 1);
                    }
                }
            },
            existedAddAll: function() {
                this.exist_selected_info_types = this.existed_data.map(item => item.id);
            },
            addAll: function() {
                if (this.current.branch.id == 0 && this.current.layer.id == 0) {
                    this.branches.forEach(branch => {
                        branch.products.forEach(product => {
                            product.productinfo_type.forEach(item => {
                                if (!this.selected_info_types.includes(item.id)) {
                                    this.selected_info_types.push(item.id);
                                }
                            });
                        });

                        branch.layers.forEach(layer => {
                            layer.products.forEach(product => {
                                product.productinfo_type.forEach(item => {
                                    if (!this.selected_info_types.includes(item.id)) {
                                        this.selected_info_types.push(item.id);
                                    }
                                });
                            });
                        });
                    });
                } else if (this.current.branch.id != 0 && this.current.layer.id == 0) {
                    this.current.branch.products.forEach(product => {
                        product.productinfo_type.forEach(item => {
                            if (!this.selected_info_types.includes(item.id)) {
                                this.selected_info_types.push(item.id);
                            }
                        });
                    });
                } else {
                    this.current.layer.products.forEach(product => {
                        product.productinfo_type.forEach(item => {
                            if (!this.selected_info_types.includes(item.id)) {
                                this.selected_info_types.push(item.id);
                            }
                        });
                    });
                }
            },
            existedRemoveAll: function() {
                this.exist_selected_info_types = [];
            },
            removeAll: function() {
                let need_to_remove = [];

                if (this.current.branch.id == 0 && this.current.layer.id == 0) {
                    this.selected_info_types = [];
                } else if (this.current.branch.id != 0 && this.current.layer.id == 0) {
                    this.selected_info_types.forEach((item, index) => {
                        if (this.current.branch.products.find(product => product.productinfo_type.find(pt => pt.id === item))) {
                            need_to_remove.push(item);
                        }
                    });

                    need_to_remove.forEach(item => {
                        const index = this.selected_info_types.indexOf(item);

                        if (index > -1) {
                            this.selected_info_types.splice(index, 1);
                        }
                    });
                } else {
                    this.selected_info_types.forEach((item, index) => {
                        if (this.current.layer.products.find(product => product.productinfo_type.find(pt => pt.id === item))) {
                            need_to_remove.push(item);
                        }
                    });

                    need_to_remove.forEach(item => {
                        const index = this.selected_info_types.indexOf(item);

                        if (index > -1) {
                            this.selected_info_types.splice(index, 1);
                        }
                    });
                }
            },
            saveContentNum: function(id, num) {
                self = this;

                $.ajax({
                    type: "post",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    async: false,
                    url: "{{url('Giftwithpurchase/saveContentNum')}}",
                    data: {
                        id: self.basic.id,
                        productinfo_type_id: id,
                        num: num,
                    },
                    success: function(response) {
                        if (response.code == 1) {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-success']
                            });
                        } else {
                            Vue.toasted.show(response.msg, {
                                duration: 1500,
                                className: ["toasted-primary", 'bg-danger']
                            });
                        }
                    }
                });

                this.getGiftWithPurchase();
            },
        }
    });
</script>
@endsection