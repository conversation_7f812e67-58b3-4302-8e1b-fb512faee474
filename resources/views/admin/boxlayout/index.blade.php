@extends('admin.Public.aside')
@section('title')G功能應用項目 > 客製禮盒版型@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu" onclick="location.reload()">
            <li><a>G功能應用項目</a></li>
            <li><a>客製禮盒版型</a></li>
            @if($data['searchKey'])
                <li><a>搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>
        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                <input type="text" name="search" class="form-control text-center" placeholder="請輸入版型分類名稱">
                <a class="btn sendbtn ml-2" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="d-flex flex-wrap pt-2 pb-2">
            <a class="btn clearbtn" onclick="show_modal('new')">
                <i class="bi bi-plus-lg add"></i>
                <span class="ml-1">新增</span>
            </a>
            <!-- <a class="btn" style="background-color: var(--white); border: 1px solid var(--border);">
                <div>
                    <span></span>
                    <i class="bi bi-chevron-down ml-1"></i>
                </div>
            </a> -->
            <button class="btn ml-2 dropdown-toggle" style="background-color: var(--white); border: 1px solid var(--border);" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                編輯
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuLink" style="min-width: 9rem;">
                <a class="dropdown-item d-flex justify-content-between align-items-center" onclick="multiOperate('online');">
                    <div class="switch" style="margin: 0;">
                        <input type="checkbox" disabled checked>
                        <span class="slider round"></span>
                    </div>
                    <span>上架</span>
                </a>
                <a class="dropdown-item d-flex justify-content-between align-items-center" onclick="multiOperate('offline');">
                    <div class="switch" style="margin: 0;">
                        <input type="checkbox" disabled>
                        <span class="slider round"></span>
                    </div>
                    <span>下架</span>
                </a>
                <a class="dropdown-item d-flex justify-content-between align-items-center" onclick="multiOperate('delete');">
                    <i class="bi bi-trash"></i>
                    <span>刪除</span>
                </a>
            </div>


            <!-- <div class="ml-3 d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <i class="bi bi-chevron-down"></i></div>
                <div class="edit-item none">
                    <a onclick="multiOnline();">
                        <p class="mb-0">上架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                   
                    <a onclick="multiOffline();">
                        <p class="mb-0">下架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                    @if($data['admin_type']!='distribution')
                        <a onclick="multiDelete();" class="border-top">
                            刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                        </a>
                    @endif
                </div>
            </div> -->
        </div>

        <!--表格-->
        <div class="edit_form">
            <table class="table table-rwd" style="min-width:768px;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input class="box_all" type="checkbox" style="cursor:pointer;" onclick="operate_all();"></th>
                        <th style="width: 250px;">排序</th>
                        <th style="width: 80px;">上下架</th>
                        <th>版型分類名稱</th>
                        <th>版型數量</th>
                        <th>刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['box_layout_cate']->items()) == true)
                        <tr><td colspan='10'>沒有資料</td></tr>
                    @else
                    @foreach($data['box_layout_cate']->items() as $vo)
                        <tr>
                            <td><input type="checkbox" class="box box_layoutCheckbox" data-id="{{$vo->id}}" onclick="operate_one();"></td>
                            <td><input type="number" min="0" value="{{$vo->order}}" onblur="change_order('{{$vo->id}}')" data-id-order="{{$vo->id}}"></td>
                            <td>
                                <div class="switch" onclick="change_online('{{$vo->id}}')">
                                    <input type="checkbox" @checked($vo->online)>
                                    <span class="slider round"></span>
                                </div>
                            </td>
                            <td><a href="{{url('boxlayout/edit')}}?id={{$vo->id}}">{{$vo->name}}</a><i class="ml-3 bi bi-pencil-square cursor-pointer" onclick="show_modal('edit', '{{$vo->id}}', '{{$vo->name}}', '{{$vo->order}}');"></i></td>
                            <td>{{$vo->count}}</td>
                            <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                        </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
        </div>
        <div class="text-center">
            {{$data['box_layout_cate']->links('pagination.customize')}}
        </div>
    </div>

    <!-- 新增/編輯Modal -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title"></h5>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="id" value="-1">
                    <p>請輸入版型分類名稱：<br>
                        <input name="name" type="text" class="form-control">
                    </p>
                    <p>請輸入排序：<br>
                        <input name="order" type="number" min="0" class="form-control">
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" onclick="submit_modal()"></button>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('ownJS')
<script src="{{__PUBLIC__}}/js/action.js"></script>
<script>
    function show_modal(mode, id, name, order) {
        if (mode == 'new') {
            $.ajax({
                url: "{{url('boxlayout/check_box_content_cate')}}",
                type: 'POST',
                async: false,
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                success: function(response) {
                    if (response == 0) {
                        alert('請先至 F商品管理區/禮盒內容物管理 新增內容物分類');
                        return;
                    }

                    $('input[name="name"]').val('');
                    $('input[name="order"]').val('0');
                    $('input[name="id"]').val(-1);
                    $('#functionModal .modal-title').text('新增版型分類名稱');
                    $('.sendbtn').text('新增');
                    $('#functionModal_btn').click();
                },
                error: function(xhr) {
                    alert('新增失敗');
                    console.log(xhr);
                }
            });
        } else {
            $('input[name="name"]').val(name);
            $('input[name="order"]').val(order);
            $('input[name="id"]').val(id);
            $('#functionModal .modal-title').text('編輯版型分類名稱');
            $('.sendbtn').text('編輯');
            $('#functionModal_btn').click();
        }
    }

    function submit_modal() {
        const ID = $('input[name="id"]').val();

        $.ajax({
            url: "{{url('boxlayout/create_and_update')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                id: ID,
                name: $('input[name="name"]').val(),
                order: $('input[name="order"]').val(),
            },
            success: function(response) {
                alert(response.msg);
                $('#functionModal').modal('hide');

                if (ID == -1) {
                    location.href = "{{url('boxlayout/edit')}}?id=" + response.id;
                } else {
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('新增失敗');
                console.log(xhr);
            }
        });
    }

    function change_online(id) {
        $.ajax({
            url: "{{url('boxlayout/change_online')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                alert(response.msg);
                location.reload();
            },
            error: function(xhr) {
                alert('更改失敗');
                console.log(xhr);
            }
        });
    }

    function change_order(id) {
        var order = $(`input[data-id-order="${id}"]`).val();

        $.ajax({
            url: "{{url('boxlayout/change_order')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                id: id,
                order: order
            },
            success: function(response) {
                alert(response.msg);
                location.reload();
            },
            error: function(xhr) {
                alert('更改失敗');
                console.log(xhr);
            }
        });
    }

    function delete_one(id){
        if (!confirm('確認刪除?')) {
            return;
        }

        $.ajax({
            url: "{{url('boxlayout/delete_one')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                alert(response.msg);
                location.reload();
            },
            error: function(xhr) {
                alert('刪除失敗');
                console.log(xhr);
            }
        });
    }

    function operate_all() {
        if ($(".box_all").prop("checked")) {
            $(".box").prop("checked", true);
        } else {
            $(".box").prop("checked", false);
        }
    }

    function operate_one() {
        if ($(".box:checked").length == $(".box").length) {
            $(".box_all").prop("checked", true);
            $(".box_all").prop('indeterminate', false);
        } else if ($(".box:checked").length == 0) {
            $(".box_all").prop("checked", false);
            $(".box_all").prop('indeterminate', false);
        } else {
            $(".box_all").prop('indeterminate', true);
        }
    }

    function multiOperate(action) {
        let ids = [];

        $(".box:checked").each(function() {
            ids.push($(this).data("id"));
        });

        if (ids.length == 0) {
            alert('請選擇要操作的項目');
            return;
        }

        if (action == 'delete') {
            if (!confirm('確認刪除?')) {
                return;
            }
        }

        $.ajax({
            url: "{{url('boxlayout/multi_operate')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            dataType: 'json',
            data: {
                ids: ids,
                action: action
            },
            success: function(response) {
                alert(response.msg);
                location.reload();
            },
            error: function(xhr) {
                alert('操作失敗');
                console.log(xhr);
            }
        });
    }
</script>
@endsection