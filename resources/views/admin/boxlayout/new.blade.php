@extends('admin.Public.aside')
@section('title')G功能應用項目 > 客製禮盒版型@endsection

@section('content')
<div id="content">
    <ul id="title" class="brand-menu" onclick="javascript:location.href='index'">
        <li><a>G功能應用項目</a></li>
        <li><a href="{{url('Boxlayout/index')}}">客製禮盒版型</a></li>
        <li><a>{{$data['layout']['name']}}</a></li>
    </ul>
    <div id="layout_div">
        <a class="btn sendbtn mb-2" href="{{url('Boxlayout/index')}}">
            <i class="bi bi-arrow-left"></i>
            <span class="ml-1">回上一頁</span>
        </a>
        <div class="admin-content mb-5">
            <div class="d-flex align-items-center mb-2" style="column-gap:0.8rem;">
                <h5 style="font-weight: bold; margin-bottom: 0;">禮盒版型總覽</h5>
                <div>已加入：<span v-text="layouts.length"></span></div>
            </div>
            <button type="button" class="btn-sm clearbtn mb-3" @click="openInfoModal">新增</button>
            <div class="row selected-items">
                <div class="col-lg-2 col-md-4 col-sm-6" v-for="(item, index) in layouts" :key="'layout'+(index+1)">
                    <div class="item">
                        <div class="img" :style="{backgroundImage:`url(${item.image_path})`}"></div>
                        <div class="d-flex" style="column-gap:0.5rem;">
                            <button type="button" class="btn-sm sendbtn" @click="editLayout(index)">編輯</button>
                            <button type="button" class="btn-sm sendbtn" @click="removeLayout(item.id)">刪除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container pb-5" v-show="temp_layout.image_path">
            <div class="row">
                <div class="col-lg-5">
                    <div class="text-danger">
                        <p>注意事項：</p>
                        <p>1. 格子的尺寸及位置，即為消費者於前台選擇禮盒內容物的顯示區域，盡量不要重疊，以免影響顯示</p>
                        <p>2. 欲更換圖片或調整格子數量，請重新新增</p>
                    </div>
                    <div class="modal-left mb-3">
                        <div class="img-preview" ref="preview_img" :style="{backgroundImage:`url(${temp_layout.image_path})`}">
                            <div v-for="(block, index) in temp_layout.blocks" :key="'block'+ (index+1)"
                                class="draggable-box" ref="blocks"
                                :style="{ width: block.width + '%', height: block.height + '%', left: block.left + '%', top: block.top + '%' }">
                                <div class="d-flex flex-column">
                                    <span v-text="index + 1" class="mb-2"></span>
                                    <span>點選拖曳擺放</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7">
                    <div style="margin-bottom: 60px;">
                        <h5 style="font-weight:bold;" class="mb-3">內容物分類設定： (點+可加入禮盒內容物分類)</h5>
                        <div class="added-item" v-for="(block, index) in temp_layout.blocks" :key="'items'+ (index + 1)">
                            <div class="mb-2">
                                <span v-text="index + 1"></span>. (共選 <span v-text="block.items.length"></span> 項)
                            </div>
                            <div class="box">
                                <button type="button" class="btn-sm sendbtn" @click="openItemSelectModal(index)"><i class="bi bi-plus"></i></button>
                                <button type="button" class="remove" v-for="(subItem, subIndex) in block.items" :key="'block'+(subIndex+1)" @click="removeSelectedItem(index, subIndex)">
                                    <span v-text="subItem.category_name"></span>
                                    <span><i class="bi bi-x-circle-fill"></i></span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button v-show="status=='new'" type="button" class="btn sendbtn" @click="confirmLayout">新增</button>
                    <button v-show="status=='edit'" type="button" class="btn sendbtn" @click="confirmLayout(currrent_layout_index)">儲存</button>
                </div>
            </div>
        </div>
        <div class="modal fade" id="infoModal" data-backdrop="static" tabindex="-1" aria-labelledby="infoModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">新增禮盒版型</h5>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="mb-2">上傳背景圖片：<span class="text-danger">(圖片檔名不能有空格；建議尺寸：700px*700px)</span></div>
                            <label for="layout_img" class="layout_img_file">
                                選擇檔案
                                <input type="file" id="layout_img" ref="layout_img" @change="handleFileUpload" accept="image/*">
                            </label>
                            <span v-text="temp_layout.file_name"></span>
                        </div>
                        <div class="form-group">
                            <label for="layout_num">版型格子數</label>
                            <input type="number" id="layout_num" class="form-control d-inline-block w-auto" v-model="temp_layout.block_num" min="1">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn sendbtn" @click="cancelCreate">取消</button>
                        <button type="button" class="btn clearbtn" @click="createLayout">新增</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal boxModal fade" id="itemSelectModal" data-backdrop="static">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">內容物設定</h5>
                    </div>
                    <div class="modal-body">
                        <p class="text-danger">*請至少選擇一種分類</p>
                        <div class="item-row">
                            <div class="item" v-for="(item, index) in modal_block_items" :key="'category'+ (index+1)">
                                <input type="checkbox" :id="'category_item' + (index + 1)" :value="item" v-model="checked_category">
                                <label :for="'category_item' + (index + 1)" v-text="item.category_name" class="mb-0"></label>
                            </div>
                        </div>
                        <div>已選取：<span v-text="checked_category.length"></span></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn sendbtn" @click="setBlockItem">儲存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('ownJS')
<script src="{{__PUBLIC__}}/js/interact.min.js"></script>
<script type="text/javascript">
const layoutId = "{{$data['layout']['id']}}";

// 初始格子尺寸
const BLOCK = {
    width: 20,
    height: 20,
    left: 0,
    top: 0,
    items: [],
}

// 初始版型
const LAYOUT = {
    id: 0,
    block_num: 1,
    blocks: [{
        ...BLOCK
    }],
    file_name: "",
    image_path: "",
}

// Toasted duration
const DURATION = 1500;

var layoutVM = new Vue({
    el: '#layout_div',
    data: {
        /* 新增或編輯狀態 */
        status: "",

        /* 正在編輯的版型 */
        temp_layout: {
            ...LAYOUT,
        },

        /* 所有加入的版型 */
        layouts: [],

        /* 禮盒內容物分類 */
        modal_block_items: [],

        /* modal選中的分類 */
        checked_category: [],

        /* 當前版型 */
        currrent_layout_index: null,

        /* 當前盒子 */
        currrent_block_index: null,

        previewWidth: 0,
        previewHeight: 0,
    },
    watch: {
        "temp_layout.image_path"(newVal) {
            if (newVal) {
                this.$nextTick(() => {
                    this.updateCanvasSize();
                });
            }
        }
    },
    methods: {
        getLayout: function() {
            let self = this;

            $.ajax({
                url: "{{url('admin/boxlayout/get_layout')}}",
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id: layoutId,
                },
                success: function(response) {
                    if (response.code != 1) {
                        alert(response.msg);
                        history.back();
                    } else {
                        Vue.toasted.show(response.msg, {duration:DURATION, className: ["toasted-primary", 'bg-success']});
                        self.layouts = response.data;
                    }
                },
                error: function(xhr, response) {
                    Vue.toasted.show(response.message, {duration:DURATION, className: ["toasted-primary", 'bg-danger']});
                }
            });
        },
        getModalBlockItems: function() {
            $.ajax({
                url: "{{url('Boxlayout/get_modal_block_items')}}",
                type: "POST",
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                success: function(response) {
                    if (response.status == 0) {
                        alert(response.msg);
                        location.reload();

                        return;
                    }

                    layoutVM.modal_block_items = response.data;
                },
                error: function() {
                    alert("系統錯誤，請稍後再試");
                    location.reload();
                }
            });
        },
        openInfoModal: function() {
            if (this.temp_layout.file_name) {
                alert("當前有正在編輯的版型");
            } else {
                $("#infoModal").modal("show");
            }
        },
        handleFileUpload: function(e) {
            let file = e.target.files[0];
            this.temp_layout.file_name = file.name;
        },
        cancelCreate: function() {
            $('#infoModal').modal('hide');
            this.status = "";
            this.temp_layout = {
                ...LAYOUT,
            }
        },
        createLayout: function() {
            this.status = "new";
            let fileInput = this.$refs.layout_img;

            if (!this.temp_layout.file_name || this.temp_layout.block_num < 1) {
                alert("請上傳背景圖片並輸入禮盒格子數");
            } else {
                let reader = new FileReader();
                reader.onload = (e) => {
                    this.temp_layout.image_path = e.target.result;
                };
                reader.readAsDataURL(fileInput.files[0]);
                this.temp_layout.blocks = Array.from({
                    length: this.temp_layout.block_num
                }, () => ({
                    ...BLOCK
                }));
                $("#infoModal").modal("hide");
            }
        },
        // 取得畫布尺寸
        updateCanvasSize() {
            if (this.$refs.preview_img) {
                this.previewWidth = this.$refs.preview_img.offsetWidth;
                this.previewHeight = this.$refs.preview_img.offsetHeight;
            }
        },
        openItemSelectModal: function(index) {
            this.currrent_block_index = index;
            if (this.temp_layout.blocks[index].items.length > 0) {
                this.checked_category = this.temp_layout.blocks[index].items;
            }
            $("#itemSelectModal").modal("show");
        },
        setBlockItem: function() {
            this.checked_category.sort(function(a, b) {
                return a.id - b.id
            });
            this.temp_layout.blocks[this.currrent_block_index].items = this.checked_category;
            this.checked_category = [];
            this.currrent_block_index = null;
            $("#itemSelectModal").modal("hide");
        },
        removeSelectedItem: function(index, subIndex) {
            this.temp_layout.blocks[index].items.splice(subIndex, 1);
        },
        editLayout: function(index) {
            if (this.temp_layout.file_name) {
                alert("當前有正在編輯的版型");
                return;
            }
            this.status = "edit";
            this.currrent_layout_index = index;
            this.temp_layout = this.layouts[index];
        },
        confirmLayout: function(index) {
            // 檢查是否有未選擇內容物的格子
            let item_check = this.temp_layout.blocks.some(block => block.items.length == 0);
            if (item_check) {
                alert("每個格子至少要允許放置一種分類");
                return;
            }

            // 檢查是否有 block 仍是預設值
            let block_check = this.temp_layout.blocks.some(block => block.left === BLOCK.left && block.top === BLOCK.top && block.width === BLOCK.width && block.height === BLOCK.height);
            if (block_check) {
                alert("請調整每個格子的尺寸及位置");
                return;
            }

            // 新增或更新版型
            if (this.status == "new") {
                this.layouts.push(this.temp_layout);
            } else {
                this.layouts[index] = this.temp_layout;
            }

            // 後端儲存
            this.store();

            // 重置
            this.resetData();
        },
        store: function() {
            $.ajax({
                url: "{{ url('admin/boxlayout/store') }}",
                type: 'POST',
                async: false,
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id: layoutId,
                    layouts: this.layouts,
                },
                success: function(response) {
                    if (response.code == 0) {
                        Vue.toasted.show(response.msg, {duration:DURATION, className: ["toasted-primary", 'bg-danger']});
                    } else {
                        Vue.toasted.show(response.msg, {duration:DURATION, className: ["toasted-primary", 'bg-success']});
                    }
                },
                error: function(xhr, response) {
                    Vue.toasted.show(response.msg, {duration:DURATION, className: ["toasted-primary", 'bg-danger']});
                }
            });

            this.getLayout();
            this.resetData();
        },
        removeLayout: function(layout_id) {
            $.ajax({
                url: "{{ url('admin/boxlayout/delete_layout') }}",
                type: 'POST',
                async: false,
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id: layout_id,
                },
                success: function(response) {
                    if (response.code == 0) {
                        Vue.toasted.show(response.msg, {duration:DURATION, className: ["toasted-primary", 'bg-danger']});
                    } else {
                        Vue.toasted.show(response.msg, {duration:DURATION, className: ["toasted-primary", 'bg-success']});
                    }
                },
                error: function(xhr, response) {
                    Vue.toasted.show(response.message, {duration:DURATION, className: ["toasted-primary", 'bg-danger']});
                }
            });

            this.getLayout();
            this.resetData();
        },
        resetData: function() {
            this.temp_layout = {
                ...LAYOUT,
            };
            this.currrent_layout_index = null;
            this.status = "";
            $('#layout_img').val('');
        },
        initDraggable() {
            interact(".draggable-box")
                .draggable({
                    inertia: true,
                    modifiers: [
                        interact.modifiers.restrictRect({
                            restriction: "parent",
                            endOnly: true
                        })
                    ],
                    onmove: this.dragMoveListener
                })
                .resizable({
                    edges: {
                        left: true,
                        right: true,
                        top: true,
                        bottom: true
                    },
                    modifiers: [
                        interact.modifiers.restrictEdges({
                            outer: "parent"
                        })
                    ],
                    listeners: {
                        move: this.resizeListener
                    }
                })
        },
        dragMoveListener(event) {
            const blockEl = event.target.closest(".draggable-box");
            if (!blockEl) return;

            const index = this.$refs.blocks.indexOf(blockEl);
            if (index === -1) return;

            const left = event.dx;
            const top = event.dy;
            this.temp_layout.blocks[index].left = parseFloat(this.temp_layout.blocks[index].left) + (left / this.previewWidth) * 100;
            this.temp_layout.blocks[index].top = parseFloat(this.temp_layout.blocks[index].top) + (top / this.previewHeight) * 100;
        },
        resizeListener(event) {
            const blockEl = event.target.closest(".draggable-box");
            if (!blockEl) return;

            const index = this.$refs.blocks.indexOf(blockEl);
            if (index === -1) return;

            const width = event.rect.width;
            const height = event.rect.height;

            this.temp_layout.blocks[index].width = (width / this.previewWidth) * 100;
            this.temp_layout.blocks[index].height = (height / this.previewHeight) * 100;
        },
    },
    // 初始化
    mounted() {
        this.getLayout();
        this.getModalBlockItems();
        this.initDraggable();
        window.addEventListener("resize", this.updateCanvasSize);
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.updateCanvasSize);
    },
});
</script>
@endsection