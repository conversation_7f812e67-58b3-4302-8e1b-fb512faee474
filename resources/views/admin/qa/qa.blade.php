@extends('admin.Public.aside')
@section('title')E圖文編輯項目 > 常見問題@endsection
@section('css')
@endsection

@section('content')
    <!-- 新增修改問答開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">問題內容</h5>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <span class="remark">分類：</span>
                            <input type="text" v-model="category" class="form-control"/>
                        </div>
                        <div class="col-12">
                            <span class="remark">問題：</span>
                            <textarea v-model="q" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <span class="remark">回答：</span>
                            <input type="hidden" id="editor" class="form-control">
                            <textarea v-model="a" class="form-control d-none" rows="4"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增修改問答結束 -->

    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">E圖文編輯項目</a></li>
            <li><a href="###" onclick="javascript:location.href='index'">常見問題</a></li>
        </ul> 
        <div class="searchbox">
            <form action="" name="searchForm" method="get" name="searchForm"  class="searchKeyBox">
                @csrf
                <input type="text" name="searchKey" value="{{$data['searchKey']}}" class="form-control mr-1 mb-1" placeholder="搜尋標題/內容">
                <input type="text" name="category" value="{{$data['category']}}" class="form-control mr-1 mb-1" placeholder="搜尋分類">
                
                <a class="btn sendbtn mr-1" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn" href="/admin/qa/index">清除搜尋</a>
            </form>            
        </div> 

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn"  onclick="newBlock();"><i class="bi bi-plus-lg add small"></i>  新增</a>
            <div class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a onclick="multiOnline();">
                        <p class="mb-0">上架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                    <a onclick="multiOffline();">
                        <p class="mb-0">下架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                    <a onclick="multiDelete();" class="border-top">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
                <!-- 編輯結束 -->
            </div>
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width:992px;">
                <thead>
                    <tr>
                        <th style="width: 20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=qaCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 60px">排序</th>
                        <th style="width: 80px">上下架</th>
                        <th>問題</th>
                        <th>分類</th>
                        <th style="width: 60px">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['qa'])==true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['qa'] as $vo)
                        <tr id="qa_{{$vo->id}}">
                            <td><input type="checkbox" class="qaCheckbox" alt="{{$vo->id}}"></td>
                            <td><input type="number" v-model = "order_id" @blur="change_order()"></td>
                            <td>
                                <label class="switch">
                                    <input type="checkbox" v-model="online">
                                    <span class="slider round"></span>
                                </label>
                            </td>
                            <td><a href="###" @click="openBox" v-text="q"></a></td>
                            <td v-text="category"></td>
                            <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                        </tr>
                    @endforeach
                    @endif
                </tbody>                
                
            </table>
        </div>
        
        <div class="text-center">
            {{$data['qa']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        KindEditor.ready(function (K) {
            editor = K.create('#editor', {
                afterBlur: function () { this.sync(); },
                langType: 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'emoticons', 'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright'
                ],
                width: '100%',
                height: '300px',
                resizeType: 0
            });
        });
    </script>
    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('qa/cellCtrl')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                        location.reload();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.createQa = function (Data) {
            $.ajax({
                url: "{{url('qa/doCreate')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        location.reload();
                    }else{
                        alert('新增失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('新增失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.updateQa = function (Data) {
            $.ajax({
                url: "{{url('qa/update')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        alert('更改成功');
                        BoxVM.updateCallerData();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {q: "", a: "", category:"", id: 0, caller: null}
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
                qNl2br: function () {
                    return this.q.replace(/\n/g, '<br>');
                },
                aNl2br: function () {
                    editor.sync();
                    this.a = editor.html();
                    return this.a.replace(/\n/g, '<br>');
                }
            },
            methods: {
                ajaxSubmit: function () {
                    var Data = {
                        q: this.qNl2br,
                        a: this.aNl2br,
                        a: this.aNl2br,
                        category : this.category
                    }
                    if(this.caller == 'new'){
                        this.createQa(Data);
                    }else{
                        Data.id = this.id;
                        this.updateQa(Data);
                    }
                },
                updateCallerData: function () {
                    this.caller.q = this.q;
                    this.caller.a = this.a;
                    this.caller.category = this.category;
                    $('#functionModal').modal('hide');
                }
            }
        });

        @if(empty($data['qa'])==false)
        @foreach($data['qa'] as $vo)
            var qa_{{$vo->id}} = {
                id: "{{$vo->id}}",
                order_id: "{{$vo->order_id}}",
                q: "<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $vo->q); ?>",
                a: `<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $vo->a); ?>`,
                category: "{{$vo->category}}",
                online: +"{{$vo->online}}"
            }
            var qa_{{$vo->id}}_VM = new Vue({
                el: '#qa_{{$vo->id}}',
                data: qa_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    },
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.q = this.q;
                        BoxVM.a = this.a;
                        editor.html(BoxVM.a);
                        BoxVM.category = this.category;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    },
                    change_order: function () {
                        blockData = {
                            id: this.id,
                            order_id: this.order_id
                        }
                        this.blockCtrl(blockData);
                    },
                }
            });
        @endforeach
        @endif

        function newBlock(){
            BoxVM.id = 0;
            BoxVM.q = "";
            BoxVM.a = "";
            editor.html(BoxVM.a);
            BoxVM.category = "";
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.qaCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id){
            if(confirm('確認刪除?')){
                location.href = "{{url('qa/delete')}}?id="+id;
            }
        }
        function multiDelete() {
            if(confirm("確定刪除?")){
                var form = document.createElement("form");
                form.action = "{{url('qa/multiDelete')}}";
                form.method = "post";
                form.className = 'd-none';

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";
                form.appendChild(multiId);
                document.body.appendChild(form);

                $('#block_block').show();
                form.submit();
                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('qa_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('qa_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }
    </script>
@endsection