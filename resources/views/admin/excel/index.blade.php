@extends('admin.Public.aside')
@section('title')G功能應用項目 > 機身碼管理@endsection
@section('css')
    <style type="text/css">
        

        .dot_green {
            height: 25px;
            width: 25px;
            background-color: rgb(0, 255, 0);
            border-radius: 50%;
            display: inline-block;
        }
        .dot_red {
            height: 25px;
            width: 25px;
            background-color: rgb(255, 0, 0);
            border-radius: 50%;
            display: inline-block;
        }
        .dot_load {
            height: 15px;
            width: 15px;
            background-color:#E0E0E0;
            border-radius: 50%;
            display: inline-block;
            margin-left:5px;
            -webkit-animation-name: example; /* Safari 4.0 - 8.0 */
            -webkit-animation-duration: 2s; /* Safari 4.0 - 8.0 */
            -webkit-animation-iteration-count: infinite; /* Safari 4.0 - 8.0 */
            animation-name: example;
            animation-duration: 2s;
            animation-iteration-count: infinite;
            position: relative;

        }
       /* Safari 4.0 - 8.0 */
        @-webkit-keyframes example {
        0%   {background-color:red; left:0px; top:0px;}
        25%  {background-color:yellow; left:300px; top:0px;}
        50%  {background-color:blue; left:300px; top:300px;}
        75%  {background-color:green; left:0px; top:300px;}
        100% {background-color:red; left:0px; top:0px;}
        }

        /* Standard syntax */
        @keyframes example {
        0%   {background-color:red; left:0px; top:0px;}
        25%  {background-color:yellow; left:300px; top:0px;}
        50%  {background-color:blue; left:300px; top:300px;}
        75%  {background-color:green; left:0px; top:300px;}
        100% {background-color:red; left:0px; top:0px;}
        }
        input[type=file] {
            display: -webkit-inline-box;
        }

     /* 新增經驗區塊 */
     .add-news{
            position: absolute;
            background: #fff;
            padding:20px;
            width: 370px;
            height:300px;
            z-index:999;
            top:calc(50% - 400px / 2);
            left:calc(50% - 370px / 2);
        }
        .add-news textarea{
            margin-top:-15px;
            margin-left:52px;
            width:278px;
            height:180px;
            padding:5px
        }            
        .add-news .button{
            right:20px;
            bottom:15px;
            position:absolute;
        }
		.qaCheckbox{
			width: 100%;
		}
		table{
			margin-top: 0px;
		}
    </style>
@endsection
@section('content')
<div id="block" onclick="CloseAdd('.add-news')"></div>
    <div id="content">
        <!-- 新增修改問答開始 -->
        <div class="add-news none" id="Box" style="height:410px;">
            <div style="padding: 10px;height: 320px;">
                <p>匯入中</p><span class="dot_load"></span>
            </div>
        </div>
            <!-- 新增修改問答結束 -->
        <!--新增與編輯-->


        <ul id="title" class="brand-menu" onclick="javascript:location.href='index'">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">機身碼管理</a></li>
            @if($data['searchKey'] != '')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>
        <div class="searchbox justify-content-start">
           
            <form action="{{url('Excel/Import')}}" class="search-position width-70" name="excelForm" method="post"  enctype="multipart/form-data"> 
                @csrf
                <input type="file" class="form-control mr-1" name="file" id="file_excel"/>
                <a class="btn sendbtn mr-1 mb-2" onclick="newBlock();">匯入檔案</a>
                <a class="btn sendbtn mb-2" href="/public/static/index/example.xlsx" download>下載範本</a>
            
            </form>
			
            <form action="" name="searchForm" method="get" class="search-position width-70">
                @csrf
                <input type="text" name="searchKey" class="form-control p-2  mr-1 text-center" value="{{$data['searchKey']}}" placeholder="搜尋">
                <select name="statusKey" class="p-2 mr-2 border mb-2">
                    @if($data['statusKey']=='0')
                      <option value="">全部</option>
                    　<option value="1">已註冊</option>
                      <option value="0" selected>未註冊</option>
                    @elseif($data['statusKey']=='1')
                      <option value="">全部</option>
                    　<option value="1" selected>已註冊</option>
                      <option value="0">未註冊</option>
                    @else
                      <option value="" selected>全部</option>
                    　<option value="1">已註冊</option>
                      <option value="0">未註冊</option>
                    @endif
                </select>

                <select name="brandKey" class="p-2 mr-2 border mb-2">
        
                    @if($data['brandKey']=='')
                         <option value="" selected>全部品牌</option>
                         @if(empty($data['brand']) == false)
                         @foreach($data['brand'] as $vo)
                            @if($data['brandKey']==$vo->product_brand)
                                <option value="{{$vo->product_brand}}" selected>{{$vo->product_brand}}</option>
                            @else
                                <option value="{{$vo->product_brand}}" >{{$vo->product_brand}}</option>
                            @endif
                        @endforeach
                        @endif
                    @else
                        <option value="" >全部品牌</option>
                        @if(empty($data['brand']) == false)
                        @foreach($data['brand'] as $vo)
                            @if($data['brandKey']==$vo->product_brand)
                                <option value="{{$vo->product_brand}}" selected>{{$vo->product_brand}}</option>
                            @else
                                <option value="{{$vo->product_brand}}" >{{$vo->product_brand}}</option>
                            @endif
                        @endforeach
                        @endif
                    @endif
                    

                </select>

                <a class="btn sendbtn mb-2 mr-1" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn mb-2" onclick="javascript:location.href='index'">清除查詢</a>
            </form>

            <div class="frame mt-2 ask_status_box border-top">
                <div>
                    <a class="edit mr-2" onclick="multiDelete();">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                    <span class="d-inline-block remark text-danger">機身碼「XXXXXXXXXXXXXX」代表消費者可任意輸入</span>
                </div>
				<div class="ask_status">
					共{{$data['total']}}個，已註冊：{{$data['regisetered']}}；未註冊：{{$data['un_regisetered']}}；註冊率：{{number_format($data['regiseter_rate'],2)}}%
                </div>
            </div>
            <div class="edit_form w-100">
                <table class="table-rwd table" style="min-width: 1400px;">
                
                    <!-- 標題 -->
                    <thead>
                        <tr>
                            <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=qaCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                            <th sstyle="width:60px">編碼</th>
                            <th>機身碼</th>
                            <th>匯入日期</th>
                            <th>商品名稱</th>
                            <th>品牌名稱</th>
                            <th>註冊狀態</th>
                            <th>會員編號</th>
                            <th>註冊日期</th>
                        </tr>
                    </thead>
                    <!-- 內容 -->
                    <tbody class="tableListA">
                        <tr></tr>
                        @if(empty($data['num']) == false)
                        @foreach($data['num'] as $key => $vo)
                        <tr>
                            <td><input type="checkbox" class="qaCheckbox" alt="{{$vo->id}}"></td>
                            <td>{{$key + 1 + 50 * ($data['num']->currentPage() - 1)}}</td>
                            <td>{{$vo->product_code}}</td>
                            <td>
                                    {{$vo->createtime}}
                            </td>
                            <td>{{$vo->product_name}}</td>
                            <td>{{$vo->product_brand}}</td>
                            <td>
                                @if($vo->status==1) 
                                    <span class="dot_green"></span>
                                @else
                                    <span class="dot_red"></span>
                                @endif
                            </td>
                            <!-- <td><a href="{{url('productinfo/edit')}}?id={{$vo->id}}">{{$vo->id}}</a></td> -->
                            <td>{{$vo->number}}</td>
                            <td>{{$vo->regtime}}</td>

                            <!--<td>256</td>
                            <td>1000</td>-->
                        </tr>
                        @endforeach
                        @endif
                    </tbody>
                </table>
            </div>
            <div class="text-center">
                {{$data['num']->links('pagination.customize')}}
            </div>
        </div>
    </div>
@endsection
@section('ownJS')
<script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>

        $(function(){

            $(document).ready(function(){
                $(window).resize(function() {
                    var i ;
                    for(i=1 ; i<=8; i++){
                        $('table').find('td:nth-child('+i+')').outerWidth($('table').find('th:nth-child('+i+')').outerWidth());
                    }
                    if($('table').find('thead').find('tr').outerWidth() != $('table').find('tbody').find('tr').outerWidth()){
                        for(i=4 ; i<=8; i++){
                            $('table').find('td:nth-child('+i+')').css('padding-right','calc(19px - .5em)')
                        }
                    }
                });
            });

            var i ;
            for(i=1 ; i<=8; i++){
                $('table').find('td:nth-child('+i+')').outerWidth($('table').find('th:nth-child('+i+')').outerWidth());
            }
            if($('table').find('thead').find('tr').outerWidth() != $('table').find('tbody').find('tr').outerWidth()){
                for(i=4 ; i<=8; i++){
                    $('table').find('td:nth-child('+i+')').css('padding-right','calc(19px - .5em)')
                }
            }
        })
    var Box = {id:0, content: "", title: "", caller: null}
    var BoxVM = new Vue({
        el: '#Box', 
        data: Box,
        /*
        computed: {
            contentNl2br: function () {
                return this.content.replace(/\n/g, '<br>');
            }
        },
        */
        watch: {
            content: function (val) {
                editor.html(val);
            }
        },
        methods: {
            ajaxSubmit: function () {
                editor.sync();
                this.content = editor.html();
                this.content = this.content.replace(/\n/g, '')
                if(this.caller == 'new'){
                    this.createNews({
                        content: this.content,
                        title: this.title
                    });
                }else{
                    this.updateNews({
                        id: this.id,
                        content: this.content,
                        title: this.title
                    });
                }
            },
            updateCallerData: function () {
                this.caller.content = this.content;
                this.caller.title = this.title;
                CloseAdd('.add-news');
            }
        }
    });

    function newBlock(){
        if($("#file_excel").val() != ''){
            BoxVM.id = 0;
            BoxVM.content = "";
            BoxVM.title = "";
            BoxVM.caller = "new";
            ShowAdd('.add-news');
            excelForm.submit();
        }else{
            alert('請選擇檔案');
        }
    }

    function multiDelete() {
            var form = document.createElement("form");
            form.action = "{{url('Excel/multiDelete_or')}}";
            form.method = "post";
            form.className = 'd-none';

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            multiId.name = "id";
            form.appendChild(multiId);
            document.body.appendChild(form);

            $('#block_block').show();
            form.submit();
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }
	
	function getMultiId() {
            var multiIdArray = [];
            $('.qaCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }
    </script>
@endsection
