@extends('admin.Public.aside')
@section('title')網紅列表@endsection


@section('content')
    <!-- 新增修改網紅開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box" style="max-width: 1024px;">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">網紅內容</h5>
                    
                </div>
                <div class="modal-body">
                    <h4 class="main-title col-12">基本資料</h4>
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-lg-6 col-12 mb-2">
                                <label class="mb-0" for="email">帳號(MAIL)：<span class="text-danger">*</span></label>
                                <input id="email" v-model="email" class="form-control">
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <label class="mb-0" for="password">密碼：<span class="text-danger">*</span></label>
                                <input id="password" v-model="password" class="form-control">
                            </div>

                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="kol_name">網紅名：<span class="text-danger">*</span></label>
                                <input id="kol_name" v-model="kol_name" class="form-control">
                            </div>
                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="real_name">姓名：</label><input id="real_name" v-model="real_name" class="form-control">
                            </div>
                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="english_name">英文：</label><input id="english_name" v-model="english_name" class="form-control">
                            </div>
                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="category">分類：</label><input id="category" v-model="category" class="form-control">
                            </div>
                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="phone">電話：</label><input id="phone" v-model="phone" class="form-control">
                            </div>
                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="mobile">手機：<span class="text-danger">*</span></label>
                                <input id="mobile" v-model="mobile" class="form-control">
                            </div>
                            <div class="col-lg-8 col-12 mb-2">
                                <label class="mb-0" for="address">地址：</label><input id="address" v-model="address" class="form-control">
                            </div>
                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="address_memo">備註：</label><input id="address_memo" v-model="address_memo" class="form-control">
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <label class="mb-0" for="bank_name">匯款銀行：<span class="text-danger">*</span></label>
                                <input id="bank_name" v-model="bank_name" class="form-control">
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <label class="mb-0" for="bank_account">匯款帳號：<span class="text-danger">*</span></label>
                                <input id="bank_account" v-model="bank_account" class="form-control">
                            </div>
                            <div class="col-lg-4 col-12 mb-2">
                                <label class="mb-0" for="id_no">身份証：</label><input id="id_no" v-model="id_no" class="form-control">
                            </div>
                            <div class="col-lg-8 col-12 mb-2">
                                <label class="mb-0" for="memo">備註：</label><input id="memo" v-model="memo" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap justify-content-center">
                        <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <div v-if="id == 0" class="w-100">
                        <div class="text-danger remark">註：合作起訖日、結算日周期請新增完後再以「編輯」方式添加</div>
                    </div>
                    <div v-if="id != 0" class="w-100">
                        <div class="text-danger remark">註：合作起訖日、結算日周期設定之後就不可再修改</div>
                        <div id="tabs" class="w-100">
                            <ul>
                                <li><a href="#tabs-1">合作期間</a></li>
                                <li><a href="#tabs-2">結算統計</a></li>
                                <li>
                                    <a :href="'/admin/kol/salelist?id='+id" target="_blank">銷售列表</a>
                                </li>
                                <li><a href="#tabs-3">代銷中商品</a></li>
                                <li><a href="#tabs-4">販售商品紀錄</a></li>
                            </ul>
                            <div id="tabs-1">
                                <div class="row m-0">
                                    <div class="col-lg-4 col-12 mb-2">
                                        <label class="mb-0" for="date_start">開始日期：<span class="text-danger">*</span></label>
                                        <input id="date_start" v-model="period.date_start" class="form-control" type="date">
                                    </div>
                                    <div class="col-lg-4 col-12 mb-2">
                                        <label class="mb-0" for="date_end">結束日期：<span class="text-danger">*</span></label>
                                        <input id="date_end" v-model="period.date_end" class="form-control" type="date">
                                    </div>
                                    <div class="col-lg-2 col-6 mb-2">
                                        <label class="mb-0" for="count_days">結算周期(日)：<span class="text-danger">*</span></label>
                                        <input id="count_days" v-model="period.count_days" class="form-control" type="number" min="1">
                                    </div>
                                    <div class="col-lg-2 col-6 mb-2 d-flex align-items-center">
                                        <button type="button" class="btn sendbtn" @click="create_period">儲存</button>
                                    </div>
                                </div>
                                <hr>
                                <table class="productTable table-rwd w-100 table-mobile">
                                    <thead>
                                        <tr>
                                            <th>開始日期</th>
                                            <th>結束日期</th>
                                            <th>結算周期(日)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="period in sale_period">
                                            <td data-th="開始日期" v-text="period.date_start"></td>
                                            <td data-th="結束日期" v-text="period.date_end"></td>
                                            <td data-th="結算周期(日)" v-text="period.count_days"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div id="tabs-2">
                                <span class="text-danger">此區僅統計「已結算」的銷售數據，其於統計請透過「銷售列表」查看</span>
                                <table class="productTable table-rwd w-100 table-mobile">
                                    <thead>
                                        <tr>
                                            <th>開賣日</th>
                                            <th>商品名</th>
                                            <th>商品圖</th>
                                            <th>售價</th>
                                            <th>己結算銷售數量</th>
                                            <th>己結算銷售總額</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="product in sale_record">
                                            <td data-th="開賣日" v-text="product.s_time"></td>
                                            <td data-th="商品名"><a target="_blank" :href="'{{request()->server('REQUEST_SCHEME')}}://'+product.product.url" v-text="product.product.name"></a></td>
                                            <td  data-th="商品圖"><img :src="'{{request()->server('REQUEST_SCHEME')}}://'+product.product.url2"></td>
                                            <td data-th="售價" v-text="product.product.price"></td>
                                            <td data-th="己結算銷售數量" v-text="product.num"></td>
                                            <td data-th="己結算銷售總額" v-text="product.total"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div id="tabs-3">
                                <table class="productTable table-rwd w-100 table-mobile">
                                    <thead>
                                        <tr>
                                            <th>商品名</th>
                                            <th>商品圖</th>
                                            <th>代銷網址</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="product in selling_product">
                                            <td data-th="商品名" v-text="product.title"></td>
                                            <td data-th="商品圖"><img :src="product.pic" style="max-width:75px"></td>
                                            <td data-th="代銷網址">
                                                <input class="copyurl" type="text" readonly
                                                       :value="'{{request()->server('REQUEST_SCHEME')}}://'+'{{request()->server('HTTP_HOST')}}'+'/Product/productinfo?id='+ product.id +'&kol='+ product.kol_id"
                                                       style="width: 70%">
                                                <input type="button" value="查看商品" @click="open_page(product.id, product.kol_id)">
                                                <input type="button" value="複製網址" @click="copy_url(this)">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div id="tabs-4">
                                <table class="productTable table-rwd w-100 table-mobile">
                                    <thead>
                                        <tr>
                                            <th>商品名</th>
                                            <th>商品圖</th>
                                            <th>設定時間</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="product in selling_product_record">
                                            <td data-th="商品名" v-text="product.title"></td>
                                            <td data-th="商品圖"><img :src="product.pic" style="max-width:75px"></td>
                                            <td data-th="代銷網址">
                                                <span v-text="product.time_format"></span> ~
                                                <span v-text="product.time_e_format"></span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增修改問答網紅結束 -->

    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">H行銷項目</a></li>
            <li><a href="###">網紅列表</a></li>
            <li onclick="javascript:location.href='index'">
                <a href="###">
                @if($data['searchKey'] != '')
                    搜尋：{{$data['searchKey']}}
                @endif
                </a>
            </li>
        </ul>

        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox">
                @csrf
                <input type="text" name="searchKey"  class="form-control mr-1 text-center" placeholder="請輸入帳號/網紅名/姓名/英文名/手機/分類" value="{{$data['searchKey']}}">
                <a class="btn sendbtn mb-1 mr-1" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn mb-1" onclick="location.href='/admin/kol/index?searchKey='">清除搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn" onclick="newBlock();">
                <i class="bi bi-plus-lg add small" onclick="newBlock();"></i>  新增
            </a>
            <div class="btn sendbtn" onclick="multiDelete();">刪除  <span style="margin-left: 15px;" class="bi bi-trash"></span></div>
        </div>

        <!--表格 開始-->
        <div class="edit_form" >
            <table class="table table-rwd" style="min-width: 1400px;">
                <thead>
                    <tr>
                        <th style="width: 20px;">
                            <input type="checkbox" class="activityCheckboxAll" style="cursor:pointer;"
                                   onclick="$('.table input[class=qaCheckbox]').prop('checked', ($(this).is(':checked')?true:false))">
                        </th>
                        <th style="width: 80px;">編號</th>
                        <th>帳號</th>
                        <th>網紅名</th>
                        <th>姓名</th>
                        <th>電話</th>
                        <th>手機</th>
                        <th>地址</th>
                        <th style="width: 60px;">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['kol'])==true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['kol'] as $vo)
                    <tr id="kol_{{$vo->id}}">
                        <td><input type="checkbox" class="qaCheckbox" alt="{{$vo->id}}"></td>
                        <td v-text="id"></td>
                        <td><a href="###" @click="openBox" v-text="email"></a><span class="bi bi-pencil-square"></span></td>
                        <td>
                            <a href="/admin/kol/salelist?id={{$vo->id}}" target="_blank" v-text="kol_name"></a>
                            <svg class="view" width="1em" height="1em" viewBox="0 0 16 16" class="bi bi-card-list" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M14.5 3h-13a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5zm-13-1A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-13z"/>
                            <path fill-rule="evenodd" d="M5 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 5 8zm0-2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0 5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5z"/>
                            <circle cx="3.5" cy="5.5" r=".5"/>
                            <circle cx="3.5" cy="8" r=".5"/>
                            <circle cx="3.5" cy="10.5" r=".5"/>
                            </svg>
                        </td>
                        <td v-text="real_name"></td>
                        <td v-text="phone"></td>
                        <td v-text="mobile"></td>
                        <td v-text="address"></td>
                        <td><span class="bi bi-trash" onclick="if(confirm('確定刪除？')){location.href = '{{url('kol/delete')}}?id={{$vo->id}}'}"></span></td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
        </div>
        <div class="text-center">
            {{$data['kol']->links('pagination.customize')}}
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.createKol = function (Data) {
            $.ajax({
                url: "{{url('kol/doCreate')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(resp) {
                    if(resp.code==1){
                        location.reload();
                    }else{
                        bg_class = 'bg-danger';
                        Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                }
            });
        };
        Vue.prototype.updateKol = function (Data) {
            $.ajax({
                url: "{{url('kol/update')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(resp) {
                    if(resp.code==1){
                        bg_class = 'bg-success';
                        BoxVM.updateCallerData();
                    }else{
                        bg_class = 'bg-danger';
                    }
                    Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                },
                error: function(xhr) {
                    console.log(xhr);
                }
            });
        };

        empty_model_data = { 
            id: '0', email: "", password: "", 
            kol_name:"", real_name:"", english_name:"", 
            category:"", phone:"", mobile:"", 
            address:"", address_memo:"", 
            bank_name:"", bank_account:"", 
            id_no:"", memo:"",

            caller: null,
            sale_period: [],
            sale_record: [],
            selling_product: [],
            selling_product_record: [],
        };
        empty_model_period = {
            date_start: '',
            date_end: '',
            count_days: '',
        }
        var Box = JSON.parse(JSON.stringify(empty_model_data));
        Box.period = JSON.parse(JSON.stringify(empty_model_period));
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            methods: {
                ajaxSubmit: function () {
                    var Data = {
                        email: this.email,
                        password: this.password,
                        kol_name: this.kol_name,
                        real_name: this.real_name,
                        english_name: this.english_name,
                        category: this.category,
                        phone: this.phone,
                        mobile: this.mobile,
                        address: this.address,
                        address_memo: this.address_memo,
                        bank_name: this.bank_name,
                        bank_account: this.bank_account,
                        id_no: this.id_no,
                        memo: this.memo,
                    }
                    if(this.id == '0'){
                        this.createKol(Data);
                    }else{
                        Data.id = this.id;
                        this.updateKol(Data);
                    }
                },
                updateCallerData: function () {
                    const keys = Object.keys(empty_model_data);
                    for (var i = 0; i < keys.length; i++) {
                        this.caller[keys[i]] = this[keys[i]]
                    }
                    $('#functionModal_btn').click();
                },
                open_page: function(prod_id, kol_id){
                    url = "{{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}/Product/productinfo?id="+ prod_id +"&kol="+ kol_id;
                    window.open(url);
                },
                copy_url: function(item){
                    target = $(event.target);
                    var copyText = target.prev().prev();
                    copyText.select();
                    document.execCommand("Copy");
                    Vue.toasted.show('複製成功',{duration:1500});
                },

                create_period: function(){
                    self = this;
                    data = JSON.parse(JSON.stringify(self.period));
                    data.kol_id = self.id;
                    $.ajax({
                        url: "{{url('kol/create_period')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: data,
                        success: function(resp) {
                            if(resp.code==1){
                                bg_class = 'bg-success';
                                self.period = JSON.parse(JSON.stringify(empty_model_period));
                                self.get_sale_period(self.id)
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                        },
                        error: function(xhr) {
                            console.log(xhr);
                        }
                    });
                },
                get_sale_period: function(kol_id){
                    self = this;
                    $.ajax({
                        url: '/index/kol/get_sale_period',
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        datatype: 'json',
                        data: {
                            kol_id: kol_id,
                        },
                        error: function (xhr) {
                            alert('失敗');
                            console.error(xhr);
                        },
                        success: function (response) {
                            self.sale_period = response;
                        },
                    });
                },
            }
        });

        @if(empty($data['kol'])==false)
        @foreach($data['kol'] as $vo)
            var kol_{{$vo->id}} = {
                id: "{{$vo->id}}",
                email: "{{$vo->email}}",
                password: "{{$vo->password}}",
                kol_name: "{{$vo->kol_name}}",
                real_name: "{{$vo->real_name}}",
                english_name: "{{$vo->english_name}}",
                category: "{{$vo->category}}",
                phone: "{{$vo->phone}}",
                mobile: "{{$vo->mobile}}",
                address: "{{$vo->address}}",
                address_memo: "{{$vo->address_memo}}",
                bank_name: "{{$vo->bank_name}}",
                bank_account: "{{$vo->bank_account}}",
                id_no: "{{$vo->id_no}}",
                memo: "{{$vo->memo}}",
                caller: null,

                sale_period: [],
                sale_record: [],
                selling_product: [],
                selling_product_record: [],
            }
            var kol_{{$vo->id}}_VM = new Vue({
                el: '#kol_{{$vo->id}}',
                data: kol_{{$vo->id}},
                methods: {
                    openBox: function () {
                        const keys = Object.keys(empty_model_data);
                        for (var i = 0; i < keys.length; i++) {
                            BoxVM[keys[i]] = this[keys[i]]
                        }
                        BoxVM.period = JSON.parse(JSON.stringify(empty_model_period));

                        BoxVM.caller = this;
                        if(this.id != 0){
                            BoxVM.get_sale_period(this.id);
                            this.get_sale_record(this.id);
                            this.get_selling_product(this.id);
                            this.get_selling_product(this.id, -1);

                            // 初始化頁籤功能
                            setTimeout(function(){ $( "#tabs" ).tabs({
                                beforeActivate: function(event, ui){
                                    var href = $(event.currentTarget).attr('href');
                                    if(href.slice(0,1)!='#'){
                                        window.open(href);
                                        event.preventDefault();
                                        return;
                                    }
                                }
                            }); }, 5);
                        }

                        $('#functionModal_btn').click();
                    },
                    get_sale_record: function(kol_id){
                        $.ajax({
                            url: '/index/kol/get_sale_record',
                            type: 'POST',
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            datatype: 'json',
                            data: {
                                kol_id: kol_id,
                                type: 2,
                            },
                            error: function (xhr) {
                                alert('失敗');
                                console.error(xhr);
                            },
                            success: function (response) {
                                BoxVM.sale_record = response;
                            },
                        });
                    },
                    get_selling_product: function(kol_id, is_using=''){
                        $.ajax({
                            url: `/index/kol/get_selling_product?kol_id=${kol_id}&is_using=${is_using}`,
                            type: 'GET',
                            datatype: 'json',
                            error: function (xhr) {
                                alert('失敗');
                                console.error(xhr);
                            },
                            success: function (response) {
                                if(is_using!=-1){
                                    BoxVM.selling_product = response;
                                }else{
                                    BoxVM.selling_product_record = response;
                                }
                            },
                        });
                    },
                }
            });
        @endforeach
        @endif

        function newBlock(){
            const keys = Object.keys(empty_model_data);
            const empty_model_data_new = JSON.parse(JSON.stringify(empty_model_data))
            for (var i = 0; i < keys.length; i++) {
                BoxVM[keys[i]] = empty_model_data_new[keys[i]]
            }
            BoxVM.period = JSON.parse(JSON.stringify(empty_model_period));
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.qaCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function multiDelete() {

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());

            if(multiId.value=='[]'){
                alert('請選擇網紅');
                return;
            }

            if(confirm("確定刪除？")){
                var form = document.createElement("form");
                form.action = "{{url('kol/multiDelete')}}";
                form.method = "post";
                form.className = 'd-none';

                multiId.name = "id";
                form.appendChild(multiId);
                
                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);
                document.body.appendChild(form);

                $('#block_block').show();
                form.submit();
                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }
    </script>

@endsection