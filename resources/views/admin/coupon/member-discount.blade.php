@extends('admin.Public.aside')
@section('title'){{Lang::get('H行銷項目')}} - {{Lang::get('優惠券管理')}}@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('H行銷項目')}}</li>
            <li><a href="{{url('Coupon/index')}}">{{Lang::get('優惠券管理')}}</a></li>
        </ul>
        <div class="searchbox">
            <form action="{{url('Coupon/index')}}" name="searchForm" method="get"  class="searchKeyBox flex-nowrap align-items-center">
                @csrf
                <input type="text" class="form-control mr-3" name="searchKey" value="{{$_GET['searchKey']??''}}" placeholder="{{Lang::get('名稱')}}">
                時間區間：
                <input type="date" class="date form-control mr-1" name="start" value="{{$_GET['start']??''}}" style="width:130px;"/>
                <input type="date" class="date form-control mr-1" name="end" value="{{$_GET['end']??''}}" style="width:130px;"/>
                <a class="btn sendbtn" onclick="searchForm.submit();">{{Lang::get('搜尋')}}</a>
            </form>
        </div>
        <div class="frame">
            <a href="{{url('Coupon/create')}}" class="btn clearbtn" >
                <i class="bi bi-plus-lg add small"></i>  {{Lang::get('新增')}}
            </a>
            <!-- 多項編輯開始 -->
            <span class="d-inline-block position-relative">
                <div class="edit">
                    {{Lang::get('編輯')}} <span class="bi bi-chevron-down"></span>
                </div>
                <div class="edit-item none">
                    <a onclick="multiOnline();">
                        <p class="mb-0">{{Lang::get('上架')}}&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                  
                    <a onclick="multiOffline();">
                        <p class="mb-0">{{Lang::get('下架')}}&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
               
                    <a onclick="multiDelete();" class="border-top">
                        {{Lang::get('刪除')}} <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
            </span>
            <!-- 多項編輯結束 -->
        </div>
        <div class="edit_form" >
            <table class="table table-rwd" style="min-width: 1400px;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=couponCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%;"></th>
                        <th style="width: 60px;">{{Lang::get('上下架')}}</span></th>
                        <th>{{Lang::get('編號')}}</th>
                        <th>{{Lang::get('名稱')}}</th>
                        <th>{{Lang::get('開始日期')}}</th>
                        <th>{{Lang::get('結束日期')}}</th>
                        <th style="width: 80px;">{{Lang::get('生產張數')}}</th>
                        <th style="width: 120px;">{{Lang::get('已領取張數')}}</th>
                        <th style="width: 120px;">{{Lang::get('已使用張數')}}</th>
                        <th style="width: 60px;">{{Lang::get('刪除')}}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['rowDataItems'] as $vo)
                    <tr id="coupon_{{$vo->id}}">
                        <td><input type="checkbox" class="couponCheckbox" alt="{{$vo->id}}"></td>
                        <td>
                            <label class="switch" style="display:inline-flex; margin-top: 5px;">
                                <input type="checkbox" v-model="online">
                                <span class="slider round"></span>
                            </label>
                        </td>
                        <td>{{$vo->number}}</td>
                        <td><a href="{{url('Coupon/show') . '?id=' . $vo->id}}">{{$vo->title}}</a></td>
                        <td>{{date("Y-m-d", $vo->start)}}</td>
                        <td>{{date("Y-m-d", $vo->end)}}</td>
                        <td>{{$vo->num}}</td>
                        <td>{{$vo->sellCount}}</td>
                        <td>{{$vo->useCount}}</td>
                        <td><span class="bi bi-trash" onclick="del_coupon({{$vo->id}})"></span></td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="text-center">
            {{$data['coupon']->links('pagination.customize')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script>
        $(function () {
            // 新增
            $('.add').click(function(){
                if($('.add-item').is(':visible')==true){
                    $('.add-item').hide();
                }
                else{
                    $('.add-item').show();
                }
            })
            // 編輯
            $('.edit').click(function(){
                if($('.edit-item').is(':visible')==true){
                    $('.edit-item').hide();
                }
                else{
                    $('.edit-item').show();
                }
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Coupon/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                    }else{
                        alert("{{Lang::get('操作失敗')}}");
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert("{{Lang::get('操作失敗')}}");
                    console.log(xhr);
                }
            });
        };

        @if(empty($data['coupon']) == false)
        @foreach($data['coupon'] as $vo)
            var coupon_{{$vo->id}} = new Vue({
                el: '#coupon_{{$vo->id}}',
                data: {
                    id: +"{{$vo->id}}",
                    online: +"{{$vo->online}}"
                },
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    }
                }
            });
        @endforeach
        @endif

        function getMultiId() {
            var multiIdArray = [];
            $('.couponCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function del_coupon(coupon_id){
            if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }
            location.href = "{{url('Coupon/delete')}}?id=" + coupon_id;
        }
        function multiDelete() {
            if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }
            var form = document.createElement("form");
            form.action = "{{url('Coupon/multiDelete')}}";
            form.method = "post";
            form.className = 'd-none';

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            multiId.name = "id";
            form.appendChild(multiId);
            document.body.appendChild(form);

            $('#block_block').show();
            form.submit();
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('coupon_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('coupon_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }
    </script>
@endsection