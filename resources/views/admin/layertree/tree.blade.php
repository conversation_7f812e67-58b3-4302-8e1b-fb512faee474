@extends('admin.Public.aside')
@section('title')分館分類樹@endsection

@section('css')
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/datepicker.css">
@endsection

@section('content')
    <iframe id="boxFormIframe" name="boxFormIframe" style="display: none;"></iframe>

    <!-- 新增/修改商品分類開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal main-modal fade" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">分類內容</h5>
                </div>
                <form name="boxForm" :action="action" method="post" target="boxFormIframe" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-body">
                        <div class="formblock">
                            <div class="item">本類名稱
                                <input v-model="title" name="title" type="text" class="form-control" value="女性保健">
                            </div>
                            <div class="item">
                                狀態
                                <select v-model="online" name="online" class="form-control" style="width: 120px;">
                                    <option value="1">開啟</option>
                                    <option value="0">隱藏</option>
                                    <option value="2">關閉</option>
                                </select>
                            </div>
                            <div class="item">
                                排序<input type="number" class="form-control" style="width: 120px;"  name="order_id" v-model="order_id">
                                <span class="remark text-danger">(排序越小越前面)</span>
                            </div>
                            <div class="item">開始日期　<input type="date" class="form-control" id="start" v-model="start"></div>
                            <div class="item">結束日期　<input type="date" id="end" class="form-control" v-model="end"></div>
                            <div class="item"><a href="###" @click="noTimeLimit" class="noendbtn">沒有結束日期</a></div>
                            
                            <input type="hidden" name="end" v-model="endTime">
                            <input type="hidden" name="start" v-model="startTime">
                            <input type="hidden" name="id" v-model="id">
                            <input type="hidden" name="parent_id" v-model="parent_id">
                            <input type="hidden" name="branch_id" v-model="branch_id">
                            <div class="item">
                                上傳圖片
                                <input id="image_del" type="hidden" name="image_del" value="0">
                                <div class="img-box">
                                    <span class="bi bi-pencil-square editbtn" ></span>
                                    <span class="bi bi-image"></span>
                                    <input id="input_file" type='file' class="upl" ref="img" name="image" accept="image/*" @change="previewImg">
                                    <img class="preview" :src="src"/>
                                </div>
                                <a href="###" @click="cancel_img" class="delate">
                                    <span class="bi bi-trash "></span>
                                </a>
                                <span class="text-danger remark d-inline" >建議大小：40*40</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <span class="mb-1">本類SEO設定(EX:健康食品,我愛念書)10組內</span>
                            <textarea class="form-control" rows="4" type="text" name="webtype_keywords"  v-model="webtype_keywords"></textarea>
                        </div>
                        <div>
                            <span class="mb-1">本類描述(隱藏資訊)200字內</span>
                            <textarea class="form-control" rows="8" type="text" name="webtype_description" v-model="webtype_description"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn sendbtn" @click="formSubmit">儲存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- 新增/修改商品分類結束 -->

    <!-- 新增分館開始 -->
    <div class="modal fade main-modal" id="functionModal2" tabindex="-1" role="dialog" aria-labelledby="functionModal2Label" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">新增館</h5>
                    
                </div>
                <div class="modal-body">
                    <div class="formblock">
                        <div >
                            <p>新館名稱：
                                <input class="newProductName form-control w-100" type="text" autofocus="">
                            </p>
                            <p>排序：
                                <input class="newProductOrder form-control w-100" type="number" value="0">
                            </p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn sendbtn" onclick="add_masterlayer(this, 'index/product/product')">儲存</a>
                    <button class="btn clearbtn" data-dismiss="modal" aria-label="Close">取消</a>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增分館結束 -->

    <div id="content">
    
        <ul id="title" class="brand-menu">
            <li><a href="###">F商品管理區</a></li>
            <li><a href="###">分館分類樹</a></li>
        </ul>
        <div id="treeMV">
            <div class="frame">
               
                <a href="###" class="btn clearbtn mr-3" @click="open_add_modal"><span class="bi bi-plus-lg add" ></span> 新增</a>
                <distributors_area ref="distributors_area" :my_distributor_id="{{$data['my_distributor_id']}}"></distributors_area>
            </div>
            <div class="treebox">
                <div class="tableListA">
                    <!-- 館 -->
                    <div class="tree_column masterlayer view_frame">
                        <div :class="['column_item', item.online=='0'?'hd':'', item.online=='2'?'cs':'', openlayer==index?'active':'']"  v-for="(item, index) in tree_list" @click="changeOpenLayer(index)">
                            <div class="item">
                                <div class="tree-select">
                                    <div class="list">
                                        <span class="name">排序</span><input type="number" class="border" v-model="item.order_id" @blur="change_order(item.id, item.order_id)">
                                    </div>
                                    <div class="list">
                                        <span class="name">狀態</span>
                                        <select v-model="item.online" class="border" name="online" @change="change_data(item, 'online')">
                                            <option value="1">開啟</option>
                                            <option value="0">隱藏</option>
                                            <option value="2">關閉</option>
                                        </select>
                                    </div>
                                    <template v-if="distributors_current==0">
                                        <div class="list">
                                            <span class="name">選單</span>
                                            <select v-model="item.show_on_nav" class="border" name="show_on_nav" @change="change_data(item, 'show_on_nav')">
                                                <option value="1">顯示</option>
                                                <option value="0">隱藏</option>
                                            </select>
                                        </div>  
                                    </template>
                                </div>
                                <p class="title w-100"><input type="text" class="form-control p-1 border" v-model="item.title" @blur="change_data(item, 'title')"></p>
                                <template v-if="distributors_current==0">
                                    <p>選單商品<span class="remark text-danger d-inline">(請填商品id以,分隔)</span>：
                                        <input class="form-control p-1 border" type="text" v-model="item.recommend" @blur="change_data(item, 'recommend')">
                                    </p>
                                </template>
                                <div class="mb-3">
                                    <span>外崁EDM ID：</span>
                                    <input type="number" min="1" class="form-control border" v-model="item.out_ID" @blur="change_data(item, 'out_ID')">
                                </div>
                                <span v-text="'總含本階層以下總數：'+count_all_product(item)"></span>&nbsp;
                                <span v-text="'本層數：' + item.product_num"></span><br>
                            </div>
                            <div class="btn_area">
                                <span class="bi bi-eye" @click="view_product(item.id)"></span>
                                <span class="bi bi-pencil-square treeEditBtn" @click="change_page('/{{$data['admin_type']}}/product/index?id='+item.id)"></span>
                                <span class="bi bi-trash  productTrash" @click="del_maseter_layer(item.id)"></span>
                                <span class="bi bi-plus-lg" @click="open_master_box()">子階層</span>
                                <span class="bi bi-plus-lg" @click="change_page('/{{$data['admin_type']}}/productinfo/allcreate?prev_id='+item.id)">商品</span>
                            </div>
                        </div>
                    </div>

                    <!-- 分類 -->
                    <div class="tree_column view_frame" v-if="tree_list[openlayer]">
                        <!-- ////////一基本單位////////
                            <div class="tree_row">
                                <div class="column_item">
                                    <p>分類1-1</p>
                                </div>
                                <div class="tree_column"></div>
                            </div>
                        //////////////////////////////-->
                        <!---------------------------------------------------->
                        <tree_template v-for="(item, index) in tree_list[openlayer]['content']" :key="index" :tree="item"></tree_template>
                        <!---------------------------------------------------->
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/moment.min.js"></script>
    
    <script type="text/javascript">
        var admin_type = "{{$data['admin_type']}}";

        /* 分館、分類共用function */
        var change_data_f = function (controller, id, column, value){
            // console.log(id+':'+online);
            postData = {};
            postData['id'] = id;
            postData[column] = value;
            $.ajax({
                url: "/"+admin_type+"/"+controller+"/cell_ctrl",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: postData,
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                    }else{
                        msg = response.message ? response.message : '更改失敗';
                        Vue.toasted.show(msg, vt_error_obj);
                    }
                },
                error: function(xhr) {
                    Vue.toasted.show('更改失敗', vt_error_obj);
                    console.log(xhr);
                }
            });
        }
        var change_f = function (controller, data){
            $.ajax({
                url: "/"+admin_type+"/"+controller+"/update",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                datatype: 'json',
                data: data,
                error: function (xhr) {
                },
                success: function (response) {
                    bg_set = response.code ? vt_success_obj : vt_error_obj;
                    Vue.toasted.show(response.msg, bg_set);
                    get_tree_data();
                }
            });
        }
        var del_layer_f = function(controller,id){
            if(confirm("確定刪除此階層？")){
                $.ajax({
                    url: "/"+admin_type+"/"+controller+"/delete?id="+id,
                    type: 'GET',
                    datatype: 'json',
                    error: function (xhr) {
                    },
                    success: function (response) {
                        bg_set = response.code ? vt_success_obj : vt_error_obj;
                        Vue.toasted.show(response.msg, bg_set);
                        get_tree_data();
                    }
                });
            }
        }
    </script>
    <script type="text/javascript">
        // 供應商切換模組串接-開始
        function init_after_get_distributors(){
            get_tree_data();
        }
        function click_distributor_emit(id){
            treeMV.distributors_current = id;
            treeMV.get_tree_data();
        }
        // 供應商切換模組串接-結束

        const my_distributor_id = "{{$data['my_distributor_id']}}";
        var tree_data = {
            tree_list: [
                /*參考資料格式*/
                // {'name':"分館", 'product_num':5, 'id':'', 'order_id':0, 'content':[/*下一層內容*/]},
            ],
            openlayer:0,
            distributors_current: my_distributor_id,
            my_distributor_id: my_distributor_id,
        }
        function get_tree_data(){
            Vue.toasted.show('資料載入中',{duration:1500, className: ["toasted-primary", 'bg-success']});
            tree_data['tree_list'] = [];

            $.ajax({
                url: "{{url('layertree/get_product_tree')}}?distributor_id="+treeMV.distributors_current, //請求的url地址
                dataType: "json", //返回格式為json
                type: "GET", //請求方式
                success: function(req) {
                    // console.log(req)
                    tree_data['tree_list'] = req;

                    Vue.toasted.show('載入完成',{duration:1500, className: ["toasted-primary", 'bg-success']});
                },
                error: function() {
                    //請求出錯處理
                }
            });
        }

        Vue.component('tree_template',{
            template: `
                <div class="tree_row">
                    <div :class="['column_item', tree.online=='0'?'hd':'', tree.online=='2'?'cs':'']">
                        <div class="item">
                            <div class="tree-select">
                                <div class="list"> <span class="name">排序:</span><input type="number" class="border" v-model="tree.order_id" @blur="change_order(tree.id, tree.order_id, tree.parent_id, tree.branch_id)"></div>
                                <div class="list"> <span class="name">狀態</span>
                                    <select class="border" v-model="tree.online" name="online" @change="change_data(tree, 'online')">
                                        <option value="1">開啟</option>
                                        <option value="0">隱藏</option>
                                        <option value="2">關閉</option>
                                    </select>
                                </div>
                            </div>
                            <p class="title w-100"><input type="text" class="form-control p-1 border" v-model="tree.title" @blur="change_data(tree, 'title')"></p>
                            <span v-text="'總含本階層以下總數：'+count_all_product(tree)"></span>&nbsp;
                            <span v-text="'本層數：' + tree.product_num"></span><br>
                            
                        </div>
                        <div class="btn_area">
                            <span class="bi bi bi-eye" @click="view_product(tree.id)"></span>
                            <span class="bi bi-pencil-square treeEditBtn" @click="open_second_box(tree, 'edit')"></span>
                            <span class="bi bi-trash" @click="del_second_layer(tree.id)"></span>
                            <span class="bi bi-plus-lg" @click="open_second_box(tree, 'create', tree.id)">子階層</span>
                            <span class="bi bi-plus-lg" @click="create_second_product(tree.branch_id)">商品</span>
                        </div>
                    </div>
                    <div class="tree_column" v-if="tree.content.length>0">
                        <tree_template v-for="(item, index) in tree.content" :key="index" :tree="item"></tree_template>
                    </div>
                </div>
            `,
            props: {
                tree: Object
            },
            computed:{
            },
            methods:{ 
                change_page: function(url){
                    window.open(url);
                },
                open_second_box: function (item, type, branch_id = 0) {
                    if(type=="create"){
                        BoxVM.id = 0;
                        BoxVM.title = '';
                        BoxVM.src = '';
                        BoxVM.start = '';
                        BoxVM.end = '';
                        BoxVM.webtype_keywords = '';
                        BoxVM.webtype_description = '';
                        BoxVM.caller = null;
                        BoxVM.action = "{{url('typeinfo/create')}}";
                        BoxVM.parent_id = treeMV.tree_list[treeMV.openlayer]['id'];
                        BoxVM.branch_id = branch_id;
                        BoxVM.online = 1;
                        BoxVM.order_id = 0;
                    }else if(type=="edit"){
                        BoxVM.id = item.id;
                        BoxVM.title = item.title;
                        BoxVM.src = item.pic ? '/public/static/index/' + item.pic : '';
                        BoxVM.start = item.start ? this.timestampToTime(item.start) : '';
                        BoxVM.end = item.end ? this.timestampToTime(item.end) : '';
                        BoxVM.webtype_keywords = item.webtype_keywords;
                        BoxVM.webtype_description = item.webtype_description;
                        BoxVM.caller = item;
                        BoxVM.action = "{{url('typeinfo/update')}}";
                        BoxVM.parent_id = treeMV.tree_list[treeMV.openlayer]['id'];
                        BoxVM.branch_id = item.branch_id;
                        BoxVM.online = item.online;
                        BoxVM.order_id = item.order_id;
                    }
                    $('#functionModal_btn').click();
                },
                del_second_layer: function(id){
                    del_layer_f('typeinfo',id);
                },
                timestampToTime: function(timestamp) {
                    var date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
                    var Y = date.getFullYear() + '-';
                    var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
                    var D = this.pad(date.getDate(),2);
                    // var h = this.pad(date.getHours(),2) + ':';
                    // var m = this.pad(date.getMinutes(),2) + ':';
                    // var s = this.pad(date.getSeconds(),2);
                    // return Y+M+D+h+m+s;
                    return Y+M+D;
                },
                pad: function(num, n) {
                    // console.log(num+':'+n)
                    if(n>num){
                      return Array(n).join(0) + String(num)
                    }else{
                        return num;
                    }
                },
                create_second_product: function(branch_id){
                    console.log(branch_id);

                    if(branch_id == 0){
                        this.change_page('/'+admin_type+'/productinfo/allcreate?parent_id='+this.tree.id)
                    }else{
                        this.change_page('/'+admin_type+'/productinfo/allcreate?branch_id='+this.tree.id)
                    }
                },
                change_data: function(item, column){
                    change_data_f('typeinfo',item.id, column, item[column]);
                },
                change_order: function(id,order,parent_id,branch_id){
                    data = {
                        id:id,
                        order_id: order,
                        parent_id: parent_id,
                        branch_id: branch_id,
                    };
                    change_f('typeinfo',data);
                },
                count_all_product: function(obj_list){
                    num = obj_list['product_num'];
                    for(var i=0; i<obj_list['content'].length; i++) {
                        num = num + this.count_all_product(obj_list['content'][i])
                    }
                    return num;
                },
                view_product: function(id){
                    window.open("{{url('All/index')}}?searchBranch="+id);
                },
            }
        })

        // 館分類樹
        var treeMV = new Vue({
                el: '#treeMV',
                data: tree_data,
                computed: {
                },
                methods: {
                    open_add_modal: function(){
                        if(this.my_distributor_id!=this.distributors_current){
                            Vue.toasted.show('無法新增他人的分館',{duration:1500, className: ["toasted-primary", "bg-warning"]});
                        }else{
                            $('#functionModal2').modal('show');
                        }
                    },
                    get_tree_data: function(id){
                        get_tree_data();
                    },
                    change_page: function(url){
                        window.open(url);
                    },
                    changeOpenLayer: function (index) {
                        this.openlayer = index;
                    },
                    del_maseter_layer: function(id){
                        del_layer_f('product',id);
                    },
                    open_master_box: function () {
                        BoxVM.id = 0;
                        BoxVM.title = '';
                        BoxVM.src = '';
                        BoxVM.start = '';
                        BoxVM.end = '';
                        BoxVM.webtype_keywords = '';
                        BoxVM.webtype_description = '';
                        BoxVM.caller = null;
                        BoxVM.action = "{{url('typeinfo/create')}}";
                        BoxVM.parent_id = treeMV.tree_list[treeMV.openlayer]['id'];
                        BoxVM.branch_id = 0;
                        BoxVM.online = 1;
                        BoxVM.order_id = 0;
                        $('#functionModal_btn').click();
                    },
                    change_data: function(item, column){
                        change_data_f('product',item.id, column, item[column]);
                    },
                    change_order: function(id,order){
                        data = {
                            id:id,
                            order_id: order,
                        };
                        change_f('product',data);
                    },
                    count_all_product(obj_list){
                        num = obj_list['product_num'];
                        for(var i=0; i<obj_list['content'].length; i++) {
                            num = num + this.count_all_product(obj_list['content'][i])
                        }
                        return num;
                    },
                    view_product: function(id){
                        window.open("{{url('All/index')}}?searchPrev="+id);
                    },
                }
            });

        function add_masterlayer(item, type) {
            var input = $('.newProductName');
            var input_Order = $('.newProductOrder');
            if(input.val() == ''){
                Vue.toasted.show('請輸入名稱',{duration:1500, className: ["toasted-primary", "bg-warning"]});
                return
            }

            if(type.indexOf('product')>0){
                var post_url =  "{{url('Product/create')}}"
            }else if(type.indexOf('distribution')>0){
                var post_url = "{{url('stronghold/create')}}"
            }else{
                Vue.toasted.show('新增有誤', vt_error_obj);
                return
            }
            $.ajax({
                url: post_url,
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                datatype: 'json',
                data: {
                    title: input.val(),
                    order_id: input_Order.val(),
                },
                error: function(xhr) {
                    console.error(xhr);
                },
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('新增成功', vt_success_obj);
                        //console.log(response);
                        input.val('');
                        input_Order.val(0);
                        get_tree_data();
                        $('#functionModal2').modal('hide');
                    }else{
                        console.error(response);
                    }
                }
            });
        }

        // 編輯/新增分類
        var Box = {
            id: 0, parent_id: 0, branch_id: 0, title: '', start: '',
            end: '', src: '', action: '',
            caller: null, webtype_keywords: '', webtype_description:'', online: 0, order_id:0,
        }
        var BoxVM = new Vue({
            el: '#Box',
            data: Box,
            computed: {
                startTime: function () {
                    // console.log(this.start);
                    return moment(this.start, 'YYYY-MM-DD').unix();
                },
                endTime: function () {
                    // console.log(this.end);
                    return moment(this.end, 'YYYY-MM-DD').unix();
                }
            },
            methods: {
                formSubmit: function () {
                    $('#block_block').show();
                    setTimeout(function(){
                        document.boxForm.submit();
                    }, 50);
                },
                previewImg: function () {
                    console.log(this.$refs.img.files);
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        Box.src = e.target.result;
                        $('#image_del').val(0);
                    }
                    reader.readAsDataURL(this.$refs.img.files[0]);
                },
                cancel_img: function () {
                    $('#image_del').val(1);
                    Box.src='';
                },
                updateCallerData: function () {
                    this.caller.src = this.src;
                    this.caller.title = this.title;
                    this.caller.start = this.start;
                    this.caller.end = this.end;
                    $('#functionModal').modal('hide');
                },
                noTimeLimit: function () {
                    this.end = '';
                    this.$forceUpdate();
                }
            }
        });

        $('#boxFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('.text-effect span').text();
            if(uploadStatus == "更新成功" || uploadStatus == "新增成功"){
                Vue.toasted.show(uploadStatus,  vt_success_obj);
                get_tree_data();
                $('#input_file').val("");
                $('#functionModal').modal('hide');
            }else{
                Vue.toasted.show(uploadStatus, vt_error_obj);
            }
            $('#block_block').hide();
        });
    </script>
@endsection