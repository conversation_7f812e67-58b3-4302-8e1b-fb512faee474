@extends('admin.Public.aside')

@section('title')
J參數設定區 > 系統信管理
@endsection

@section('css')
@endsection

@section('content')
    <!-- 文字編輯器 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="changeTextBox">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" v-text="title + '內容'">內容</h5>
                </div>
                <div class="modal-body">
                    <input type="hidden" v-model="content">
                    <input type="hidden" id="editor">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>

    <div id="content">
        <p id="title">J參數設定區 > 系統信管理</p>
        <div id="systemEmailView" style="margin:50px">
            @if(!isset(config('control.close_function_current')['會員管理']))
                <a class="consentLink" href="###" @click="openBox(0, '註冊成功信')">註冊成功信</a>
                <a class="consentLink" href="###" @click="openBox(3, '忘記密碼信')">忘記密碼信</a>
            @endif

            @if(!isset(config('control.close_function_current')['客戶來函']))
                <a class="consentLink" href="###" @click="openBox(1, '回函成功信')">回函成功信</a>
            @endif

            @if(!isset(config('control.close_function_current')['訂單管理']))
                <a class="consentLink" href="###" @click="openBox(2, '訂購成功信')">訂購成功信</a>
                <a class="consentLink" href="###" @click="openBox(4, '訂單取消信')">訂單取消信</a>
                @if(config('control.control_register')==1)
                    <a class="consentLink" href="###" @click="openBox(6, '活動提醒信')">活動提醒信</a>
                    <a class="consentLink" href="###" @click="openBox(7, '活動取消信')">活動取消信</a>
                @endif
            @endif

            @if(!isset(config('control.close_function_current')['商品問答']))
                <a class="consentLink" href="###" @click="openBox(5, '商品詢問信')">商品詢問信</a>
            @endif
        </div>
    </div>
@endsection

@section('ownJS')
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        KindEditor.ready(function(K) {
            editor = K.create('#editor', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items:['source', '|',  'hr','|','emoticons','|','forecolor','bold', 'italic', 'underline','link', 'unlink',],
                width:'100%',
                height:'500px',
                resizeType:0
            });
        });
    </script>
    <script>
        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Admin/system_email_update')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.code ==1){
                        alert('更新成功');
                        changeTextBoxVM.updateCallerData();
                        $('#functionModal').modal('hide');
                    }else{
                        alert('更新失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更新失敗');
                    console.log(xhr);
                }
            });
        };

        /*系統信種類*/
        var letter_type =  [
            {column:"signup_complete", title:""},
            {column:"contact_complete", title:""},
            {column:"order_complete", title:""},
            {column:"forget_password", title:""},
            {column:"order_cancel", title:""},
            {column:"product_qa", title:""},
            {column:"act_remind", title:""},
            {column:"act_cancel", title:""},
        ];
        var changeTextBox = {
            number: 0,
            content: '',
            title: '',
            visibility:　false,
            caller: null
        }
        var changeTextBoxVM = new Vue({
            el: '#changeTextBox', 
            data: changeTextBox,
            watch: {
                content: function (val) {
                    editor.html(val);
                }
            },
            methods: {
                ajaxSubmit: function () {
                    editor.sync();
                    this.content = editor.html();
                    var Data = {id: 1}
                    Data.column = letter_type[this.number]['column'];
                    Data.value = this.content.replace(/\n/g, '');

                    this.blockCtrl(Data);
                },
                updateCallerData: function () {
                    content_num = 'content'+this.number;
                    this.caller[content_num] = this.content;
                }
            }
        });

		///////andy/////多行文字串//////
		function heredoc(fn) {
			return fn.toString().replace(/[\\]/g,"") + '\n'
		}

		var tmpl = [];
        /*註冊成功信*/
     	tmpl[0] = heredoc(function(){
			/*{!! $data['system_email']['signup_complete'] !!}*/
		});
        /*回函成功信*/
		tmpl[1] = heredoc(function(){
			/*{!! $data['system_email']['contact_complete'] !!}*/
		});
        /*訂購成功信*/
		tmpl[2] = heredoc(function(){
			/*{!! $data['system_email']['order_complete'] !!}*/
		});
        /*忘記密碼信*/
        tmpl[3] = heredoc(function(){
            /*{!! $data['system_email']['forget_password'] !!}*/
        });
        /*訂單取消信*/
        tmpl[4] = heredoc(function(){
            /*{!! $data['system_email']['order_cancel'] !!}*/
        });
        /*商品詢問信*/
        tmpl[5] = heredoc(function(){
            /*{!! $data['system_email']['product_qa'] !!}*/
        });
        /*活動提醒信*/
        tmpl[6] = heredoc(function(){
            /*{!! $data['system_email']['act_remind'] !!}*/
        });
        /*活動取消信*/
        tmpl[7] = heredoc(function(){
            /*{!! $data['system_email']['act_cancel'] !!}*/
        });

		for(var i=0; i<tmpl.length; i++){
			tmpl[i] = tmpl[i].split('*');
			delete tmpl[i][0];
			var lastnum = tmpl[i].length -1;
			delete tmpl[i][lastnum];
		}
		/////////////////////////////////////////////

        var systemEmailModel = {};
        for(var i=0; i<tmpl.length; i++){
            content_num = 'content' + (i);
            systemEmailModel[content_num] = tmpl[i].join('');
        }

        var systemEmailVM = new Vue({
            el: "#systemEmailView",
            data: systemEmailModel,
            methods:{
                openBox: function (number, title) {
                    content_num = 'content'+number;
                    changeTextBox.content = this[content_num];
                    changeTextBox.title = title;

                    changeTextBox.number = number;
                    changeTextBox.visibility = true;
                    changeTextBox.caller = this;
                    $('#functionModal_btn').click();
                }
            }
        });
    </script>
@endsection