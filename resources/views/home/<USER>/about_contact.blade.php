@extends('home.Public.mainTpl')

@section('title')
{{$data['frontend_menu']['about']['second_menu']['about_contact']['name']}} - {{$data['frontend_menu']['about']['name']}} | {{$data['seo'][0]['title']}}
@endsection

@section("css")
@endsection

@section("content")
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['about']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu']['about']['pic']}});">
            <div class="container">
                <h2 class="page-title" style="color:{{$data['frontend_menu']['about']['text_color']}}">
                    {{$data['frontend_menu']['about']['second_menu']['about_contact']['name']}}</h2>
                    <!-- <span class="enText">{{$data['frontend_menu']['about']['en_name']}}</span> -->
            </div>
        </section>
        @endif
    @endif
    
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                @if(empty(config('control.close_function_current')['關於我們']))
                    <li><a>{{$data['frontend_menu']['about']['name']}}</a></li>
                @endif
                <li id="aboutDirectoryText" href="{{url('About/about_contact')}}">{{$data['frontend_menu']['about']['second_menu']['about_contact']['name']}}</li>
            </ul>
        </div>
    </section>
    <section class="container  aboutUsBox">
        <div class="tabRow">
            @if($data['distributor_id'] == 0 )
                <div class="tabTitleBox">
                    <ul class="nav tabNavBox">
                        @if(empty(config('control.close_function_current')['關於我們']))
                            <li><a href="{{url('About/about_story')}}"><span>{{$data['frontend_menu']['about']['second_menu']['about_story']['name']}}</span></a></li>
                        @endif
                        <li><a href="{{url('About/about_map')}}"><span>{{$data['frontend_menu']['about']['second_menu']['about_map']['name']}}</span></a></li>
                        @if(empty(config('control.close_function_current')['客戶來函']))
                            <li><a href="{{url('About/about_contact')}}"><span>{{$data['frontend_menu']['about']['second_menu']['about_contact']['name']}}</span></a></li>
                        @endif
                    </ul>
                </div>
            @endif
            <div class="tab-pane tabContent" id="contactUs">
                <div class="content contact">
                    <!-- //////////////////////////////////////////////////////// -->
                    <div class="form-row mb-2rem">
                        <div class="col-md-6">
                            <div class="form-row">
                                <div class="form-group col-12">
                                    <label>{{Lang::get('問題分類')}}</label>
                                    <div class="text__center">
                                        <select class="form-control" v-model="model.wanted">
                                            <option value="{{Lang::get('訂單問題')}}">{{Lang::get('訂單問題')}}</option>
                                            <option value="{{Lang::get('商品詢問')}}">{{Lang::get('商品詢問')}}</option>
                                            @foreach($data['contact_type'] as $vo)
                                                <option value="{{$vo}}">{{$vo}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-12">
                                    <label for="name">{{Lang::get('您的姓名')}}</label><span class="smallText">{{Lang::get('必填')}}</span>
                                    <input type="text" class="form-control" id="name" v-model="model.name" autocomplete="off">
                                    <input type="hidden" v-model="model.distributor_id">
                                </div>
                                <div class="form-group col-12">
                                    <label for="email">{{Lang::get('電子信箱')}}</label><span class="smallText">{{Lang::get('必填')}}</span>
                                    <input type="email" class="form-control" id="email" v-model="model.email" autocomplete="off">
                                </div>

                                <div class="form-group col-12" v-if="model.wanted=='{{Lang::get('訂單問題')}}'">
                                    <label for="order_number">{{Lang::get('訂單編號')}}</label><span class="smallText">{{Lang::get('必填')}}</span>
                                    <input type="text" class="form-control" id="order_number" v-model="model.order_number" autocomplete="off">
                                </div>
                                <div class="form-group col-12" v-if="model.wanted=='{{Lang::get('商品詢問')}}'">
                                    <label for="order_number">{{Lang::get('商品ID或名稱')}}</label><span class="smallText">{{Lang::get('必填')}}</span>
                                    <input type="text" class="form-control" id="prod_id" v-model="model.prod_id" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-row">
                                <div class="form-group col-12">
                                    <label for="in_tel">{{Lang::get('聯絡電話')}}</label>
                                    <input type="text" class="form-control" id="in_tel" v-model="model.homephone" autocomplete="off">
                                </div>
                                <div class="form-group col-12">
                                    <label for="in_mobile">{{Lang::get('行動電話')}}</label>
                                    <input type="text" class="form-control" id="in_mobile" v-model="model.phone" autocomplete="off">
                                </div>
                                <div class="form-group col-12">
                                    <label>{{Lang::get('連絡時間')}}</label>
                                    <div class="text__center">
                                        <select class="form-control" v-model="model.freeTime">
                                            <option value="{{Lang::get('皆可')}}" selected>{{Lang::get('皆可')}}</option>
                                            <option value="09:00-12:00">09:00-12:00</option>
                                            <option value="13:00-18:00">13:00-18:00</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center w-100">   
                            <span class="smallText mb-3">{{Lang::get('請確認聯絡方式是否填寫正確，以確保能收到後續處理回覆。')}}</span>
                        </div>
                        <div class="form-group col-12">
                            <label for="in_content">{{Lang::get('問題描述')}}</label>
                            <textarea class="form-control mb_1rem" id="in_content" rows="6" autocomplete="off" v-model="model.message"></textarea>
                        </div>
                    </div>
                    <div class="form-row bottomBox">
                        @if(config('extra.shop.google_recaptcha_sitekey'))
                        <div class="w-100 d-flex justify-content-center mb-3">
                            <div class="g-recaptcha text-center" data-callback="captcha_onclick" 
                                 data-sitekey="{{config('extra.shop.google_recaptcha_sitekey')}}">
                            </div>
                            <input type="hidden" id="recaptchaValidator" />
                        </div>
                        @endif

                        <div class="col-12 text-center">
                            <a href="##" class="submitBtn" @click="send_contactForm">
                                <span class="sendbtn rwd_floatR text-white">
                                {{Lang::get('送出')}}
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////////////////////////////////////////////////// -->
    </section>
@endsection

@section("ownJS")

<script type="text/javascript">
        
        let contactUs_data = {
            model: {
                wanted: '{{$data["default_wanted"]}}',
                name: "",
                distributor_id: '{{$data["distributor_id"]}}',
                email: "",
                order_number: '{{$data["order_number"]}}',
                prod_id: '{{$data["prod_id"]}}',
                homephone: "",
                phone: "",
                freeTime: "{{Lang::get('皆可')}}",
                message: '{{$data["default_message"]}}',

                recaptcha: '',
            }
        }
        
        var contactUsVM = new Vue({
            el: '#contactUs',
            data: contactUs_data,
            methods: {
                send_contactForm: function(){
                    
                    self = this;
                    if(self.model.name==''){
                        Vue.toasted.show("{{Lang::get('請輸入姓名')}}", vt_warning_obj);
                        return;
                    }
                    if(self.model.email==''){
                        Vue.toasted.show("{{Lang::get('請輸入信箱')}}", vt_warning_obj);
                        return;
                    }

                    if(self.model.wanted=="{{Lang::get('商品詢問')}}"){
                        if(self.model.prod_id==''){
                            Vue.toasted.show("{{Lang::get('請輸入商品ID或名稱')}}", vt_warning_obj);
                            return;
                        }
                    }else{
                        self.model.prod_id = '';
                    }
                    if(self.model.wanted=="{{Lang::get('訂單問題')}}"){
                        if(self.model.order_number==''){
                            Vue.toasted.show("{{Lang::get('請輸入訂單編號')}}", vt_warning_obj);
                            return;
                        }
                    }else{
                        self.model.order_number = '';
                    }

                    self.model.recaptcha = $('#recaptchaValidator').val();

                    $('#body_block').show()
                    $.ajax({
                        method: 'post',
                        headers: {
                                'X-CSRF-Token': csrf_token 
                        },
                        dataType : 'json',
                        url: "{{url('About/doContact')}}",
                        data: self.model,
                        success: function(resp){
                            $('#body_block').hide();
                            if(resp.code){
                                Swal.fire({
                                    title: resp.msg,
                                    icon: 'success',
                                    content:'',
                                    confirmButtonText:"{{Lang::get('確認')}}",
                                    confirmButtonColor: 'var(--btn-mainlink)',
                                }).then((result) => {
                                    location.reload();
                                });
                            }else{
                                Vue.toasted.show(resp.msg, vt_error_obj);
                            }
                        },
                        error: function(resp){
                            Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                            $('#body_block').hide();
                        },
                    });
                },
            },
        });
        
    </script>

    @if(config('extra.shop.google_recaptcha_sitekey'))
        <!-- 機器人驗證 -->
        <script type="text/javascript">
            function captcha_onclick() {
                document.getElementById('recaptchaValidator').value = grecaptcha.getResponse();
            }
            window.addEventListener('pageshow', (event) => {
                if(grecaptcha.getResponse()){
                    grecaptcha.reset();
                }
            });
        </script>
    @endif
@endsection