<div class="accordion w-100" id="proAccordion">
    <div class="top-menu"> {!! Lang::get('產品分類<span>menu</span>') !!}</div>
    <div class="item-title product_menu_phone" aria-expanded="false"
         data-toggle="collapse" data-target="#cardBox_area">
        <h4 class="">
            @if(isset($data['title_array']))
                {{$data['frontend_menu'][strtolower($data['controller'])]['name']}}
                @foreach($data['title_array'] as $ta)
                    / {{$ta['title']}}
                @endforeach
            @else
                {!! Lang::get('產品分類<span>menu</span>') !!}
            @endif
        </h4>
        <i class="position-relative"></i>
    </div>
    <!-- 產品選單 -->
    <div :class="['cardBox_area', window_innerWidth()<1200 ? 'collapse' : '']" id="cardBox_area">
        <div class="cardBox" v-for="item in sideProductMenu">
            <div class="cardBox-header">
                <a href="javascript:void(0);" aria-expanded="true"
                   data-toggle="collapse" :data-target="'#collapse' + item.id"
                   v-if="item.subType.length>0" :class="['protitle', item.show ? '' : 'collapsed']">
                    <div class="d-flex align-items-center pro-menu-title">
                        <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + item.pic" v-if="item.pic">
                        <span v-text="item.title"></span>
                    </div>
                </a>
                <a :href="'/index/product/'+ item.action +'?id=' + item.id" v-if="item.subType.length==0">
                    <div class="d-flex align-items-center pro-menu-title">
                        <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + item.pic" v-if="item.pic">
                        <span v-text="item.title"></span>
                    </div>
                </a>
            </div>
            <div :id="'collapse' + item.id" :class="['collapse', item.show]" data-parent="#leftBox">
                <ul class="navList">
                    <li v-for="prop in item.subType">
                        <a :href="'/index/product/typeinfo?id=' + prop.id">
                            <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + prop.pic" v-if="prop.pic">
                            <span v-text="prop.title"></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!-- 活動專區選單 -->
    <!-- <div class="cardBox" v-for="item in activityMenu">
        <div class="cardBox-header">
            <a :href="item.url">
                <div class="d-flex align-items-center">
                    <span v-text="item.title"></span>
                </div>
            </a>
        </div>
    </div> -->
</div>
