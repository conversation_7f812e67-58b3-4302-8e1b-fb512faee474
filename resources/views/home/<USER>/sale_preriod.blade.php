@extends('home.Public.mainTpl')

@section('title'){{Lang::get('銷售列表')}} - {{Lang::get('網紅後台')}}@endsection

@section('css')
    <style>
        .hide{
            display: none;
        }

        .memberContentBox .bindingBox a.bindingBtn.use-btn:hover{
            cursor: default;
        }

        .orderTable td, .orderTable th{ text-align: center; }
        .orderTable img{
            width: 50px;
        }

        .tableListA table.productTable{
            border: none;
        }
        .productTable img{
            max-width: 100px;
        }
        .tableListA .productTable tr td{
            background: #fff;
            border: none;
            width: 20%;
        }
        ul.tabNavBox{margin-bottom: 0;}
        .activeBtn{background-color: transparent !important; color: var(--btn-link-white)!important; }
        .tabNavBox input{border:none !important;background-color: transparent !important; color: var(--btn-sublink)!important;display: block;width: 100%;height: 100%; cursor: pointer;}
        .tabNavBox input:hover,ul.tabNavBox li a:hover{color: var(--btn-link-white)!important;}
    </style>
@endsection

@section('content')
    <div>
        <section class="directoryRow">
            <div class="container">
                <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Kol/kol_data')}}">{{Lang::get('網紅後台')}}</a></li>
                <li><a href="{{url('Kol/sale_list')}}">{{Lang::get('銷售列表')}}</a></li>
                </ul>
            </div>
        </section>

        <section class="container max-wideVersion productPublic">
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->

            <div id="itemBox" class="memberInforBox">

                <div id="leftBox">
                    <!-- /////////////////////////////////////////// -->
                    @include('home.kol.kol_menu')
                    <!-- /////////////////////////////////////////// -->
                </div>

                <div id="rightContentBox" class="innerPageBox memberContentBox">
                    <div class="paddingSpacing">
                        <div class="pack">
                            <div class="memberTop">
                                <div class="titleBox">
                                    <div class="title">
                                        <h3>{{Lang::get('銷售列表')}}</h3>
                                    </div>    
                                </div>
                            </div>

                            <div class="d-flex justify-content-center mb-2">
                                <form action="" id="searchForm" name="searchForm" method="get">
                                    @csrf
                                    {{Lang::get('開始時間區間')}}
                                    <input type="date" name="start" placeholder="{{Lang::get('開始時間')}}" value="{{$data['start']}}">~
                                    <input type="date" name="end" placeholder="{{Lang::get('結束時間')}}" value="{{$data['end']}}">
                                    <input type="hidden" name="id" value="{$kol.id}">
                                    <input type="hidden" name="type" value="{{$data['type']}}">
                                    <input type="hidden" name="page" value="{$page}">
                                    <span class="d-inline-block">
                                        <a class="button" onclick="$('#searchForm').submit();">{{Lang::get('搜尋')}}</a>
                                        <a class="button" onclick="location.href='/index/kol/sale_list?type={{$data['type']}}'">
                                            {{Lang::get('清除搜尋')}}
                                        </a>
                                    </span>

                                </form>
                            </div>

                            <!--查看類型-->
                            <ul class="nav tabNavBox justify-content-center">
                                <li :class="['nav-item','{{$data['type']}}' == '0' ? 'active' : '']">
                                    <a href="/index/kol/sale_list?type=0">
                                        {{Lang::get('未結算')}}
                                    </a>
                                </li>
                                <li :class="['nav-item','{{$data['type']}}' == '1' ? 'active' : '']">
                                    <a href="/index/kol/sale_list?type=1">
                                        {{Lang::get('結算中')}}
                                    </a>
                                </li>
                                <li :class="['nav-item','{{$data['type']}}' == '2' ? 'active' : '']">
                                    <a href="/index/kol/sale_list?type=2">
                                        {{Lang::get('己結算')}}
                                    </a>
                                </li>
                            </ul>

                            <div class="memberMiddle">
                                <div class="row">
                                    <table class="orderTable table table-striped table-bordered table-rwd">
                                        <thead>
                                            <tr class="tr-only-hide">
                                                <th>{{Lang::get('開始時間')}}</th>
                                                <th>{{Lang::get('結束時間')}}</th>
                                                <th>{{Lang::get('總價')}}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(item) in period_sale">
                                                <td data-th="{{Lang::get('開始時間')}}">
                                                    <a data-toggle="modal" data-target="#viewDetail" @click="open_page(item.id)">
                                                        <span v-text="item.period_start"></span>
                                                    </a>
                                                </td>
                                                <td data-th="{{Lang::get('結束時間')}}" v-text="item.period_end"></td>
                                                <td data-th="{{Lang::get('總價')}}" v-text="item.total"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row paginationBox">
                        <div class="col-12 boxCenter">
                            <ul class="pagination" v-if="totalpage!=0">
                                <li class="disabled" v-if="currentpage==1"><span>«</span></li>
                                <li v-if="currentpage!=1"><a href="/index/kol/sale_list?type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page=1"><span>«</span></a></li>
                                
                                <li v-for="page in page_array()" :class="[page==currentpage?'active':'']">
                                    <span v-if="page==currentpage" v-text="page"></span>
                                    <a v-if="page!=currentpage" :href="'/index/kol/sale_list?type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+page">
                                        <span  v-text="page"></span>
                                    </a>
                                </li>
                                
                                <li class="disabled" v-if="currentpage==totalpage"><span>»</span></li>
                                <li v-if="currentpage!=totalpage"><a :href="'/index/kol/sale_list?type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+totalpage">»</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
        </section>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"></script>
    <script type="text/javascript">
        var rightContentBoxData = { period_sale: JSON.parse(`{{json_encode($data['period_sale'])}}`.replace(/&quot;/g, '"').trim()), detail: [], totalpage:parseInt("{{$data['totalpage']}}"), currentpage:parseInt("{{$data['page']}}") };
        var rightContentBoxVM = new Vue({
            el: '#rightContentBox', 
            data: rightContentBoxData,
            computed: {
            },
            methods: {
                page_array: function(){
                    var pages = [];
                    for (var i = -4; i < 4; i++) {
                        page = i+this.currentpage;
                        if( 1 <= page && page <= this.totalpage)
                            pages.push(page);

                        if(pages.length==5)
                            return pages;
                    }
                    return pages;
                },
                open_page: function(period){
                    url = "{{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}/index/kol/sale_detail?period="+ period;
                    window.open(url);
                },
            }
        });
    </script>

@endsection

