@extends('home.Public.mainTpl')
@section('title'){{Lang::get('購物車')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
<style>
    .modal {
        overflow: scroll !important;
    }

    #contWrap {
        width: 100%;
    }

    .orderTable.table td.examinee_top {
        padding: 0px;
    }

    .orderTable.table-rwd.proIntro td.examinee_top:before {
        display: none;
    }

    .orderTable .examinee_table.table-rwd tr {
        padding: 0px;
    }

    @media only screen and (max-width: 1280px) {
        .orderTable.table-rwd.proIntro td:before {
            width: auto;
        }
    }

    .add_prod img {
        max-width: 80px;
        max-height: 80px;
    }

    .add_prod>div {
        width: calc(100% - 80px);
    }

    .add_prod span.btn {
        font-size: 14px;
        line-height: 14px;
        padding: 5px;
    }

    .lineThrough {
        text-decoration: line-through;
    }

    .num {
        color: #ff4300;
    }
</style>
@endsection
@section('mycode')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh-tw.js"></script>
<script>
    $(function() {
        var mdwBtn = $('.modalBtn');
        mdwBtn.on('click', function(e) {
            e.preventDefault();
            var setMdw = $(this),
                setHref = setMdw.attr('href');
            console.log(setHref)

            if (setHref != undefined && setHref != "") {
                $('#boxModel').html('<iframe id="contWrap"></iframe></div>');
                $('#contWrap').attr('src', setHref);
            }
        });
    });
</script>
@endsection

@section('content')
<section class="directoryRow">
    <div class="container">
        <ul>
            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
            <li><a href="{{url('Cart/choose_shop')}}">{{Lang::get('購物車')}}</a></li>
        </ul>
    </div>
</section>
<section class="container max-wideVersion productPublic">
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox" class="shopcarInforBox">
        <div class="shopcarContentBox  memberContentBox">
            <div class="memberTop">
                <div class="titleBox">
                    <div class="title">
                        <h3>{{Lang::get('購物車')}}</h3>
                    </div>
                </div>
            </div>
            <div class="memberMiddle memberitems">
                <div class="orderDetailsBox shoppingCartBox">

                    @if(config('control.control_register')==1)
                    <ul class="nav tabNavBox justify-content-end">
                        <li class="nav-item">
                            <a class="process_btn" data-toggle="modal" data-target="#descriptionContModel">{{Lang::get('流程說明')}}</a>
                        </li>
                    </ul>
                    @endif

                    <div class="cart-items mb-4" id="cart_table">
                        <h4>{{Lang::get('商品內容')}}
                            （ <span v-text="'{{Lang::get('共 XX 項')}}'.replace('XX', cartData.length)"></span> ）
                        </h4>
                        <table id="cart_table" class="orderTable table table-rwd">
                            <thead>
                                <tr class="tr-only-hide">
                                    <th>{{Lang::get('圖片')}}</th>
                                    <th>{{Lang::get('品名')}}</th>
                                    <th style="width: 200px">{{Lang::get('數量')}}</th>
                                    <th style="width: 100px">{{Lang::get('單價')}}</th>
                                    <th style="width: 100px">{{Lang::get('總價')}}</th>
                                    <th style="width: 100px">{{Lang::get('刪除')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template v-for="(vo, vo_key) in cartData">
                                    <!-- 商品資料 -->
                                    <tr class="exam_tr">
                                        <td data-th="{{Lang::get('圖片')}}">
                                            <div class="smallProImg position-relative" :style="{'background-image': 'url('+'{{__PUBLIC__}}'+vo.info_pic1+')'}">
                                                <span class="prod_tag kol_tag" v-if="!closeKol && vo.key_type.slice(0,3)=='kol'">{{Lang::get('網紅推薦')}}</span>
                                                <span class="prod_tag add_tag" v-if="!closeAddPrice && vo.key_type.slice(0,3)=='add'">{{Lang::get('加價購')}}</span>
                                            </div>
                                        </td>
                                        <td data-th="{{Lang::get('品名')}}">
                                            <span class="productNameBox" tabindex="0" data-toggle="tooltip" :title="vo.info_title+' - '+vo.type_title">
                                                <span class="productName">
                                                    <span v-text="vo.info_title"></span>
                                                    <span v-if="vo.type_title" v-text="' - '+vo.type_title"></span>
                                                </span>
                                                <span class="productName text-danger" v-if="(thirdpart_money==1) && control_card_pay==1 && vo.card_pay==0">
                                                    &nbsp;&nbsp;{{Lang::get('不可刷卡')}}
                                                </span>
                                            </span>
                                            <a class="btn btn-sm text-white" style="background-color:var(--subtitle);" v-if="control_register==1 && vo.is_registrable==1" @click="add_examinee(vo.type_id)">
                                                {{Lang::get('填寫報名資料')}}
                                            </a>
                                        </td>
                                        <td data-th="{{Lang::get('數量')}}">
                                            <div class="d-inline-block">
                                                <div class="input-group spinner">
                                                    <div class="input-group-prepend">
                                                        <button class="btn text-monospace" type="button" @click="changeNum(-1, vo.type_id)">
                                                            <i class="bi bi-dash-lg"></i>
                                                        </button>
                                                    </div>
                                                    <input type="number" class="count form-control cart-count" min="1" :max="vo.limit_num" step="1" v-model="vo.num" :id="'count_' + vo.type_id" style="text-align: center;" @blur="realChangeNum(vo.num, vo.type_id)" />
                                                    <div class="input-group-append">
                                                        <button class="btn text-monospace" type="button" @click="changeNum(1, vo.type_id)">
                                                            <i class="bi bi-plus-lg"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td data-th="{{Lang::get('單價')}}">
                                            {{config('extra.shop.dollar_symbol')}}<span v-text="formatPrice(toNumber(vo.countPrice))"></span>
                                        </td>
                                        <td data-th="{{Lang::get('總價')}}">
                                            {{config('extra.shop.dollar_symbol')}}<span v-text="formatPrice(toNumber(vo.countPrice*vo.num))"></span>
                                        </td>
                                        <td data-th="{{Lang::get('刪除')}}">
                                            <a class="deleteBtn" href="###" @click="deleteCtrl(vo.type_id)">
                                                <i class="icon-bin"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <template v-if="control_register==1 && vo.is_registrable==1">
                                        <!-- 考生資料 -->
                                        <tr class="examinee_tr" :id="'examinee'+vo.type_id">
                                            <td colspan="7" class="examinee_top">
                                                <div class="container p-0 overflow-x-scroll">
                                                    <table class="orderTable table-striped table-bordered table-rwd examinee_table">
                                                        <thead>
                                                            <tr class="tr-only-hide text-dark">
                                                                <th><small>{{Lang::get('序號')}}</small></th>
                                                                <th v-for="(field) in vo.fields">
                                                                    <small v-text="field.title"></small>
                                                                </th>
                                                                <th><small>{{Lang::get('刪除')}}</small></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr v-for="(exinfo, index) in vo.examinee" class="cursor-pointer">
                                                                <td data-th="{{Lang::get('序號')}}"
                                                                    @click="edit_examinee(vo.type_id, exinfo.id)">
                                                                    <a><small v-text="index + 1"></small></a>
                                                                </td>
                                                                <td v-for="field in vo.fields" :data-th="field.title"
                                                                    @click="edit_examinee(vo.type_id, exinfo.id)">
                                                                    <span v-if="register_data(index, vo.type_id)['field_id_'+field.id]">
                                                                        <small v-if="types_need_checked.indexOf(field.type) != -1" v-text="register_data(index, vo.type_id)['field_id_'+field.id].join()"></small>
                                                                        <small v-if="['file'].indexOf(field.type) != -1" v-text="register_data(index, vo.type_id)['field_id_'+field.id]['file_name']"></small>
                                                                        <small v-if="types_need_checked.indexOf(field.type)==-1 && ['file'].indexOf(field.type)==-1" v-text="register_data(index, vo.type_id)['field_id_'+field.id]"></small>
                                                                    </span>
                                                                </td>
                                                                <td data-th="{{Lang::get('刪除')}}"><small @click="del_examinee(index, vo.type_id)">{{Lang::get('刪除')}}</small></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </template>
                            </tbody>
                        </table>
                        <div class="subTotal">小計： {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}} <span v-text="formatPrice(toNumber(total))"></span> </div>
                    </div>
                    @if ($data['gift_with_purchase'])
                    <div class="cart-items mb-4">
                        <h4>滿額贈內容</h4>
                        @if ($data['consent']['gift_with_purchase'])
                        <div class="px-5 py-2" style="border: 1px solid #dee2e6; border-top: none;">
                            {!! $data['consent']['gift_with_purchase'] !!}
                        </div>
                        @endif
                        <table class="orderTable table">
                            <thead>
                                <tr class="tr-only-hide">
                                    <th class="text-center">贈送商品</th>
                                    <th class="text-center">配合分館</th>
                                    <th class="text-center">達成金額</th>
                                    <th class="text-center">活動期間</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($data['gift_with_purchase'] as $item)
                                <tr class="tr-only-hide">
                                    <td class="">
                                        @foreach($item['content'] as $row)
                                        <div class="ml-5 mt-2">
                                            <img src="{{__PUBLIC__}}{{$row['picture']}}" class="img-fluid" style="max-width: 50px; max-height: 50px;">
                                            <span class="ml-2">{{$row['title']}}</span>
                                            <span class="ml-2">x {{$row['num']}}</span>
                                        </div>
                                        @endforeach
                                    </td>
                                    <td class="text-center" style="white-space: pre-line;">{{$item['description']}}</td>
                                    <td class="text-center">{{$item['min_purchase_amount']}}</td>
                                    <td class="text-center">{{$item['period']}}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif

                    @if(empty(config('control.close_function_current')['加價購設定']) && count($data['addprice_group'])>0)
                    <h3 class="subtitle">加價購商品</h3>
                    <div class="container">
                        <div class="row">
                            @if(empty($data['addprice_group']) == false)
                            @foreach($data['addprice_group'] as $addprice)
                            <div class="col-xl-4 col-lg-4 col-md-6 col-12 add_prod">
                                <img src="{{__PUBLIC__}}{{$addprice['pic'][0]}}">
                                <div>
                                    <h3 class="title">{{$addprice['pi_title']}}-{{$addprice['title']}}</h3>
                                    <div class="priceBox">
                                        <p class="originalPrice mr-1">
                                            {{Lang::get('定價')}}{{$addprice['price']}}&nbsp;&nbsp;{{Lang::get('售價')}}{{config('extra.shop.dollar_symbol')}}{{$addprice['count']}}
                                        </p>
                                        <p class="offerPrice">
                                            {{Lang::get('加購價')}}{{config('extra.shop.dollar_symbol')}}
                                            <span class="price">{{$addprice['adp_dis']}}</span>
                                        </p>
                                        <span class="btn addCart more ml-2" onclick="addprice_to_cart(`{{$addprice['cart_id']}}`)">{{Lang::get('加入購物車')}}</span>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                            @endif
                        </div>
                    </div>
                    @endif
                    <br>

                    <!-- /////////////// -->
                    @include('home.cart.cart_form')
                </div>
            </div>
        </div>
    </div>
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
</section>
@endsection

@section('Modal')
@include('home.cart.cart_modal')
@endsection

@section('ownJS')
<!-- ////////////////////////////////////////////////////// -->
<script>
    function shaveEllipsis(h) {
        // shave(".productName", h, {
        //     classname: "classname",
        //     character: '......'
        // });
    };

    $(window).on("resize", function() {
        var width1281 = Modernizr.mq('(min-width: 1281px)');

        if (width1281) {
            shaveEllipsis(80);
        } else {
            shaveEllipsis(20);
        }
    }).resize();

    $(function() {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>

@include('home.cart.cart_ctrl_js')

<!-- 加價購功能 -->
<script>
    function addprice_to_cart(cart_id) {
        $.ajax({
            url: "{{url('Cart/cartCtrl')}}",
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrf_token
            },
            datatype: 'json',
            data: {
                cmd: 'increase',
                num: 1,
                product_id: cart_id,
                cart_session: 'cart_all',
            },
            error: function(xhr) {
                console.error(xhr);
            },
            success: function(response) {
                if (response.code) {
                    Swal.fire({
                        title: "{{Lang::get('操作成功')}}",
                        icon: 'success',
                        // content:'',
                        confirmButtonText: "{{Lang::get('確認')}}",
                        confirmButtonColor: 'var(--btn-mainlink)',
                    }).then((result) => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: response.msg,
                        icon: 'error',
                        // content:'',
                        confirmButtonText: "{{Lang::get('確認')}}",
                        confirmButtonColor: 'var(--btn-mainlink)',
                    });
                }
            }
        });
    }
</script>

@include('home.cart.cart_vm_js')

<script>
    // 確認下單
    async function go_buy() {
        $('#body_block').show();
        let order_data = get_order_form_data();

        if (order_data.become_member == 1) {
            resp = await $.ajax({
                url: "{{url('Login/order_create_account')}}",
                type: 'POST',
                datatype: 'json',
                data: order_data,
            });

            if (resp.code == 0) {
                Vue.toasted.show(resp.msg, vt_error_obj);
                $('#body_block').hide();
                e.preventdefault();

                return;
            }
        }

        $('#cartform').submit();
    }
</script>
@endsection