@extends('home.Public.mainTpl')
@section('title'){{Lang::get('招募會員列表')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Member/down_line')}}">{{Lang::get('招募會員列表')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('招募會員列表')}}</h3>
                                </div>
                            </div>

                            <ul class="nav tabNavBox justify-content-center">
                                <li class="nav-item @if($data['type']=='all') active @endif">
                                    <a class=""  href="{{url('Member/down_line')}}?type=all">{{Lang::get('全部會員')}}</a>
                                </li>
                                <li class="nav-item @if($data['type']=='normal') active @endif">
                                    <a class=""  href="{{url('Member/down_line')}}?type=normal">{{Lang::get('一般會員')}}</a>
                                </li>
                                <li class="nav-item @if($data['type']=='vip') active @endif">
                                    <a class=""  href="{{url('Member/down_line')}}?type=vip">{{Lang::get('VIP會員')}}</a>
                                </li>
                                <!-- 
                                <li class="nav-item @if($data['type']=='vip_ed') active @endif">
                                    <a class=""  href="{{url('Member/down_line')}}?type=vip_ed">{{Lang::get('SVIP會員')}}</a>
                                </li>
                                -->
                            </ul>
                        </div>

                        <div class="memberMiddle">
                            <div class="headingBox">
                                <h3 class="subtitle"><span>{{Lang::get('招募會員列表')}}</span></h3>
                            </div>
                            <form action="{{url('Member/down_line')}}" method="get">
                                @csrf
                                <input type="hidden" name="type" value="{$type}">
                                <div class="d-flex mt-2 w-50">
                                    <input type="text" name="search" value="{{$data['search']}}" placeholder="{{Lang::get('請輸入姓名')}}" class="form-control mr-4">
                                    <button class="btn form-control w-50">{{Lang::get('查詢')}}</button>
                                </div>
                            </form>
                            <div class="couponBox">
                                <table class="orderTable table table-striped table-bordered table-rwd">
                                    <thead>
                                        <tr class="tr-only-hide">
                                            <th>{{Lang::get('會員姓名')}}</th>
                                            <th>{{Lang::get('會員編號')}}</th>
                                            <th>{{Lang::get('加入時間')}}</th>
                                            <th>{{Lang::get('會員等級')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($data['down_lines'])==false)
                                        @foreach($data['down_lines']->items() as $vo)
                                            <form action="{{url('Cart/redirect3Next')}}" method="post">
                                                @csrf
                                                <tr>
                                                    <td data-th="{{Lang::get('會員姓名')}}">{{$vo['name']}}</td>
                                                    <td data-th="{{Lang::get('會員編號')}}">{{$vo['number']}}</td>
                                                    <td data-th="{{Lang::get('加入時間')}}">
                                                        {{date('Y/m/d H:i',$vo['createtime'])}}
                                                    </td>
                                                    <td data-th="{{Lang::get('會員等級')}}">
                                                        <span class="bonusNum">
                                                            @if($vo['vip_id']==0)無@else{{$vo['vip_name']}}@endif
                                                        </span>
                                                    </td>
                                                </tr>
                                            </form>
                                        @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- //////////////////////////////////// -->
                        <div class="row paginationBox">
                            <div class="col-12 boxCenter">
                                {{$data['down_lines']->links('pagination.customize')}}
                            </div>
                        </div>
                        <!-- //////////////////////////////////// -->
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
@endsection

