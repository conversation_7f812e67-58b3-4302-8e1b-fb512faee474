@extends('home.Public.mainTpl')

@section('title'){{Lang::get('會員優惠券')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Coupon/coupon')}}">{{Lang::get('會員優惠券')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{Lang::get('會員優惠券')}}</h3>
                            </div>
                        </div>
                        <ul class="nav tabNavBox justify-content-center owl-carousel tabCarousel owl-theme">
                            <li class="nav-item item">
                                <a data-toggle="modal" data-target="#descriptionContModel">{{Lang::get('使用說明')}}</a>
                            </li>
                            <li class="nav-item active item">
                                <a href="{{url('Coupon/coupon')}}">{{Lang::get('我的會員優惠券')}}</a>
                            </li>
                            <li class="nav-item item">
                                <a href="{{url('Coupon/record')}}">{{Lang::get('使用紀錄')}}</a>
                            </li>
                            <li class="item">
                                <a href="{{url('Product/coupon')}}">{{Lang::get('領取會員優惠券')}}</a>
                            </li>
                        </ul>
                    </div>
                    <div class="memberMiddle">
                        <div class="headingBox">
                            <h3 class="title mb-3"><span>{{Lang::get('會員優惠券登錄')}}</span></h3>
                        </div>
                        <div class="memberMiddle">
                            <form id="coupon_key" name="couponForm" action="{{url('Coupon/getCouponByNumber')}}" method="post">
                                @csrf
                                <div class="form-group">
                                    <label>{{Lang::get('序號')}}</label>
                                        <input type="text" class="form-control" name="number" aria-describedby="emailHelp" placeholder="{{Lang::get('請輸入會員優惠券序號(英文須留意大小寫)')}}">
                                        <a class="use-btn" onclick="couponForm.submit();">{{Lang::get('確認')}}</a>
                                </div>
                            </form>
                        </div>
                        <div class="couponBox">
                            <h3 class="title mb-3">{{Lang::get('我的會員優惠券')}}</h3>
                            <table class="orderTable table table-striped table-bordered table-rwd">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th>{{Lang::get('登錄日期')}}</th>
                                        <th>{{Lang::get('名稱')}}</th>
                                        <th>{{Lang::get('期限')}}</th>
                                        <th style="width: 250px;">{{Lang::get('會員優惠券轉移')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['Coupons'] as $vo)
                                    <tr>
                                        <td data-th="{{Lang::get('登錄日期')}}">{{date('Y-m-d', $vo['coupon_pool_login_time'])}}</td>
                                        <td data-th="{{Lang::get('名稱')}}">
                                            <a data-toggle="modal" data-target="#couponDescModel" onclick="couponDesc({{$vo['coupon_id']}})">{{$vo['coupon_title']}}</a>
                                        </td>
                                        <td data-th="{{Lang::get('期限')}}">{{date('Y-m-d', $vo['coupon_end'])}}</td>
                                        <td data-th="{{Lang::get('會員優惠券轉移')}}">
                                            @if($vo['coupon_transfer'])
                                                <form class="d-flex" name="coupon_key2_{$vo.coupon_pool_id}" action="{{url('Coupon/transforCoupon')}}" method="post">
                                                    @csrf
                                                    <div>
                                                        <input class="form-control" type="text" placeholder="{{Lang::get('會員編號')}}" name="number"/>
                                                        <input class="form-control" type="hidden" name="id" value="{$vo.coupon_pool_id}"/>
                                                    </div>
                                                    <a onclick="coupon_key2_{$vo.coupon_pool_id}.submit();" class="btn more">{{Lang::get('確認')}}</a>
                                                </form>
                                            @else
                                                <span>{{Lang::get('不可轉移')}}</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="memberBottom">
                        <div>
                            {!!$data['consent_other']!!}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- descriptionContModel start-->
        <div class="modal fade " id="descriptionContModel" tabindex="-1" role="dialog" aria-labelledby="descriptionContModelTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content ">
                    <div class="modal-header">
                        <h5 class="modal-title" id="descriptionContModelTitle">{{Lang::get('使用說明')}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <!-- <img src="img/process_img_01.png" alt=""> -->
                        <p>{!!$data['consent']!!}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- descriptionContModel end-->

        <!-- couponDescModel start -->
        <div class="modal fade" id="couponDescModel" tabindex="-1" role="dialog" aria-labelledby="couponDescModelTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="couponDescModelTitle">{{Lang::get('會員優惠券說明')}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" id="couponDesc"></div>
                </div>
            </div>
        </div>
        <!-- couponDescModel end -->

        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
    <!-- modal-backdrop fade show -->
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/tabOwlCarousel.js"></script>
    <script>
        $('#couponDescModel').on('show.bs.modal', function (e) {
            $('#couponDescModel').addClass('disabled');
            $('#couponDescModel').on('shown.bs.modal', function (e) {
                $('#couponDescModel').removeClass('disabled');
            })
        })
    </script>

    <script>
        function couponDesc(id) {
            $.ajax({
                method: "POST",
                dataType: "json",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                url: "{{url('Coupon/description')}}",
                data: {
                    id: id
                },
                success: function (data) {
                    content = data.content ? data.content : '';
                    $('#couponDesc').html(content.replace(/\n/g, "<br>"));
                },
            });
        }
    </script>
@endsection