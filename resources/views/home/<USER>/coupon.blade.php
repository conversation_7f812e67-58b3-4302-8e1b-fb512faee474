@extends('home.Public.mainTpl')
@section('title'){{Lang::get('會員優惠券專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')


<section class="directoryRow">
    <div class="container">
        <ul>
            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
            <li>{{Lang::get('會員優惠券專區')}}</li>
        </ul>
    </div>
</section>
<section class="container max-wideVersion productPublic">
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox">
        <div id="rightContentBox" class="innerPageBox">
            <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
            <div class="titleBox">
                <div class="title">
                    <h3>{{Lang::get('會員優惠券專區')}}</h3>
                </div>
            </div>
            <!-- coupon start -->
            <!-- proBrandZone start -->
            <div class="proBrandZone">
                <div class="proBox couponBox">
                    @foreach($data['productinfo'] as $productvo)
                        <div class="item">
                            <a href="{{url('Product/couponinfo')}}?id={{$productvo['id']}}">
                                <div class="img" style="background-image: url({{__PUBLIC__}}{{$productvo['pic']}});"></div>
                                <div class="textBox">
                                    <h3>{{$productvo['title']}}</h3>
                                    <div class="priceBox">
                                        <span class="time">
                                            {{Lang::get('有效日期')}}：<span>{{ date('Y-m-d', $productvo['end']) }}</span>
                                        </span>
                                    </div>
                                    <div class="priceBox">
                                        <span class="offerPrice">
                                            {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}} <span class="price">{{$productvo['discount']}}</span>
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
                <!-- /////////////////////////////////// -->
                <!-- /////////////////////////////////// -->
            </div>
            <!-- proBrandZone end -->
            <!-- coupon end -->
        </div>
    </div>
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
</section>

@endsection
@section('ownJS')
@endsection

