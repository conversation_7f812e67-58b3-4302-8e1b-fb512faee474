@extends('home.Public.mainTpl')
@section('title'){{Lang::get('銷售明細')}} - {{Lang::get('網紅後台')}}@endsection
@section('css')
    <style>
        .hide{
            display: none;
        }

        .memberContentBox .bindingBox a.bindingBtn.use-btn:hover{
            background-color: #ff7300;
            cursor: default;
        }

        .orderTable td, .orderTable th{ text-align: center; }
        .orderTable img{
            width: 50px;
        }

        .tableListA table.productTable{
            border: none;
        }
        .productTable img{
            max-width: 100px;
        }
        .tableListA .productTable tr td{
            background: #fff;
            border: none;
            width: 20%;
        }

        .printBtn{
            border: none;
            border-radius: 5px;
            padding: 7.5px 20px;
            cursor: pointer;
            margin: 0 auto;
        }
        .printBtn:hover{
            background-color: #333333;
            color: #fff;
        }

        .topTypeBtn{
            border: none;
            border-bottom: 1px solid #ff4300;
            color: #000;
            padding: .5rem 1.25rem;
            cursor: pointer;
        }

        .table thead th{
            vertical-align: middle;
        }
        .kolTable{
            min-width: 550px;
        }
        .productTable{
            min-width: 350px;
        }
        .kolTable th{
            background-color: #f0f1f2;
            border: 1px #fff solid;
            padding-right: 5px;
        }
        .orderTable.table td.product_td{
            padding: 0px;
        }
        .productTable{
            width: 100%;
        }
        .productTable tr.productTable_trhead td{
            background-color: #f0f1f2;
            border: 1px solid #fff;
        }
        @media (max-width: 1280px){
            .orderTable.table td.product_td{
                padding: 0px 0.25rem;
            }
            .productTable tr.productTable_trhead td{
                font-weight: bold;
            }
            .orderTable.table-rwd.table-bordered .productTable tr td{
                text-align: center;
            }
            .orderTable.table-rwd .productTable td:before, 
            .orderTable.table-rwd td.product_td:before{
                display: none;
            }
            .orderTable.table-rwd .productTable td:before{
                text-align: left;
            }
            .orderTable.table-striped .productTable tbody tr{
                padding: 0px;
                display: flex;
            }
        }

         /* 列印相關設定 */
        @page{
            size:auto;
        }
        @media print{
            body, 
            body ::-webkit-scrollbar-track{
                background-color: #fff !important;
            }
            a[href]:after {
                content: none !important;
            } 
            .noprint{
               display:none
            }
            .table td, .table th{
                border: none !important;
            }
            .kolTable th{
                background-color: #f0f1f2 !important;
                border: 1px #fff solid !important;
            }
            .productTable tr.productTable_trhead td{
                background-color: #f0f1f2 !important;
                border: 1px solid #fff !important;
            }

            * {
                page-break-inside: auto;
            }
        }
    </style>
@endsection

@section('content')
    <div id="kolpage">
        <section class="container max-wideVersion directoryRow">
            <ul class="clearfix">
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Kol/kol_data')}}">{{Lang::get('網紅後台')}}</a></li>
                <li><a href="{{url('Kol/sale_detail')}}?period={{request()->get('period')}}">{{Lang::get('銷售明細')}}</a></li>
            </ul>
        </section>

        <section class="container max-wideVersion productPublic">
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->

            <div id="itemBox" class="memberInforBox">

                <div id="leftBox">
                    <!-- /////////////////////////////////////////// -->
                    @include('home.kol.kol_menu')
                    <!-- /////////////////////////////////////////// -->
                </div>

                <div id="rightContentBox" class="innerPageBox memberContentBox">
                    <div class="paddingSpacing">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('銷售明細')}}</h3>
                                </div>
                            </div>
                        </div>

                        <!--查看類型-->
                        <ul class="nav tabNavBox justify-content-center">
                            <li class="nav-item">
                                <a href="/index/kol/sale_list?type=0">
                                    {{Lang::get('未結算')}}
                                </a>
                            </li>
                            <li :class="['nav-item',!period_sale_one.confirm_date ? 'active' : '']">
                                <a href="/index/kol/sale_list?type=1">
                                    {{Lang::get('結算中')}}
                                </a>
                            </li>
                            <li :class="['nav-item',period_sale_one.confirm_date ? 'active' : '']">
                                <a href="/index/kol/sale_list?type=2">
                                    {{Lang::get('己結算')}}
                                </a>
                            </li>
                        </ul>

                        <div class="memberMiddle">
                            <div class="row m-0 w-100">
                                <h3>
                                    <span class="d-inline-block" v-text="period_sale_one.period_start"></span>~
                                    <span class="d-inline-block mr-2" v-text="period_sale_one.period_end"></span>
                                    @if($data['kol_period_term']['confirm_date'])
                                        <span class="d-inline-block">
                                        {{Lang::get('確認時間')}}：
                                        {{date('Y-m-d H:i', $data['kol_period_term']['confirm_date'])}}
                                        </span>
                                    @endif
                                </h3>
                                <div class="w-100 overflow-x-scroll mb-3">
                                    <table class="kolTable table mb-0">
                                        <tr>
                                            <th>{{Lang::get('網紅名')}}</th>
                                            <td>{{$data['kol']['kol_name']}}</td>
                                            <th>{{Lang::get('姓名')}}</th>
                                            <td>{{$data['kol']['real_name']}}</td>
                                            <th>{{Lang::get('暱稱')}}</th>
                                            <td>{{$data['kol']['english_name']}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{Lang::get('分類')}}</th>
                                            <td>{{$data['kol']['category']}}</td>
                                            <th>{{Lang::get('電話')}}</th>
                                            <td>{{$data['kol']['phone']}}</td>
                                            <th>{{Lang::get('手機')}}</th>
                                            <td>{{$data['kol']['mobile']}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{Lang::get('地址')}}</th>
                                            <td colspan="3">{{$data['kol']['address']}}</td>
                                            <th>{{Lang::get('備註')}}</th>
                                            <td>{{$data['kol']['address_memo']}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{Lang::get('匯款銀行')}}</th>
                                            <td colspan="1">{{$data['kol']['bank_name']}}</td>
                                            <th>{{Lang::get('匯款帳號')}}</th>
                                            <td colspan="3">{{$data['kol']['bank_account']}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{Lang::get('身份証')}}</th>
                                            <td>{{$data['kol']['id_no']}}</td>
                                            <th>{{Lang::get('備註')}}</th>
                                            <td colspan="3">{{$data['kol']['memo']}}</td>
                                        </tr>
                                    </table>
                                </div>

                                <hr>
                                <h5 class="m-0 w-100" style="margin:0.5%;text-align: right;">{{Lang::get('本單總金額')}}：<span v-text="period_sale_one.total"></span></h5>
                                <!--銷售表格 開始-->
                                <table class="orderTable table table-striped table-bordered table-rwd">
                                    <thead class="">
                                        <tr class="tr-only-hide">
                                            <th>{{Lang::get('序號')}}</th>
                                            <th>{{Lang::get('日期')}}</th>
                                            <th>{{Lang::get('訂單編號')}}</th>
                                            <th>{{Lang::get('客戶名')}}</th>
                                            <th>{{Lang::get('寄送地')}}</th>
                                            <th colspan="5">{{Lang::get('商品')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(item, index) in orderform">
                                            <td data-th="{{Lang::get('序號')}}" v-text="index+1"></td>
                                            <td data-th="{{Lang::get('日期')}}" v-text="timestamp_to_date(item.create_time)"></td>
                                            <td data-th="{{Lang::get('訂單編號')}}" v-text="item.order_number"></td>
                                            <td data-th="{{Lang::get('客戶名')}}" v-text="item.user_name"></td>
                                            <td data-th="{{Lang::get('寄送地')}}" v-text="item.transport_location"></td>
                                            <td data-th="" colspan="5" class="product_td">
                                                <table class="productTable">
                                                    <tr class="productTable_trhead">
                                                        <td>{{Lang::get('商品名')}}</td>
                                                        <td>{{Lang::get('商品圖')}}</td>
                                                        <td>{{Lang::get('單價')}}</td>
                                                        <td>{{Lang::get('數量')}}</td>
                                                        <td>{{Lang::get('總價')}}</td>
                                                    </tr>
                                                    <tr v-for="(product) in item.product" v-if="product.key_type=='kol{{$data['kol']['id']}}'">
                                                        <td data-th="{{Lang::get('商品名')}}"><a target="_blank" :href="'{{request()->server('REQUEST_SCHEME')}}://'+product.url" v-text="product.name"></a></td>
                                                        <td data-th="{{Lang::get('商品圖')}}"><img :src="'{{request()->server('REQUEST_SCHEME')}}://'+product.url2"></td>
                                                        <td data-th="{{Lang::get('單價')}}" v-text="product.price"></td>
                                                        <td data-th="{{Lang::get('數量')}}" v-text="product.num"></td>
                                                        <td data-th="{{Lang::get('總價')}}" v-text="product.total"></td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5 style="text-align: right;">{{Lang::get('本單總金額')}}：<span v-text="period_sale_one.total"></span></h5>

                            <div class="row m-0 w-100 text-center">
                                <input class="printBtn noprint" type="button" value="{{Lang::get('列印明細')}}" @click="print_page()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
        </section>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"></script>
    <script type="text/javascript">
        var rightContentBoxData = { 
            period_sale: JSON.parse(`{{json_encode($data['kol_period_term'])}}`.replace(/&quot;/g,'"')),
            orderform: JSON.parse(`{{json_encode($data['orderform'])}}`.replace(/&quot;/g,'"')),
        }
        var rightContentBoxVM = new Vue({
            el: '#rightContentBox', 
            data: rightContentBoxData,
            computed: {
                period_sale_one: function(){
                    return this.period_sale;
                },
            },
            methods: {
                timestamp_to_date : function(unix_timestamp){
                    var a = new Date(unix_timestamp * 1000);
                    return moment(a).format('YYYY-MM-DD')
                },
                print_page: function(){
                    //获取要打印的Dom内容
                    let newDomHtml = $('#kolpage').html();
                    window.document.body.innerHTML = newDomHtml;
                    $('#leftBox').remove();
                    window.print();
                    window.location.reload();   //解决打印之后按钮失效的问题，刷新页面
                    return false;
                },
            }
        });
    </script>
@endsection

