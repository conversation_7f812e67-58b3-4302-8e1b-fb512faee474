@extends('home.Public.mainTpl')
@section('title'){{Lang::get('會員登入')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')

<style type="text/css">
    .memberContentBox p {
        line-height: 1.5rem;
    }
</style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li id="aboutDirectoryText"><a href="{{url('Login/login')}}">{{Lang::get('會員登入')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion min-heightVersion aboutUsBox">
        <div class="titleBox">
            <div class="title">
                <h3>{{Lang::get('會員登入')}}</h3>
            </div>
        </div>
        <div class="container shoppingCart">
            <div class="row justify-content-center">
                <div class="modal-body" style="max-width: 576px;">
                    <form action="{{url('Login/pagelogin')}}" method="post" name="loginForm">
                        @csrf
                        <div class="form-group">
                            <div class="col-md-2 col-12">
                                <div class="row">
                                    <label>{{Lang::get('帳號')}}</label>
                                </div>
                            </div>
                            <div class="col-md-10 col-12">
                                <div class="row">
                                    <input type="text" class="form-control" name="account" aria-describedby="emailHelp" placeholder="{{ucfirst(App\Services\pattern\MemberInstance::$account_column)}}" value="" />
                                    <input type="hidden" class="form-control" name="redirect" value="{{$data['login_redirect']}}" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-2 col-12">
                                <div class="row"><label for="in_password">{{Lang::get('密碼')}}</label></div>
                            </div>
                            <div class="col-md-10 col-12">
                                <div class="row"><input type="password" name="password" class="form-control"
                                        id="in_password" placeholder="Password" value="" /></div>
                            </div>
                        </div>
                        <div class="verifyCode">
                            <label>{{Lang::get('驗證碼')}}</label>
                            <div class="verification" id="verification_loginForm"></div>
                        </div>
                        <div class="submitBox">
                            <input class="submitBtn cursor-pointer border-0" type="button" value="{{Lang::get('登入')}}" onclick="submitForm('loginForm')" id="check_btn_loginForm">
                            <ul class="registeredBox">
                                <li><a href="{{url('Login/signup')}}" class="registereBtn">{{Lang::get('立即註冊')}}</a></li>
                                |
                                <li><a onclick="$('#goForgetPasswordModel').click();" class="forgetPwdBtn" data-dismiss="modal" aria-label="Close">{{Lang::get('忘記密碼')}}</a></li>
                            </ul>
                        </div>
                    </form>
                    <div class="row justify-content-center thPartyPlatformBox">
                        @if(config('extra.social_media.FB_appID'))
                            <a href="###" class="p-0 col-sm-4 col-12" onclick="FBLogin('{{url('Member/member')}}');">
                                <img class="p-1" src="{{__PUBLIC__}}/img/icon_facebook-en.png" alt="">
                            </a>
                        @endif
                        @if(config('extra.social_media.client_id'))
                            <a href="###" class="p-0 col-sm-4 col-12" onclick="LineAuth('{{url('Member/member')}}');">
                                <img class="p-1" src="{{__PUBLIC__}}/img/icon_line-en.png" alt="">
                            </a>
                        @endif
                        @if(config('extra.social_media.Google_appId'))
                            <a href="###" class="p-0 col-sm-4 col-12" onclick="GoogleLogin(0, '{{url('Member/member')}}');">
                                <img class="p-1" src="{{__PUBLIC__}}/img/icon_google.png" alt="">
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
<script type="text/javascript">
        /*登入頁驗證碼*/
            var codeVerifyCheck_loginForm = true
            $('#verification_loginForm').codeVerify({
                type: 1,
                // arith:24,
                width: '100%',
                height: '40px',
                fontSize: '30px',
                codeLength: 4,
                btnId: 'check_btn_loginForm',
                ready: function () {},
                success: function () {
                    codeVerifyCheck_loginForm = true;
                },
                error: function () {
                    Vue.toasted.show("{{Lang::get('驗證失敗')}}", vt_error_obj);
                    codeVerifyCheck_loginForm = false;
                }
            });
    </script>

    <!-- 社群登入重新導向 -->
    <script type="text/javascript">
        var redirect = localStorage.getItem('redirect');
        if(redirect){
            localStorage.setItem('redirect', "");
            location.href = redirect;
        }
    </script>
@endsection
