
                                    @if(!isset(config('control.close_function_current')['網紅列表']))
                                        <li>
                                            @if($data['kol_logined'] == 'true')
                                                <!-- 已登入 -->
                                                <a href="{{url('Kol/kol_data')}}" data-text="{{Lang::get('網紅後台')}}">
                                                   <i aria-hidden="true" class="bi bi-star"></i>
                                                   <span class="d-lg-none d-block">{{Lang::get('網紅後台')}}</span>
                                                </a>
                                            @else
                                                <!-- 未登入 -->
                                                <a id="kol_login" data-toggle="modal" data-text="{{Lang::get('網紅登入')}}" data-target="#kolLogin">
                                                    <i aria-hidden="true" class="bi bi-star"></i>
                                                    <span class="d-lg-none d-block">{{Lang::get('網紅登入')}}</span>
                                                </a>
                                            @endif
                                        </li>
                                    @endif

                                    @if(!isset(config('control.close_function_current')['註冊商品回函']))
                                        <li>
                                            @if($data['user']['name'] == '')
                                                <!-- 未登入 -->
                                                <a href="javascript:unregistered()" data-text="{{Lang::get('商品註冊')}}">
                                            @else
                                                <!-- 已登入 -->
                                                <a href="javascript:register_product(0)" data-text="{{Lang::get('商品註冊')}}">
                                            @endif
                                                @include('home.Public/svg_register_product')
                                                <span class="d-lg-none d-block">{{Lang::get('商品註冊')}}</span>
                                            </a>
                                        </li>
                                    @endif

                                    <li class="d-lg-block">
                                        <a data-toggle="modal" data-target="#phoneSearch" data-text="{{Lang::get('商品搜尋')}}">
                                            @include('home.Public/svg_search')
                                            <span class="d-lg-none d-block">{{Lang::get('商品搜尋')}}</span>
                                        </a>
                                    </li>

                                    @if(!isset(config('control.close_function_current')['訂單管理']))
                                        <li class="top_nav">
                                            <a href="{{url('orderform/tracking')}}" data-text="{{Lang::get('訂單查詢')}}">
                                                @include('home.Public/svg_orderform')
                                                <span class="d-lg-none d-block">{{Lang::get('訂單查詢')}}</span>
                                            </a>
                                        </li>
                                    @endif

                                    @if(!isset(config('control.close_function_current')['會員管理']))
                                        <li class="d-lg-block">
                                            @if($data['user']['id'] != '0')                                        	
                                            	<!-- 會員已登入 -->
                                            	<a href="{{url('Member/member')}}" data-text="{{Lang::get('會員專區')}}">
                                            		<i class="bi bi-person"></i>
                                            		<span class="d-lg-none d-block">{{Lang::get('會員專區')}}</span>
                                            	</a>
                                        	@else
    	                                        <!-- 會員未登入 -->
    	                                        <a id="login" data-text="{{ Lang::get('會員登入') }}" data-toggle="modal" data-target="#memberLogin" onclick="$('#bonus_info').hide()">
    	                                            <i class="bi bi-person"></i>
    	                                            <span class="d-lg-none d-block">{{Lang::get('會員登入')}}</span>
    	                                        </a>
                                        	@endif
                                        </li>
                                    @endif

                                    <li class="d-lg-block">
                                        <span class="d-flex">
                                            <!-- <a data-toggle="modal" data-target="#phoneSearch">
                                                @include('home.Public/svg_search')
                                            </a> -->
                                            @if(!isset(config('control.close_function_current')['訂單管理']))
                                            
                                                @if($data['user']['id'] != '0' || !isset(config('control.close_function_current')['會員管理']))
                                                    <a href="{{url('Cart/choose_shop')}}">
                                                        <i class="bi bi-cart"></i>
                                                        <span class="counter prodNum mainbtn">{{($data['cartCount']) ?? "0"}}</span>
                                                    </a>
                                                @else
                                                    <a data-toggle="modal" data-target="#memberLogin" onclick="$('#bonus_info').show()">
                                                        <i class="bi bi-cart"></i>
                                                        <span class="counter prodNum mainbtn">{{($data['cartCount']) ?? "0"}}</span>
                                                    </a>
                                                @endif
                                            @endif
                                        </span>
                                    </li>