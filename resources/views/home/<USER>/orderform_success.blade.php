@extends('home.Public.mainTpl')
@section('title'){{Lang::get('訂單資訊')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('mycode')
    @if(empty($data['seo'][0]['google_ads_conversion_id']) == false)
    <!-- Google Ads Conversion Tracking -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-{{$data['seo'][0]['google_ads_conversion_id']}}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', "AW-{{$data['seo'][0]['google_ads_conversion_id']}}"); // 你的轉換 ID
        gtag('event', 'conversion', {
            'send_to': "AW-{{$data['seo'][0]['google_ads_conversion_id']}}/{{$data['seo'][0]['google_ads_conversion_tag']}}", // 轉換標籤
            'value': "{{$data['singleData']['total']}}",
            'currency': 'TWD'
        });
    </script>
    <!-- End Google Ads Conversion Tracking -->
    @endif
@endsection
@section('content')
<section class="directoryRow">
    <div class="container">
        <ul>
            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
            @if($data['user']['id'] != '0')
            <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
            @endif
            <li><a>{{Lang::get('訂單資訊')}}</a></li>
        </ul>
    </div>
</section>
<section class="container max-wideVersion productPublic">
    <div id="itemBox" class="memberInforBox">
        <div id="leftBox">
        @include('home.Public.member_menu')
        </div>
        <div id="rightContentBox" class="innerPageBox memberContentBox">
            <div class="paddingSpacing">
                <div class="memberTop">
                </div>
                <div class="memberMiddle">
                </div>
            </div>
            <div class="memberBottom">
                <div>
                    <h3>非常感謝您的購買，本頁將於<span id="countdown"></span>秒後跳轉至訂單資訊</h3>
                </div>
            </div>
        </div>
</section>
@endsection
@section('ownJS')
<script>
    // 設定倒數計時的秒數
    let countdownSeconds = 5;
    document.getElementById('countdown').textContent = countdownSeconds;

    // 更新倒數計時的函數
    function updateCountdown() {
        var countdownElement = document.getElementById('countdown');
        countdownElement.textContent = countdownSeconds;

        if (countdownSeconds > 0) {
            countdownSeconds--;
            setTimeout(updateCountdown, 1000); // 每秒更新一次
        } else {
            window.location.href = `{{url('Orderform/orderform_c')}}?id={{$data['singleData']['order_number']}}`; // 倒數結束後跳轉到訂單資訊頁面
        }
    }

    // 開始倒數計時
    updateCountdown();
</script>
@endsection