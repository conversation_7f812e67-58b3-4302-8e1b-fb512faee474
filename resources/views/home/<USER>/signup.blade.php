@extends('home.Public.mainTpl')
@section('title'){{Lang::get('會員註冊')}} | {{$data['seo'][0]['title']}}@endsection

@if($data['recommend_user'])
<!--社群分享SEO-->
@section('ogtitle'){{$data['recommend_user']['share_title']}}@endsection
@section('ogdescription'){{$data['recommend_user']['share_text']}}@endsection
@section('ogurl'){{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}{{request()->server('REQUEST_URI')}}@endsection
@section('ogimage'){{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}{{$data['recommend_user']['share_pic']}}@endsection
@endif

@section('css')
@endsection

@section('content')
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<section class="directoryRow">
    <div class="container">
        <ul>
            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
            <li id="aboutDirectoryText"><a href="{{url('Login/signup')}}">{{Lang::get('會員註冊')}}</a></li>
        </ul>
    </div>
</section>

<section class="container max-wideVersion min-heightVersion aboutUsBox">
    <div class="titleBox">
        <div class="title">
            <h3>{{Lang::get('會員註冊')}}</h3>
        </div>
    </div>


    <div class="memberContentBox">
        @if(config('control.control_down_line')==1)
        @if($data['recommend_user'])
        <div class="col-12 mb-4">
            {!! $data['recommend_user']['recommend_content'] !!}
        </div>
        @endif
        @endif

        <form action="{{url('Login/dosignup')}}" method="post" name="signupForm" id="signupForm" enctype="multipart/form-data">
            @csrf
            <div class="memberMiddle memberitems">
                @if(config('control.control_platform')==1)
                <div class="form-group col-sm-12 col-12">
                    <input type="radio" name="user_type_radio" v-model="user_type_radio" id="user_type_radio_0" value="0"><label for="user_type_radio_0">{{Lang::get('一般會員')}}</label>
                    <input type="radio" name="user_type_radio" v-model="user_type_radio" id="user_type_radio_1" value="1"><label for="user_type_radio_1">{{Lang::get('供應商')}}</label>
                </div>
                @endif
                <div class="row">
                    @if(config('control.control_down_line')==1)
                    <div class="form-group col-sm-6 col-12">
                        <label for="su_name" class="col-form-label">{{Lang::get('推薦者')}}({{Lang::get('會員編號')}})</label>
                        <input type="text" class="form-control" name="upline_user" id="upline_user" value="{{$data['recommend_user_number']}}">
                    </div>
                    <div class="form-group col-sm-6 col-12"></div>
                    @endif

                    <div class="form-group col-sm-6 col-12">
                        <label for="su_name" class="col-form-label">{{Lang::get('姓名')}}</label>
                        <span class="smallText mr-1">{{Lang::get('必填')}}</span>
                        <span class="smallText">{{Lang::get('請務必提供真實姓名')}}</span>
                        <input type="text" class="form-control" name="name" id="su_name" placeholder="">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label for="su_email" class="col-form-label">{{Lang::get('帳號')}}</label>
                        <span class="smallText mr-1">{{Lang::get('必填')}}</span>
                        <span class="smallText">{{Lang::get('系統將發送認證信至此')}}</span>
                        <input type="text" class="form-control " name="email" id="su_email" placeholder="E-mail">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label for="su_password" class="col-form-label">{{Lang::get('密碼')}}</label><span class="smallText">{{Lang::get('必填')}} {{Lang::get('密碼需包含英文及數字')}}</span>
                        <input type="password" class="form-control " name="password" id="su_password">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label for="su_passwordB" class="col-form-label">{{Lang::get('再確認密碼')}}</label><span class="smallText">{{Lang::get('必填')}}</span>
                        <input type="password" class="form-control" name="passwordB" id="su_passwordB" placeholder="">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label for="su_phone" class="col-form-label">{{Lang::get('行動電話')}}</label><span class="smallText">{{Lang::get('必填')}}</span>
                        <input type="text" class="form-control" name="phone" id="su_phone" placeholder="範例：09123456789">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label for="su_tele" class="col-form-label">{{Lang::get('聯絡電話')}}</label>
                        <input type="text" class="form-control" name="tele" id="su_tele" placeholder="範例：02-12345678">
                    </div>

                    <!-- /////////// -->
                    <div class="form-group col-sm-6 col-12">
                        <label for="" class="col-form-label">{{Lang::get('生日')}}</label>
                        <!-- <input type="text" class="form-control " id="" placeholder="生日下拉式"> -->
                        <input type="date" class="form-control" name="birthday" id="su_birthday" placeholder="" value="">
                    </div>

                    @if(empty(config('control.close_function_current')['會員瀏覽商品設定']))
                    <div class="form-group col-sm-6 col-12">
                        <label for="" class="col-form-label">{{Lang::get('商品瀏覽權限')}}</label>
                        <select class="form-control" name="product_view_id">
                            @foreach($data['product_view'] as $vo)
                            <option value="{{$vo['id']}}">{{$vo['name']}}</option>
                            @endforeach
                        </select>
                    </div>
                    @endif

                    <!-- /////////// -->
                    <div class="form-group col-12">
                        <label for="" class="col-form-label">{{Lang::get('地址')}}</label><span class="smallText">{{Lang::get('必填')}}</span>
                        <div class="addressBox">
                            <div class="row">
                                <div class="col-sm-6 use-mb">
                                    <div class="d-flex">
                                        <div class="col-4 use-col">
                                            <select name="F_I_CNo" id="myCity" class="ui fluid selection dropdown no label use-btn">
                                                <option value="">{{Lang::get('請選擇鄉鎮區')}}</option>
                                                @foreach($data['city'] as $vo)
                                                <option value="{{$vo['AutoNo']}}">{{$vo['Name']}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-4 use-col">
                                            <select name="F_I_TNo" id="myTown" class="ui fluid selection dropdown no label use-btn">
                                                <option value="">{{Lang::get('請選擇鄉鎮區')}}</option>
                                            </select>
                                        </div>
                                        <div class="col-4 use-col">
                                            <input type="text" class="form-control" id="myZip" name="F_S_NH_Zip" size="3" placeholder="{{Lang::get('郵遞區號')}}"
                                                readonly="ture">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 use-mb">
                                    <input name="F_S_NH_Address" type="text" class="form-control" id="" placeholder="{{Lang::get('地址')}}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <template v-if="user_type_radio==1">
                        <div class="form-group col-sm-6 col-12">
                            <label class="col-form-label">{{Lang::get('店鋪名稱')}}</label>
                            <input type="text" class="form-control" name="shop_name">
                        </div>
                        <div class="form-group col-sm-6 col-12"></div>
                        <div class="form-group col-sm-6 col-12">
                            <label class="col-form-label">{{Lang::get('公司登記文件')}}</label>
                            <input type="file" class="form-control" name="file_company">
                        </div>
                        <div class="form-group col-sm-6 col-12">
                            <label class="col-form-label">{{Lang::get('個人身份文件')}}</label>
                            <input type="file" class="form-control" name="file_person">
                        </div>
                        <div class="form-group col-sm-12 col-12">
                            <label class="col-form-label">{{Lang::get('收款帳號')}}</label>
                            <div class="row m-0">
                                <div class="col-sm-2">
                                    <input type="text" class="form-control" name="bank" placeholder="{{Lang::get('銀行名稱')}}">
                                </div>
                                <div class="col-sm-2">
                                    <input type="text" class="form-control" name="bank_code" placeholder="{{Lang::get('分行代號')}}">
                                </div>
                                <div class="col-sm-2">
                                    <input type="text" class="form-control" name="bank_account_name" placeholder="{{Lang::get('戶名')}}">
                                </div>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" name="bank_account_code" placeholder="{{Lang::get('銀行帳號')}}">
                                </div>
                            </div>
                        </div>
                    </template>

                    @if(config('extra.shop.google_recaptcha_sitekey'))
                    <div class="w-100 d-flex justify-content-center mb-3">
                        <div class="g-recaptcha text-center" data-callback="captcha_onclick"
                            data-sitekey="{{config('extra.shop.google_recaptcha_sitekey')}}">
                        </div>
                        <input type="hidden" name="recaptcha" id="recaptchaValidator" />
                    </div>
                    @endif

                    <hr>
                    <div class="form-group col-12 text-center">
                        <div class="form-check term-error-box">
                            <input class="form-check-input" type="checkbox" id="su_agreement" name="term" value="">
                            <label class="form-check-label" for="su_agreement">
                                <span>{{Lang::get('我已閱讀並同意遵守本網站之條款')}}
                                    <a href="###" style="color:red;" data-toggle="modal" data-target="#memberRuleModel">({{Lang::get('查看')}})</a>
                                </span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group col-12 text-center">
                        <label class="use-btn" style="cursor: pointer;">
                            {{Lang::get('下一步，進行Email認證')}}
                            <input type="submit" value="{{Lang::get('下一步，進行Email認證')}}" id="check_btn_signupForm">
                        </label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /////////////////////////////////////////////////////////////////////////////////////// -->
</section>
@endsection

@section('Modal')
<!-- Modal -->
<div class="modal fade shoppingCart" id="memberRuleModel" tabindex="-1" role="dialog" aria-labelledby="memberRuleModelTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <div class="modal-header">
                <h5 class="modal-title" id="memberRuleModelTitle">{{Lang::get('會員條款')}}</h5>
            </div>
            <div class="modal-body">
                {!!$data['consent']['member']!!}
            </div>
        </div>
    </div>
</div>
@endsection

@section('ownJS')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.2/localization/messages_zh.min.js"></script>

<script type="text/javascript">
    signupForm_data = {
        user_type_radio: 0,
    }
    var signupFormVM = new Vue({
        el: '#signupForm',
        data: signupForm_data,
        method: {},
    })
</script>

<script>
    var codeVerifyCheck_signupForm = true; /*配合mainTpl送表單檢查*/

    $(document).ready(function() {
        setTimeout(function() {
            if ($('#myCity').val() != "") {
                init_city();
            }
        }, 500);;
    });
    $('#myCity').change(function() {
        init_city();
    });

    function init_city() {
        var CNo = $('#myCity').val();
        $.ajax({
            type: "POST",
            headers: {
                'X-CSRF-Token': csrf_token
            },
            url: "{{url('Login/town_ajax')}}",
            cache: false,
            data: {
                CNo: CNo
            },
            error: function() {
                alert("{{Lang::get('操作失敗')}}");
            },
            success: function(data) {
                console.log(data);
                $('#myTown').html(`<option value="">{{Lang::get('請選擇鄉鎮區')}}</option>` + data);
                $('#myZip').val(""); //避免重新選擇縣市後郵遞區號還存在，所以在重新選擇縣市後郵遞區號欄位清空
            }
        });
    }

    //利用jQuery的ajax把縣市編號(CNo)傳到town_ajax.php把相對應的區域名稱回傳後印到選擇區域(鄉鎮)下拉選單
    $('#myTown').change(function() {
        var TNo = $('#myTown').val();
        $.ajax({
            type: "POST",
            headers: {
                'X-CSRF-Token': csrf_token
            },
            url: "{{url('Login/zip_ajax')}}",
            cache: false,
            data: {
                TNo: TNo
            },
            error: function() {
                alert("{{Lang::get('操作失敗')}}");
            },
            success: function(data) {
                $('#myZip').val(data);
            }
        });
    });


    $.validator.addMethod(
        "regex",
        function(value, element, regexp) {
            return this.optional(element) || new RegExp(regexp).test(value);
        },
        "Please check your input."
    );

    $("#signupForm").validate({
        submitHandler: function() {
            $('#body_block').show();
            setTimeout(() => {
                submitForm('signupForm');
            }, 100);
        },
        rules: {
            name: {
                required: true
            },
            password: {
                required: true
            },
            passwordB: {
                required: true,
                equalTo: "#su_password"
            },
            email: {
                remote: {
                    url: "{{url('Ajax/ckaccount')}}",
                    type: "post",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    }
                },
                required: true,
                email: true
            },
            phone: {
                required: true,
                number: true
            },
            addr: {
                required: true
            },
            F_I_CNo: {
                required: true
            },
            F_I_TNo: {
                required: true
            },
            F_S_NH_Address: {
                required: true
            },
            term: {
                required: true
            },

            file_company: {
                required: true
            },
            file_person: {
                required: true
            },
            bank: {
                required: true
            },
            bank_code: {
                required: true
            },
            bank_account_name: {
                required: true
            },
            bank_account_code: {
                required: true
            },
        },
        messages: {
            name: {
                required: "{{Lang::get('名稱不得為空')}}"
            },
            password: {
                required: "{{Lang::get('密碼不得為空')}}",
            },
            passwordB: {
                required: "{{Lang::get('密碼不得為空')}}",
                equalTo: "{{Lang::get('密碼不一致')}}"
            },
            email: {
                remote: "{{Lang::get('帳號已經存在')}}",
                required: "{{Lang::get('帳號不得為空')}}",
                email: "{{Lang::get('帳號格式錯誤')}}"
            },
            phone: {
                required: "{{Lang::get('手機不得為空')}}",
                number: "{{Lang::get('手機只能是數字')}}"
            },
            addr: {
                required: "{{Lang::get('地址不得為空')}}"
            },
            F_I_CNo: {
                required: "{{Lang::get('請選擇縣市')}}"
            },
            F_I_TNo: {
                required: "{{Lang::get('請選擇鄉鎮區')}}"
            },
            F_S_NH_Address: {
                required: "{{Lang::get('請輸入地址')}}"
            },
            term: {
                required: "{{Lang::get('請勾選')}}"
            },

            file_company: {
                required: "{{Lang::get('請選擇公司登記文件')}}"
            },
            file_person: {
                required: "{{Lang::get('請選擇個人身份文件')}}"
            },
            bank: {
                required: "{{Lang::get('請輸入銀行名稱')}}"
            },
            bank_code: {
                required: "{{Lang::get('請輸入分行代號')}}"
            },
            bank_account_name: {
                required: "{{Lang::get('請輸入戶名')}}"
            },
            bank_account_code: {
                required: "{{Lang::get('請輸入銀行帳號')}}"
            },
        }
    });
</script>

@if(config('extra.shop.google_recaptcha_sitekey'))
<!-- 機器人驗證 -->
<script type="text/javascript">
    function captcha_onclick() {
        document.getElementById('recaptchaValidator').value = grecaptcha.getResponse();
    }

    window.addEventListener('pageshow', (event) => {
        if (grecaptcha.getResponse()) {
            grecaptcha.reset();
        }
    });
</script>
@endif
@endsection