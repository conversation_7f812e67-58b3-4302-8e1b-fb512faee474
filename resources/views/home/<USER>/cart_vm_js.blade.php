<script>
    /*根據不同配送方式顯示不同資訊*/
    function send_way_infos(value = null) {
        $('.shipping_blank').hide();

        if (!value) {
            return;
        }

        let shipping_type = maskVM.type_decode;
        let name, free_rule, shipping_price, prepare_days;

        for (let i = 0; i < shipping_type.length; i++) {
            if (shipping_type[i].shipping_fee_id == value) {
                name = shipping_type[i].name;
                free_rule = shipping_type[i].free_rule;
                shipping_price = shipping_type[i].price;
                prepare_days = shipping_type[i].prepare_days;
                final_priceVM.currentShippingType = i;

                break;
            }
        }

        final_priceVM.finalShipping = 0;
        final_priceVM.finalShippingDiscountDiff = 0;
        $('#discountOver').addClass('d-none');

        if (shipping_price > 0) {
            final_priceVM.finalShipping = Number(shipping_price);
            if (final_priceVM.finalsubTotal < free_rule) {
                /*未達免運條件*/
                if (free_rule != 999999999) {
                    final_priceVM.finalShippingDiscountDiff = free_rule - final_priceVM.finalsubTotal;
                }
            } else {
                final_priceVM.finalShipping = 0;

                if (free_rule != 999999999) {
                    $('#discountOver').removeClass('d-none');
                }
            }
        }

        $('#transport_date_div').show();

        const disableDates = [
            function(date) {
                return date.getDay() === parseInt(`{{$data['exclude_weekday']}}`);
            },
        ];

        const excludeDates = JSON.parse(`{{$data['exclude_dates']}}`.replace(/&quot;/g, '"'));

        if (excludeDates.length > 0) {
            excludeDates.forEach(function(item) {
                disableDates.push(new Date(item));
            });
        }

        const prepareStart = new Date();
        prepareStart.setDate(prepareStart.getDate() + parseInt(prepare_days));

        $("#arrival_date").flatpickr({
            minDate: prepareStart,
            "disable": disableDates,
            locale: "zh_tw",
            dateFormat: "Y-m-d",
        });

        switch (name) {
            case "{{Lang::get('7-11取貨')}}":
            case "{{Lang::get('全家取貨')}}":
            case "{{Lang::get('萊爾富取貨')}}":
            case "{{Lang::get('OKmart取貨')}}":
                $('#sync_member').show();
                $('#transport_location_name').parent().show();
                $('#transport_location_phone').parent().show();
                $('#transport_email').parent().show();
                $('#store_pickup').show();
                $('#cz_select').show();
                $('.must_need_addr').hide();

                break;
            case "{{Lang::get('到店取貨')}}":
            default:
                $('#sync_member').show();
                $('#transport_location_name').parent().show();
                $('#transport_location_phone').parent().show();
                $('#transport_email').parent().show();
                $('#pickUpRemind_btn').click();

                break;
            case "{{Lang::get('宅配')}}":
            case "{{Lang::get('黑貓宅配')}}":
            case "黑貓宅配(貨到付款)":
            case "{{Lang::get('冷藏宅配')}}":
            case "{{Lang::get('冷凍宅配')}}":
                $('#sync_member').show();
                $('#transport_location_name').parent().show();
                $('#transport_location_phone').parent().show();
                $('#transport_email').parent().show();
                $('#cz_select').show();
                $('.must_need_addr').show();

                break;
        }

        payingVM.type_decode = Array.from(payingVM.all_type);

        if (name == "{{Lang::get('黑貓宅配')}}") {
            payingVM.type_decode = payingVM.type_decode.filter(function(item) {
                return item.name != '貨到付款';
            });
        } else if (name == "黑貓宅配(貨到付款)") {
            payingVM.type_decode = payingVM.type_decode.filter(function(item) {
                return item.name == '貨到付款';
            });
        }
    }

    function carrierNumLenChange() {
        const carrierType = Number($('select[name="CarrierType"]').val());
        $('#CarrierNum').val('');

        if (carrierType == 3) {
            $('#CarrierNum').attr('maxlength', 8);
        } else {
            $('#CarrierNum').attr('maxlength', 16);
        }
    }

    function assign_memeber_data() {
        $('#transport_location_name').val(`{{$data['user']['name']}}`);
        $('#transport_location_phone').val(`{{$data['user']['phone']}}`);
        $('#transport_email').val(`{{$data['user']['email']}}`);
    }

    const invoiceCkeck = "{{config('extra.ecpay.InvoiceCheck')}}";

    $('#confirm_form').on('click', async function() {
        if (cart_tableVM.cartData.length == 0) {
            swal_fire("{{Lang::get('購物車內無商品')}}");

            return;
        }

        let error_arr = [];
        const send_way_value = $('select[name="send_way"]').val(),
            pay_way_value = $('select[name="pay_way"]').val();
        let send_way_name, pay_way_name;

        if (send_way_value) {
            send_way_name = $('option:selected', 'select[name="send_way"]').text().split(' | ')[0];
        } else {
            swal_fire("{{Lang::get('請選擇')}}" + "{{Lang::get('選擇配送方式')}}");

            return;
        }

        if (pay_way_value) {
            pay_way_name = $('option:selected', 'select[name="pay_way"]').text();
        } else {
            swal_fire("{{Lang::get('請選擇')}}" + "{{Lang::get('選擇付款方式')}}");

            return;
        }

        if ($('select[name="invoice_style"]').val() == '') {
            swal_fire("{{Lang::get('請選擇發票開立方式')}} ");

            return;
        }

        /* --- Test Start --- */
        if (use_thirdpart_logistic && send_way_name == "{{Lang::get('宅配')}}" && pay_way_name == "{{Lang::get('貨到付款')}}") {
            swal_fire("{{Lang::get('宅配無法使用貨到付款')}}");

            return;
        }

        if (send_way_name == "{{Lang::get('宅配')}}" && (pay_way_name.includes("匯款") || pay_way_name.includes("轉帳"))) {
            swal_fire("{{Lang::get('宅配請於線上完成付款')}}");

            return;
        }

        const subTotal = parseInt($('.subTotal span').text());

        if (ecpay_shippable_supermarket.indexOf(send_way_name) != -1 && subTotal > 20000) {
            swal_fire("{{Lang::get('超商取貨無法運送金額超過20000元之商品')}}");

            return;
        }

        if ($('#transport_location_name').val() == '') {
            error_arr.push("{{Lang::get('請輸入姓名')}}");
        }

        let regex;
        regex = /^09[0-9]{8}$/;

        if ($('#transport_location_phone').val() == '') {
            error_arr.push("{{Lang::get('請輸入手機')}}");
        } else {
            if ($('#transport_location_phone').val().match(regex) == false) {
                error_arr.push("{{Lang::get('請輸入09開頭的10位手機號碼')}}");
            }
        }

        regex = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g;

        if ($('#transport_email').val() == '') {
            error_arr.push("{{Lang::get('請輸入信箱')}}");
        } else {
            if (regex.test($('#transport_email').val()) == false) {
                error_arr.push("{{Lang::get('email格式錯誤')}}");
            }
        }

        let city_town;

        if (["{{Lang::get('7-11取貨')}}", "{{Lang::get('全家取貨')}}", "{{Lang::get('萊爾富取貨')}}", "{{Lang::get('OKmart取貨')}}"].includes(send_way_name) == false) {
            if (send_way_name == "{{Lang::get('宅配')}}" || send_way_name == "{{Lang::get('黑貓宅配')}}" || send_way_name == "{{Lang::get('冷藏宅配')}}" || send_way_name == "{{Lang::get('冷凍宅配')}}" || send_way_name == "黑貓宅配(貨到付款)") {
                if ($('#myCity').val() == '') {
                    error_arr.push("{{Lang::get('請選擇縣市')}}");
                }

                if ($('#myTown').val() == '') {
                    error_arr.push("{{Lang::get('請選擇鄉鎮區')}}");
                }

                if ($('#myCity').val() && $('#myTown').val()) {
                    city_town = await get_city_town($('#myCity').val(), $('#myTown').val());

                    if (city_town.code != 200) {
                        error_arr.push("{{Lang::get('縣市鄉鎮區選擇有誤')}}");
                    }
                }

                if ($('#transport_location').val() == '') {
                    error_arr.push("{{Lang::get('請輸入地址')}}");
                }
            }
        } else {
            if (use_thirdpart_logistic) {
                if ($('#store').val() == '') {
                    error_arr.push("{{Lang::get('請選擇門市')}}");
                }
            } else {
                if ($('#storeNo').val() == '' || $('#storeName').val() == '' || $('#storeAddr').val() == '') {
                    error_arr.push("{{Lang::get('門市資訊不完整')}}");
                } else {
                    let regex = /^[0-9]{4,}$/;

                    if ($('#storeNo').val().match(regex) === null) {
                        error_arr.push("{{Lang::get('店鋪號碼格式錯誤')}}");
                    }
                }
            }
        }

        if ($('#need_arrival_date').val() == '1' && $('#arrival_date').val() == '') {
            error_arr.push("{{Lang::get('請選擇配送日期')}}");
        }

        switch ($('select[name="invoice_style"]').val()) {
            // 個人共通性載具
            case '3':
                if ($('#CarrierType').val() == '3') {
                    if ($('#CarrierNum').val() == '') {
                        error_arr.push("請輸入載具編號");
                    } else {
                        let regex = /^\/[0-9A-Z\.\+\-]{7}$/;

                        if ($('#CarrierNum').val().match(regex) === null) {
                            error_arr.push("載具編號格式錯誤");
                        }

                        // 可能會暫時性關閉，因財政部系統可能會進行不預期的維護作業
                        if (invoiceCkeck != '') {
                            $.ajax({
                                url: "{{url('Cart/invoice_carrier_check')}}",
                                type: 'GET',
                                async: false,
                                data: {
                                    CarrierNum: $('[name="CarrierNum"]').val(),
                                },
                                success: function(response) {
                                    if (response.code == 1) {
                                        Vue.toasted.show(response.msg, {
                                            duration: 1500,
                                            className: ["toasted-primary", 'bg-success']
                                        });
                                    } else if (response.code == 9000001) {
                                        Vue.toasted.show(response.msg, {
                                            duration: 1500,
                                            className: ["toasted-primary", 'bg-warning']
                                        });
                                    }
                                },
                                error: function(response) {
                                    error_arr.push('載具編號碼驗證有誤');
                                }
                            });
                        }
                    }
                } else if ($('#CarrierType').val() == '2') {
                    if ($('#CarrierNum').val() == '') {
                        error_arr.push("請輸入自然人憑證號碼");
                    } else {
                        regex = /^[A-Z]{2}\d{14}$/;

                        if (!$('#CarrierNum').val().match(regex)) {
                            error_arr.push("自然人憑證號碼格式錯誤");
                        }
                    }
                } else {
                    error_arr.push("請選擇載具類型");
                }

                break;
                // 公司戶發票(三聯發票)
            case '4':
                if ($('#company_title').val() == '') {
                    error_arr.push("{{Lang::get('請輸入')}}{{Lang::get('公司抬頭')}}");
                }

                if ($('#uniform_numbers').val() == '') {
                    error_arr.push("{{Lang::get('請輸入')}}{{Lang::get('統一編號')}}");
                } else {
                    regex = /^\d{8}$/;

                    if (!$('#uniform_numbers').val().match(regex)) {
                        error_arr.push("{{Lang::get('統一編號格式錯誤')}}");
                    }
                }

                break;
                // 捐贈
            case '5':
                if ($('#LoveCode').val() == '') {
                    error_arr.push("請輸入捐贈碼");
                } else {
                    // 可能會暫時性關閉，因財政部系統可能會進行不預期的維護作業
                    $.ajax({
                        url: "{{url('Cart/invoice_lovecode_check')}}",
                        type: 'GET',
                        async: false,
                        data: {
                            LoveCode: $('[name="LoveCode"]').val(),
                        },
                        success: function(response) {
                            if (response.code == 0) {
                                error_arr.push(response.msg);
                            }
                        },
                        error: function(response) {
                            error_arr.push('捐贈碼驗證有誤');
                        }
                    });
                }

                break;
            default:
                break;
        }

        if (final_priceVM.discountSelect != 'none_discount') {
            if (final_priceVM.discountSelect == 'directcoupon') {
                if ($('input[name="directcoupon_input"]').val() == '') {
                    error_arr.push("{{Lang::get('請輸入活動優惠券代碼')}}");
                } else {
                    if (final_priceVM.code_success_name == '' && final_priceVM.code_success_info == '') {
                        error_arr.push("{{Lang::get('活動優惠券代碼未套用')}}");
                    }
                }
            }
        }

        if ($('#accept_policy').prop('checked') == false) {
            error_arr.push("{{Lang::get('未勾選購買須知')}}");
        }
        /* --- Test End --- */

        if (error_arr.length > 0) {
            let error_html = '';
            error_arr.forEach((error) => {
                error_html += `<span>${error}</span><br>`;
            });
            $('#errorDetail').html(error_html);
            $('#errorModal_btn').click();
        } else {
            if (["{{Lang::get('宅配')}}", "{{Lang::get('到店取貨')}}", "{{Lang::get('黑貓宅配')}}", "{{Lang::get('冷藏宅配')}}", "{{Lang::get('冷凍宅配')}}", "黑貓宅配(貨到付款)"].includes(send_way_name) == true) {
                if (send_way_name == "{{Lang::get('宅配')}}" || send_way_name == "{{Lang::get('黑貓宅配')}}" || send_way_name == "{{Lang::get('冷藏宅配')}}" || send_way_name == "{{Lang::get('冷凍宅配')}}" || send_way_name == "黑貓宅配(貨到付款)") {
                    $('input[name="addrC"]').val($('#myZip').val() + ' ' + city_town.result + $('#transport_location').val());
                }
            } else {
                if (use_thirdpart_logistic) {
                    $('input[name="addrC"]').val($('input[name="CVSStoreID"]').val() + ' ' + $('input[name="CVSStoreName"]').val() + ' ' + $('input[name="CVSTelephone"]').val());
                } else {
                    $('input[name="addrC"]').val($('#storeNo').val() + ' ' + $('#storeName').val() + ' ' + $('#storeAddr').val());
                }
            }

            let itemLength = 0,
                itemContent = '';

            for (item of cart_tableVM.real_cartData) {
                itemLength += Number(item.num);
                var item_name = item.info_title;
                item_name += item.type_title ? (' - ' + item.type_title) : '';
                itemContent += `
                <div class="item d-flex justify-content-between align-items-center">
                    <div class="d-flex flex-wrap align-items-center">
                        <div class="img mr-2" style="background-image: url(&quot;/public/static/index/${item.info_pic1}&quot;);"></div>
                        <p class="mb-0">${item_name}</p>
                    </div>
                    <div class="d-flex flex-column align-items-end">
                        <span class="mb-2">x<span>${final_priceVM.formatPrice(item.num)}</span></span>
                        <span>{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}${final_priceVM.formatPrice(item.countPrice * item.num)}</span>
                    </div>
                </div>
                `;
            }

            let itemLength_text = "{{Lang::get('共 XX 件')}}".replace('XX', final_priceVM.formatPrice(itemLength));
            $('#finalModal_itemLength').html(itemLength_text);
            let item_count_text = "{{Lang::get('共 XX 件商品')}}".replace('XX', final_priceVM.formatPrice(itemLength));
            $('#finalModal_itemCount').html(item_count_text);
            $('#itemContent').html(itemContent);

            let gift = await $.ajax({
                url: "{{url('Cart/get_current_gift')}}",
                type: "GET",
                async: false,
            });

            if (gift.code == 0) {
                alert(gift.msg + '，請聯繫客服人員');
            } else {
                if (gift.code == 200) {
                    console.log(gift.msg);
                    $('.gift').hide();
                    $('#giftContent').html('');
                } else {
                    if (!gift.data) {
                        $('.gift').hide();
                        $('#giftContent').html('');
                    } else {
                        $('.gift').show();

                        let giftContent = '';

                        if (gift.data.length == 0) {
                            $('.gift').hide();
                            $('#giftContent').html('');
                        } else {
                            gift.data.forEach((g) => {
                                giftContent += `
                                <div class="item d-flex justify-content-between align-items-center">
                                    <div class="d-flex flex-wrap align-items-center">
                                        <div class="img mr-2" style="background-image: url(&quot;/public/static/index/${g.picture}&quot;);"></div>
                                        <p class="mb-0">${g.title}</p>
                                    </div>
                                    <div class="d-flex flex-column align-items-end">
                                        <span class="mb-2">x<span>${g.num}</span></span>
                                    </div>
                                </div>
                                `;
                            });

                            $('#giftContent').html(giftContent);

                            $('input[name="gift"]').val(JSON.stringify(gift.data.map(g => {
                                return {
                                    id: g.id,
                                    num: g.num,
                                };
                            })));
                        }
                    }
                }

                $('#finalModal_subTotal').html(`{{config('extra.shop.dollar_symbol')}}` + final_priceVM.formatPrice(final_priceVM.finalsubTotal));
                $('#finalModal_discount').html(`-{{config('extra.shop.dollar_symbol')}}` + final_priceVM.formatPrice(final_priceVM.finalDiscount));
                $('#finalModal_payWayName').html(pay_way_name);
                $('#finalModal_sendWayName').html(send_way_name);
                $('#finalModal_shipping').html(`{{config('extra.shop.dollar_symbol')}}` + final_priceVM.formatPrice(final_priceVM.finalShipping));
                $('#finalModal_total').html(`{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}` + final_priceVM.formatPrice(final_priceVM.finalsubTotal - final_priceVM.finalDiscount + final_priceVM.finalShipping));

                $('#confirmModal_btn').click();
            }
        }
    });

    function swal_fire(message) {
        Swal.fire({
            title: message,
            icon: 'error',
            confirmButtonText: "{{Lang::get('確認')}}",
            confirmButtonColor: 'var(--btn-mainlink)',
        });
    }
</script>
<!-- 付款法功能 -->
<script>
    var payingData = {
        all_type: [],
        type_decode: [],
        pay_way: '',
        cartData: [],
    }
    var payingVM = new Vue({
        el: '#paying_div',
        data: payingData,
        computed: {
            can_card_pay() {
                for (let idx = 0; idx < this.cartData.length; idx++) {
                    const element = this.cartData[idx];

                    if (element.card_pay == 0) {
                        return false;
                    }
                }

                return true;
            },
        },
        methods: {},
    });
    /* 初始化勾選付法 */
    function init_paying() {
        var paying_type = payingVM.type_decode;

        if (paying_type.length == 0) {
            $('#confirm_form').hide();

            Swal.fire({
                title: "{{Lang::get('目前購買之商品無可用付款方式，無法下單')}}",
                icon: 'error',
                content: '',
                confirmButtonText: "{{Lang::get('確認')}}",
                confirmButtonColor: 'var(--btn-mainlink)',
            });
        }
    }
</script>
<!-- 運費功能 -->
<script>
    var shippingData = {
        type_decode: [],
        send_way: 0,
    }
    var maskVM = new Vue({
        el: '#shipping_div',
        data: shippingData,
        computed: {},
        methods: {},
    });
    /* 初始化勾選運費 */
    function init_shipping() {
        var shipping_type = maskVM.type_decode;

        if (shipping_type.length > 0) {
            maskVM.send_way = shipping_type[0]['shipping_fee_id'];
        } else {
            $('#confirm_form').hide();

            Swal.fire({
                title: "{{Lang::get('目前購買之商品無可用配送方式，無法下單')}}",
                icon: 'error',
                content: '',
                confirmButtonText: "{{Lang::get('確認')}}",
                confirmButtonColor: 'var(--btn-mainlink)',
            });
        }
    }

    function get_order_form_data() {
        let order_data = {};
        let data = $('#cartform').serialize();
        data = decodeURIComponent(data);
        data = data.split('&');

        for (let i = 0; i < data.length; i++) {
            let temp = data[i].split('=');
            order_data[temp[0]] = temp[1];
        }

        cartform_obj = new FormData(cartform);
        order_data['become_member'] = cartform_obj.get('become_member') ?? '0';

        return order_data;
    }

    function selectPlace() {
        if (use_thirdpart_logistic) {
            let order_data = get_order_form_data();
            let selectPlace_url = "{{url('Cart/selectPlace')}}" + '?' + 'Order_Data=' + JSON.stringify(order_data);
            location.href = selectPlace_url;
        } else {
            let send_way_name = $('option:selected', 'select[name="send_way"]').text().split(' | ')[0];

            switch (send_way_name) {
                case "{{Lang::get('7-11取貨')}}":
                    window.open('https://emap.pcsc.com.tw/');

                    break;
                case "{{Lang::get('全家取貨')}}":
                    window.open('https://www.family.com.tw/Marketing/Map');

                    break;
                case "{{Lang::get('萊爾富取貨')}}":
                    window.open('https://www.hilife.com.tw/storeInquiry_street.aspx');

                    break;
                case "{{Lang::get('OKmart取貨')}}":
                    window.open('https://www.okmart.com.tw/convenient_shopSearch');

                    break;
                default:
                    break;
            }
        }
    }
</script>
<!-- 捐贈碼功能 -->
<script>
    var loveCodeData = {
        type_decode: [],
        lovecode: 0,
    }
    var loveCodeVM = new Vue({
        el: '#loveCode_div',
        data: loveCodeData,
        methods: {
            changeLoveCode: function() {
                var code = $('#LoveCode_select').val();

                if (code == '') {
                    $('#LoveCode').val('');
                } else {
                    $('#LoveCode').val(code);
                }
            },
        },
    });
    /* 初始化捐贈碼 */
    function init_love_code() {
        var loveCode_type = loveCodeVM.type_decode;

        if (loveCode_type.length > 0) {
            loveCodeVM.lovecode = loveCode_type[0]['id'];
            $('#LoveCode_select').val(loveCode_type[0]['code']);
            $('#LoveCode').val(loveCode_type[0]['code']);
        }
    }
</script>

<!-- 縣市下拉選 -->
<script>
    //利用jQuery的ajax把縣市編號(CNo)傳到town_ajax.php把相對應的區域名稱回傳後印到選擇區域(鄉鎮)下拉選單
    if ($('#myCity').val()) {
        var CNo = $('#myCity').val();
        $.ajax({
            type: "POST",
            headers: {
                'X-CSRF-TOKEN': csrf_token
            },
            url: "{{url('Login/town_ajax')}}",
            cache: false,
            data: {
                CNo: CNo
            },
            success: function(data) {
                $('#myTown').html(`<option value="">{{Lang::get('請選擇鄉鎮區')}}</option>` + data);
                $('#myZip').val(""); //避免重新選擇縣市後郵遞區號還存在，所以在重新選擇縣市後郵遞區號欄位清空
            }
        });
    }

    $('#myCity').change(function() {
        var CNo = $('#myCity').val();
        $.ajax({
            type: "POST",
            headers: {
                'X-CSRF-TOKEN': csrf_token
            },
            url: "{{url('Login/town_ajax')}}",
            cache: false,
            data: {
                CNo: CNo
            },
            success: function(data) {
                $('#myTown').html(`<option value="">{{Lang::get('請選擇鄉鎮區')}}</option>` + data);
                $('#myZip').val(""); //避免重新選擇縣市後郵遞區號還存在，所以在重新選擇縣市後郵遞區號欄位清空
            }
        });
    });

    //利用jQuery的ajax把縣市編號(CNo)傳到town_ajax.php把相對應的區域名稱回傳後印到選擇區域(鄉鎮)下拉選單
    $('#myTown').change(function() {
        var TNo = $('#myTown').val();
        $.ajax({
            type: "POST",
            headers: {
                'X-CSRF-TOKEN': csrf_token
            },
            url: "{{url('Login/zip_ajax')}}",
            cache: false,
            data: {
                TNo: TNo
            },
            success: function(data) {
                $('#myZip').val(data);
            },
        });
    });

    async function get_city_town(city, town) {
        let result;

        try {
            data = await $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                url: "{{url('Login/city_town_ajax')}}",
                cache: false,
                async: false,
                data: {
                    city: city,
                    town: town
                },
            });
            result = data;
        } catch (error) {
            result = '';
        }

        return result;
    }
</script>

<script type="text/javascript">
    // 發票設定
    function showInputs(type) {
        $('.invoice-input').hide();
        $('#needUniform').val('0');
        $('#uniform_numbers').val('');
        $('#company_title').val('');
        $('#CarrierType').val('3');
        carrierNumLenChange();
        $('#CarrierNum').val('');
        $('#LoveCode_select').val($('#LoveCode_select .main option').first().val());
        $('#LoveCode').val($('#LoveCode_select .main option').first().val());

        switch (type) {
            case '2':
                $('#emailNote').show();

                break;
            case '3':
                $('#CarrierType').parent().parent().show();

                break;
            case '4':
                $('#company_title').parent().parent().show();

                break;
            case '5':
                $('#LoveCode_select').parent().parent().show();

                break;
            default:
                break;
        }
    }
    showInputs($('#invoice_style').val());

    $(function() {
        /*優惠方案選擇*/
        let accordionBtns = $('#myAccordion input[type="radio"]');
        accordionBtns.click(function() {
            let btn = $(this);

            if (btn.next().next().hasClass("d-none")) {
                btn.next().next().removeClass("d-none");
            }

            accordionBtns.each(function(index, otherBtn) {
                if (btn.is(otherBtn) == false) {
                    $(otherBtn).next().next().addClass("d-none");
                }
            });
        });

        /*避免enter送出訂單*/
        $('#cartform input').on('keydown', function(e) {
            if (e.keyCode == 13) {
                e.preventDefault();

                return false;
            }
        });
    });
</script>

<script>
    const cart_session = 'cart_all';
    const no_addr_ship = "{{Lang::get('到店取貨')}}"; /*免填地址的運送方式 到店取貨*/
    /*免選擇縣市的運送方式*/
    const dont_need_select_ct = [
        no_addr_ship,
        "{{Lang::get('全家取貨')}}",
        "{{Lang::get('7-11取貨')}}",
        "{{Lang::get('萊爾富取貨')}}",
        "{{Lang::get('OKmart取貨')}}"
    ];
    const ecpay_shippable_supermarket = JSON.parse("{{json_encode(config('extra.ecpay.shippable_supermarket'))}}".replace(/&quot;/g, '"').trim()); /*綠界物流有物流功能的 超商 運送方式*/
    const use_thirdpart_logistic = "{{config('control.thirdpart_logistic')}}" == "1" /*是否有使用第三方物流功能*/ ;
    const use_thirdpart_invoice = "{{config('control.thirdpart_invoice')}}" == "1" /*是否有使用綠界電子發票功能*/ ;
    const closeMember = "{{config('control.close_function_current')['會員管理'] ?? ''}}"; /*關閉會員功能*/

    let final_prices = {
        discountSelect: null,

        discount: null,
        available_points: 0,
        finalsubTotal: 0,
        finalDiscount: 0,
        finalShipping: 0,
        finalShippingDiscountDiff: 0,
        currentShippingType: 0,

        point_error_msg: '',
        point_success_info: '',
        point_input: 0,

        code_error_msg: '',
        code_success_name: '',
        code_success_info: '',

        coupon_pool_id: 0,
    }

    const final_priceVM = new Vue({
        el: '#finalPart',
        data: final_prices,
        watch: {
            discountSelect: {
                handler(v_new, v_old) {
                    if (v_new == v_old) {
                        return;
                    }

                    this.set_total(v_new);
                },
            },
        },
        methods: {
            formatPrice(price) {
                return price.toLocaleString();
            },
            toNumber(num) {
                return Number(num);
            },
            select_coupon(coupon_pool_id) {
                this.coupon_pool_id = coupon_pool_id;
                this.set_discount_selected('coupon');
            },
            set_discount_selected(new_discountSelect) {
                this.discountSelect = new_discountSelect;
                this.set_total(new_discountSelect);
            },
            set_total: async function(new_discountSelect) {
                var orderdata_discount = new_discountSelect;
                var orderdata_point = 0;
                var orderdata_directcoupon_code = '';

                if (new_discountSelect == 'firstbuy_select') {
                    /*會員首購優惠*/
                    this.finalDiscount = this.discount.discountData.firstBuyDiscount.discount;
                } else if (new_discountSelect == 'vipdiscount_select') {
                    /*VIP會員優惠*/
                    this.finalDiscount = this.discount.discountData.vipDiscount.discount;
                } else if (new_discountSelect == 'points_0') {
                    /*紅利點數優惠*/
                    orderdata_discount = 'none_discount';

                    if (!this.point_input || this.point_input == 0) {
                        this.finalDiscount = 0;
                    } else {
                        if (this.point_input > this.finalsubTotal + this.finalShipping) {
                            this.point_error_msg = `您最多可使用<span class="num" style="color: var(--mark);">${this.finalsubTotal + this.finalShipping}</span>點`;
                            this.point_success_info = ``;

                            this.finalDiscount = 0;
                            Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                        } else if (this.point_input > this.discount.points_limit) {
                            this.point_error_msg = `您最多可使用<span class="num" style="color: var(--mark);">${this.discount.points_limit}</span>點`;
                            this.point_success_info = ``;

                            this.finalDiscount = 0;
                            Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                        } else {
                            this.point_error_msg = ``;
                            this.point_success_info = `此次使用點數<span class="num" style="color: var(--mark);">${this.point_input}</span>點，折抵<span class="num" style="color: var(--mark);">${this.point_input}</span>元。`;

                            orderdata_point = this.point_input;
                            this.finalDiscount = this.point_input;
                            orderdata_discount = 'points_' + this.point_input;
                        }
                    }
                } else if (new_discountSelect == 'directcoupon') {
                    /*直接輸入型優惠券*/
                    orderdata_discount = 'none_discount';
                    var resp = await this.get_direct_coupon_discount();

                    if (resp.user_code) {
                        if (resp.status == 0) {
                            this.code_error_msg = `{{Lang::get('請重新檢查代碼是否填寫有誤或購買條件是否達成')}}`;
                            this.code_success_name = ``;
                            this.code_success_info = ``;

                            this.finalDiscount = 0;
                            Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                        } else {
                            this.code_error_msg = ``;
                            this.code_success_name = resp.name;
                            this.code_success_info = `折抵${resp.discount}元，此折扣碼${resp.end}。`;

                            this.finalDiscount = Number(resp.discount);
                            orderdata_directcoupon_code = resp.user_code;
                            orderdata_discount = 'directcoupon_' + Number(resp.id);
                        }
                    } else {
                        this.code_error_msg = resp.msg;
                        this.code_success_name = ``;
                        this.finalDiscount = 0;
                        Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                    }
                } else if (new_discountSelect == 'coupon') {
                    /*會員優惠券*/
                    use_coupon = false;

                    if (this.coupon_pool_id) {
                        for (let idx = 0; idx < this.discount.discountData.coupon.length; idx++) {
                            const coupon = this.discount.discountData.coupon[idx];

                            if (coupon.coupon_pool_id == this.coupon_pool_id) {
                                use_coupon = true;
                                this.finalDiscount = coupon.discount;
                                orderdata_discount = 'coupon_' + this.coupon_pool_id;
                            }
                        }
                    }

                    if (!use_coupon) {
                        orderdata_discount = 'none_discount';
                        this.finalDiscount = 0;
                    }
                } else if (new_discountSelect == 'acts_0') {
                    /*活動優惠*/
                    let act_discount = this.finalsubTotal - this.discount.discountData.acts.sum;

                    if (act_discount > 0) {
                        this.finalDiscount = act_discount;
                    } else {
                        orderdata_discount = 'none_discount';
                        this.finalDiscount = 0;
                    }
                } else {
                    this.discountSelect = 'none_discount';

                    this.point_error_msg = '';
                    this.point_success_info = '';

                    this.code_success_name = '';
                    this.code_success_info = '';

                    this.finalDiscount = 0;
                }

                $('input[name="discount"]').val(orderdata_discount);
                $('input[name="point"]').val(orderdata_point);
                $('input[name="directcoupon_code"]').val(orderdata_directcoupon_code);
            },
            async get_direct_coupon_discount() {
                var user_code = $('input[name="directcoupon_input"]').val();

                if (!user_code) {
                    this.code_error_msg = ``;
                    this.code_success_name = ``;
                    this.code_success_info = ``;

                    return {};
                }

                return await $.ajax({
                    url: "{{url('Coupondirect/get_discount')}}",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': csrf_token
                    },
                    data: {
                        user_code: encodeURI(user_code),
                    },
                });
            },
        },
    });

    let cart_table_data = {
        cartData: [],
        real_cartData: [],
        discount: null,
        types_need_checked: [],
        types_need_limit: [],
        types_need_option: [],
        customize_box: [],
        total: 0,

        control_card_pay: "{{config('control.control_card_pay')}}",
        control_register: "{{config('control.control_register')}}",
        thirdpart_money: "{{config('control.thirdpart_money')}}",
        /*關閉網紅功能*/
        closeKol: "{{config('control.close_function_current')['網紅列表'] ?? ''}}",
        /*關閉加價購功能*/
        closeAddPrice: "{{config('control.close_function_current')['加價購設定'] ?? ''}}",
    }

    let OrderData_temp = `{!! $data['OrderData'] !!}`.replaceAll(/\r\n/g, "\\n").trim();

    function get_cart_data(need_init = false) {
        $.ajax({
            type: 'post',
            dataType: 'json',
            headers: {
                'X-CSRF-TOKEN': csrf_token
            },
            url: "{{url('Cart/ajax_cart_data')}}",
            data: {
                cart_session: cart_session
            },
            success: function(resp) {
                if (resp.cartData.length == 0) {
                    $('#confirm_form').hide();
                }

                if (resp.customize_box.length > 0) {
                    payingVM.cartData = resp.cartData.slice(0, 1);
                    cart_table_data.cartData = resp.cartData.slice(0, 1);
                    cart_table_data.real_cartData = resp.cartData;
                } else {
                    payingVM.cartData = resp.cartData;
                    cart_table_data.cartData = resp.cartData;
                    cart_table_data.real_cartData = resp.cartData;
                }

                cart_table_data.discount = resp.discount[0];

                if (cart_table_data.discount.acts_c > 0) {
                    final_priceVM.discountSelect = "acts_0";
                } else {
                    final_priceVM.discountSelect = "none_discount";
                }

                cart_table_data.types_need_checked = resp.types_need_checked;
                cart_table_data.types_need_limit = resp.types_need_limit;
                cart_table_data.types_need_option = resp.types_need_option;
                cart_table_data.customize_box = resp.customize_box;

                cart_table_data.total = 0;
                resp.cartData.forEach((value) => {
                    cart_table_data.total += Number(value.countPrice) * Number(value.num);
                });

                final_priceVM.finalsubTotal = cart_table_data.total;
                final_priceVM.discount = resp.discount[0];
                final_priceVM.available_points = resp.discount[0].discountData.points[0].point;

                payingData.all_type = resp.paying;
                payingData.type_decode = resp.paying;
                shippingData.type_decode = resp.shipping;
                loveCodeData.type_decode = resp.love_code_list;

                send_way_infos($('[name="send_way"]').val());

                if (need_init) {
                    init_paying();
                    init_shipping();
                    init_love_code();
                    setTimeout(() => {
                        if (OrderData_temp) {
                            OrderData_temp = JSON.parse(OrderData_temp);

                            for (let key in OrderData_temp) {
                                if (key == 'selectPlace') {
                                    if (OrderData_temp[key]['CVSStoreID'] && OrderData_temp[key]['CVSStoreName']) {
                                        $('#store').val(OrderData_temp[key]['CVSStoreID'] + ' ' + OrderData_temp[key]['CVSStoreName']);
                                    }

                                    for (let key2 in OrderData_temp[key]) {
                                        $('input[name="' + key2 + '"]').val(OrderData_temp[key][key2]);
                                    }
                                } else {
                                    if (['ps', ].includes(key)) {
                                        $('textarea[name="' + key + '"]').val(OrderData_temp[key]);
                                    } else if (['send_way', 'F_I_CNo', 'F_I_TNo', 'pay_way', 'invoice_style', 'CarrierType', ].includes(key) == false) {
                                        $('input[name="' + key + '"]').val(OrderData_temp[key]);
                                    } else {
                                        $('select[name="' + key + '"]').val(OrderData_temp[key]);

                                        if (key == 'send_way') {
                                            send_way_infos(OrderData_temp[key]);
                                        } else if (key == 'invoice_style') {
                                            showInputs(OrderData_temp[key]);
                                        }
                                    }
                                }
                            }
                        }
                    }, 500);
                }
            },
            error: function(xhr) {
                console.log(xhr);
            }
        });
    }
    get_cart_data(true);

    const cart_tableVM = new Vue({
        el: '#cart_table',
        data: cart_table_data,
        methods: {
            get_target_product: function(type_id) {
                self = this;

                for (var i = 0; i < self.cartData.length; i++) {
                    const item = self.cartData[i];

                    if (item.type_id == type_id) {
                        return [i, item];
                    }
                }

                return [-1, {}];
            },
            get_total: function() {
                self = this;

                if (self.cartData.length == 0) {
                    $('#confirm_form').hide();
                    swal_fire("{{Lang::get('購物車內無商品')}}");

                    return;
                }

                self.total = 0;

                self.cartData.forEach((value) => {
                    self.total += Number(value.countPrice) * Number(value.num);
                });

                final_priceVM.finalsubTotal = self.total;

                if ($('select[name="send_way"]').val()) {
                    if (maskVM.type_decode[final_priceVM.currentShippingType].free_rule >= 0 && final_priceVM.finalsubTotal < maskVM.type_decode[final_priceVM.currentShippingType].free_rule && maskVM.type_decode[final_priceVM.currentShippingType].price > 0) {
                        final_priceVM.finalShipping = Number(maskVM.type_decode[final_priceVM.currentShippingType].price);
                        final_priceVM.finalShippingDiscountDiff = maskVM.type_decode[final_priceVM.currentShippingType].free_rule - final_priceVM.finalsubTotal;
                        $('#discountDiff').removeClass('d-none');
                        $('#discountOver').addClass('d-none')
                    } else {
                        if (maskVM.type_decode[final_priceVM.currentShippingType].free_rule >= 0) {
                            final_priceVM.finalShipping = 0;
                            final_priceVM.finalShippingDiscountDiff = 0;
                            $('#discountDiff').addClass('d-none');
                            $('#discountOver').removeClass('d-none');
                        } else {
                            final_priceVM.finalShipping = maskVM.type_decode[final_priceVM.currentShippingType].price;
                            final_priceVM.finalShippingDiscountDiff = 0;
                            $('#discountDiff').addClass('d-none');
                            $('#discountOver').addClass('d-none');
                        }
                    }
                }
            },
            changeNum: async function(direction, type_id) {
                self = this;
                let amount = Number($('#count_' + type_id).val());

                if (direction == 1) {
                    amount = amount + 1;
                } else if (direction == -1) {
                    amount = amount - 1
                }

                $('#count_' + type_id).val(amount);

                self.realChangeNum(amount, type_id);
            },
            realChangeNum: async function(amount, type_id) {
                self = this;
                [index, item] = self.get_target_product(type_id);

                if (index > -1) {
                    item.num = amount;
                    num = amount;

                    if (num != 0) {
                        await cart_cartCtrl(num, type_id, 'assign');
                    } else {
                        self.cartData.splice(index, 1);

                        await cart_cartCtrl(num, type_id, 'delete');
                    }

                    get_cart_data();
                    final_priceVM.set_discount_selected('none_discount');
                    self.get_total();

                    return;
                }
            },
            deleteCtrl: async function(type_id) {
                self = this;
                [index, item] = self.get_target_product(type_id);

                if (index != -1) {
                    self.cartData.splice(index, 1);

                    await cart_cartCtrl(0, type_id, 'delete');

                    get_cart_data();
                    final_priceVM.set_discount_selected('none_discount');
                    self.get_total();

                    return;
                }
            },
            formatPrice(price) {
                return price.toLocaleString();
                // return price;
            },
            toNumber(num) {
                return Number(num);
            },
            /*解code 填寫資料*/
            register_data: function(examinee_index, type_id) {
                self = this;
                [index, item] = self.get_target_product(type_id);

                if (index != -1) {
                    data = item.examinee[examinee_index]['register_data'];

                    if (typeof(data) == 'string') {
                        data = JSON.parse(data)
                    }
                } else {
                    data = {};
                }

                return data;
            },
            /*開啟填寫報名資料畫面(新增)*/
            add_examinee: function(type_id) {
                self = this;
                /*檢查填寫的報名資料數量*/
                [index, item] = self.get_target_product(type_id);

                if (index != -1) {
                    if (item.examinee.length >= item.num) {
                        Vue.toasted.show("{{Lang::get('報名資料已填寫完畢')}}", vt_warning_obj);

                        return;
                    }

                    self.open_examinee_panel(type_id, examinee_id = 0);
                }
            },
            /*開啟填寫報名資料畫面(修改)*/
            edit_examinee: function(type_id, examinee_id) {
                self = this;
                self.open_examinee_panel(type_id, examinee_id);
            },
            /*打開填寫報名資料面板*/
            open_examinee_panel: function(type_id, examinee_id) {
                self = this;
                [index, item] = self.get_target_product(type_id);

                if (index != -1) {
                    $('#model_btn').click();
                    $('#modelTitle').html("{{Lang::get('填寫報名資料')}}");
                    $.ajax({
                        url: "{{url('Examination/examinee_panel')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        data: {
                            vm_name: 'cart_tableVM',
                            title: item.info_title + ' - ' + item.type_title,
                            type_id: item.type_id,
                            type_product_id: item.type_product_id,
                            examinee_id: examinee_id,
                        },
                        success: function(re) {
                            $('#boxModel').html(re);
                        },
                    });
                }
            },
            /*新增、修改報名資料*/
            save_exinfo: function(examinee_id, register_data, type_id) {
                self = this;
                [index, item] = self.get_target_product(type_id);

                if (index != -1) {
                    $('#body_block').show();

                    return $.ajax({
                        url: "{{url('Examination/examinee_save')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        data: {
                            type_id: item.type_id,
                            type_product_id: item.type_product_id,
                            examinee_id: examinee_id,
                            register_data: register_data,
                        },
                        success: function(res) {
                            if (res.code == 1) {
                                examinee_data = JSON.parse(res.msg);
                                examinee_data.id = examinee_data.examinee_id;
                                delete examinee_data.examinee_id;

                                if (examinee_id == 0) {
                                    /*新增*/
                                    self.cartData[index].examinee.push(examinee_data);
                                } else {
                                    /*修改*/
                                    for (var i = 0; i < self.cartData[index].examinee.length; i++) {
                                        if (self.cartData[index].examinee[i].id == examinee_id) {

                                            Vue.set(self.cartData[index].examinee, i, examinee_data);
                                        }
                                    }
                                }

                                $('#model span[aria-hidden="true"]').click();
                                Vue.toasted.show("{{Lang::get('操作成功')}}", {
                                    duration: 1500,
                                    className: "bg-success"
                                });
                            } else {
                                Vue.toasted.show(res.msg, {
                                    duration: 1500,
                                    className: "bg-danger"
                                });
                            }
                            $('#body_block').hide();
                        },
                        error: function(e) {
                            $('#body_block').hide();
                        },
                    });
                }
            },
            /*刪除報名資料*/
            del_examinee: function(examinee_index, type_id) {
                self = this;
                [index, item] = self.get_target_product(type_id);

                if (index != -1) {
                    const examinee_id = item.examinee[examinee_index].id;
                    $.ajax({
                        url: "{{url('Examination/examinee_delete')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        data: {
                            examinee_id: examinee_id,
                        },
                        success: function(res) {
                            if (res.code == 1) {
                                self.cartData[index].examinee.splice(examinee_index, 1);
                                Vue.toasted.show(res.msg, {
                                    duration: 1500,
                                    className: "bg-success"
                                });
                            } else {
                                Vue.toasted.show(res.msg, {
                                    duration: 1500,
                                    className: "bg-danger"
                                });
                            }
                        },
                    });
                }
            },
        },
        updated: function() {
            $('[data-toggle="tooltip"]').tooltip();
        },
    })
    window['cart_tableVM'] = cart_tableVM;
</script>