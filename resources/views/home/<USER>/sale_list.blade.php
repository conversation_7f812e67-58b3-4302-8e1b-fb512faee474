@extends('home.Public.mainTpl')
@section('title'){{Lang::get('銷售列表')}} - {{Lang::get('網紅後台')}}@endsection
@section('css')
    <style>
        .hide {
            display: none;
        }

        .memberContentBox .bindingBox a.bindingBtn.use-btn:hover {
            cursor: default;
        }

        .orderTable td,
        .orderTable th {
            text-align: center;
        }

        .orderTable img {
            width: 50px;
        }

        .tableListA table.productTable {
            border: none;
        }

        .productTable img {
            max-width: 100px;
        }

        .tableListA .productTable tr td {
            background: #fff;
            border: none;
            width: 20%;
        }

        ul.tabNavBox {
            margin-bottom: 0;
        }

        .activeBtn {
            background-color: transparent !important;
            color: var(--btn-link-white) !important;
        }

        .tabNavBox input {
            border: none !important;
            background-color: transparent !important;
            color: var(--btn-sublink) !important;
            display: block;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .tabNavBox input:hover,
        ul.tabNavBox li a:hover {
            color: var(--btn-link-white) !important;
        }
    </style>
@endsection
@section('content')
    <div>
        <section class="directoryRow">
            <div class="container">
                <ul>
                    <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                    <li><a href="{{url('Kol/kol_data')}}">{{Lang::get('網紅後台')}}</a></li>
                    <li><a href="{{url('Kol/sale_list')}}">{{Lang::get('銷售列表')}}</a></li>
                </ul>
            </div>
        </section>
        <section class="container max-wideVersion productPublic">
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
            <div id="itemBox" class="memberInforBox">
                <div id="leftBox">
                    @include('home/kol/kol_menu')
                </div>
                <div id="rightContentBox" class="innerPageBox memberContentBox">
                    <div class="paddingSpacing">
                        <div class="pack">
                            <div class="memberTop">
                                <div class="titleBox">
                                    <div class="title">
                                        <h3>{{Lang::get('銷售列表')}}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-center mb-2">
                                <form action="" id="searchForm" name="searchForm" method="get">
                                    @csrf
                                    {{Lang::get('下單時間區間')}}
                                    <input type="date" name="start" placeholder="{{Lang::get('開始時間')}}" value="{$start}">~
                                    <input type="date" name="end" placeholder="{{Lang::get('結束時間')}}" value="{$end}">
                                    <input type="hidden" name="id" value="{{$data['kol']['id']}}">
                                    <input type="hidden" name="type" value="{{$data['type']}}">
                                    <span class="d-inline-block">
                                        <a class="button" onclick="$('#searchForm').submit();">{{Lang::get('搜尋')}}</a>
                                        <a class="button" onclick="location.href='/index/kol/sale_list?type={{$data['type']}}'">{{Lang::get('清除搜尋')}}</a>
                                    </span>
                                </form>
                            </div>

                            <!--查看類型-->
                            <ul class="nav tabNavBox justify-content-center">
                                <li :class="['nav-item','{{$data['type']}}' == '0' ? 'active' : '']">
                                    <a href="/index/kol/sale_list?type=0">
                                        {{Lang::get('未結算')}}
                                    </a>
                                </li>
                                <li :class="['nav-item','{{$data['type']}}' == '1' ? 'active' : '']">
                                    <a href="/index/kol/sale_list?type=1">
                                        {{Lang::get('結算中')}}
                                    </a>
                                </li>
                                <li :class="['nav-item','{{$data['type']}}' == '2' ? 'active' : '']">
                                    <a href="/index/kol/sale_list?type=2">
                                        {{Lang::get('己結算')}}
                                    </a>
                                </li>
                            </ul>

                            <div class="memberMiddle">
                                <div class="row">
                                    <table class="orderTable table table-striped table-bordered table-rwd">
                                        <thead>
                                            <tr class="tr-only-hide">
                                                <th>{{Lang::get('序號')}}</th>
                                                <th>{{Lang::get('日期')}}</th>
                                                <th>{{Lang::get('訂單編號')}}</th>
                                                <th>{{Lang::get('客戶名')}}</th>
                                                <th>{{Lang::get('寄送地')}}</th>
                                                <th>{{Lang::get('總金額')}}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(item, index) in orderform">
                                                <td data-th="{{Lang::get('序號')}}" v-text="index+1"></td>
                                                <td data-th="{{Lang::get('日期')}}" v-text="timestamp_to_date(item.create_time)"></td>
                                                <td data-th="{{Lang::get('訂單編號')}}">
                                                    <a data-toggle="modal" data-target="#viewDetail" @click="view_detail(item.product)" v-text="item.order_number"></a>
                                                </td>
                                                <td data-th="{{Lang::get('客戶名')}}" v-text="item.user_name"></td>
                                                <td data-th="{{Lang::get('寄送地')}}" v-text="item.transport_location"></td>
                                                <td data-th="{{Lang::get('總金額')}}" v-text="count_total(item.product)"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row paginationBox">
                        <div class="col-12 boxCenter">
                            <ul class="pagination" v-if="totalpage!=0">
                                <li class="disabled" v-if="currentpage==1"><span>«</span></li>
                                <li v-if="currentpage!=1"><a href="/index/kol/sale_list?type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page=1"><span>«</span></a></li>
                                
                                <li v-for="page in page_array()" :class="[page==currentpage?'active':'']">
                                    <span v-if="page==currentpage" v-text="page"></span>
                                    <a v-if="page!=currentpage" :href="'/index/kol/sale_list?type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+page">
                                        <span  v-text="page"></span>
                                    </a>
                                </li>
                                
                                <li class="disabled" v-if="currentpage==totalpage"><span>»</span></li>
                                <li v-if="currentpage!=totalpage"><a :href="'/index/kol/sale_list?type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+totalpage">»</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
            <!-- /////////////////////////////////////////// -->
        </section>

        <!--viewDetail Modal Start  詳細內容///////////////////////////////////////////////-->
        <div class="modal fade shoppingCart memberLogin smallMOdel" id="viewDetail" tabindex="-1" role="dialog" aria-labelledby="memberLoginTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="memberLoginTitle">{{Lang::get('詳細內容')}}</h5>
                    </div>
                    <div class="modal-body">
                        <table class="productTable orderTable table table-striped table-bordered table-rwd">
                            <thead>
                                <tr class="tr-only-hide">
                                    <th>{{Lang::get('商品名')}}</th>
                                    <th>{{Lang::get('商品圖')}}</th>
                                    <th>{{Lang::get('單價')}}</th>
                                    <th>{{Lang::get('數量')}}</th>
                                    <th>{{Lang::get('總價')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(product) in detail">
                                    <td data-th="{{Lang::get('商品名')}}" v-text="product.name"></td>
                                    <td data-th="{{Lang::get('商品圖')}}"><img :src="'{{request()->server('REQUEST_SCHEME')}}://'+product.url2"></td>
                                    <td data-th="{{Lang::get('單價')}}" v-text="product.price"></td>
                                    <td data-th="{{Lang::get('數量')}}" v-text="product.num"></td>
                                    <td data-th="{{Lang::get('總價')}}" v-text="product.total"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"></script>
    <script type="text/javascript">
        var rightContentBoxData = { orderform: JSON.parse(`{{json_encode($data['orderform'])}}`.replace(/&quot;/g, '"').trim()), totalpage:parseInt("{{$data['totalpage']}}"), currentpage:parseInt("{{$data['page']}}") }
        var rightContentBoxVM = new Vue({
            el: '#rightContentBox', 
            data: rightContentBoxData,
            methods: {
                page_array: function(){
                    var pages = [];
                    for (var i = -4; i < 4; i++) {
                        page = i+this.currentpage;
                        if( 1 <= page && page <= this.totalpage)
                            pages.push(page);

                        if(pages.length==5)
                            return pages;
                    }
                    return pages;
                },
                timestamp_to_date : function(unix_timestamp){
                    var a = new Date(unix_timestamp * 1000);
                    return moment(a).format('YYYY-MM-DD')
                },
                count_total : function(item){
                    total = 0;
                    for (var i = 0; i < item.length; i++) {
                        if(item[i]['key_type']=='kol{$kol.id}')
                            total += Number(item[i]['total']);
                    }
                    return total;
                },
                view_detail: function(item){
                    order = [];
                    for (var i = 0; i < item.length; i++) {
                        if(item[i]['key_type']=='kol{$kol.id}')
                            order.push(item[i]);
                    }
                    // console.log(order);
                    viewDetailVM.detail=order;
                },
            }
        });

        var viewDetail_data = {
            detail: [],
        }
        var viewDetailVM = new Vue({
            el: '#viewDetail', 
            data: viewDetail_data,
        });
    </script>

@endsection

