@extends('home.Public.mainTpl')

@section('title'){{Lang::get('我的收藏')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')

@endsection

@section('content')
<section class="directoryRow">
    <div class="container">
        <ul>
            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
            <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
            <li><a href="{{url('Member/favorites_product')}}">{{Lang::get('我的收藏')}}</a></li>
        </ul>
    </div>
</section>
<section class="container max-wideVersion productPublic">
    <div id="itemBox" class="memberInforBox">
        <div id="leftBox">
            @include('home.Public.member_menu')
        </div>
        <div id="rightContentBox" class="innerPageBox memberContentBox">
            <div class="paddingSpacing">
                <div class="titleBox">
                    <div class="title">
                        <h3>{{Lang::get('我的收藏')}}</h3>
                    </div>
                </div>
                <div class="memberMiddle">
                    <div class="couponBox">
                        <h3 class="title mb-3">{{Lang::get('商品列表')}}</h3>
                        <table class="orderTable table table-striped table-bordered table-rwd">
                            <thead>
                                <tr class="tr-only-hide">
                                    <th>{{Lang::get('圖片')}}</th>
                                    <th>{{Lang::get('名稱')}}</th>
                                    <!-- <th>{{Lang::get('編碼')}}</th> -->
                                    <th>{{Lang::get('操作')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(empty($data['products'])==false)
                                @foreach($data['products'] as $vo)
                                <tr>
                                    <td data-th="{{Lang::get('圖片')}}">
                                        <a href="{{url('Product/productinfo')}}?id={{$vo->id}}">
                                            <img src="{{__PUBLIC__}}{{$vo->pic}}" style="max-width:50px">
                                        </a>
                                    </td>
                                    <td data-th="{{Lang::get('名稱')}}">
                                        <a href="{{url('Product/productinfo')}}?id={{$vo->id}}">{{$vo->title}}</a>
                                    </td>
                                    <!-- <td data-th="{{Lang::get('編碼')}}">{{$vo->ISBN}}</td> -->
                                    <td data-th="{{Lang::get('操作')}}" width="120"><a href="javascript:store_record('{{$vo->id}}')" class="use-btn">{{Lang::get('取消')}}</a></td>
                                </tr>
                                @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                    <div class="row paginationBox">
                        <div class="col-12 boxCenter">
                            {{$data['products']->links('pagination.customize')}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('ownJS')
<script type="text/javascript">
    function store_record(prodInfoId) {
        $.ajax({
            method: "post",
            url: "{{url('Ajax/store_record')}}",
            headers: {
                'X-CSRF-Token': csrf_token
            },
            data: {
                prodInfoId: prodInfoId,
                status: "0",
            },
        }).success(function(data) {
            if (data.code == '1') {
                Swal.fire({
                    title: "{{Lang::get('操作成功')}}",
                    icon: 'success',
                    content: '',
                    confirmButtonText: "{{Lang::get('確認')}}",
                    confirmButtonColor: 'var(--btn-mainlink)',
                }).then((result) => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: "{{Lang::get('操作失敗')}}",
                    icon: 'error',
                    content: '',
                    confirmButtonText: "{{Lang::get('確認')}}",
                    confirmButtonColor: 'var(--btn-mainlink)',
                });
            }
        }).error(function(res) {
            // $('#body_block').hide();
        }) //error
    }
</script>
@endsection