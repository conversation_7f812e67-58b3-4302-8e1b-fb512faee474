@extends('home.Public.mainTpl')
@section('title'){{Lang::get('紅利點數')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Points/points')}}">{{Lang::get('紅利點數')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('紅利點數')}}</h3>
                                </div>
                            </div>
                            <button class="btn more float-right noPrint" type="button" data-toggle="modal" data-target="#descriptionContModel">{{Lang::get('使用說明')}}</button>
                        </div>
                        <div class="memberMiddle">
                            <div>
                                <h3 class="title mb-3">{{Lang::get('點數統計')}}</h3>
                                <p>
                                    {{Lang::get('目前累計紅利')}}：
                                    <span class="bonusNum"><span>{{$data['current_points']}}</span></span>

                                    @if(config('control.control_point_duration') != 99)
								        <span style="padding-left: 150px;">
                                            {{Lang::get('將到期的點數')}}
                                            ({{date('Y').'-'.config('control.control_point_duration_date')}})：
                                            <span>{{$data['expiring_points']}}</span>
                                        </span>
                                    @endif
							    </p>
                            </div>
                            <hr>
                            <div class="couponBox">
                                <h3 class="title mb-3">{{Lang::get('歷史紀錄')}}</h3>
                                <table class="orderTable table table-striped table-bordered table-rwd">
                                    <thead>
                                        <tr class="tr-only-hide">
                                            <th width="100">{{Lang::get('日期')}}</th>
                                            <th width="200">{{Lang::get('說明')}}</th>
                                            <th width="100">{{Lang::get('數量')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
									@foreach($data['records'] as $vo)	
                                        <tr>
                                            <td data-th="{{Lang::get('日期')}}">{{$vo['msg_time']}}</td>	
											<td data-th="{{Lang::get('說明')}}">{{$vo['msg']}}</td>
											<td data-th="{{Lang::get('數量')}}">{{$vo['points']}}</td>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="memberBottom">
                            <div>
                            {!!$data['consent_other']!!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- descriptionContModel start-->
        <div class="modal fade " id="descriptionContModel" tabindex="-1" role="dialog" aria-labelledby="descriptionContModelTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content ">
                    <div class="modal-header">
                        <h5 class="modal-title" id="descriptionContModelTitle">{{Lang::get('使用說明')}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>{!! $data['consent'] !!}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- descriptionContModel end-->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
@endsection