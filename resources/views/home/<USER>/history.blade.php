@extends('home.Public.mainTpl')
@section('title'){{Lang::get('訂單列表')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Orderform/history')}}">{{Lang::get('訂單列表')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{Lang::get('訂單列表')}}</h3>
                            </div>
                        </div>
                        <ul class="nav tabNavBox justify-content-center">
                            <li class="nav-item ">
                                <a href="{{url('Orderform/orderform')}}">{{Lang::get('訂單')}}</a>
                            </li>
                            <li class="nav-item active">
                                <a href="{{url('Orderform/history')}}">{{Lang::get('歷史紀錄')}}</a>
                            </li>
                        </ul>
                    </div>
                    <div class="memberMiddle">
                        <div>
                            <table class="orderTable table table-striped table-bordered table-rwd">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th>{{Lang::get('購買日期')}}</th>
                                        <th>{{Lang::get('訂單編號')}}</th>
                                        <th>{{Lang::get('總價')}}</th>
                                        <th style="width: 100px;">{{Lang::get('付款方式')}}</th>
                                        <th style="width: 100px;">{{Lang::get('出貨狀態')}}</th>
                                        <th style="width: 100px;">{{Lang::get('訂單狀態')}}</th>
                                        <th style="width:180px;">{{Lang::get('賣家備註')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(empty($data['orderform']) == false)
                                    @foreach($data['orderform'] as $vo)
                                        <tr>
                                            <td data-th="{{Lang::get('購買日期')}}">{{date('Y/m/d',$vo->create_time)}}</td>
                                            <td data-th="{{Lang::get('訂單編號')}}"><a href="{{url('Orderform/orderform_c')}}?id={{$vo->order_number}}">{{$vo->order_number}}</a></td>
                                            <td data-th="{{Lang::get('總價')}}">{{config('extra.shop.dollar_symbol')}}{{number_format($vo->total)}}</td>
                                            <td data-th="{{Lang::get('付款方式')}}">
                                                @if(isset($data['pay_fee_dict']['k_'.$vo->payment]))
                                                    {{$data['pay_fee_dict']['k_'.$vo->payment]['name']}}
                                                @else
                                                    {{$vo->payment}}
                                                @endif
                                            </td>
                                            <td data-th="{{Lang::get('出貨狀態')}}">{{config('extra.order.TRANSPORT_STATE')[$vo->transport_state]}}</td>
                                            <td data-th="{{Lang::get('訂單狀態')}}">
                                                @if($vo->status == 'Complete')
                                                    -{{Lang::get('完成')}}-
                                                @elseif($vo->status == 'Cancel')
                                                    -{{Lang::get('取消')}}-
                                                @else
                                                    -{{Lang::get('退貨')}}-
                                                @endif
                                            </td>
                                            <td data-th="{{Lang::get('賣家備註')}}">{{$vo->ps2}}</td>
                                        </tr>
                                    @endforeach
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        <hr>
                    </div>
                    <div class="memberBottom">
                        <div>
                            {!!$data['consent_other']!!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
@endsection