@extends('home.Public.mainTpl')
@section('title'){{Lang::get('報名資料查詢')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
                <section class="directoryRow">
                    <div class="container">
                        <ul>
                            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                            <li><a href="{{url('Examination/tracking_registration')}}">{{Lang::get('報名資料查詢')}}</a></li>
                        </ul>
                    </div>
                </section>
                <section class="container max-wideVersion productPublic member_nofooterimg examination">
                    <!-- /////////////////////////////////////////// -->
                    <!-- /////////////////////////////////////////// -->
                    <!-- /////////////////////////////////////////// -->
                    <div id="itemBox" class="memberInforBox">
                        <div id="rightContentBox" class="innerPageBox memberContentBox">
                            <div class="paddingSpacing">
                                <div class="memberTop">
                                    <div class="titleBox">
                                        <div class="title">
                                            <h3>{{Lang::get('報名資料查詢')}}</h3>
                                        </div>
                                    </div>
                                    <div class="orderBtnBox">
                                        <ul class="nav tabNavBox justify-content-end">
                                            <li class="nav-item">
                                                <a data-toggle="modal" data-target="#processModel">{{Lang::get('操作說明')}}</a>
                                            </li>
                                        </ul>
                                       
                                    </div>
                                </div>
								<div class="container">
                                    <div class="row">
                                        <!-- <img class="col-8 offset-2" src= "/public/static/index/image/derding/track.png";>  -->
                                    </div>
                                    <div class="row">
                                        <form class="col-md-8 offset-md-2 col-12" id="tracking_registration_view_form" 
                                              method="post" action="{{url('Examination/tracking_registration_view')}}" >
                                            @csrf
                                            <div class="memberMiddle">
                                                <label>{{Lang::get('姓名')}}</label>
                                                <div class="form-group">
                                                    <input type="text" name="name" class="form-control">
                                                </div>
                                                <label>{{Lang::get('信箱')}}</label>
                                                <div class="form-group">
                                                    <input type="text" name="email" class="form-control">
                                                </div>
                                                <div class="text-center">
                                                    <a class="use-btn" href="javascript:$('#tracking_registration_view_form').submit()">{{Lang::get('查詢')}}</a>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
								</div>
                            </div>
                        </div>
                    </div>
                    <!-- processModel start-->
                    <div class="modal fade " id="processModel" tabindex="-1" role="dialog" aria-labelledby="processModelTitle" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content ">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="processModelTitle">{{Lang::get('操作說明')}}</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <p>{{$data['g_process']}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- processModel end-->

                    <!-- /////////////////////////////////////////// -->
                    <!-- /////////////////////////////////////////// -->
                    <!-- /////////////////////////////////////////// -->
					<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
					  <div class="modal-dialog" role="document">
						<div class="modal-content">
						  <div class="modal-header">
							<h5 class="modal-title" id="exampleModalLabel">{{Lang::get('填寫報名資料')}}</h5>
							<button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
							  <span aria-hidden="true">&times;</span>
							</button>
						  </div>
						  <div class="modal-body" id="boxModel">
							...
						  </div>
						  <div class="modal-footer">

						  </div>
						</div>
					  </div>
					</div>
                </section>
@endsection
@section('ownJS')
@endsection
