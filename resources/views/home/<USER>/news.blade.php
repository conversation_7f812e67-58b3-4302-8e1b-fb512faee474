@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['news']['name']}} | {{$data['seo'][0]['title']}}@endsection

@section("css")@endsection

@section("content")
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['news']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu']['news']['pic']}});">
            <div class="container">
                <h2 class="page-title" style="color:{{$data['frontend_menu']['news']['text_color']}}">
                    {{$data['frontend_menu']['news']['name']}}</h2>
                <!-- <span class="enText">{{$data['frontend_menu']['news']['en_name']}}</span> -->
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('News/news')}}">{{$data['frontend_menu']['news']['name']}}</a></li>
            </ul>
        </div>
    </section>
    <section class="productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox">
            <div id="rightContentBox" class="innerPageBox">
                <div class="proBrandZone container">
                    <div class="newsList row">
                        @foreach($data['news'] as $vo)
                            <div class="col-lg-4 col-md-6 col-12 list w-100 mb-4">
                                 <a href="{{url('News/news_c')}}?id={{$vo->id}}">
                                    <div class="img" style="background-image: url({{__PUBLIC__}}{{$vo->pic}});"></div>
                                    <p class="time mb-1"></i> {{$vo->time}}</p>
                                    <div class="text">
                                        <h3 class="mb-1">{{$vo->title}}</h3>
                                        <p>{{$vo->description}}</p>
                                    </div>
                                 </a>
                            </div>
                        @endforeach
                    </div>
                    <!-- /////////////////////////////////// -->
                    <div class="row paginationBox">
                        <div class="col-12 boxCenter">
                            {{$data['news']->links('pagination.customize')}}
                        </div>
                    </div>
                    <!-- /////////////////////////////////// -->
                </div>
                <!-- announcement end -->
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section("ownJS")
@endsection