@extends('home.Public.mainTpl')
@section('title'){{Lang::get('報名紀錄')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
    <style type="text/css">
        #ticketModel .modal-dialog{
          min-width: 670px;
        }
    </style>
@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a>{{Lang::get('報名紀錄')}}</a></li>
            </ul>
        </div>
    </section>
    
    <section class="container max-wideVersion productPublic member_nofooterimg examination">
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{Lang::get('報名紀錄')}}</h3>
                            </div>
                        </div>
                        <div class="orderBtnBox">
                            <ul class="nav tabNavBox justify-content-end">
                                <li class="nav-item">
                                    <a data-toggle="modal" data-target="#processModel">{{Lang::get('操作說明')}}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <h3>{{Lang::get('查詢信箱')}}:{{$data['email']}}</h3>
					<table class="orderTable table table-striped table-bordered table-rwd">
						<thead>
						<tr class="tr-only-hide">
                            <th>{{Lang::get('報名名稱')}}</th>
							<th>{{Lang::get('報名狀態')}}</th>
							<th>{{Lang::get('開始日期')}}</th>
						</tr>
						</thead>
						@foreach($data['re'] as $vo)
							<tr id="examinee{{$vo['id']}}">
								<td data-th="{{Lang::get('報名名稱')}}"><a onclick="open_examinee_panel('examinee{{$vo['id']}}_VM', '{{$vo['id']}}', '{{$vo['tinfo_id']}}', '{{$data['email']}}')">
                                    {{$vo['in_title']}}-{{$vo['area_title']}}</a>
                                </td>
                                <td data-th="{{Lang::get('報名狀態')}}">
                                    @if($vo['cancel']==1)
                                        {{Lang::get('已取消')}}
                                    @else
                                        {{Lang::get('完成')}}
                                    @endif
                                </td>
								<td data-th="{{Lang::get('開始日期')}}">
                                    @if($vo['act_time']=='')
                                        {{Lang::get('無時間')}}
                                    @else
                                        {{str_replace('T', ' ', $vo['act_time'])}}
                                    @endif
                                </td>
							</tr>
						@endforeach
					</table>
                </div>

                <div class="col-12 text-center">
                    <a href="{{url('Examination/tracking_registration')}}" class="use-btn">{{Lang::get('返回搜尋')}}</a>
                </div>
            </div>
        </div>

        <!-- ticketModel start-->
        <div class="modal fade " id="ticketModel" tabindex="-1" role="dialog" aria-labelledby="ticketModelTitle" aria-hidden="true" style="z-index:9999;background-color: #0009;">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content ">
                    <div class="modal-header">
                        <h5 class="modal-title" id="ticketModelTitle">{{Lang::get('電子准考證')}}<button type="button" class="btn btn-secondary" id="download" onclick="download()">{{Lang::get('下載')}}</button></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" style="display: flex; flex-direction: column; align-items: center;">
                    </div>
                </div>
            </div>
        </div>
        <!-- ticketModel end-->

        <!-- processModel start-->
        <div class="modal fade " id="processModel" tabindex="-1" role="dialog" aria-labelledby="processModelTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content ">
                    <div class="modal-header">
                        <h5 class="modal-title" id="processModelTitle">{{Lang::get('操作說明')}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>{{$data['g_process']}}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- processModel end-->
@endsection

@section('Modal')
    <!-- Modal -->
    <a id="model_btn" class="visibility:hidden" data-toggle="modal" data-target="#model"></a>
    <div class="modal fade shoppingCart" id="model" tabindex="-1" role="dialog" aria-labelledby="modelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="modelTitle">{{Lang::get('填寫報名資料')}}</h5>
                </div>
                <div class="modal-body" id="boxModel">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{Lang::get('關閉')}}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
	<script>
        function open_examinee_panel(vm_name, examinee_id, type_id, email){
            $('#model_btn').click();

            $('#modelTitle').html("{{Lang::get('填寫報名資料')}}");
            $.ajax({
                url: "{{url('Examination/examinee_panel')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                data: {
                    vm_name         : vm_name,
                    type_id         : type_id,
                    examinee_id     : examinee_id,
                    email           : email,
                },          
                success: function(re) {
                    $('#boxModel').html(re);
                    // $('#post_exinfo').hide();
                },
            });
        }

        /*列印准考證(目前無用)*/
        // function print_admin_ticket(id,examinee_id){
        //     $.ajax({
        //         url: "{{url('Examination/personal_show')}}?id="+id,
        //         type: 'GET',        
        //         success: function(re) {
        //             //$('#exampleModal').modal('show');
        //             $('#ticketModel .modal-body').html(re);
        //         }
        //     });
        // }
        // $('#ticketModel .close').on('click', function(e){
        //     $('#ticketModel .modal-body').html('');
        // });
        // $('#ticketModel').on('click', function(e){
        //     if(e.target==e.currentTarget){
        //         $('#ticketModel .modal-body').html('');
        //     }
        // });

        var email = `{{$data['email']}}`;
        @if(empty($data['re']) == false)
        @foreach($data['re'] as $vo_e)
            var examinee{{$vo_e['id']}}_data = { 
                vm_name: "examinee{{$vo_e['id']}}_VM",
                type_id: "{{$vo_e['type_id']}}", 
                type_product_id: "{{$vo_e['prod_id']}}",
                fields: [
                    @foreach($vo_e['register_fields'] as $vo_f)
                        {field_id:"field_id_{{$vo_f['id']}}", title:"{{$vo_f['title']}}", type:"{{$vo_f['type']}}" },
                    @endforeach

                ],
                examinee: [
                    {
                        examinee_id:"{{$vo_e['id']}}", 
                        register_data:`{!! $vo_e['register_data'] !!}`,
                    },
                ],
            }
            window['examinee{{$vo_e['id']}}_VM_init'] = function(){
                return new Vue({
                    el: '#examinee{{$vo_e['id']}}',
                    data: examinee{{$vo_e['id']}}_data,
                    computed:{
                    },
                    methods:{
                        /*解code 填寫資料*/
                        register_data: function(index){
                            self = this;
                            data = self.examinee[index]['register_data']
                            if(typeof(data)=='string'){
                                data = JSON.parse(data)
                            }
                            return data;
                        },
                        /*新增、修改報名資料*/
                        save_exinfo: function(examinee_id, register_data){
                            self = this;
                            $('#body_block').show();
                            $.ajax({
                                url: "{{url('Examination/examinee_save')}}",
                                type: 'POST',
                                headers: {
                                    'X-CSRF-Token': csrf_token 
                                },
                                data: {
                                    type_id         : self.type_id,
                                    type_product_id : self.type_product_id,
                                    examinee_id     : examinee_id,
                                    register_data   : register_data,
                                    email           : email,
                                },          
                                success: function(res) {                          
                                    if(res.code == 1){
                                        if(examinee_id==0){/*新增*/
                                            self.examinee.push(JSON.parse(res.msg));
                                        }
                                        else{/*修改*/
                                            for (var i = 0; i < self.examinee.length; i++) {
                                                if(self.examinee[i].examinee_id == examinee_id){
                                                    Vue.set(self.examinee, i, JSON.parse(res.msg));
                                                }
                                            }
                                        }
                                        $('#model span[aria-hidden="true"]').click();
                                        Vue.toasted.show("{{Lang::get('操作成功')}}", {duration:1500, className:"bg-success"});
                                    }else{
                                        Vue.toasted.show(res.msg, {duration:1500, className:"bg-danger"});
                                    }
                                    $('#body_block').hide();
                                },
                                error: function(e){
                                    $('#body_block').hide();
                                },
                            });
                        },
                    },
                });
            }
            var examinee{{$vo_e['id']}}_VM = typeof(Vue)!='undefined' ? window['examinee{{$vo_e['id']}}_VM_init']() : "";
        @endforeach
        @endif
	</script>
@endsection
