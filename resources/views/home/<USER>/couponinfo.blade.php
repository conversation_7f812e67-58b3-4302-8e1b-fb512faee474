@extends('home.Public.mainTpl')

@section('title'){{$data['productinfo']['title']}} - {{Lang::get('會員優惠券專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Product/coupon')}}">{{Lang::get('會員優惠券專區')}}</a></li>
                <li>{{$data['productinfo']['title']}}</li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox">
            <div id="rightContentBox" class="innerPageBox couponBox">
                <div class="titleBox">
                    <div class="title">
                        <h3>{{Lang::get('會員優惠券專區')}}</h3>
                    </div>
                </div>

                <div class="row productIntroBox arrow no-gutters">
                    <div class="col-xl-6 col-12 proImgBox">
                        @if($data['productinfo']['pic'])
                            <div class="bgImg-responsive-12fBy12" style="background-image: url({{__PUBLIC__}}{{$data['productinfo']['pic']}});"></div>
                        @endif
                    </div>
                    <div class="col-xl-6 col-12 pdSpacing ">                   
                        <div class="proTitleBox">
                            <h3 class="mb-3">{{$data['productinfo']['title']}}</h3>
                            <span class="num d-block mb-2">{{Lang::get('品號')}}：<span>{{$data['productinfo']['number']}}</span></span>
                            <span class="num">{{Lang::get('有效日期')}}：<span>{{ date("Y-m-d", $data['productinfo']['end']) }}</span></span>
                        </div>
                        <div class="contentBox">
                            <p>{{$data['productinfo']['content']}}</p>
                        </div>
                        <div class="amountBox" style="color:#5a5a5a;">
                            @switch($data['productinfo']['price'])
                                @case(0)
                                    <span class="offerPriceBox">{{Lang::get('會員優惠券面額')}}</span>
                                    <span class="offerPriceBox">
                                        <span class="amount">{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}
                                            <span id="total" itemprop="price">{{$data['productinfo']['discount']}}</span>
                                        </span>
                                    </span>
                                @break
                                @default
                                    <span class="offerPriceBox">{{Lang::get('售價')}}</span>
                                    <span class="offerPriceBox">
                                        <span class="amount">{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}
                                            <span id="total" itemprop="price">{{$data['productinfo']['price']}}</span>
                                        </span>
                                    </span>
                            @endswitch
                        </div>
                        <div class="row no-gutters bottomBox">
                            <div class="col-6 mbSpacing">
                                <!-- /////////////////////// -->
                                <div class="d-flex spinnerBox">
                                    <p class="mr-1">{{Lang::get('數量')}}</p>
                                    <div class="input-group spinner">
                                        <div class="input-group-prepend">
                                            <button class="btn text-monospace minus" type="button">-</button>
                                        </div>
                                        <input type="number" class="form-control count" min="1" max="1" value="1" id="itemcounter" readonly>
                                        <div class="input-group-append">
                                            <button class="btn text-monospace plus" type="button">+</button>
                                        </div>
                                    </div>
                                </div>
                                <!-- /////////////////////// -->
                            </div>
                        </div>
                        <div>
                            @switch($data['productinfo']['price'])
                                @case(0)
                                    <div class="couponBox" onclick="get_free_coupon()">
                                        <a id="infocartButton">{{Lang::get('領取會員優惠券')}}</a>
                                    </div>
                                @break
                                @default
                                    <div class="col-12 cartBoxFixed">
                                        <div class="row  cartRow">
                                            <div class="col-6 mbSpacing addCartBox">
                                                <span class="d-none" id="getdiscount"></span>
                                                <a href="javascript:void(0)" class="cartButton3" onclick='cartCtrl_coupon()'><i class="icon-add_car"></i>{{Lang::get('加入購物車')}}</a>
                                            </div>
                                            <div class="col-6 mbSpacing shoppingCartBox">
                                                <a href="javascript:void(0)" onclick='cartCtrlGO_coupon()'>
                                                    <i class="icon-money"></i>{{Lang::get('直接購物')}}
                                                </a>
                                                <!-- //////////////////////////////////// -->
                                                <span id="selectTypeBtn" href="" data-toggle="modal" data-target="#selectType"></span>
                                                <span id="checkoutBtn" href="" data-toggle="modal" data-target="#shoppingCart"></span>
                                                <!-- //////////////////////////////////// -->
                                            </div>
                                        </div>
                                    </div>
                            @endswitch
                        </div>
                    </div>

                    <div class="returnBtn">
                        <a href="{{url('Product/coupon')}}" >{{Lang::get('返回列表')}}</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('Modal')
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/productIntroTabs.js"></script>
    <script src="{{__PUBLIC__}}/js/productIntroAddLessOperation.js"></script>
    <!-- ///////////////////////////////////// -->
    <!-- 商品圖下的頁籤切換 start -->
    <script type="text/javascript">
        $(function(){
            var _showTab = 0;
            $('.goods_dct').each(function(){
                var $tab = $(this);
                var $defaultLi = $('ul.tabs li', $tab).eq(_showTab).addClass('active');
                $($defaultLi.find('a').attr('href')).siblings().hide();
                $('ul.tabs li', $tab).click(function() {
                    var _clickTab = $(this).find('a').attr('href');
                    $(this).addClass('active').siblings('.active').removeClass('active');
                    $(_clickTab).stop(false, true).fadeIn().siblings().hide();
                    return false;
                }).find('a').focus(function(){
                    this.blur();
                });
            });
        });
        /*加入購物車*/
        function cartCtrl_coupon (){
            if("{{$data['user']['id']}}"=='0'){
                alert("{{Lang::get('請先登入會員')}}");
                $('#checkoutBtn').click(); //會員登入畫面
                return;
            }
            var successFunction = function (response) {
                ///////add btn animatione///////////////////////////
                change_cart_num(response.msg);
                ////////////////////////////////////////////////////
                $.ajax({
                    type: "GET",
                    async: true,   
                    dataType: "json",
                    url: "{{url('Product/cart')}}",
                    success: function (msg) {
                        $('#totalAmount').html(msg)
                        $('#get_checkout').click();
                    }
                });
            }
            ajax_cartCtrl(successFunction, "{{$data['productinfo']['id']}}_coupon", $('#itemcounter').val()); /*送出請求*/
        }
        /*直接購物*/
        function cartCtrlGO_coupon() {
            if("{{$data['user']['id']}}"=='0'){
                alert("{{Lang::get('請先登入會員')}}");
                $('#checkoutBtn').click(); //會員登入畫面
                return;
            }
            var successFunction = function (response) {
                ///////add btn animatione///////////////////////////
                change_cart_num(response.msg);
                ////////////////////////////////////////////////////
                if("{{$data['user']['id']}}"=='0'){
                    $('#go_cart').click(); //會員登入畫面
                }else{
                    document.location.href="{{url('Cart/choose_shop')}}";
                }
            }
            ajax_cartCtrl(successFunction, "{{$data['productinfo']['id']}}_coupon", $('#itemcounter').val()); /*送出請求*/
        }
        function get_free_coupon(){
            $.ajax({
                type: "POST",
                async: true,
                datatype: 'json',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                data: {
                    coupon_id: "{{$data['productinfo']['id']}}",
                    cmd: 'assign',
                    num: $('#itemcounter').val(),
                },
                url: "{{url('Product/getFreeCoupon')}}",
                success: function (result) {
                    alert(result.msg);
                },
                error: function (xhr) {
                    alert("{{Lang::get('發生錯誤')}}");
                    console.error(xhr);
                }
            });
        }
    </script>
@endsection



