@extends('home.Public.mainTpl')

@section('title'){{Lang::get('會員優惠券')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Coupon/coupon')}}">{{Lang::get('會員優惠券')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{Lang::get('會員優惠券')}}</h3>
                            </div>
                        </div>
                        <ul class="nav tabNavBox justify-content-center owl-carousel tabCarousel owl-theme">
                            <li class="nav-item item">
                                <a href="{{url('Coupon/coupon')}}">{{Lang::get('我的會員優惠券')}}</a>
                            </li>
                            <li class="nav-item active item">
                                <a href="{{url('Coupon/record')}}">{{Lang::get('使用紀錄')}}</a>
                            </li>
                            <li class="item">
                                <a href="{{url('Product/coupon')}}">{{Lang::get('領取會員優惠券')}}</a>
                            </li>
                        </ul>
                    </div>
                    <div class="memberMiddle">
                        <div class="headingBox">
                            <h3 class="title mb-3"><span>{{Lang::get('會員優惠券登錄')}}</span></h3>
                        </div>
                        <div class="memberMiddle">
                            <form id="coupon_key" name="couponForm" action="{{url('Coupon/getCouponByNumber')}}" method="post">
                                @csrf
                                <div class="form-group">
                                    <label>{{Lang::get('序號')}}</label>
                                    <input type="text" class="form-control" name="number" aria-describedby="emailHelp"
                                        placeholder="{{Lang::get('請輸入會員優惠券序號(英文須留意大小寫)')}}">
                                    <a class="use-btn" onclick="couponForm.submit();">{{Lang::get('確認')}}</a>
                                </div>
                            </form>
                        </div>
                        <div class="couponBox">
                            <h3 class="title mb-3"><span>{{Lang::get('使用記錄')}}</span></h3>
                            <table class="orderTable table table-striped table-bordered table-rwd">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th style="width: 100px;">{{Lang::get('使用日期')}}</th>
                                        <th>{{Lang::get('名稱')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['Coupons'] as $vo)
                                    <tr>
                                        @if($vo['coupon_pool_use_time'])
                                            <td data-th="{{Lang::get('使用日期')}}">{{$vo['coupon_pool_use_time']}}</td>
                                        @else
                                            <td data-th="{{Lang::get('使用日期')}}">{{Lang::get('過期')}}</td>
                                        @endif
                                        <td data-th="{{Lang::get('名稱')}}">{{$vo['coupon_title']}}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="memberBottom">
                        <div>
                            {!!$data['consent_other']!!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
<script src="{{__PUBLIC__}}/js/tabOwlCarousel.js"></script>
@endsection