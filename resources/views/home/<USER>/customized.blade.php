@extends('home.Public.mainTpl')

@section('css')
<link rel="stylesheet" href="/public/static/index/css/customized_box.css" />
<style>
    .directoryRow {
        margin-top: 0;
        padding-top: 2rem;
    }

    .directoryRow a {
        color: var(--main-color)
    }

    .modal {
        background: rgba(0, 0, 0, 0.5);
    }

    .modal-backdrop {
        display: none;
    }
</style>
@endsection

@section('content')
<main id="customize_div">
    <section class="position-relative">
        <div class="container">
            <div class="left-customize">
                <div class="directoryRow">
                    <a class="cursor-pointer" onclick="history.back()"><i class="bi bi-chevron-left"></i>回上頁</a>
                </div>
                <h4 class="title" style="font-size:32px;" v-text="product_title"></h4>
                <div class="row">
                    <div class="col-md-6" v-for="(item, index) in layout_complete" :key="'layer_left' + (index + 1)">
                        <div class="item">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="number">第 <span v-text="index + 1"></span> 層</div>
                                <div class="btns">
                                    <a href="#" class="select" @click.prevent="openLayoutSelectModal(index)">
                                        <i class="bi bi-plus"></i>選擇版型
                                    </a>
                                    <a href="#" class="clear" @click.prevent="clearLayout(index)"><i class="bi bi-trash"></i>清空</a>
                                </div>
                            </div>
                            <div class="img-container">
                                <div class="img-preview" ref="previewImgs" :style="{backgroundImage:`url(${item.image})`}">
                                    <div v-for="(subItem, subIndex) in item.blocks" :key="'box_item' + (subIndex + 1)" class="draggable-box" :style="getBoxStyle(subItem)">
                                        <span v-if="!subItem.image" v-text="subIndex + 1"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right-inner-select">
                <h4 style="font-size:20px;" class="title mb-0">內容物選擇</h4>
                <div class="inner-list">
                    <div class="item" v-for="(item, index) in layout_complete" :key="'layer_right' + (index + 1)">
                        <div class="number mb-3">第 <span v-text="index + 1"></span> 層</div>
                        <template v-if="!item.id">
                            <p style="color:#D45C40;">尚未選擇版型！</p>
                        </template>
                        <template v-else>
                            <div class="layer" v-for="(subItem, subIndex) in item.blocks" :key="'block' + (subIndex + 1)">
                                <div>
                                    <span v-text="subIndex + 1"></span>:
                                </div>
                                <template v-if="subItem.content_id">
                                    <a href="#" class="remove">
                                        <span v-text="subItem.name"></span><i class="bi bi-x-circle-fill" @click.prevent="removeSelectedContent(index, subIndex)"></i>
                                    </a>
                                </template>
                                <template v-else>
                                    <a href="#" class="select" @click.prevent="openContentSelectModal(index, subIndex)">
                                        <i class="bi bi-plus"></i>選擇內容物
                                    </a>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
                <div style="padding: 0 15px;">
                    <label class="form-label" for="purchase">購買數量：(不得低於<span v-text="min_purchase"></span>)</label>
                    <input type="number" class="form-control" id="purchase" placeholder="請輸入數量" :min="min_purchase" v-model="purchase">
                </div>
                <div class="confirm-btn-container">
                    <a href="#" class="confirm-btn" @click.prevent="submitFormData">確認</a>
                </div>
            </div>
        </div>
    </section>
    <!-- 版型選擇modal -->
    <a href="#" class="btn btn-primary" id="layoutSelectModalBtn" data-toggle="modal" data-target="#layoutSelectModal" style="display:none;">Open Modal</a>
    <div class="modal fade boxModal shoppingCart" id="layoutSelectModal">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content" style="border-radius:0px;">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">選擇版型</h5>
                </div>
                <div class="modal-body">
                    <div>
                        <div class="row">
                            <div class="col-lg-3 col-sm-6" v-for="(item, index) in layout_model" :key="'layout' + (index + 1)">
                                <label class="item" :for="item.id">
                                    <input type="radio" name="current_layout_id" :value="item.id" :id="item.id" v-model="current_layout_id">
                                    <div class="img" :style="{backgroundImage:`url(${item.image})`}"></div>
                                </label>
                            </div>
                        </div>
                        <div class="submitBox">
                            <a href="#" class="submitBtn" @click.prevent="setLayout">確認</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 內容物選擇modal -->
    <a href="#" class="btn btn-primary" id="contentSelectModalBtn" data-toggle="modal" data-target="#contentSelectModal" style="display:none;">Open Modal</a>
    <div class="modal fade boxModal shoppingCart" id="contentSelectModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content" style="border-radius:0px;">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="contentSelectModalLabel">選擇內容物</h5>
                </div>
                <div class="modal-body">
                    <div class="category" v-for="(item, index) in current_content_model" :key="'category' + (index + 1)">
                        <h4 class="item-title" v-text="item.name"></h4>
                        <div class="row">
                            <div class="col-lg-3 col-6" v-for="(subItem, subIndex) in item.items" :key="'items' + (subIndex + 1)">
                                <label class="item" :for="subItem.id">
                                    <input type="radio" name="current_content_id" :value="subItem.id" :id="subItem.id" v-model="current_content_id">
                                    <div>
                                        <div class="img" :style="{backgroundImage:`url(${subItem.pic})`}"></div>
                                        <h5><span v-text="subItem.productinfo_title"></span><span v-if="subItem.productinfo_type_title">-</span><span v-if="subItem.productinfo_type_title" v-text="subItem.productinfo_type_title"></span></h5>
                                        <div v-if="subItem.num == 0"><span>完售</span></div>
                                        <div v-else>價格：$<span v-text="subItem.price"></span></div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" class="submitBtn" @click.prevent="setBlockContent">確認</a>
                </div>
            </div>
        </div>
    </div>
</main>

@endsection
@section('ownJS')
<script src="/public/static/index/js/html2canvas.min.js"></script>
<script type="text/javascript">
    const customizeVM = new Vue({
        el: '#customize_div',
        data: {
            // 商品id
            product_id: parseInt("{{$data['product_id']}}"),
            // 品項id
            productinfo_type_id: parseInt("{{$data['productinfo_type_id']}}"),
            // 商品名稱
            product_title: "{{$data['product_title']}}",
            // 禮盒層數
            box_layer: parseInt("{{$data['box_layer']}}"),
            // 最低購買數量
            min_purchase: parseInt("{{$data['min_purchase']}}"),
            // 購買數量
            purchase: parseInt("{{$data['min_purchase']}}"),
            // 版型內容
            layout_model: JSON.parse("{{json_encode($data['layout_model'], JSON_UNESCAPED_UNICODE)}}".replace(/&quot;/g, '"').trim()),
            // 所有可用內容物
            content_model: JSON.parse("{{json_encode($data['content_model'], JSON_UNESCAPED_UNICODE)}}".replace(/&quot;/g, '"').trim()),
            // 客戶完成之版型內容
            layout_complete: [],
            // 當前版型id
            current_layout_id: null,
            // 當前層序
            current_layer_index: null,
            // 當前格子序
            currrent_block_index: null,
            // 當前商品內容物id
            current_content_id: null,
            // 當前可用內容物
            current_content_model: [],
        },
        mounted() {
            for (let i = 0; i < this.box_layer; i++) {
                this.layout_complete.push({
                    id: null,
                    image: "",
                    blocks: [],
                });
            };
        },
        computed: {
            getBoxStyle() {
                return (block) => ({
                    width: block.width + "%",
                    height: block.height + "%",
                    left: block.left + "%",
                    top: block.top + "%",
                    backgroundImage: block.image ? `url(${block.image})` : "none",
                    ...(block.image ? {
                        backgroundSize: "contain",
                        backgroundRepeat: "no-repeat",
                        backgroundPosition: "center"
                    } : {
                        backgroundColor: "rgba(255, 255, 255, 0.5)",
                        border: "1px solid #000"
                    }),
                });
            },
        },
        methods: {
            openLayoutSelectModal: function(index) {
                this.current_layer_index = index;

                $('#layoutSelectModalBtn').click();
            },
            setLayout: function() {
                if (!this.current_layout_id) {
                    alert("請選擇版型");
                    return;
                }

                const Model = this.layout_model.find(item => item.id == this.current_layout_id);

                this.layout_complete[this.current_layer_index] = {
                    id: Model.id,
                    image: Model.image,
                    blocks: Array.from(Model.blocks, (item) => {
                        return {
                            width: item.width,
                            height: item.height,
                            left: item.left,
                            top: item.top,
                            items: item.items,
                            content_id: null,
                            image: null,
                            name: null,
                        };
                    }),
                };

                $('#layoutSelectModal .close').click();

                this.reset();
            },
            clearLayout: function(index) {
                if (index >= 0 && index < this.layout_complete.length) {
                    this.$set(this.layout_complete, index, {
                        id: null,
                        image: "",
                        blocks: [],
                    });
                }
            },
            openContentSelectModal: function(index, subIndex) {
                $('#contentSelectModalBtn').click();

                this.current_layer_index = index;
                this.currrent_block_index = subIndex;

                this.current_content_model = Array.from(this.layout_complete[index].blocks[subIndex].items, (item) => {
                    return this.content_model.find(content => content.id == item);
                });
            },
            setBlockContent: function() {
                let content_parent = this.content_model.find(item => item.item_ids.indexOf(this.current_content_id) > -1);

                let content = content_parent.items.find(item => item.id == this.current_content_id);

                if (!content) {
                    alert("請選擇內容物");
                    return;
                }

                if (content.num == 0) {
                    Swal.fire({
                        title: '內容物已完售',
                        icon: 'error',
                        confirmButtonText: "{{Lang::get('確認')}}",
                        confirmButtonColor: 'var(--btn-mainlink)',
                    });

                    return;
                }

                this.layout_complete[this.current_layer_index].blocks[this.currrent_block_index].content_id = content.id;
                this.layout_complete[this.current_layer_index].blocks[this.currrent_block_index].image = content.pic;
                this.layout_complete[this.current_layer_index].blocks[this.currrent_block_index].name = content.productinfo_title + (content.productinfo_type_title ? " - " + content.productinfo_type_title : "");

                $('#contentSelectModal .close').click();

                this.reset();
            },
            removeSelectedContent: function(index, subIndex) {
                if (index >= 0 && index < this.layout_complete.length) {
                    const original_data = this.layout_complete[index].blocks[subIndex];

                    this.$set(this.layout_complete[index].blocks, subIndex, {
                        width: original_data.width,
                        height: original_data.height,
                        left: original_data.left,
                        top: original_data.top,
                        items: original_data.items,
                        content_id: null,
                        image: null,
                        name: null,
                    });
                }

                this.layout_complete = JSON.parse(JSON.stringify(this.layout_complete));
            },
            reset: function() {
                this.current_layout_id = null;
                this.current_layer_index = null;
                this.currrent_block_index = null;
                this.current_content_id = null;
                this.current_content_model = [];
            },
            submitFormData: async function() {
                try {
                    if (this.layout_complete.filter(item => item.id !== null).length < this.box_layer) {
                        throw new Error('尚有版層未設定版型');
                    }

                    this.layout_complete.forEach((item, index) => {
                        item.blocks.forEach((block, blockIndex) => {
                            if (block.content_id === null) {
                                throw new Error(`第${index + 1}層第${blockIndex + 1}格尚未選擇內容物`);
                            }
                        });
                    });

                    if (this.purchase < this.min_purchase) {
                        throw new Error('購買數量不得低於' + this.min_purchase);
                    }
                } catch (error) {
                    Swal.fire({
                        title: error.message,
                        icon: 'error',
                        confirmButtonText: "{{Lang::get('確認')}}",
                        confirmButtonColor: 'var(--btn-mainlink)',
                    });

                    return;
                }

                $('#body_block').show();

                const formData = new FormData();
                const previewElements = this.$refs.previewImgs;

                for (let i = 0; i < previewElements.length; i++) {
                    const canvas = await html2canvas(previewElements[i]);
                    const blob = await new Promise((resolve) => canvas.toBlob(resolve, 'image/png'));
                    formData.append(`images[${i}]`, blob);
                }

                formData.append('layout_complete', JSON.stringify(this.layout_complete));
                formData.append('purchase', this.purchase);
                formData.append('productinfo_type_id', this.productinfo_type_id);

                $.ajax({
                    url: "{{url('Cart/add_customize_box_to_cart')}}",
                    type: 'POST',
                    async: false,
                    datatype: 'json',
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#body_block').hide();

                        if (response.code == 1) {
                            if ("{{$data['user']['id']}}" != '0' || "{{empty(config('control.close_function_current')['會員管理'])}}" == "") {
                                document.location.href = "{{url('Cart/choose_shop')}}";
                            } else {
                                $('#go_cart').click(); //會員登入畫面
                            }
                        } else {
                            Swal.fire({
                                title: response.msg,
                                icon: 'error',
                                confirmButtonText: "{{Lang::get('確認')}}",
                                confirmButtonColor: 'var(--btn-mainlink)',
                            });
                        }
                    },
                    error: function(xhr) {
                        alert("{{Lang::get('發生錯誤')}}");
                        console.error(xhr);
                    }
                });
            }
        },
    });
</script>
@endsection