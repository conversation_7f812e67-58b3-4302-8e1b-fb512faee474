@extends('home.Public.mainTpl')

@section('title'){{$data['seo'][0]['title']}}@endsection

@section('css')@endsection

@section('content')
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['experience']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu']['experience']['pic']}});">
            <div class="container">
                <h2 class="page-title" style="color:{{$data['frontend_menu']['experience']['text_color']}}">
                    {{$data['frontend_menu']['experience']['name']}}</h2>
                <!-- <span class="enText">{{$data['frontend_menu']['experience']['en_name']}}</span> -->
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Experience/experience')}}">{{$data['frontend_menu']['experience']['name']}}</a></li>
            </ul>
        </div>
    </section>

    <section class="container max-wideVersion productPublic">
        <div id="itemBox">
            <?php /*
                <div id="leftBox">
                    <!-- /////////////////////////////////////////// -->
                    <!-- Side Product Menu -->
                    <!-- /////////////////////////////////////////// -->
                </div>
            */ ?>
            <div id="rightContentBox" class="innerPageBox">
                <div class="row liveStreamBox no-gutters">
                    @if(empty($data['experience']) == false)
                    @foreach($data['experience'] as $vo)
                    <div class="col-12 liveList">
                        <a href="{{url('Experience/experience_c')}}?id={{$vo->id}}">
                            <div class="row no-gutters">
                                <div class="col-sm-3">
                                    <div class="img" style="background-image: url({{__PUBLIC__}}{{$vo->pic}});"></div>
                                </div>
                                <div class="col-sm-9 contentBetween">
                                    <div class="contBox">
                                        <h3 class="liveTitleEllipsis">{{$vo->title}}</h3>
                                        <!-- {{$vo->content}} -->
                                    </div>
                                    <span class="more"> {{Lang::get('了解更多')}} </span>
                                </div>
                            </div>
                        </a>
                    </div>
                    @endforeach
                    @endif
                </div>
                <!-- ////////////////////////////////// -->
                <div class="row paginationBox">
                    <div class="col-12 boxCenter">
                        {{$data['experience']->links('pagination.customize')}}
                    </div>
                </div>
                <!-- ////////////////////////////////// -->
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
    <script>
        function shaveEllipsis(h) {
            shave(".liveTitleEllipsis", h, {
                classname: "classname",
                character: '......'
            });
        };

        $(window).on("resize", function() {
            var width768 = Modernizr.mq('(min-width: 768px)');
            var width1600 = Modernizr.mq('(min-width: 1600px)');
            if (width1600) {
                shaveEllipsis(85);
            } else if (width768) {
                shaveEllipsis(70);
            } else {
                shaveEllipsis(50);
            }
        }).resize();
    </script>
@endsection