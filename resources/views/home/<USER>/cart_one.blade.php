@extends('home.Public.mainTpl')
@section('title'){{Lang::get('購物車')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
  <style>
    #question_container{
      max-height: unset !important;
    }
    #post_exinfo{
      display: none;
    }
  </style>
@endsection

@section('mycode')
@endsection

@section('content')
  <div id="rwd_edm" class="container">
   {!! $data['edm_html'] !!}
  </div>  
  <div id="cart_table"></div>
  <div  id="boxModel" class="container shoppingCart mt-4 mb-4">
    {!! $data['examinee_panel_view'] !!}
  </div>
  <div class="container shoppingCart mt-4 mb-4">
    <div class="memberMiddle memberitems">
      @include('home.cart.cart_form')
    </div>
  </div>
@endsection

@section('Modal')
  @include('home.cart.cart_modal')
@endsection

@section('ownJS')
  {!! $data['edm_style_source'] !!}

  @include('home.cart.cart_vm_js')

  <script>
    /*覆寫 selectPlace*/
    async function selectPlace() {
      // 儲存報名資料
      try {
        var resp = await save_exinfo_data();
        if(!resp.code){ return; }
      } catch (error) {
      }

      if (use_thirdpart_logistic) {
        let order_data = get_order_form_data();
        let selectPlace_url ="{{url('Cart/selectPlace')}}" + '?' + 'Order_Data=' + JSON.stringify(order_data);
        location.href = selectPlace_url;
      } else {
        let send_way_name = $('option:selected', 'select[name="send_way"]').text().split(' | ')[0];
        switch (send_way_name) {
          case "{{Lang::get('7-11取貨')}}":
            window.open('https://emap.pcsc.com.tw/');
            break;
          case "{{Lang::get('全家取貨')}}":
            window.open('https://www.family.com.tw/Marketing/Map');
            break;
          case "{{Lang::get('萊爾富取貨')}}":
            window.open('https://www.hilife.com.tw/storeInquiry_street.aspx');
            break;
          case "{{Lang::get('OKmart取貨')}}":
            window.open('https://www.okmart.com.tw/convenient_shopSearch');
            break;
          default:
            break;
        }
      }
    }

    // 確認下單
    async function go_buy(){
      $('#body_block').show();

      // 儲存報名資料
      try {
        var resp = await save_exinfo_data();
        if(!resp.code){ return; }
      } catch (error) {
      }

      let order_data = get_order_form_data();
      // console.log(order_data);
      if(order_data.become_member==1){
        resp = await $.ajax({
          url: "{{url('Login/order_create_account')}}",
          type: 'POST',
          datatype: 'json',
          data: order_data,
        });
        if(resp.code==0){
          Vue.toasted.show(resp.msg, vt_error_obj);
          $('#body_block').hide();
          e.preventdefault();
          return;
        }
      }
      $('#cartform').submit();
    }
  </script>
@endsection