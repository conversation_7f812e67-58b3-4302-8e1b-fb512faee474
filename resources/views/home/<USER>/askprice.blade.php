@extends('home.Public.mainTpl')

@section('title'){{Lang::get('商品詢價紀錄')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
@endsection

@section('content')
        <section class="directoryRow">
            <div class="container">
                <ul>
                    <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                    <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                    <li><a href="{{url('Askprice/askprice')}}">{{Lang::get('商品詢價紀錄')}}</a></li>
                </ul>
            </div>
        </section>

        <section class="buyform container max-wideVersion productPublic">
            <div id="itemBox" class="memberInforBox">
                <div id="leftBox">
                    <!-- /////////////////////////////////////////// -->
                    @include('home.Public.member_menu')
                    <!-- /////////////////////////////////////////// -->
                </div>
                <div id="rightContentBox" class="innerPageBox memberContentBox">
                    <div class="paddingSpacing">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('商品詢價紀錄')}}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="memberMiddle"> 
                            <h3 class="subtitle">{{Lang::get('詢問內容')}}</h3>
                            <div class="buyformBox">
                                <table class="askprice orderTable table table-bordered table-rwd">
                                    <thead>
                                        <tr class="tr-only-hide head">
                                            <th style="width: calc(100% - 300px - 40px)">{{Lang::get('商品')}}</th>
                                            <th style="width: 100px">{{Lang::get('詢價金額')}}</th>
                                            <th style="width: 100px;">{{Lang::get('同意售價')}}</th>
                                            <th style="width: 140px">{{Lang::get('可購買期限')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="contact in model.contacts" class="">
                                            <td colspan="4" >
                                                <div class="all">
                                                    <ul class="itembox">
                                                        <li class="smallProImg">
                                                            <img v-if="contact.product_pic" :src="'' + contact.product_pic"  >
                                                        </li>
                                                        <li class="name">
                                                            <p>
                                                                <a :href="'{{url('Product/productinfo')}}?id=' + contact.product_id" target="_blank">
                                                                    <span v-text="contact.product_name"></span>
                                                                    <template v-if="contact.product_type_name">
                                                                        - <span v-text="contact.product_type_name"></span>
                                                                    </template>
                                                                </a>
                                                            </p>
                                                            <p class="mt-1 num">{{Lang::get('數量')}}:<span v-text="contact.num"></span></p>
                                                            <span class="CartBox mb-1">
                                                                <span v-text="contact.status ? '{{Lang::get('已回覆')}}' : '{{Lang::get('待回覆')}}'"></span>
                                                                <span class="mark_text" v-if="contact.status && contact.bought==1" >
                                                                    {{Lang::get('已購買')}}
                                                                </span>
                                                            </span>
                                                        </li>
                                                        <li data-th="{{Lang::get('詢價金額')}}" class="price">
                                                            {{config('extra.shop.dollar_symbol')}}<span v-text="contact.price"></span></li>
                                                        <li data-th="{{Lang::get('同意售價')}}" class="price_final">
                                                            <span v-if="contact.price_final">
                                                                {{config('extra.shop.dollar_symbol')}}<span v-text="contact.price_final"></span>
                                                            </span>
                                                        </li>
                                                        <li data-th="{{Lang::get('可購買期限')}}" class="date">
                                                            <span v-if="contact.expired_date"><span v-text="contact.expired_date"></span></span>
                                                        </li>
                                                    </ul>
                                                    <div class="saleRepaly">
                                                        <div class="item">
                                                            <a class="recordBtn collapsed" data-toggle="collapse" :href="'#record' + contact.id" role="button" aria-expanded="false" :aria-controls="'record' + contact.id">
                                                                {{Lang::get('詢價紀錄')}}
                                                                <svg width="16" height="16" viewBox="0 0 32 32"><path fill="currentColor" d="M12.969 4.281L11.53 5.72L21.812 16l-10.28 10.281l1.437 1.438l11-11l.687-.719l-.687-.719z"/></svg>
                                                            </a>
                                                            <div class="collapse multi-collapse recordAns" :id="'record' + contact.id" >
                                                                <div class="card card-body">
                                                                    <div class="qs">
                                                                        <div class="name">{{Lang::get('留言')}}:<span v-text="contact.ask"></span></div>
                                                                        <div class="date">
                                                                            @include('home.Public.svg_calendar')
                                                                            <span v-text="contact.ask_time"></span> 
                                                                        </div>
                                                                    </div>
                                                                    <div class="saleAns">
                                                                        <div class="name">
                                                                            {{Lang::get('賣家回覆')}}: <span v-text="contact.response"></span><span v-text="contact.status ? '' : '{{Lang::get('待回覆')}}'"></span>
                                                                        </div>
                                                                        <div class="date" v-if="contact.response_time">
                                                                            @include('home.Public.svg_calendar')
                                                                            <span v-text="contact.response_time"></span> 
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                              </div>
                                                        </div> 
                                                        <div class="item">                                                                 
                                                            <div v-if="contact.price_final" class="order_price mr-2">
                                                                <span class="total">{{Lang::get('結帳金額')}}</span> <span>{{config('extra.shop.dollar_symbol')}}</span><span v-text="contact.price_final"></span>
                                                            </div> 
                                                            <div>
                                                                <template v-if="contact.status && contact.bought==0">
                                                                    <button type="button" class="btn salebtn" @click="ask_again(contact.id)">{{Lang::get('再次詢價')}}</button>
                                                                    <template v-if="contact.agree">
                                                                        <button type="button" class="btn buybtn" @click="buy_product(contact.id)">{{Lang::get('購買')}}</button>
                                                                    </template>
                                                                </template>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>   
                                        </tr>
                                    </tbody>
                                </table> 
                            </div>
                        </div>
                    </div>

                    <a id="contactAgainModal_btn" data-toggle="modal" data-target="#contactAgainModal" class="d-none"></a>
                    <div class="modal replySale shoppingCart fade" id="contactAgainModal" tabindex="-1" role="dialog" aria-labelledby="contactAgainModalLabel" aria-hidden="true">
                        <div class="modal-dialog" role="document" id="replyBoxView">
                            <div class="modal-content">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                <div class="modal-header">
                                    <h5 class="modal-title">{{Lang::get('再次詢價')}}</h5>
                                </div>
                                <div class="modal-body">
                                    <div class="ask_price_content">
                                        <div class="pic">
                                            <img v-if="ask_again_model.product_pic" :src="'' + ask_again_model.product_pic" class="smallProImg">
                                        </div>
                                        <div class="text">
                                            <h3 v-text="ask_again_model.product_name"></h3><h3><template v-if="ask_again_model.product_type_name">
                                                -<span v-text="ask_again_model.product_type_name"></span>
                                            </template></h3>
                                            <div class="d-flex flex-wrap justify-content-between w-100">
                                                <p class="askpriceModel_product_num">{{Lang::get('數量')}}：<input class="form-control mb-2 w-50" type="number" min="0" v-model="ask_again_model.num"></p>
                                                <div class="askprice_order">
                                                    <label class="name">{{Lang::get('詢價金額')}}：</label>
                                                    <input class="form-control price" type="number" min="0" v-model="ask_again_model.price">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>{{Lang::get('留言')}}：</label>
                                        <textarea rows="3" class="form-control ans mb-2" v-model="ask_again_model.ask"></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer justify-content-center">
                                    <button type="button" class="btn salebtn" @click="do_ask_again">{{Lang::get('送出')}}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 修改詢價表結束 -->
                    <form name="buy_product_form" action="{{url('Cart/buy_askprice')}}" method="post">
                        @csrf
                        <input type="hidden" name="contact_id" v-model="replyBoxModel.main.id">
                    </form>
                </div>
            </div>
        </section>
    </div>
@endsection
@section('ownJS')
    <script type="text/javascript">
        // 初始化vue
        var content_area_data = {
            searchKey: '',
            timeRange: [],
            model:{
                search_type: 'keyword',
                search: "",
                contacts: [],
                status: '',
                page: 1,
            },
            replyBoxModel:{
                main:{
                    id: 0, 
                    name: '', 
                    phone: '', 
                    email: '', 
                    product_name: '',
                    product_type_name: '', 
                    product_pic: '',
                    create_time: '',
                },
                current: {
                    ask: '',
                    response: '',
                    response_time: '',
                    price: '',
                    price_final: '',
                    num: '', 
                    expired_date: '',
                    status: 0,
                    agree: 0,
                    bought: 0,
                },
                history: [],
            },

            max_page_num: 9,
            pages: [1],
            last_page: 1,
            total: 0,

            /*再次詢價*/
            ask_again_model:{
                ask: '',
                price: '',
                num: '',
                product_name: '',
                product_type_name: '', 
                product_pic: '',
                
            },
        }
        var content_areaVM = new Vue({
            el: '#rightContentBox',
            data: content_area_data,
            methods: {
                /*列表相關的方法*/
                searchKeyword: function () {
                    self = this;
                    self.model.search_type = 'keyword';
                    self.getList()
                },
                searchTime: function () {
                    self = this;
                    self.timeRange = $('#searchTimeInput').val().split(" - ");
                    self.model.search_type = 'date';
                    self.getList();
                },
                change_page: function(p){
                    self = this;
                    // $('.table_scroll_area').scrollTop(0);
                    self.model.page = p;
                    self.getList();
                },
                searchStatus: function(status){
                    self = this;
                    self.model.status = status;
                    self.model.page = 1;
                    self.getList();
                },
                getList: async function(){
                    self = this;
                    if(self.model.search_type=='keyword'){
                        searchdata = { 
                            'searchKey': self.searchKey,
                        };
                    }
                    if(self.model.search_type=='date'){
                        searchdata = {
                            'start': self.timeRange[0], 
                            'end':self.timeRange[1],
                        }
                    }
                    searchdata.type = self.model.search_type;
                    searchdata.status = self.model.status;
                    searchdata.page = self.model.page;
                    
                    await $.ajax({
                        type: "post",
                        dataType: 'json',
                        url : "{{url('Askprice/getList')}}",
                        data: searchdata,
                        success: function(resp){
                            self.model.search = resp.search;
                            self.model.contacts = resp.contacts;

                            self.last_page = resp.last_page;
                            self.total = resp.total;
                        },
                    });

                    self.init_pages();
                },
                init_pages: function(){
                    self = this;
                    self.pages = [self.model.page];
                    var check_num = 1;
                    while (
                        self.pages.length<self.max_page_num && (
                            self.model.page-check_num>=1 || 
                            self.model.page+check_num<=self.last_page
                        )
                    ) {
                        if(self.model.page-check_num>=1){ 
                            self.pages.push(self.model.page-check_num);
                        }
                        if(self.model.page+check_num<=self.last_page){
                            self.pages.push(self.model.page+check_num);
                        }
                        check_num += 1;
                    }
                    self.pages = self.pages.sort();
                },           

                openBox: async function (contact_id) {
                    self = this;
                    await $.ajax({
                        type: "post",
                        dataType: 'json',
                        url : "{{url('Askprice/getOne')}}",
                        data: {
                            contact_id: contact_id,
                        },
                        success: function(resp){
                            if(resp.main){
                                self.replyBoxModel.main = resp.main;
                                self.replyBoxModel.current = resp.current;
                                self.replyBoxModel.history = resp.history;
                            }
                        },
                    });
                    $('#contactModal_btn').click();
                },

                /*詳細內容相關的方法*/
                ask_again: function (main_id) {
                    self = this;
                    self.replyBoxModel.main.id = main_id;
                    self.ask_again_model.ask = '';
                    self.ask_again_model.price = '';
                    self.ask_again_model.num = '';
                    for (var i = 0; i < self.model.contacts.length; i++) {
                        const target = self.model.contacts[i];
                        if( target.id == main_id){
                            self.ask_again_model.product_name = target.product_name;
                            self.ask_again_model.product_type_name =  target.product_type_name;
                            self.ask_again_model.product_pic =  target.product_pic;
                        }
                    }
                   
                    
                    $('#contactAgainModal_btn').click();
                },
                do_ask_again: async function () {
                    self = this;
                    $('#body_block').show();
                    await $.ajax({
                        type: "post",
                        dataType: 'json',
                        url : "{{url('Askprice/do_ask_again')}}",
                        data: {
                            contact_id: self.replyBoxModel.main.id,
                            ask: self.ask_again_model.ask,
                            price: self.ask_again_model.price,
                            num: self.ask_again_model.num,
                            product_name: self.ask_again_model.product_name,
                            product_type_name: self.ask_again_model.product_type_name,
                            product_pic: self.ask_again_model.product_pic,
                          
                        },
                        success: function(resp){
                            bg_class = resp.code ? 'bg-success' : 'bg-danger';
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            if(resp.code){
                                $('#contactAgainModal .close').click();
                                $('#contactModal .close').click();
                                self.getList();
                            }
                            $('#body_block').hide();
                        },
                        error: function(e){
                            $('#body_block').hide();
                        },
                    });
                },
                buy_product: function (main_id) {
                    self = this;
                    self.replyBoxModel.main.id = main_id;
                    setTimeout(()=>{
                        buy_product_form.submit();
                    }, 100);
                },
            }
        });

        $(document).ready(()=>{
            content_areaVM.searchKeyword();
        });
    </script>
@endsection