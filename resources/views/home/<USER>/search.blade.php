@extends('home.Public.mainTpl')
@section('title'){{Lang::get('商品搜尋')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a class="cursor-initial" href="javascript:void(0);">{{Lang::get('商品搜尋')}}</a></li>
                <li><a class="cursor-initial" href="javascript:void(0);">{{$data['keyword']}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox">
            <!--<div id="leftBox">
                  Side Product Menu 
                </div>-->
            <div id="rightContentBox" class="innerPageBox">
                <div class="titleBox">
                    <div class="title">
                        <h3>{{Lang::get('商品搜尋')}}：{{$data['keyword']}}</h3>
                    </div>
                </div>

                <!-- proBrandZone start -->
                <div class="proBrandZone">
                    <div class="titleBrandBox">
                        <div class="leftBox">
                            <div class="d-flex flex-wrap justify-content-between align-items-center ml-2 mr-2">
                                <span>
                                    {{Lang::get('商品數')}}： {{$data['rowCount']}}
                                    @if(!isset(config('control.close_function_current')['會員瀏覽商品設定']))
                                        ({{Lang::get('請留意您的瀏覽權限')}})
                                    @endif
                                </span>
                                @include('home.product.search_setting')
                            </div>
                        </div>
                    </div>
                    @if(count($data['productinfo']->items())==0)
                        <div class="proItem"><h1>{{Lang::get('無資料')}}</h1></div>
                    @else
                        <div class="proBox proItemBox">
                            @foreach($data['productinfo']->items() as $k => $productvo)
                                <div class="item">
                                    <div class="img_container">
                                        <div class="img" style="background-image: url({{__PUBLIC__}}/{{$productvo->pic1}});"></div>
                                        @if(in_array($productvo->id, $data['store_products']))
                                            <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(0, {{$productvo->id}})">
                                                <i class="bi bi-heart-fill"></i>
                                            </a>
                                        @else
                                            <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(1, {{$productvo->id}})">
                                                <i class="bi bi-heart"></i>
                                            </a>
                                        @endif
                                        @if(!empty($data['coupon_button'][$productvo->id]))
                                            <a class="couponLabel" {{$data['coupon_button'][$productvo->id] ?? ''}}>
                                                <p><span>{{Lang::get('優惠券')}}</span></p>
                                            </a>
                                        @endif
                                    </div>
                                    <div class="img_container addcart_area">
                                        <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                            <div class="img"></div>
                                        </a>
                                        <div class="addcarBox d-none d-lg-block">
                                            @if(isset($productvo->show[0]['idtype']))
                                                <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                    {{Lang::get('加入購物車')}}
                                                </a>
                                             @endif
                                        </div>
                                    </div>
                                    <div class="textBox">
                                        <h3>
                                            <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                                {{ Str::limit($productvo->title, 40) }} 
                                            </a>
                                        </h3>
                                        <div class="d-flex flex-wrap" style="column-gap:0.5rem;">
                                            @if(isset($data['act_button'][$productvo->id]['act_data']['link']))
                                                <span class="activityLabel">
                                                    <a {{ $data['act_button'][$productvo->id]['act_data']['link'] ?? ''}}>
                                                        {{$data['act_button'][$productvo->id]['act_data']['type_name']}}
                                                    </a>
                                                </span>
                                            @endif
                                            <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                                <div class="priceBox">
                                                    <span class="originalPrice">{!! $productvo->show[0]['originalPrice'] !!}</span>
                                                    <span class="offerPrice">{!! $productvo->show[0]['offerPrice'] !!}</span>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="addcarBox d-block d-lg-none">
                                            @if(isset($productvo->show[0]['idtype']))
                                                <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                    {{Lang::get('加入購物車')}}
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <div class="row paginationBox">
                            <div class="col-12 boxCenter">
                                {!! $data['productinfo']->links('pagination.customize') !!}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
    @include('home.product.search_setting_js')
@endsection