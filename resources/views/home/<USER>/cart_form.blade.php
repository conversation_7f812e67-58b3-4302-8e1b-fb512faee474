
                    <form name="cartform" id="cartform" action="{{url('Cart/buy')}}" method="post">
                        @csrf
                        <div class="row cart-form">
                            <div class="col-lg-6">
                                <div class="mb-4">
                                    <h4>{{Lang::get('選擇配送方式')}}</h4>
                                    <div class="form-box">
                                        <div class="form-group" id="shipping_div">
                                            <label for="delivery">{{Lang::get('送貨方式')}} <span class="required">*</span></label>
                                            <select class="form-control" name="send_way" onchange="send_way_infos(this.value)">
                                                <option value="">--{{Lang::get('請選擇')}}--</option>
                                                <template v-for="item in type_decode">
                                                    <option v-if="item.price == 0" :value="item.shipping_fee_id" :key="'label_send_way_'+item.shipping_fee_id" v-text="item.name + ' |  {{Lang::get('免運費')}}'"></option>
                                                    <option v-else-if="item.free_rule > 0" :value="item.shipping_fee_id" :key="'label_send_way_'+item.shipping_fee_id">
                                                        <span v-text="item.name + ' |  {{Lang::get('運費')}} {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + item.price"></span>
                                                        <span v-if="item.free_rule!=999999999" v-text="'、{{Lang::get('免運金額')}} {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + item.free_rule"></span>
                                                    </option>
                                                    <option v-else :value="item.shipping_fee_id" :key="'label_send_way_'+item.shipping_fee_id" v-text="item.name + ' |  {{Lang::get('運費')}} {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + item.price"></option>
                                                </template>
                                            </select>
                                        </div>
                                        @if(empty(config('control.close_function_current')['會員管理']))
                                        @if($data['user']['id'] > 0)
                                        <div class="mb-2 float-left shipping_blank" style="display: none;" id="sync_member">
                                            <a class="submitBtn" style="border-radius: 5px;" onclick="assign_memeber_data()" href="###">
                                                {{Lang::get('同會員資料')}}
                                            </a>
                                        </div>
                                        @endif
                                        @endif
                                        <div class="form-group shipping_blank" style="display: none;">
                                            <label for="transport_location_name">{{Lang::get('收件人姓名')}} <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="transport_location_name" name="transport_location_name" placeholder="{{Lang::get('請填入收件人真實姓名，以確保順利收件')}}">
                                        </div>
                                        <div class="form-group shipping_blank" style="display: none;">
                                            <label for="transport_location_phone">{{Lang::get('收件人行動電話')}} <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="transport_location_phone" name="transport_location_phone" placeholder="{{Lang::get('請輸入有效手機號碼')}}">
                                        </div>
                                        <div class="form-group shipping_blank" style="display: none;">
                                            <label for="transport_email">{{Lang::get('收件人電子信箱')}} <span class="required">*</span></label>
                                            <input type="email" class="form-control" id="transport_email" name="transport_email" placeholder="{{Lang::get('請輸入電子信箱')}}">
                                        </div>
                                        <div id="cz_select" class="row use-row shipping_blank" style="display: none;">
                                            <div class="col-12 use-col mb-2">
                                                <label for="addr">{{Lang::get('寄送地址')}} <span class="required must_need_addr">*</span></label>
                                            </div>
                                            <div class="col-sm-4 col-12 use-col mb-2">
                                                <select name="F_I_CNo" id="myCity" class="form-control">
                                                    <option value="">{{Lang::get('請選擇縣市')}}</option>
                                                    @foreach($data['city'] as $vo)
                                                        <option value="{{$vo['AutoNo']}}">{{$vo['Name']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-sm-4 col-12 use-col mb-2">
                                                <select name="F_I_TNo" id="myTown" class="form-control">
                                                    <option value="">{{Lang::get('請選擇鄉鎮區')}}</option>
                                                </select>
                                            </div>
                                            <div class="col-sm-4 col-12 use-col mb-2">
                                                <input type="text" class="form-control" id="myZip" name="F_S_NH_Zip" size="3" placeholder="{{Lang::get('郵遞區號')}}" readonly="ture">
                                            </div>
                                            <div class="col-12 use-col mb-2">
                                                <input type="text" class="form-control" id="transport_location" name="transport_location" placeholder="{{Lang::get('請輸入到貨地址')}}">
                                            </div>
                                        </div>
                                        @if(config('control.thirdpart_logistic')==1)
                                        <div class="form-group shipping_blank" style="display: none;" id="store_pickup">
                                            <label for="store">{{Lang::get('取貨門市')}} <span class="required">*</span></label>
                                            <input type="text" class="form-control mb-2" style="cursor: auto;" name="store" id="store" placeholder="{{Lang::get('請點選下方按鈕選擇門市')}}" readonly>
                                            <button type="button" class="select-branch-btn" onclick="selectPlace()">{{Lang::get('選擇門市')}}</button>
                                            <input type="hidden" name="MerchantTradeNo">
                                            <input type="hidden" name="LogisticsSubType">
                                            <input type="hidden" name="CVSStoreID">
                                            <input type="hidden" name="CVSStoreName">
                                            <input type="hidden" name="CVSAddress">
                                            <input type="hidden" name="CVSTelephone">
                                            <input type="hidden" name="CVSOutSide">
                                            <input type="hidden" name="ExtraData">
                                        </div>
                                        @else
                                        <div class="form-group shipping_blank" style="display: none;" id="store_pickup">
                                            <label for="store">{{Lang::get('取貨門市')}} <span class="required">*</span></label>
                                            <div class="d-flex mb-2" style="gap: 0.5rem;">
                                                <input type="text" class="form-control" style="cursor: auto;" id="storeNo" placeholder="{{Lang::get('請填入店鋪號')}}" maxlength="8">
                                                <input type="text" class="form-control" style="cursor: auto;" id="storeName" placeholder="{{Lang::get('請填入店鋪名')}}" maxlength="10">
                                            </div>
                                            <input type="text" class="form-control mb-2" style="cursor: auto;" id="storeAddr" placeholder="{{Lang::get('請填入店鋪地址')}}" maxlength="35">
                                            <button type="button" class="select-branch-btn" id="mart_url" onclick="selectPlace()">{{Lang::get('門市資訊查詢')}}</button>
                                        </div>
                                        @endif
                                        <input type="hidden" name="addrC" />

                                        <div class="form-group" style="display: none;" id="transport_date_div">
                                            <label for="need_arrival_date">指定到貨日期 <span class="required">*</span></label>
                                            <!-- <select class="form-control w-50" name="need_arrival_date" id="need_arrival_date" onchange="$('#arrival_date').toggle()">
                                                <option value="0">不指定</option>
                                                <option value="1">指定</option>
                                            </select> -->
                                            <input type="hidden" class="form-control mt-2" id="need_arrival_date" name="need_arrival_date" value="1" />
                                            <input type="date" class="form-control mt-2" id="arrival_date" name="arrival_date" placeholder="請選擇日期" />
                                        </div>

                                        <div class="form-group">
                                            <label for="ps">{{Lang::get('其他備註')}}</label>
                                            <textarea id="ps" class="form-control" rows="5" name="ps"></textarea>
                                        </div>
                                    </div>

                                </div>
                                <div class="mb-4" id="paying_div">
                                    <h4>{{Lang::get('選擇付款方式')}}</h4>
                                    <div class="form-group form-box">
                                        <label>{{Lang::get('付款方式')}} <span class="required">*</span></label>
                                        <select class="form-control" name="pay_way" v-model="pay_way">
                                          <option value="">--{{Lang::get('請選擇')}}--</option>
                                          <option v-for="item in type_decode" :key="'label_pay_way_'+item.pay_fee_id" :value="item.pay_fee_id" v-text="item.name"></option>
                                        </select>
                                    </div>
                                    @if(config('control.thirdpart_money'))
                                        <template v-if="[3,4].indexOf(pay_way)!=-1">
                                            <p>{{Lang::get('如果刷卡失敗請至會員專區>訂單查詢內「補單」')}}</p>
                                        </template>
                                        @if(config('control.control_card_pay'))
                                            <template v-if="[3,4].indexOf(pay_way)!=-1 && !can_card_pay">
                                                <p>{{Lang::get('此筆訂單含有不可刷卡之商品，若欲使用線上刷卡，請刪除該商品並重新整理頁面')}}</p>
                                            </template>
                                        @endif
                                    @endif
                                </div>
                                <div>
                                    <h4>{{Lang::get('發票設定')}}</h4>
                                    <div class="form-group form-box">
                                        <label for="invoice">{{Lang::get('請選擇發票開立方式')}} <span class="required">*</span></label>
                                        <select class="form-control mb-2" name="invoice_style" onchange="showInputs(this.value)">
                                          <option value="">--{{Lang::get('請選擇')}}--</option>
                                          <!-- <option value="1">{{Lang::get('個人實體紙本發票')}}</option> -->
                                          @if(config('control.thirdpart_invoice')==1)
                                            <option value="2">{{Lang::get('個人電子郵件寄送發票')}}</option>
                                            <option value="3">{{Lang::get('個人共通性載具')}}</option>
                                          @endif
                                          <option value="4">{{Lang::get('公司戶發票')}}</option>
                                          <option value="5">{{Lang::get('捐贈')}}</option>
                                        </select>
                                        @if(config('control.thirdpart_invoice')==1)
                                            <span class="required invoice-input" style="display: none;" id="emailNote">{{Lang::get('發票將會寄至您的 Email，進入連結即可列印出紙本發票')}}</span>
                                        @endif
                                        <div class="form-row invoice-input" style="display: none;">
                                            <div class="form-group col-lg-6">
                                                <label for="carrier_type">{{Lang::get('載具類型')}}</label>
                                                <select name="CarrierType" id="CarrierType" class="form-control" onchange="carrierNumLenChange();">
                                                    <option value="3">{{Lang::get('手機條碼')}}</option>
                                                    <option value="2">{{Lang::get('自然人憑證')}}</option>
                                                </select>
                                              </div>
                                            <div class="form-group col-lg-6">
                                              <label for="carrier_number">{{Lang::get('載具編號')}}</label>
                                              <input type="text" class="form-control" name="CarrierNum" id="CarrierNum" value="" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-row invoice-input" style="display: none;">
                                            <div class="form-group col-md-6">
                                              <label for="company_name">{{Lang::get('公司抬頭')}}</label>
                                              <input type="text" class="form-control" id="company_title"
                                              name="company_title" placeholder="">
                                            </div>
                                            <div class="form-group col-md-6">
                                              <label for="gui_number">{{Lang::get('統一編號')}}</label>
                                              <input type="text" class="form-control" id="uniform_numbers"
                                              name="uniform_numbers" placeholder="" maxlength="8">
                                            </div>
                                        </div>
                                        <div class="form-row invoice-input" id="loveCode_div" style="display: none;">
                                            <div class="form-group col-lg-6">
                                                <label for="donate-target">{{Lang::get('捐贈對象')}}</label>
                                                <select id="LoveCode_select" class="form-control" @change="changeLoveCode()">
                                                    <optgroup class="main" label="{{Lang::get('捐贈單位')}}">
                                                        <option v-for="item in type_decode" :value="item.code" v-text="item.name"></option>
                                                    </optgroup>
                                                    <optgroup label="{{Lang::get('其他')}}">
                                                        <option value="">{{Lang::get('其他')}}</option>
                                                    </optgroup>
                                                </select>
                                              </div>
                                            <div class="form-group col-lg-6">
                                              <label for="donate_number">{{Lang::get('捐贈碼')}}</label>
                                              <input id="LoveCode" type="text" class="form-control" name="LoveCode" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-4">
                                    <h4>{{Lang::get('付款金額')}}</h4>
                                    <div class="form-box" id="finalPart">
                                        <div id="myAccordion" class="mb-2">
                                            @if(empty(config('control.close_function_current')['會員管理']))
                                                @if($data['user']['id'] != 0)
                                                    <!-- 因目前未有推薦會員後的相關消費行為，故先註解 -->
                                                    <!-- <div class="form-check mb-2" id="referral">
                                                        <input class="form-check-input" type="radio" name="discount_selected" value="referralCode" id="referral_code">
                                                        <label class="form-check-label" for="referral_code" >
                                                            使用推薦碼
                                                        </label>
                                                        <div id="referralCode" class="mb-2 d-none myAccordion_blank" >
                                                            <div class="d-flex justify-content-between">
                                                                <input type="text" class="form-control" name="referralCode_input">
                                                                <button type="button" class="cursor-pointer">{{Lang::get('確認')}}</button>
                                                            </div>
                                                        </div>
                                                    </div> -->
                                                    <template v-if="discount!=null">
                                                        @if(empty(config('control.close_function_current')['會員優惠設定']))
                                                            @if(config('control.control_FirstBuyDiscount')==1)
                                                                <template v-if="discount.discountData.firstBuyDiscount.can_use==1">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input" type="radio" name="discount_selected" value="firstbuy_select" id="firstbuy_select" v-model="discountSelect">
                                                                        <label class="form-check-label" for="firstbuy_select">
                                                                            {{Lang::get('會員首購優惠')}}
                                                                            <span>(<span v-text="discount.discountData.firstBuyDiscount.note"></span>)</span>
                                                                        </label>
                                                                    </div>
                                                                </template>
                                                            @endif
                                                            @if(config('control.control_VipDiscount')==1)
                                                                <template v-if="discount.discountData.vipDiscount.can_use==1">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input" type="radio" name="discount_selected" value="vipdiscount_select" id="vipdiscount_select" v-model="discountSelect">
                                                                        <label class="form-check-label" for="vipdiscount_select">
                                                                            {{Lang::get('會員等級優惠')}}
                                                                            <span v-text="discount.discountData.vipDiscount.note"></span>
                                                                        </label>
                                                                    </div>
                                                                </template>
                                                            @endif
                                                        @endif
                                                    </template>

                                                    @if(empty(config('control.close_function_current')['點數設定']))
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="radio" name="discount_selected" value="points_0" id="point_discount" v-model="discountSelect">
                                                        <label class="form-check-label" for="point_discount">
                                                            {{Lang::get('使用紅利點數')}}
                                                        </label>
                                                        <div id="pointDiscount" class="mb-2 d-none">
                                                            <span>
                                                                {{Lang::get('可使用點數')}}：
                                                                <span class="num mr-3" style="color: var(--mark);" v-text="available_points"></span>
                                                                {{Lang::get('請輸入欲使用的點數')}}：
                                                            </span>
                                                            <div class="d-flex justify-content-between mt-2">
                                                                <input type="number" class="form-control" step="1" v-model="point_input">
                                                                <button type="button" class="cursor-pointer" @click="set_discount_selected('points_0')">{{Lang::get('確認')}}</button>
                                                            </div>
                                                            <div class="point-response" v-if="point_error_msg">
                                                                <span v-html="point_error_msg"></span>
                                                            </div>
                                                            <div class="mt-2 use-point point-response" v-if="point_success_info">                                                       
                                                                <div class="d-flex align-items-center">
                                                                    <div class="d-flex flex-column">
                                                                        <span class="note" v-html="point_success_info"></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @endif
                                                    @if(empty(config('control.close_function_current')['直接輸入型優惠券']))
                                                        @if($data['user']['id'] != 0)
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="discount_selected" value="directcoupon" id="discount_code" v-model="discountSelect">
                                                            <label class="form-check-label" for="discount_code" >
                                                                {{Lang::get('使用折扣碼')}}
                                                            </label>
                                                            <div id="discountCode" class="mb-2 d-none">
                                                                <div class="d-flex justify-content-between">
                                                                    <input type="text" class="form-control" name="directcoupon_input">
                                                                    <button type="button" class="cursor-pointer" @click="set_discount_selected('directcoupon')">{{Lang::get('確認')}}</button>
                                                                </div>
                                                                <div class="code-response" v-if="code_error_msg">
                                                                    <span style="color: var(--mark);" v-html="code_error_msg"></span>
                                                                </div>
                                                                <div class="mt-2 use-code code-response" v-if="code_success_name">                                                       
                                                                    <div class="d-flex align-items-center">
                                                                        <span class="check-icon" style="margin-top:-6px;"><i class="bi bi-check-lg"></i></span>
                                                                        <div class="mb-2">
                                                                            <span class="mr-2" v-html="code_success_name"></span>
                                                                            <span class="note" v-html="code_success_info"></span>
                                                                        </div>
                                                                    </div>
                                                                    <span class="remove-icon cursor-pointer" @click="set_discount_selected('none_discount')">
                                                                        <i class="bi bi-x-lg"></i>
                                                                        {{Lang::get('取消套用折扣碼')}}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @endif
                                                    @endif

                                                    @if(empty(config('control.close_function_current')['優惠券專區']))
                                                        <!-- 屬於會員優惠券 -->
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="discount_selected" value="coupon" id="coupon_member" v-model="discountSelect" :disabled="discount!=null && discount.coupon_c === 0">
                                                            <label class="form-check-label" for="coupon_member">
                                                                {{Lang::get('會員優惠券')}}
                                                                <template v-if="discount!=null">
                                                                    <span class="mr-2">({{Lang::get('可以使用會員優惠券')}}:
                                                                        <span v-text="discount.coupon_c"></span>
                                                                        <span v-if="discount.coupon_c !== 0">，點選下方優惠券名稱套用</span>)
                                                                    </span>
                                                                </template>
                                                                <a href="{{url('Product/coupon')}}" target="_blank">
                                                                    {{Lang::get('領取優惠券')}}
                                                                </a>
                                                            </label>
                                                            <div class="mb-2">
                                                                <template v-if="discount!=null">
                                                                    <template v-for="vo in discount.discountData.coupon">
                                                                        <a href="###"
                                                                        :class="['conpon_select_radio d-block p-1', coupon_pool_id==vo.coupon_pool_id ? 'conpon_selected' : '']" 
                                                                        v-text="vo.coupon_title"
                                                                        @click="select_coupon(vo.coupon_pool_id)"
                                                                        ></a>
                                                                    </template>
                                                                </template>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endif
                                            @endif
                                            @if(empty(config('control.close_function_current')['活動優惠']))
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="radio" name="discount_selected" value="acts_0" id="acts_0" v-model="discountSelect" :disabled="discount!=null && discount.acts_c === 0">
                                                    <label class="form-check-label" for="acts_0">
                                                        {{Lang::get('活動優惠')}}
                                                        <span>({{Lang::get('符合條件活動')}}:
                                                            <template v-if="discount!=null">
                                                                <span v-text="discount.acts_c"></span>
                                                                <span v-if="discount.acts_c !== 0" v-text="discount.discountData.acts.actCart['1'].name"></span>
                                                            </template>)
                                                        </span>
                                                    </label>
                                                    <div class="mb-2 d-none">
                                                        <div class="optionsOfferBox act_select_context">
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                            @if($data['marketing'] > 0)
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="discount_selected" value="none_discount" id="cancel_discount" v-model="discountSelect">
                                                    <label class="form-check-label" for="cancel_discount">
                                                    {{Lang::get('取消任何折扣')}}
                                                    </label>
                                                </div>
                                            @endif
                                        </div>
                                        <!-- 計算優惠價格用 start -->
                                        <input type="hidden" name="discount" value="none_discount"/>
                                        <input type="hidden" name="point" value=""/>
                                        <input type="hidden" name="directcoupon_code" value=""/>
                                        <!-- 計算優惠價格用 end -->
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>{{Lang::get('商品小計')}}</span>
                                            <span id="finalSubtotal" v-text="'{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalsubTotal))"></span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>{{Lang::get('折扣')}}</span>
                                            <span id="finalDiscount" style="color: var(--mark);" v-text="'-{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalDiscount))"></span>
                                            <input type="hidden" name="discountAmount" :value="'{{Lang::get("扣")}}' + '{{config('extra.shop.dollar_symbol')}}' + finalDiscount">
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>{{Lang::get('運費')}}</span>
                                            <span id="finalShipping" v-text="'{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalShipping))"></span>
                                        </div>
                                        <div class="d-flex flex-row-reverse mb-2">
                                            <span v-if="finalShippingDiscountDiff>0" style="color: var(--mark); font-size: 0.8rem;">{{Lang::get("離獲得免運資格尚差")}}： {{config('extra.shop.dollar_symbol')}}<span style="color: var(--mark); font-size: 0.8rem;" v-text="formatPrice(toNumber(finalShippingDiscountDiff))"></span></span>
                                            <span class="d-none" id="discountOver" style="color: var(--success); font-size: 0.8rem;">{{Lang::get("您已獲得免運資格")}}</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between total">
                                            <span>{{Lang::get('應付合計')}}</span>
                                            <span id="finalTotal" v-text="'{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalsubTotal) - toNumber(finalDiscount) + toNumber(finalShipping))"></span>
                                        </div>
                                        <input type="hidden" name="gift" value="">
                                    </div>   
                                </div>
                                <div class="mb-4">
                                    <h4>{{Lang::get('購物條款')}}</h4>
                                    <div class="form-box">
                                        <div>{!! $data['consent']['shopping'] !!}</div>
                                        <hr>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="accept_policy">
                                            <label class="form-check-label" for="accept_policy">
                                            {{Lang::get('同意購物條款')}} <span class="required">(*{{Lang::get('必勾選')}})</span>
                                            </label>
                                        </div>
                                        <p class="mb-3">※{{Lang::get('請您再次確認以上購買資料是否無誤，並勾選「同意購物條款」後按下「確定訂單」')}}</p>

                                        @if(empty(config('control.close_function_current')['會員管理']))
                                            @if($data['user']['id'] == 0)
                                            <div class="form-check">
                                                <input type="hidden" name="upline_user" value="{{$data['productinfo_recommend']}}">
                                                <input id="defaultCheck2" class="form-check-input" type="checkbox" value="1" name="become_member">
                                                <label class="form-check-label" for="defaultCheck2">
                                                    <span class="terms">{{Lang::get('以購買資料註冊會員')}}</span>
                                                </label>
                                            </div>
                                            <p class="smallText mb-2">
                                                ※{{Lang::get('勾選後將利用所填資料註冊會員，下次購物即可開始使用會員相關優惠(預設密碼為:1234)')}}
                                            </p>
                                            @endif
                                        @endif
                                    </div>
                                    
                                </div>
                                <button type="button" class="confirm-btn" id="confirm_form">{{Lang::get('確定訂單')}}</button>
                                <button class="d-none" id="confirm_form_hidden">{{Lang::get('確定訂單')}}</button>
                            </div>
                        </div>
                    </form>