@extends('home.Public.mainTpl')

<!--搜尋引擎SEO-->
@section('title'){{$data['productinfo']['title']}} - {{$data['frontend_menu'][strtolower($data['controller'])]['name']}}| {{$data['seo'][0]['title']}}@endsection
@section('Keywords') @if($data['productinfo']['keywords'] != '') {{$data['productinfo']['keywords']}} @else {{$data['seo'][0]['seokey']}} @endif @endsection
@section('description') @if($data['productinfo']['content'] != '') {{$data['productinfo']['content']}} @else {{$data['seo'][0]['descr']}} @endif @endsection

<!--社群分享SEO-->
@section('ogtitle') @if(config('control.control_social_share')==1 && $data['productinfo']['title'] != '') {{$data['productinfo']['title']}} @else {{$data['seo'][0]['fb_title']}} @endif @endsection
@section('ogdescription') @if(config('control.control_social_share')==1 && $data['productinfo']['content'] != '') {{strip_tags($data['productinfo']['content'])}} @else {{$data['seo'][0]['fb_descr']}} @endif @endsection
@section('ogurl') @if(config('control.control_social_share')==1) http://{{request()->server('HTTP_HOST')}}{{request()->server('REQUEST_URI')}} @else http://{{request()->server('HTTP_HOST')}} @endif @endsection
@section('ogimage')@if(config('control.control_social_share')==1 && $data['productinfo']['pic'][0] != '') {{__PUBLIC__}}{{$data['productinfo']['pic'][0]}} @else {{__PUBLIC__}}{{$data['seo'][0]['fb_img']}} @endif @endsection

@section('css')
    <style type="text/css">
        /*.tabBox .tabcontent img{*/
        /*    max-width:  fit-content;*/
        /*}*/
        .tabBox .tabcontent{
            display: none;
        }
        /* .tabBox .tabcontent p{
            display: inline-block;
        } */
    </style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a class="cursor-initial">{{$data['frontend_menu'][strtolower($data['controller'])]['name']}}</a></li>
            
                @foreach($data['title_array'] as $ta)
                <li><a href="{{url('Product/'.$ta['type'])}}?id={{$ta['id']}}">{{$ta['title']}}</a></li>
                @endforeach
                <li>{{$data['productinfo']['title']}}</li>
            </ul>
        </div>
    </section>

    @if(config('control.control_prod_edm')==1)
        <div id="rwd_edm" class="rwd_edm container"></div>
    @endif

    <section id="productifnArea">
        <div class="container max-wideVersion productPublic productinfo">
            <div id="itemBox">
                <div id="rightContentBox" class="innerPageBox">
                    <div>
                        <!-- product intro start -->
                        <div class="row productIntroBox arrow no-gutters">
                            <!-- 輪播商品圖 -->
                            <div class="col-xl-6 col-12 proImgBox">
                                <div id="carousel01" class="owl-carousel owl-theme proImgCarousel d-flex align-items-center">
                                    @foreach($data['productinfo']['pic'] as $vo)	
                                        <div class="item">
                                            @if(substr( explode('.', $vo)[1], 0, 3) == 'mp4' && config('control.control_upload_film')==1)
                                                <div><video src="{{__PUBLIC__}}{{$vo}}" type="video/mp4" style="width: 100%" controls="controls"></video></div>
                                            @else
                                                <div class="bgImg-responsive-12fBy12" style="background-image: url({{__PUBLIC__}}{{$vo}});"></div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                                <div id="carousel02" class="owl-carousel owl-theme proImgCarousel d-flex align-items-center">
                                    @foreach($data['productinfo']['pic'] as $key => $vo)	
                                        <div class="item cursor-pointer img-s" value="{{$key}}">
                                            @if(substr( explode('.', $vo)[1], 0, 3) == 'mp4' && config('control.control_upload_film')==1)
                                                <video src="{{__PUBLIC__}}{{$vo}}" type="video/mp4" style="width: 100%" ></video>
                                            @else
                                                <div class="bgImg-responsive-12fBy12 "
                                                    style="background-image: url({{__PUBLIC__}}{{$vo}});"></div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="col-xl-6 col-12 pdSpacing">
                                <div class="share">
                                    <!-- 人氣 -->
                                    <span class="btn love_btn mr-1" v-if="love==0" @click="set_love(1)">
                                        <i class="bi bi-hand-thumbs-up"></i>
                                        <span v-text="love_num"></span>
                                    </span>
                                    <span class="btn love_btn fill mr-1" v-if="love!=0" @click="set_love(0)">
                                        <i class="bi bi-hand-thumbs-up-fill"></i>
                                        <span v-text="love_num"></span>
                                    </span>
                                
                                    <!-- 複製網址 -->
                                    <span class="btn share_btn fill mr-1" @click="copyURL(1)">
                                        <i class="bi bi-share"></i>
                                        <input ref="mylink" class="position-absolute" style="opacity: 0;width: 10px;">
                                    </span>
                                
                                    <!-- 收藏 -->
                                    <span class="btn store_btn mr-1" v-if="store==0" @click="set_store(1)">
                                        <i class="bi bi-heart"></i>
                                    </span>
                                    <span class="btn store_btn fill mr-1" v-if="store!=0" @click="set_store(0)">
                                        <i class="bi bi-heart-fill"></i>
                                    </span>
    
                                    <!-- 社群 -->
                                    <div class="addthis_inline_share_toolbox"></div>
                                </div>
                                <div class="proTitleBox">
                                    <h3>{{$data['productinfo']['title']}} 
                                        @if(isset($data['kol_name']))
                                            <span class="prod_tag kol_tag">{{Lang::get('網紅推薦')}}：{{$data['kol_name']}}</span>
                                        @endif
                                    </h3>
                                    <!-- @if(empty(config('control.close_function_current')['存放位置管理']))
                                        <span class="num d-inline-block  w-100">{{Lang::get('條碼')}}：{{$data['productinfo']['ISBN']}}</span>
                                    @else
                                        @if($data['productinfo']['ISBN']!='')
                                        <span class="num d-inline-block  w-100">{{$data['productinfo']['ISBN']}}</span>
                                        @endif
                                    @endif -->
                                  
                                </div>
                                <div class="proinfoBox">
                                    @if(config('extra.productinfo.property1')!='-1' && $data['productinfo']['Author'])
                                        <div class="item">
                                            <h4>{{config('extra.productinfo.property1')}}{{$data['productinfo']['Author']}}</h4>
                                        </div>
                                    @endif
                                    @if(config('extra.productinfo.property2')!='-1' && $data['productinfo']['house'])
                                        <div class="item">
                                            <h4>{{config('extra.productinfo.property2')}}{{$data['productinfo']['house']}}</h4>
                                        </div>
                                    @endif
                                    @if(config('extra.productinfo.property3')!='-1' && $data['productinfo']['house_date'])
                                        <div class="item">
                                            <h4>{{config('extra.productinfo.property3')}}{{$data['productinfo']['house_date']}}</h4>
                                        </div>
                                    @endif
                                </div>
                                <div class="specialZone">
                                    <div class="activityBox sell">
                                        @if($data['act']['type_name'])                          
                                            <span class="activity">{{$data['act']['name']}}：{{$data['act']['content']}}</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="contentBox mb-4">
                                    <h4 class="product-info-title">產品簡介：</h4>
                                    <p>{!! $data['productinfo']['content'] !!}</p>
                                </div>
                               
                                @if($data['productinfo']['prodesc']!='==未輸入==' && 
                                               $data['productinfo']['prodesc']!='' && 
                                               empty(config('control.close_function_current')['商品描述設定']))
                                        <div class="item mb-4 prodesc">
                                            {{$data['productinfo']['prodesc']}}
                                        </div>
                                @endif
                               
                                <div class="row no-gutters bottomBox">
                                    <div class="priceBox aaBox w-100">
                                        <div class="amountBox" v-if="show_price">
                                            <template v-if="current_price.price">
                                                <span class="originalPrice" v-if="current_price.price != current_price.count">
                                                    {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}
                                                    <span v-if="current_price.price" v-text="current_price.price.toLocaleString('en-US')"></span>
                                                </span>
                                            </template>
                                            <span class="offerPrice">
                                                {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}
                                                <span class="price" v-if="current_price.count!==''">
                                                    <span v-text="current_price.count.toLocaleString('en-US')"></span>
                                                </span>
                                            </span>
                                            @if($data['coupon_button'])
                                                <div class="couponBox mt-4 mt-md-0 ml-3">
                                                    <a id="infocartButton" {{$data['coupon_button']}}>{{Lang::get('領取會員優惠券')}}</a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <!-- 商品規格 -->
                                    <div class="w-100" v-if="price.length>1 && productinfo.has_price==1">
                                        <!-- 下拉選式 ->
                                        <div class="d-flex selectBox w-100">
                                            <span class="selectTitle">{{Lang::get('規格')}}</span>
                                            <select class="form-control" id="priceSelect" v-model="priceSelect">
                                                <option v-for="pricevo in price" 
                                                        :value="pricevo.id" 
                                                        v-text="pricevo.title">
                                                </option>
                                            </select>
                                        </div> -->
                                        <div class="activityBox w-100">
                                            <p class="activity format">
                                                <span class="selectTitle">{{Lang::get('規格')}}：</span>
                                                <span v-if="selectTypes.length>1" v-text="get_selectTypes_name(' ')"></span>
                                            </p>
                                        </div>
                                        <!-- 直接點選式 -->
                                        <div class="format-items activityBox  position-absolute invisible">
                                            <span v-for="pricevo in price">
                                                <label  class="price_option"
                                                        :for="'priceOption1_' + pricevo.id" 
                                                        :data="pricevo.title" v-text="pricevo.title" 
                                                        :pic_index="pricevo.pic_index"
                                                        @click="changePic(pricevo.pic_index)"></label>
                                                <input  type="radio" name="priceOption1" class="radio_price_option" radios="price_option"
                                                        v-model="priceSelect"
                                                        :id="'priceOption1_' + pricevo.id" :value="pricevo.id">
                                            </span>
                                        </div>
                                        <!-- 多品項組合式 -->
                                        <div v-for="(types, index) in show_type" class="format-items activityBox">
                                            <span v-for="type in types">
                                                <label :for="'type1_' + index + '_' + type" v-text="type" :class="'type_' + index" @click="changeTypes()"></label>
                                                <input  type="radio" class="radio_price_option"
                                                        :name="'type1_' + index" 
                                                        :radios="'type_' + index" 
                                                        v-model="selectTypes[index]"
                                                        :id="'type1_' + index + '_' + type" :value="type">
                                            </span>
                                        </div>
                                    </div>
    
                                    @if(!isset(config('control.close_function_current')['訂單管理']))
                                        <div class="activityBox amount-item"> 
                                            @if(!($data['productinfo']['is_box'] == 1 && $data['productinfo']['is_customized_box'] == 1))
                                                <div class="spinnerBox" v-if="show_price">
                                                    <p class="mr-1">{{Lang::get('數量')}}</p>
                                                    <div class="input-group spinner">
                                                        <div class="input-group-prepend">
                                                            <button class="btn text-monospace" type="button" @click="changeNum(-1)">-</button>
                                                        </div>
                                                        <input type="number" class="count form-control" min="1" :max="limit_num" step="1" v-model="itemcounter">
                                                        <div class="input-group-append">
                                                            <button class="btn text-monospace" type="button" @click="changeNum(1)">+</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                            
                                            @if(!isset(config('control.close_function_current')['庫存警示']))
                                                @if(!($data['productinfo']['is_box'] == 1 && $data['productinfo']['is_customized_box'] == 1))
                                                    <div class="specialZone mr-3" v-if="show_price">
                                                        <div class="activityBox">
                                                            <span class="activity">
                                                                <span v-for="pricevo in price" :class="[pricevo.id != current_price.id ? 'd-none' : '']">
                                                                    <div v-if="pricevo.num < pricevo.limit_num">
                                                                        {{Lang::get('庫存數')}}：
                                                                        <span v-text="pricevo.num"></span>
                                                                    </div>
                                                                </span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    @if(config('control.control_pre_buy')==1)
                                                    <div class="specialZone mr-3" v-if="show_price && productinfo.pre_buy==1">
                                                        <div class="activityBox">
                                                            <span class="activity">{{Lang::get('可超額購買')}}</span>
                                                            <span class="activity" v-if="productinfo.pre_buy_limit > 0">
                                                                ({{Lang::get('總數')}}：<span v-text="productinfo.pre_buy_limit"></span>)
                                                            </span>
                                                        </span>
                                                        </div>
                                                    </div>
                                                    @endif
                                                @endif
                                            @endif
    
                                            <div class="bankStagingBox" v-if="show_price">
                                                @if(config('control.control_card_pay')==1)
                                                    <div v-if="productinfo.card_pay == 1">
                                                        <p><span class="">{{Lang::get('可以刷卡')}}</span></p>
                                                        <!-- 
                                                            <p>
                                                                {{Lang::get('分期付款')}}：
                                                                <span class="num">3</span>、
                                                                <span class="num">6</span>、
                                                                <span class="num">12</span>、
                                                            </p>
                                                        -->
                                                    </div>
                                                    <div v-if="productinfo.card_pay != 1">
                                                        <p><span class="num">{{Lang::get('不可以刷卡')}}</span></p>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
    
                                        @if(config('control.control_down_line')==1)
                                            <div class="popularProBranch w100 pl-1 pr-1 pt-2 mb-3">
                                                <a href="###" class="togo" @click="copy_product_share_link(prodInfoId)">
                                                    {{Lang::get('取得專屬推廣連結')}} <i class="bi bi-share"></i>
                                                </a>
                                            </div>
                                        @endif
    
                                        <div class="cartBoxFixed" v-if="show_price">
                                            @if($data['productinfo']['is_box'] == 1 && $data['productinfo']['is_customized_box'] == 1)
                                            <div class="customizeBox">
                                                <a href="{{url('Product/customizebox')}}?id={{$data['productinfo']['id']}}">{{Lang::get('開始訂製禮盒')}}</a>
                                            </div>
                                            @else
                                            <div class="addCartBox">
                                                <a href="javascript:select_type('cartCtrl')" class="cartButton3">
                                                   {{Lang::get('加入購物車')}}
                                                </a>
                                            </div>
                                            <div class="shoppingBox">
                                                <a href="javascript:select_type('cartCtrlGO')">
                                                    {{Lang::get('直接購物')}}
                                                </a>
                                            </div>
                                            @endif
                                            @if(!isset(config('control.close_function_current')['詢價回函']))
                                                @if($data['productinfo']['ask_price'])                                             
                                                    <div class="addCartBox" v-if="price.length>0">
                                                        @if($data['user']['id'] != 0)
                                                            <a href="###" class="cartButton4" @click="open_askprice_model">
                                                                {{Lang::get('我要詢價')}}
                                                            </a>
                                                        @else
                                                            <a href="javascript:unregistered()" class="cartButton4">
                                                                {{Lang::get('我要詢價')}}
                                                            </a>
                                                        @endif
                                                    </div>                                              
                                                @endif
                                            @endif
                                        </div>
                                    @endif
                                    <div class="w-100 row no-gutters proPreparationBox" v-if="!show_price">
                                        <div class="col-12">
                                            <div class="mb-3">
                                                <p>
                                                    <span v-if="control_register==1 && productinfo.is_registrable==1">{{Lang::get('報名已截止')}}</span>
                                                    <span v-if="control_register==0 || productinfo.is_registrable==0">{{Lang::get('商品籌備中，目前無法訂購')}}</span>
                                                    <br>{{Lang::get('若有相關需求，歡迎來電詢問')}}
                                                </p>
                                            </div>
                                            <div class="g_addcart">
                                                <a class="sayButton" href="javascript:go_contact()">{{Lang::get('我要詢問')}}</a>
                                            </div>
                                        </div>
                                    </div>
    
                                    @if(!isset(config('control.close_function_current')['加價購設定']))
                                        <div class="col-12 row mbSpacing activityBox mb-3">
                                            @foreach($data['addprice_type_group'] as $productvo)
                                            <div class="col-lg-12 col-md-6 col-12">
                                                <span class="activity">
                                                    <span class="num">{{Lang::get('凡購買')}}</span>
                                                    @if(!empty($productvo['rule_info']['title']))
                                                        「{{$productvo['rule_info']['title']}}」
                                                    @else
                                                        「{{$productvo['rule_info']['pi_title']}}」
                                                    @endif
                                                    {{Lang::get('可加價購')}}：
                                                </span>
                                                <ul class="ml-2 mb-0">
                                                    @foreach($productvo['product'] as $p_k => $product)
                                                        <li>
                                                            <span class="activity">
                                                                「{{$product['pi_title']}}-{{$product['title']}}」，
                                                                {{Lang::get('數量')}}：{{$product['adp_p_num']}}
                                                            </span>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                            @endforeach
                                            @if(count($data['addprice_type_group']) > 0)
                                                <div class="col-12 mt-2">
                                                    <span class="activity mark">
                                                        <i class="bi bi-tag"></i> {{Lang::get('請先加入商品，再進入購物車進行加購')}}
                                                    </span>
                                                </div>
                                            @endif
                                        </div>
                                    @endif
    
                                    @if(!isset(config('control.close_function_current')['標籤設定']))
                                        <div class="labelBox">
                                            @if($data['hot_product_type'])
                                            <span class="popularityIcon">{!!$data['hot_product']!!}</span>
                                            @endif
                                            @if($data['recommend_product_type'])
                                            <span class="hornIcon">{!!$data['recommend_product']!!}</span>
                                            @endif
                                            @if($data['expiring_product_type'])
                                            <span class="inventoryIcon">{!!$data['expiring_product']!!}</span>
                                            @endif
                                            @if($data['spec_product_type'])
                                            <span class="offerIcon">{!!$data['spec_product']!!}</span>
                                            @endif
                                        </div>
                                    @endif
                                    @if(config('control.control_platform')==1)
                                        @if($data['productinfo']['distributor_id'] != 0)
                                            <div class="num d-inline-block mr-2">                                            
                                                <a href="{{url('product/distributor')}}?id={{$data['productinfo']['distributor_id']}}" target="_blank"
                                                   class="d-inline-flex align-items-center">
                                                    @include ('home.Public.svg_shop')
                                                    {{$data['productinfo']['shop_name']}} {{Lang::get('查看賣場')}}
                                                </a>
                                            </div>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>
                        <!-- product intro end -->
                    </div>
                </div>
            </div>
        </div>
        <div class="tabRow" id="proIntroTab">
            <div class="tabBox">
                <ul class="tab tabNavBox owl-carousel tabCarousel owl-theme">
                    @if($data['productinfo']['text1_online'] =="1")
                        <li>
                            <a class="tablinks active" onclick="openTabs(event, 'tab1')">
                                <span>{{Lang::get('商品介紹')}}</span>
                            </a>
                        </li>
                    @endif
                    @if($data['productinfo']['text2_online'] =="1")
                        <li>
                            <a class="tablinks " onclick="openTabs(event, 'tab2')">
                                <span>{{Lang::get('商品屬性')}}</span>
                            </a>
                        </li>
                    @endif
                    @if($data['productinfo']['text3_online'] =="1")
                        <li>
                            <a class="tablinks " onclick="openTabs(event, 'tab3')">
                                <span>{{Lang::get('訂購須知')}}</span>
                            </a>
                        </li>
                    @endif
                    @if($data['productinfo']['text4_online'] =="1")
                        <li>
                            <a class="tablinks " onclick="openTabs(event, 'tab4')">
                                <span>{{Lang::get('付款方式')}}</span>
                            </a>
                        </li>
                    @endif
                    @if($data['productinfo']['text5_online'] =="1")
                        <li>
                            <a class="tablinks " onclick="openTabs(event, 'tab5')">
                                <span>{{Lang::get('其他說明')}}</span>
                            </a>
                        </li>
                    @endif
                    @if(!isset(config('control.close_function_current')['商品問答']))
                        <li>
                            <a class="tablinks " onclick="openTabs(event, 'tab_qa');">
                                <span>{{Lang::get('商品問答')}}</span>
                            </a>
                        </li>
                    @endif
                </ul>
                <div class="tab-bottom"></div>
    
                @if($data['productinfo']['text1_online'] =="1")
                    <div id="tab1" class="tabcontent container">
                        {!! $data['productinfo']['text1'] !!}
                    </div>
                @endif
                @if($data['productinfo']['text2_online'] =="1")
                    <div id="tab2" class="tabcontent container">    
                        {!! $data['productinfo']['text2'] !!}
                    </div>
                @endif
                @if($data['productinfo']['text3_online'] =="1")
                    <div id="tab3" class="tabcontent container">
                        {!! $data['productinfo']['text3'] !!}
                    </div>
                @endif
                @if($data['productinfo']['text4_online'] =="1")
                    <div id="tab4" class="tabcontent container">
                        {!! $data['productinfo']['text4'] !!}
                    </div>
                @endif
                @if($data['productinfo']['text5_online'] =="1")
                    <div id="tab5" class="tabcontent container">
                        {!! $data['productinfo']['text5'] !!}
                    </div>
                @endif
    
                <div id="tab_qa" class="tabcontent container">
                    @if(!isset(config('control.close_function_current')['商品問答']))
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th style="width:100px;" scope="col"></th>
                                    <th scope="col">{{Lang::get('意見')}}</th>
                                    <th style="width: 120px;" scope="col">{{Lang::get('日期')}}</th>
                                </tr>
                            </thead>
                            <tbody v-for="item in prodQa">
                                <tr>
                                    <th>{{Lang::get('問題')}}</th>
                                    <td v-html="item.prod_q"></td>
                                    <td v-text="item.q_datetime"></td>
                                </tr>
                                <tr v-if="item.prod_a">
                                    <th>{{Lang::get('答覆')}}</th>
                                    <td v-html="item.prod_a"></td>
                                    <td v-text="item.a_datetime"></td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="loginQa">
                            <div class="text-right">
                                @if($data['user']['id'] != 0)
                                    <textarea class="form-control" rows="3" v-model="addProdQ"></textarea>
                                    <button type="button" class="btn togo mt-2" @click="submitProdQ()">{{Lang::get('送出')}}</button>
                                @else
                                    <button class="btn togo mt-2" data-toggle="modal" data-target="#memberLogin">{{Lang::get('登入提問')}}</button>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
    @if($data['pushItem'] != [])    
    <div class="container">
        <div class="popularProBranch suggestProductBox">
            <div class="titleBox">
                <h3 class="title"><span>相關商品</span></h3>
            </div>
            <div class="mgSpacing productListRow">
                <div class="owl-carousel owl-theme suggest-owl-carousel col-12">
                    @foreach($data['pushItem'] as $item)
                        @if(isset($item['id']))
                            <div class="item">
                                <a href="{{url('Product/productinfo')}}?id={{$item['id']}}">
                                    <div class="bgImg" style="background-image: url({{__PUBLIC__}}{{$item['pic']}});">
                                    </div>
                                    <h4>{{$item['title']}}</h4>
                                </a>
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    @endif
    <div class="container">
    @if(count($data['productRand'])>0)
        <div class="popularProBranch">
            <div class="titleBox">
                <h3 class="title"><span>{{Lang::get('其它熱門分類館')}}</span></h3>
            </div>
            <div class="row mgSpacing w-100">
                @foreach($data['productRand'] as $productRandvo)
                <div class="col-md-6 recommendBox">
                    <div>
                        <img src="{{__PUBLIC__}}{{$productRandvo['pic']}}" alt="">
                        <p>{!!$productRandvo['content']!!}</p>
                        <a href="{{url('Product/product')}}?id={{$productRandvo['id']}}" class="togo">
                            {{Lang::get('立即前往')}}<i class="icon-right"></i><span>{{$productRandvo['title']}}</span>
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    @endif
    </div>
@endsection

@section('Modal')
    <a id="askpriceModel_btn" data-toggle="modal" data-target="#askpriceModel" class="d-none"></a>
    <!-- 跳出視窗：詢價回函 -->
    <div class="modal fade shoppingCart memberLogin" id="askpriceModel" tabindex="-1" role="dialog"
        aria-labelledby="askpriceModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="memberLoginTitle">{{Lang::get('我要詢價')}}</h5>
                </div>
                <div class="modal-body">
                    <form action="{{url('Askprice/doContact')}}" method="post" name="askpriceForm">
                        @csrf
                        <div class="ask_price_content">
                            <div class="pic">
                                <img class="border" src="{{__PUBLIC__}}{{$data['productinfo']['pic'][0]}}">
                            </div>
                            <div class="text">
                                <h3 id="askpriceModel_product_name" class="askpriceModel_product_name"></h3>&nbsp;&nbsp;&nbsp;&nbsp;<h3 id="askpriceModel_product_type_name"></h3>
                                <div class="d-flex flex-wrap justify-content-between w-100">
                                    <p class="askpriceModel_product_num">{{Lang::get('數量')}}：<input type="number" class="form-control" name="num" value="1" min="1"/></p>
                                    <div class="askprice_order">
                                        <label class="name">{{Lang::get('詢價金額')}}：</label>
                                        <input type="number" class="form-control price" name="price" value="" min="0"/>
                                        <input type="hidden" name="product_id" value=""/>
                                        <input type="hidden" name="product_name" value=""/>
                                        <input type="hidden" name="product_type_id" value=""/>
                                        <input type="hidden" name="product_type_name" value=""/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>{{Lang::get('留言')}}：</label>
                            <div class="col-md-12 col-12">
                                <div class="row">
                                    <textarea name="ask" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="addCartBox">
                            <a class="cartButton4 text-center w-120 btn m-auto"  onclick="send_askpriceForm()" id="check_btn_askpriceForm">{{Lang::get('送出')}}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <a id="askpriceOkModel_btn" data-toggle="modal" data-target="#askpriceOkModel" class="d-none"></a>
    <div class="modal fade shoppingCart memberLogin" id="askpriceOkModel" tabindex="-1" role="dialog"
        aria-labelledby="askpriceOkModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="memberLoginTitle">{{Lang::get('詢價成功')}}</h5>
                </div>
                <div class="modal-body text-center w-100">
                    {{Lang::get('您可至詢價紀錄查看相關紀錄')}}
                    -><a href="{{url('Askprice/askprice')}}">{{Lang::get('立即前往')}}</a>
                </div>
                <a href="{{url('Askprice/askprice')}}" class="w-120 d-block ml-auto mr-auto salebtn btn mb-4">{{Lang::get('關閉')}}</a>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/owl.productIntro.js"></script>
    <script src="{{__PUBLIC__}}/js/productIntroTabs.js"></script>
    <script src="{{__PUBLIC__}}/js/productIntroSelect.js"></script>
    <script src="{{__PUBLIC__}}/js/productIntroAddLessOperation.js"></script>
    <script src="{{__PUBLIC__}}/js/tabOwlCarousel.js"></script>

    <script>
        $(window).resize(function() {
            init_iframe_height();
        });
        $(document).ready(function() {
            init_iframe_height();
        });
        function init_iframe_height(){
            $('.tabcontent iframe').each((index, item)=>{
                const t = $(item);
                w = t.width();
                h = t.width() / 560 * 315;
                t.attr('height', h);
            })
        }
    </script>
    <!-- 插入EDM -->
    <script type="text/javascript">
        if("{{config('extra.shop.edm_url')}}" && "{{config('control.control_prod_edm')}}"=='1' && "{{$data['productinfo']['out_ID']}}"!='0'){
            $.ajax({
                url: "{{config('extra.shop.edm_url')}}/api/product_cms/{{$data['productinfo']['out_ID']}}/view",
                type: 'GET',
                datatype: 'json',
                success: function (response) {
                    $('head').append('<link href="{{config('extra.shop.edm_url')}}/frontEndPackage/css/RWD_eidtor_style.css" rel="stylesheet" type="text/css">');
                    $('html').append('<script src="{{config('extra.shop.edm_url')}}/frontEndPackage/js/template.js"><\/script>');
                    setTimeout(function(){
                        $('#rwd_edm').html(response);
                    }, 150);
                }
            });
        }
    </script>
    <script type="text/javascript">
        // var codeVerifyCheck_askpriceForm  = true; /*配合mainTpl送表單檢查*/
        function send_askpriceForm(){
            $('#body_block').show();
            // submitForm('askpriceForm');
            post_data = {};
            askpriceForm_obj = new FormData(askpriceForm);
            const inputs = $('[name="askpriceForm"] input, [name="askpriceForm"] textarea');
            for (var i = 0; i < inputs.length; i++) {
                const name = $(inputs[i]).attr('name');
                post_data[name] = askpriceForm_obj.get(name)
            }
            post_url = $('[name="askpriceForm"]').attr('action');
            $.ajax({
                method: 'post',
                dataType: 'json',
                url: post_url,
                data: post_data,
                success: function(resp){
                    if(resp.code==1){
                        $('#askpriceModel .close').click();
                        $('#askpriceModel [name="price"]').val('');
                        $('#askpriceModel [name="num"]').val(1);
                        $('#askpriceModel [name="ask"]').val('');
                        $('#askpriceOkModel_btn').click();
                    }else{
                        Vue.toasted.show(resp.msg, vt_error_obj);
                    }
                    $('#body_block').hide();
                },
                error: function(e){
                    $('#body_block').hide();
                },
            });
        }

        function go_contact(){
            url = "{{url('About/about_contact')}}?distributor_id={{$data['productinfo']['distributor_id']}}&prod_id={{$data['productinfo']['id']}}";
            if(priceOptionVM.current_price.id){
                type_id = priceOptionVM.current_price.id.toString();
                url += ("&type_id="+type_id);
            }
            window.open(url);
        }
    </script>

    <script type="text/javascript">
        productinfoVMData = {
            /* 商品問答功能 ----------------------------*/
            prodInfoId: "{{$data['productinfo']['id']}}",
            prodInfoName: "{{$data['productinfo']['title']}}",
            prodQa: [],
            addProdQ: "",

            /* 品項功能 ----------------------------*/
            control_register: "{{config('control.control_register')}}",
            productinfo: { 
                is_registrable: '{{$data["productinfo"]["is_registrable"]}}', 
                has_price:'{{$data["productinfo"]["has_price"]}}', 
                card_pay:'{{$data["productinfo"]["card_pay"]}}',
                pre_buy:'{{$data["productinfo"]["pre_buy"]}}',
                pre_buy_limit:'{{$data["productinfo"]["pre_buy_limit"]}}',
            },
            price_json: "{{json_encode($data['price'])}}".replace(/&quot;/g,'"').trim(), /*品項*/
            itemcounter: 1,                     /*加入購物車的數量*/
            limit_num: 10,                      /*加入購物車數量上限*/
            selectTypes: [],                    /*選擇的品項分段*/

            /* 人氣功能 ----------------------------*/
            love: "{{$data['love']['record']}}",      /*是否有愛心*/
            love_num: "{{$data['love']['num']}}",     /*此商品被愛心的次數*/
            /* 我的收藏功能 ----------------------------*/
            store: "{{$data['store']['record']}}",     /*是否有收藏*/
            store_num: "{{$data['store']['num']}}",    /*此商品被收藏的次數*/
        };

        /*選擇的品項id - 初始化*/
        if ("{{json_encode($data['price'])}}".replace(/&quot;/g,'"').trim() == '[]') {
            productinfoVMData.priceSelect = 0;
        } else {
            productinfoVMData.priceSelect = JSON.parse("{{json_encode($data['price'])}}".replace(/&quot;/g,'"').trim())[0].id;
        }

        /*初始化vue 品項選擇 商品說明資訊區*/
        var productifnAreaVM = new Vue({
            el: '#productifnArea',
            data: productinfoVMData,
            // @include('home.product.select_type_vue')
        });

        /*初始化後添加methods*/
            /* 商品問答功能 ----------------------------*/
            productifnAreaVM.getProdAllQa = function () {
                $.ajax({
                    method : "post",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    dataType: 'json',
                    url : "{{url('index/Ajax/prodAllQa')}}",
                    data: {prodInfoId: productifnAreaVM.prodInfoId},
                }).success(function(resp){
                    productifnAreaVM.prodQa = resp.msg
                    for(var i=0; i<productifnAreaVM.prodQa.length;i++){
                        if(productifnAreaVM.prodQa[i]['prod_a'])
                            productifnAreaVM.prodQa[i]['prod_a'] = productifnAreaVM.prodQa[i]['prod_a'].replaceAll('\n', '<br>');
                    }
                    //console.log(this.prodQa )
                }).error(function(){
                })//error
            }
            productifnAreaVM.submitProdQ = function () {
                if(!productifnAreaVM.addProdQ){
                    Vue.toasted.show("{{Lang::get('請填寫詢問內容')}}", vt_error_obj);
                    return;
                }
                Vue.toasted.show("{{Lang::get('處理中')}}",{duration:1500});
                $('#body_block').show();
                $.ajax({
                    method: "post",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    dataType: 'json',
                    url : "{{url('Ajax/prodQaCreate')}}",
                    data: {prodInfoId:productifnAreaVM.prodInfoId, prodQ:productifnAreaVM.addProdQ},
                }).success(function(resp){
                    if (resp.code == 1){
                        productifnAreaVM.getProdAllQa();
                        productifnAreaVM.addProdQ = "";
                        bg_class = "bg-success";
                    }else{
                        bg_class = "bg-danger";
                    }
                    Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", bg_class]});
                    $('#body_block').hide();
                }).error(function(res){
                    Vue.toasted.show(res.msg, vt_error_obj);
                    $('#body_block').hide();
                })//error
            }

            /* 人氣功能 ----------------------------*/
            productifnAreaVM.set_love = function(status) {
                $('#body_block').show();
                $.ajax({
                    method : "post",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    url : "{{url('Ajax/love_record')}}",                    
                    data: {
                        prodInfoId:productifnAreaVM.prodInfoId, 
                        status:status
                    },
                }).success(function(data){
                    if(data.code == '1'){
                        productifnAreaVM.love = status;
                        productifnAreaVM.love_num = data.msg;
                        Vue.toasted.show("{{Lang::get('操作成功')}}", vt_success_obj);
                    }else{
                        Vue.toasted.show(data.msg, vt_error_obj);
                    }
                    $('#body_block').hide();
                }).error(function(res){
                    Vue.toasted.show(res.msg, vt_error_obj);
                    $('#body_block').hide();
                })//error
            }

            /* 人氣功能 ----------------------------*/
            productifnAreaVM.copyURL = function() {
                var Url = $(productifnAreaVM.$refs.mylink);
                Url.val(window.location.href);
                Url.select();
                document.execCommand("copy");
                Vue.toasted.show("{{Lang::get('操作成功')}}", vt_success_obj);
            }

            $( document ).ready(function() {
                setTimeout(function(){
                    productifnAreaVM.getProdAllQa();
                }, 1000);
            });
    </script>
@endsection
