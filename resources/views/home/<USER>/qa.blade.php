@extends('home.Public.mainTpl')
@section('title'){{$data['frontend_menu']['qa']['name']}} | {{$data['seo'][0]['title']}}@endsection
@section("css")
    <style>
        .qaBox:hover{
            cursor: pointer;
        }
    </style>
@endsection
@section("content")
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['qa']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}/{$frontend_menu['qa']['pic']});">
            <div class="container">
                <h2 class="page-title" style="color:{$data['frontend_menu']['qa']['text_color']}">{$data['frontend_menu']['qa']['name']}</h2>
                <!-- <span class="enText">{$data['frontend_menu']['qa']['en_name']}</span> -->
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Qa/qa')}}">{{$data['frontend_menu']['qa']['name']}}</a></li>
            </ul>
        </div>
    </section>

    <section class="container max-wideVersion productPublic">
        <div id="itemBox">
            <div id="rightContentBox" class="innerPageBox">
                <div class="strongholdBox">
                    <ul class="nav tabNavBox justify-content-center owl-carousel tabCarousel owl-theme">
                        <li class="item nav-item {{App\Services\CommonService::compare_return($data['cate'], '', 'active')}}">
                            <a href="{{url('Qa/qa')}}?cate=&searchText={{$data['searchText']}}">{{Lang::get('全部')}}</a>
                        </li>
                        @if(empty($data['qa_cate'])==false)
                            @foreach($data['qa_cate'] as $vo)
                            <li class="item nav-item {{App\Services\CommonService::compare_return($data['cate'], $vo['category'], 'active')}}">
                                <a  href="{{url('Qa/qa')}}?cate={{$vo['category']}}&searchText={{$data['searchText']}}">{{$vo['category']}}</a>
                            </li>
                            @endforeach
                        @endif
                    </ul>
                </div>
                
                <form action="{{url('Qa/qa')}}" method="get" name="searchQaForm" class="search-wrapper QaForm">
                    @csrf
                    <input type="text" name="searchText" placeholder="{{Lang::get('問題搜尋')}}" value="{{$data['searchText']}}">
                    <button type="submit">
                        <span class="icon-search" onclick="searchQaForm.submit();"></span>
                    </button>
                </form>
                <div class="row liveStreamBox no-gutters questionAnswerBox">
                    <div class="col-12">
                        <div class="accordion" id="qaModel">
                            @foreach($data['qa']->items() as $key => $vo)
                                @if($key!=9)
                                    <div class="qaBox">
                                        <div class="question" data-toggle="collapse" data-target="#box{{$key + 1}}" aria-expanded="true" aria-controls="box{{$key + 1}}">
                                            <div class="item">
                                                <div class="qaIcon">
                                                    <span class="num">
                                                        <span class="letter">Q</span>
                                                        <span>{{$data['qa']->currentPage() - 1}}{{$key+1}}</span>
                                                    </span>
                                                </div>
                                                <div class="text">{{$vo->q}}</div>
                                            </div>
                                        </div>
                                        <div id="box{{$key + 1}}" class="collapse answer" data-parent="#qaModel">
                                            <div class="ans">
                                                    <div class="qaIcon">
                                                        <span class="num">
                                                            <span class="letter">A</span>
                                                            <span>{{$data['qa']->currentPage() - 1}}{{$key+1}}</span>
                                                        </span>
                                                    </div>
                                                <div class="text">{!! $vo->a !!}</div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="qaBox">
                                        <div class="question" data-toggle="collapse" data-target="#box{{$key}}+1" aria-expanded="true" aria-controls="box{{$key}} + 1">
                                            <div class="item">
                                                <div class="qaIcon">
                                                    <span class="num"><span class="letter">Q</span><span>{{$data['qa']->currentPage() * 10}}</span></span>
                                                </div>
                                                <p class="text">{{$vo->q}}</p>
                                            </div>
                                        </div>
                                        <div id="box{{$key}} + 1" class="collapse answer" data-parent="#qaModel">
                                            <div class="ans">
                                                <div class="qaIcon">
                                                    <span class="num"><span class="letter">A</span><span>{{$data['qa']->currentPage() * 10}}</span></span>
                                                </div>
                                                <p class="text">{!! $vo->a !!}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="row paginationBox">
                    <div class="col-12 boxCenter">
                        {{$data['qa']->links('pagination.customize')}}
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section("ownJS")
    <script src="{{__PUBLIC__}}/js/tabOwlCarousel.js"></script>
@endsection





