@extends('admin.Public.aside')

@section('title')
門店系統 - 統計報表
@endsection

@section('cssChange')
@endsection

@section('css')
<style>
    .dropdown-selected {
        background-color: var(--sub-color) !important;
        color: white !important;
    }

    .dropdown-item:active {
        background-color: var(--sub-color) !important;
    }

    img.ranking {
        max-width: 80px;
    }

    table.ranking {
        min-width: 500px;
    }

    @media (max-width: 992px) {
        #ranking {
            flex-direction: column;
        }
    }
</style>
@endsection

@section('content')
<div class="content" id="app">
    <ul id="title" class="brand-menu">
        <li><a onclick="location.reload()">M門店系統</a></li>
        <li><a onclick="location.reload()">統計報表</a></li>
    </ul>
    <div class="d-flex justify-content-end">
        <button class="btn ml-2 clearbtn dropdown-toggle" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>
        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
            <a class="dropdown-item dropdown-0" @click="set_scale(0)">
                <span>今天</span>
            </a>
            <a class="dropdown-item dropdown-1" @click="set_scale(1)">
                <span>昨天</span>
            </a>
            <a class="dropdown-item dropdown-7" @click="set_scale(7)">
                <span>過去7天</span>
            </a>
            <a class="dropdown-item dropdown-30" @click="set_scale(30)">
                <span>過去30天</span>
            </a>
        </div>
    </div>
    <div class="d-flex flex-wrap">
        <div class="col-lg-6 col-lg-6 py-3">
            <canvas id="order_total"></canvas>
        </div>
        <div class="col-lg-6 col-sm-12 py-3">
            <canvas id="order_count"></canvas>
        </div>
        <div class="col-lg-6 col-sm-12 py-3">
            <canvas id="visitors"></canvas>
        </div>
        <div class="col-lg-6 col-sm-12 py-3">
            <canvas id="new_members"></canvas>
        </div>
    </div>
    <div class="d-flex justify-content-end align-items-center">
        <label for="rank_length" class="mr-2 my-0">
            <span>排名數量：</span>
        </label>
        <input type="number" min="5" class="form-control" id="rank_length" placeholder="排名數量" v-model="table.rank_length" @blur="get_data()" style="width: 100px; margin-right: 1rem;" />
    </div>
    <div class="d-flex justify-content-center mt-3" style="gap: 2rem;" id="ranking">
        <table class="display ranking col-lg-6 col-sm-12" id="sale_table"></table>
        <table class="display ranking col-lg-6 col-sm-12" id="view_table"></table>
    </div>
</div>
@endsection

@section('ownJS')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- dataTables -->
<link rel="stylesheet" href="https://cdn.datatables.net/2.2.2/css/dataTables.dataTables.css" />
<script src="https://cdn.datatables.net/2.2.2/js/dataTables.js"></script>

<script>
    const chartVM = new Vue({
        el: '#app',
        data: {
            scale: 0,
            chart: {
                tag: [],
                data_setting: {
                    labels: [],
                    colorR: 36,
                    colorG: 95,
                    colorB: 243,
                    colorA: 0.3,
                    tension: 0.2,
                },
                data_main_label: '',
                data_sub_label: '',
                options_setting: {
                    title_font_size: 18,
                },
                instance_map: {},
                raw_data: {},
            },
            table: {
                tag: [],
                rank_length: 5,
                columns: {},
                raw_data: {},
            },
        },
        mounted() {
            const self = this;

            this.chart.tag = $('canvas').map(function() {
                return this.id;
            }).get();

            this.table.tag = $('table').map(function() {
                return this.id;
            }).get();

            this.chart.tag.forEach(function(item) {
                self.chart.instance_map[item] = null;

                switch (item) {
                    case 'order_total':
                        self.chart.options_setting[`title_${item}`] = '成交總額';
                        break;
                    case 'order_count':
                        self.chart.options_setting[`title_${item}`] = '成交訂單總量';
                        break;
                    case 'visitors':
                        self.chart.options_setting[`title_${item}`] = '進站人數';
                        break;
                    case 'new_members':
                        self.chart.options_setting[`title_${item}`] = '新增會員數';
                        break;
                }
            });

            this.table.tag.forEach(function(item) {
                switch (item) {
                    case 'sale_table':
                        self.table.columns[item] = [{
                                title: '排名'
                            },
                            {
                                title: '商品圖'
                            },
                            {
                                title: '商品名稱'
                            },
                            {
                                title: '銷量'
                            },
                        ];
                        break;
                    case 'view_table':
                        self.table.columns[item] = [{
                                title: '排名'
                            },
                            {
                                title: '商品圖'
                            },
                            {
                                title: '商品名稱'
                            },
                            {
                                title: '商品瀏覽量'
                            },
                        ];
                        break;
                }
            });

            this.get_data(0);
        },
        methods: {
            set_scale: function(scale) {
                if ([0, 1, 7, 30].indexOf(scale) == -1) {
                    this.call_error("請選擇正確的時間範圍");
                    return;
                }

                this.scale = scale;

                this.get_data();
            },
            draw_chart: function() {
                const self = this;

                let canvas, ctx, newChart;

                self.chart.tag.forEach(function(item) {
                    canvas = document.getElementById(item);
                    ctx = canvas.getContext('2d');

                    if (self.chart.instance_map[item]) {
                        self.chart.instance_map[item].destroy();
                    }

                    newChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: self.chart.data_setting.labels,
                            datasets: [{
                                    label: self.chart.data_main_label,
                                    data: self.chart.raw_data[item + '_main'],
                                    borderColor: `rgb(${self.chart.data_setting.colorR}, ${self.chart.data_setting.colorG}, ${self.chart.data_setting.colorB})`,
                                    backgroundColor: `rgb(${self.chart.data_setting.colorR}, ${self.chart.data_setting.colorG}, ${self.chart.data_setting.colorB})`,
                                    tension: self.chart.data_setting.tension,
                                },
                                {
                                    label: self.chart.data_sub_label,
                                    data: self.chart.raw_data[item + '_sub'],
                                    borderColor: `rgba(${self.chart.data_setting.colorR}, ${self.chart.data_setting.colorG}, ${self.chart.data_setting.colorB}, ${self.chart.data_setting.colorA})`,
                                    backgroundColor: `rgba(${self.chart.data_setting.colorR}, ${self.chart.data_setting.colorG}, ${self.chart.data_setting.colorB}, ${self.chart.data_setting.colorA})`,
                                    tension: self.chart.data_setting.tension,
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: self.chart.options_setting[`title_${item}`],
                                    font: {
                                        size: self.chart.options_setting.title_font_size
                                    },
                                },
                                legend: {
                                    display: true,
                                    position: 'top',
                                    labels: {
                                        generateLabels: function(chart) {
                                            const datasets = chart.data.datasets;

                                            return datasets.map((dataset, index) => {
                                                const total = dataset.data.reduce((a, b) => parseInt(a) + parseInt(b), 0);
                                                const default_label = Chart.defaults.plugins.legend.labels.generateLabels(chart)[index];

                                                return {
                                                    ...default_label,
                                                    text: `${dataset.label} (總計: ${total})`,
                                                };
                                            });
                                        }
                                    }
                                },
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                },
                            },
                        },
                    });

                    self.chart.instance_map[item] = newChart;
                });
            },
            draw_table: function() {
                const self = this;

                self.table.tag.forEach(function(item) {
                    if ($.fn.DataTable.isDataTable(`#${item}`)) {
                        $(`#${item}`).DataTable().destroy();
                    }

                    new DataTable(`#${item}`, {
                        columnDefs: [{
                            targets: '_all',
                            orderable: false
                        }],
                        columns: self.table.columns[item],
                        data: self.table.raw_data[item],
                        paging: false,
                        searching: false,
                        info: false,
                    });
                });
            },
            get_data: function(scale = 0) {
                const self = this;

                switch (self.scale) {
                    case 0:
                        self.chart.data_main_label = '今天';
                        self.chart.data_sub_label = '昨天';
                        break;
                    case 1:
                        self.chart.data_main_label = '昨天';
                        self.chart.data_sub_label = '前天';
                        break;
                    case 7:
                        self.chart.data_main_label = '過去7天';
                        self.chart.data_sub_label = '前7天';
                        break;
                    case 30:
                        self.chart.data_main_label = '過去30天';
                        self.chart.data_sub_label = '前30天';
                        break;
                    default:
                        self.call_error("請選擇正確的時間範圍");
                        return;
                }

                $('#dropdownMenuLink').text(self.chart.data_main_label);

                $.ajax({
                    url: "{{url('shop/stats/get_data')}}",
                    type: "POST",
                    headers: {
                        'X-CSRF-TOKEN': csrf_token
                    },
                    data: {
                        scale: self.scale,
                        table_rank_length: self.table.rank_length,
                    },
                    success: function(response) {
                        if (response.status != 200) {
                            self.call_error(response.message);
                        } else {
                            self.call_seccess(response.message);

                            $('.dropdown-item').removeClass('dropdown-selected');
                            $('.dropdown-' + self.scale).addClass('dropdown-selected');

                            self.chart.data_setting.labels = response.data.chart.data_setting.labels;
                            self.chart.raw_data = response.data.chart.raw_data;

                            self.table.raw_data = response.data.table.raw_data;

                            self.draw_chart();
                            self.draw_table();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log(xhr.responseText);
                        self.call_error("資料更新失敗，請稍後再試");
                    }
                });
            },
            call_seccess: function(message) {
                Vue.toasted.show(message, {
                    duration: 1500,
                    className: ["toasted-primary", "bg-success"]
                });
            },
            call_error: function(message) {
                Vue.toasted.show(message, {
                    duration: 1500,
                    className: ["toasted-primary", "bg-danger"]
                });
            }
        },
    });
</script>
@endsection