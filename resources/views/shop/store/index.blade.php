@extends('admin.Public.aside')

@section('title')
門店系統 - 帳號查詢
@endsection

@section('cssChange')
@endsection

@section('css')
@endsection

@section('content')
<form method="post" action="/shop/store/search_memeber" class="width-50">
    @csrf
    <div class="form-group">
        <label for="account">請輸入會員手機：</label>
        <input type="text" class="form-control" style="font-size: 50px;" id="account" name="account" placeholder="會員手機">
    </div>
    <button onclick="check_input()" class="btn clearbtn float-right mt-3" style="font-size: 30px;">查詢</button>
</form>
@endsection

@section('ownJS')
<script>
    function check_input() {
        event.preventDefault();

        const regex = /^09[0-9]{8}$/;

        if (regex.test($("#account").val())) {
            $("form").submit();
        } else {
            alert("請輸入正確的手機號碼");
        }
    }
</script>
@endsection