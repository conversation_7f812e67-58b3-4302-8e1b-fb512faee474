@extends('admin.Public.aside')

@section('title')
門店系統 - 結帳
@endsection

@section('cssChange')
@endsection

@section('css')
<style>
    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .step-btn {
        font-size: 30px;
    }
</style>
@endsection

@section('content')
<form method="post" action="/shop/store/check" class="width-70">
    @csrf
    <input type="hidden" name="member_id" value="{{$data['member']['id']}}">
    <div class="form-group">
        <span>會員姓名：</span>
        <h3>{{$data['member']['name']}}</h3>
        <span>會員編號：</span>
        <h3>{{$data['member']['number']}}</h3>
        <span>會員手機：</span>
        <h3>{{$data['member']['phone']}}</h3>
        <span>會員累計消費金額：</span>
        <h3>{{number_format($data['member']['total'])}}</h3>
        <span>會員點數：</span>
        <h3>{{number_format($data['member']['point'])}}</h3>
        <div class="mt-3">
            <label for="point">請輸入欲使用點數：</label>
            <!-- <div class="d-flex mt-3 justify-content-center">
                <button class="btn clearbtn update-point-btn" onclick=update_point(0)><i class="bi bi-dash"></i></button>
                <button class="btn clearbtn ml-5 update-point-btn" onclick=update_point(1)><i class="bi bi-plus"></i></button>
            </div> -->
            <div class="d-flex">
                <input type="number" class="form-control" style="font-size: 50px;" min="0" id="set_point" placeholder="請輸入點數" />
                <button class="btn sendbtn ml-2 update-point-btn step-btn" onclick="update_point(2)">使用全部點數</button>
            </div>
            <input type="hidden" name="point" />
        </div>

        <div class="mt-3">
            <button class="btn sendbtn ml-2 update-point-btn step-btn" onclick="next_step();">下一步</button>
            <button class="btn sendbtn ml-2 step-btn previos" onclick="previous_step();" style="display: none;">回上一步</button>
        </div>
        <div class="mt-3 next-step mode-select">
            <label for="">請選擇模式：</label>
            <div class="d-flex align-items-center" style="font-size: 30px;">
                <input type="radio" class="form-control" id="mode_einvoice" name="mode" value="einvoice" />
                <label for="mode_einvoice" class="my-0 ml-2">存入載具</label>
            </div>
            <div class="d-flex align-items-center" style="font-size: 30px;">
                <input type="radio" class="form-control" id="mode_printout" name="mode" value="printout" />
                <label for="mode_printout" class="my-0 ml-2">紙本輸出</label>
            </div>
        </div>
        <div class="mt-3 next-step mode printout">
            <label for="invoice">請掃描發票左側二維條碼(First QR code)：</label>
            <input type="text" class="form-control" style="font-size: 50px;" id="invoice" name="invoice" placeholder="請掃描條碼" />
        </div>
        <div class="mt-3 next-step mode einvoice">
            <label for="invoice_number" class="mt-3">請輸入發票號碼：</label>
            <input type="text" class="form-control" style="font-size: 50px;" id="invoice_number" name="invoice_number" placeholder="請輸入發票號碼" />
            <label for="invoice_amount" class="mt-3">請輸入發票金額：</label>
            <input type="number" min="0" value="0" class="form-control" style="font-size: 50px;" id="invoice_amount" name="invoice_amount" placeholder="請輸入金額" />
            <label for="invoice_uniform_number" class="mt-3">請輸入統編：(沒有則直接不輸入)</label>
            <input type="text" class="form-control" style="font-size: 50px;" id="invoice_uniform_number" name="invoice_uniform_number" placeholder="請輸入統編" />
        </div>
    </div>
    <button type="submit" class="btn clearbtn float-right step-submit" style="font-size: 35px;">送出</button>
</form>
@endsection

@section('ownJS')
<script>
    $(".next-step").hide();
    $(".step-submit").hide();

    const Point = parseInt("{{$data['member']['point']}}");

    $("#point").attr('max', Point);

    function update_point(type) {
        event.preventDefault();

        let point = parseInt($("#point").val());

        if (isNaN(point)) {
            point = 0;
        }

        if (point > Point) {
            point = Point;
        } else if (point < 0) {
            point = 0;
        }

        if (type == 0) {
            if (point > 0) {
                point -= 1;
            }
        } else if (type == 1) {
            if (point < Point) {
                point += 1;
            }
        } else {
            point = Point;
        }

        $("#set_point").val(point);
    }

    function check_input() {
        event.preventDefault();

        try {
            if (parseInt($("#set_point").val()) > Point) {
                $('#point').val(parseInt("{{number_format($data['member']['point'])}}"));

                throw new Error(`點數不可超過${Point}`);
            }

            if (parseInt($("#set_point").val()) < 0) {
                $('#set_point').val(0);

                throw new Error(`點數不可小於0`);
            }
        } catch (e) {
            alert(e);

            return false;
        }

        alert('請至POS機打單，再進行下一步驟');

        return true;
    }

    function next_step() {
        event.preventDefault();

        if (!check_input()) {
            return false;
        }

        $('input[name="point"]').val($("#set_point").val());
        $('#set_point').attr('disabled', true);
        $('.update-point-btn').hide();
        $('.previos').show();
        $(".mode-select").show();

        // 預設顯示存入載具模式
        $('#mode_einvoice').prop('checked', true);
        $('.einvoice').show();

        $('.step-submit').show();
    }

    function previous_step() {
        event.preventDefault();

        $('#set_point').attr('disabled', false);
        $('.update-point-btn').show();
        $('.previos').hide();
        $(".next-step").hide();
        $('.step-submit').hide();
    }

    $('input[name="mode"]').change(function() {
        if ($(this).attr("id") === "mode_printout") {
            $(".mode").hide();
            $(".printout").show();
        } else {
            $(".mode").hide();
            $(".einvoice").show();
        }
    });
</script>
@endsection