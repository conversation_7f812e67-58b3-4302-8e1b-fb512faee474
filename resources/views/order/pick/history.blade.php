<!--/*串接內容*/-->
@extends('admin.Public.aside')
@section('title')待列印列表 - 揀貨歷史紀錄@endsection
@section('css')@endsection
@section('content')
<div class="content">
  <ul id="title" class="brand-menu d-flex">
    <li><a href="###">I訂單管理</a></li>
    <li><a href="###">待列印列表</a></li>
    <li><a href="###">揀貨歷史紀錄</a></li>
  </ul>
  <div class="d-flex justify-content-between mb-3">
    <div class="d-flex flex-column title">
      <h6>待取庫存</h6>
      <ul class="d-flex switch-tabs">
        <li><a href="/order/pick/index" class="mr-2">待取庫存</a></li>
        <li><a href="###" class="active">揀貨歷史紀錄</a></li>
      </ul>
    </div>
    <div class="final-check d-flex align-items-center">
      <p class="mb-0 mr-2">此頁揀貨完畢<br>即可到「待出貨」流程</p>
      <a href="/order/order_ctrl/index?order_ship_status=4">待出貨</a>
    </div>
  </div>

  <div class="frame d-flex flex-wrap justify-content-between align-items-center">
    <div class="tool_item d-flex align-items-center">  
      <button type="button" class="redo-icon" @click="get_history"><i class="bi bi-arrow-clockwise"></i></button>        
    </div>
    <div class="d-flex align-items-center">
      <button class="btn print-btn font-weight-bold mr-2" @click="print();"><i class="bi bi-printer d-inline-block mr-2"></i>列印此頁商品</button>
      <span class="d-inline-block mr-2">共 <span v-text="total_row_num"></span> 項</span>
      <span class="d-inline-block mr-2">
        <span v-text="((currentPage-1)*row_per_page)+1"></span>-
        <span v-if="currentPage*row_per_page > total_row_num" v-text="total_row_num"></span>
        <span v-else v-text="currentPage*row_per_page"></span>
        列(共 <span v-text="lastPage"></span> 頁)</span>
      <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)-1)"><i class="bi bi-chevron-left"></i></button>
      <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)+1)"><i class="bi bi-chevron-right"></i></button>
      </span>
    </div>  
  </div>
  <div class="edit_form" style="min-width: 1200px;">
    <table class="table table-rwd" id="">
      <thead>
        <tr>
            <th style="width:10%">次項</th>
            <th style="width:10%">商品編碼</th>
            @if(empty(config('control.close_function_current')['存放位置管理']))
            <th style="width:10%">揀貨編碼</th>
            @endif
            <th style="width:20%">商品</th>
            @if(!empty(config('control.close_function_current')['存放位置管理']))
            <th style="width:10%">品項</th>
            @endif
            <th style="width:10%">揀貨數量</th>
            <th style="width:20%">揀貨時間<a id="order_arrow" href="###" class="d-inline-block ml-2" @click="change_order()"><i class="bi bi-caret-down-fill"></i></a></th>
        </tr>
      </thead>
      <tbody>
        <template v-if="historyItem.length == 0">
            <tr>
                <td colspan="6" class="text-center">無資料</td>
            </tr>
        </template>
        <template v-else>
            <tr v-for="(vo, vo_idx) in historyItem">
                <td><span v-text="((currentPage - 1) * row_per_page) + vo_idx + 1"></span></td>
                <td><span v-text="vo.ISBN"></span></td>
                @if(empty(config('control.close_function_current')['存放位置管理']))
                <td><span v-text="vo.p_code"></span></td>
                @endif
                <td><span v-text="vo.title"></span></td>
                @if(!empty(config('control.close_function_current')['存放位置管理']))
                <td><span v-text="vo.type_title"></span></td>
                @endif
                <td><span v-text="vo.count"></span></td>
                <td><span v-text="vo.datetime"></span></td>
            </tr>
        </template>
      </tbody>
    </table>
  </div>
</div>

@endsection
@section("ownJS")
  <script>
    $(function() {
      $('.order_pick_index > a').addClass('active');
    });
  </script>

  <script>
    var page_location = 'history';
  </script>
  @include('order/pick/product_details')
@endsection
