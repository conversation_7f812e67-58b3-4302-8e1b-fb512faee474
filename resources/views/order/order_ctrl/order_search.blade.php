
        <div class="frame" style="min-height: 40px;">
            <div class="d-flex flex-wrap justify-content-between">
                <distributors_area ref="distributors_area" 
                               :my_distributor_id="{{$data['my_distributor_id']}}"
                               :current_distributor_id="distributors_current" :need_all="true"></distributors_area>
            </div>
        </div>

        <!-- modal select starts-->
        <div class="modal fade modalSelect" id="select" tabindex="-1" aria-labelledby="SelectLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="SelectLabel">篩選</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <!-- 表單搜尋設定 -->
                    <form action="" name="searchForm" method="get">
                        @csrf
                        <div class="modal-body">
                            <div class="pb-3 border-bottom">
                                <h6 class="font-weight-bold">日期</h6>
                                <date-picker v-model="time" range></date-picker>
                            </div>
                            <ul class="search-items pt-3">
                                <li>
                                    <input type="hidden" name="distributor_id" v-model="distributors_current">
                                    <input type="hidden" name="buy_date_st2" v-model="buy_date_st2">
                                    <input type="hidden" name="buy_date_en2" v-model="buy_date_en2">
                                    <input type="hidden" name="state" v-model="search_data.state">
                                    <input type="hidden" name="page" v-model="currentPage">
                                    <input type="hidden" name="page_count" v-model="page_count">
                                    <input type="hidden" name="order_ship_status" v-model="search_data.order_ship_status">
                                    <input type="text" name="searchKey" placeholder="訂單編號搜尋" v-model="search_data.searchKey">
                                </li>
                                <li>
                                    <input type="text" name="searchKey2" placeholder="請輸入商品名稱" v-model="search_data.searchKey2">
                                    <input type="checkbox" name="searchKey2_exclude" value="排除" id="searchKey2_exclude" v-model="searchKey2_exclude_list">
                                    <label for="searchKey2_exclude">排除</label>
                                </li>
                                <li>
                                    <input type="text" name="transport_location_name" placeholder="請輸入收件人姓名" v-model="search_data.transport_location_name">
                                </li>
                                <li>
                                    <input type="text" name="transport_location_phone" placeholder="請輸入收件人電話" v-model="search_data.transport_location_phone">
                                </li>
                                <li>
                                    <select name="payment" v-model="search_data.payment">
                                        <option value="">付款方式</option>
                                        <option v-for="vo in payments" :value="vo.id" v-text="vo.name"></option>
                                    </select>
                                </li>
                                <li>
                                    <select name="receipts_state" v-model="search_data.receipts_state">
                                        <option value="">付款狀態</option>
                                        <option v-for="vo in receipts_states" :value="vo.id" v-text="vo.name"></option>
                                    </select>
                                </li>
                                <li>
                                    <select name="transport" v-model="search_data.transport">
                                        <option value="">運送方式</option>
                                        <option v-for="vo in transports" :value="vo" v-text="vo"></option>
                                    </select>
                                </li>
                                <li>
                                    <select name="transport" v-model="search_data.arrival_date">
                                        <option value="">指定到貨日期</option>
                                        <option v-for="vo in arrival_date" :value="vo.id" v-text="vo.name"></option>
                                    </select>
                                </li>
                                <li>
                                    <span class="name">顯示筆數：</span>
                                    <select name="page_count" v-model="page_count">
                                        <option value="20">20</option>
                                        <option value="100">100</option>
                                        <option value="500">500</option>
                                        <option value="1000">1000</option>
                                    </select>
                                </li>
                                <!-- <li>
                                    @if(config('control.control_pre_buy')==1 && empty(config('control.close_function_current')['庫存警示']))
                                        <input type="checkbox" name="pre_buy" value="true" id="pre_buy" {{App\Services\CommonService::compare_return('pre_buy','true','checked')}} class="ml-3">
                                        <label for="pre_buy">包含超額購買商品</label>
                                    @endif
                                </li> -->
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn clearbtn" @click="cancel_search($event)">清除搜尋</button>
                            <button class="btn serchbtn" @click="go_search($event)">搜尋</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- modal select ends -->