@extends('admin.Public.aside')

@section("title")B會員功能管理@endsection
@section("css")
    <style>
        .dot_green {
            height: 25px;
            width: 25px;
            background-color: rgb(0, 255, 0);
            border-radius: 50%;
            display: inline-block;
        }
        .dot_red {
            height: 25px;
            width: 25px;
            background-color: rgb(255, 0, 0);
            border-radius: 50%;
            display: inline-block;
        }
        .dot_load {
            height: 15px;
            width: 15px;
            background-color:#E0E0E0;
            border-radius: 50%;
            display: inline-block;
            margin-left:5px;
            -webkit-animation-name: example; /* Safari 4.0 - 8.0 */
            -webkit-animation-duration: 2s; /* Safari 4.0 - 8.0 */
            -webkit-animation-iteration-count: infinite; /* Safari 4.0 - 8.0 */
            animation-name: example;
            animation-duration: 2s;
            animation-iteration-count: infinite;
            position: relative;

        }
        /* Safari 4.0 - 8.0 */
        @-webkit-keyframes example {
            0%   {background-color:red; left:0px; top:0px;}
            25%  {background-color:yellow; left:300px; top:0px;}
            50%  {background-color:blue; left:300px; top:300px;}
            75%  {background-color:green; left:0px; top:300px;}
            100% {background-color:red; left:0px; top:0px;}
        }
        /* Standard syntax */
        @keyframes example {
            0%   {background-color:red; left:0px; top:0px;}
            25%  {background-color:yellow; left:300px; top:0px;}
            50%  {background-color:blue; left:300px; top:300px;}
            75%  {background-color:green; left:0px; top:300px;}
            100% {background-color:red; left:0px; top:0px;}
        }
        input[type=file] {
            display: -webkit-inline-box;
        }

        .edit-item, .gift-item, .notification-item {
            min-width: 170px;
        }
        /* 新增經驗區塊 */
        .add-news{
            position: absolute;
            background: #fff;
            padding:20px;
            width: 370px;
            height:300px;
            z-index:999;
            top:calc(50% - 400px / 2);
            left:calc(50% - 370px / 2);
        }
        .add-news textarea{
            margin-top:-15px;
            margin-left:52px;
            width:278px;
            height:180px;
            padding:5px
        }            
        .add-news .button{
            right:20px;
            bottom:15px;
            position:absolute;
        }  
    </style>
@endsection

@section("content")
<div id="block" onclick="CloseAdd('.add-news')"></div>
    <div class="content">
        <!-- 匯出中動畫 -->
        <div class="add-news none" id="Box" style="height:410px;">
            <div style="padding: 10px;height: 320px;">
                <p>匯出中</p><span class="dot_load"></span>
            </div>
        </div>
        <ul id="title" class="brand-menu">
            <li><a href="###">B會員功能管理</a></li>
            <li><a href="###" onclick="javascript:location.href='{{url('order/Index/index')}}?status={{$data['urlstatus']}}'">{{$data['Member_status']}}</a></li>
            @if($data['memberKey'] !="")
                <li><a href="###">搜尋：{{$data['memberKey']}}</a></li>
            @endif
            @if($data['searchKey1'] != "")
                <li><a href="###">搜尋：{{$data['searchKey1']}}</a></li>
            @endif
            @if($data['searchKey2'] !="")
                <li><a href="###">搜尋：{{$data['searchKey2']}}</a></li>
            @endif
        </ul>
        <div>
            <div class="frame">
                <div class="searchbox mb-2">
                    <form action="{{url('order/Index/import')}}" name="excelForm" method="post"  enctype="multipart/form-data"  class="searchKeyBox">
                        @csrf
                        <input type="file" name="file" id="file_excel" class="form-control mr-1 mb-1" >
                        <button onclick="uploadFile(event);" class="btn sendbtn mr-1 mb-1">匯入檔案</button>
                        <a href="/public/static/index/example_account.xlsx" class="btn clearbtn mb-1  mr-1" download="">下載範本</a>
                        <span class="remark text-danger">也可使用匯出搜尋資料取得範本</span>
                    </form>
                </div> 
                <form action="" name="searchForm" method="get" id="searchForm">
                    @csrf
                    <select name="nameKey" id='nameKey' v-model="nameKey" class="mb-2 p-1">
                        <option value="">會員搜尋</option>
                        @if(empty(config('control.close_function_current')['註冊商品回函']))
                            <option value="1">註冊商品搜尋</option>
                        @endif
                        <option value="2">購買商品搜尋</option>
                    </select>

                    <div class="member-form">
                        <div class="item"><span class="neme">會員資料：</span>
                            <input type="text" name="memberKey" class="form-control mr-1" value="{{$data['memberKey']}}" placeholder="請輸入會員編號/姓名/信箱/手機"></div>
                        <div class="item"><span class="neme">加入開始日期:</span><input id="date_st" class="form-control mr-1" name="date_st" type="date" value="{{$data['date_st']}}"></div>
                        <div class="item"><span class="neme">結束日期:</span><input id="date_en" class="form-control mr-1" name="date_en" type="date" value="{{$data['date_en']}}"></div>
                        @if(config('control.control_VipDiscount')==1)
                        <div class="item">
                            <span class="neme">VIP狀態：</span>
                            <select name="vipType" id='vipType' class="form-control mr-1" v-model="vipType">
                                <option value="">全部</option>
                                <option value="-1">無等級</option>
                                @foreach($data['vip_type'] as $vo)
                                    <option value="{{$vo['id']}}">{{$vo['vip_name']}}</option>
                                @endforeach
                            </select>
                        </div>
                        @endif

                        @if(config('control.control_platform')==1)
                        <div class="item">
                            <span class="neme">會員類型：</span>
                            <select name="userType" id='userType' class="form-control mr-1" v-model="userType">
                                <option value="">全部</option>
                                <option value="0">一般會員</option>
                                <option value="1">供應商</option>
                            </select>
                        </div>
                        @endif
                    </div>
                    <div class="member-form" v-if="nameKey==1">
                        <div class="item">
                            <span class="neme"> 商品註冊：</span>
                            <input type="text" name="searchKey1" class="form-control mr-1" value="{{$data['searchKey1']}}" placeholder="請輸入會員編號/姓名/信箱/手機">
                        </div>
                        <div class="item">
                            <span class="neme">購買開始日期:</span>
                            <input id="buy_date_st1" class="form-control mr-1" name="buy_date_st1" type="date" value="{{$data['buy_date_st1']}}">
                        </div>
                        <div class="item">
                            <span class="neme">購買結束日期:</span>
                            <input id="buy_date_en1" class="form-control mr-1" name="buy_date_en1" type="date" value="{{$data['buy_date_en1']}}">
                        </div>
                        <div class="item">
                            <span class="neme">註冊開始日期:</span>
                            <input id="reg_date_st" class="form-control mr-1" name="reg_date_st" type="date" value="{{$data['reg_date_st']}}">
                        </div>
                        <div class="item">
                            <span class="neme">註冊結束日期:</span>
                            <input id="reg_date_en" class="form-control mr-1" name="reg_date_en" type="date" value="{{$data['reg_date_en']}}">
                        </div>
                    </div>
                    <div class="member-form" v-if="nameKey==2">
                        <div class="item">
                            <span class="neme">購買商品：</span>
                            <input type="text"  name="searchKey2" class="form-control mr-1" value="{{$data['searchKey2']}}" placeholder="請輸入會員編號/姓名/信箱/手機">
                        </div>
                        <div class="item">
                            <span class="neme">購買開始日期:</span>
                            <input id="buy_date_st2" class="form-control mr-1" name="buy_date_st2" type="date" value="{{$data['buy_date_st2']}}">
                        </div>
                        <div class="item">
                            <span class="neme">購買結束日期:</span>
                            <input id="buy_date_en2" class="form-control mr-1" name="buy_date_en2" type="date" value="{{$data['buy_date_en2']}}">
                        </div>
                    </div>
                    <button class="btn sendbtn mb-2">搜尋</button>
                </form>
                <button class="clearbtn btn" onclick="newBlock();">匯出搜尋資料</button>
            </div>
        </div>

        @if(!empty($data['tag_name']))
            <div class="text-center">
                <ul class="pagination">
                    <li @class(['active' => $data['tag']==1])>
                        <a href="javascript:change_tag(1)">{{$data['tag_name'][1]}}</a>
                    </li>
                    <li @class(['active' => $data['tag']==2])>
                        <a href="javascript:change_tag(2)">{{$data['tag_name'][2]}}</a>
                    </li>
                </ul>
            </div>
        @endif
        <hr>

        <div class="frame d-flex flex-wrap justify-content-between">
            <div>
                <a href="###" class="btn clearbtn"  data-toggle="modal" data-target="#addModal">
                    <i class="bi bi-plus-lg add small"></i>  新增
                </a>
                <!-- 多項編輯開始 -->
                <div class="tool_item ">
                    <span class="edit">
                        編輯 <span class="bi bi-chevron-down"></span>				 
                    </span>
                    <div class="edit-item  none">
                        @if(config('control.control_VipDiscount')==1)
                            <div class="mb-2 d-flex align-items-center ">
                                <p class="name mb-0">VIP等級：</p>
                                <select id="vip_type" onchange="multiOperate('vip_type');">
                                    <option value="">請選擇</option>
                                    <option value="0">無等級</option>
                                    @foreach($data['vip_type'] as $vo)
                                        <option value="{{$vo['id']}}">{{$vo['vip_name']}}</option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        <a href="###" class="mb-2" onclick="multiOperate('status', 1);">
                            <p class="mb-0">通過&nbsp;</p>
                        </a>
                        <a href="###" class="mb-2" onclick="multiOperate('status', 2);">
                            <p class="mb-0">黑名單&nbsp;</p>
                        </a>
                        <a href="###" class="mb-2" onclick="multiOperate('status', 3);">
                            <p class="mb-0">停用&nbsp;</p>
                        </a>                        
                        <a href="###" onclick="multiOperate('Delete');" class="border-top pt-2">
                            刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                        </a>
                    </div>
                </div>
                <!-- 多項編輯結束 -->

                @if($data['notification_pubkey'])
                    <!-- 推播開始 -->
                    <div class="tool_item mr-3">
                        <span class="notification">
                            推播 <span class="bi bi-chevron-down"></span>
                        </span>
                        <div class="notification-item itme_box none">
                            <p>推播標題：<input type="text" id="notification_tit" /></p>
                            <p>推播內容：<input type="text" id="notification_cont" /></p>
                            <p>開啟連結：<input type="text" id="notification_url" placeholder="請包涵http://或https://" /></p>
                            <input type="hidden" id="notification_id" />
                            <button class="btn sendbtn" onclick="notification();">
                                <p class="mb-0">確認&nbsp;</p>
                            </button>
                            <br>
                        </div>
                    </div>
                    <!-- 推播結束 -->
                @endif

                @if(!isset(config('control.close_function_current')['點數設定']))
                    <!-- 贈送開始 -->
                    <div class="tool_item ml-3">
                        <span class="gift">
                            贈送點數			 
                        </span>
                        <div class="gift-item itme_box none">
                            <p>目標：<input type="text" id="gift_member" size="12" placeholder="請輸入會員編號" /></p>
                            <p>變動說明：<input type="text" id="gift_msg" /></p>
                            <p>變動量：<input type="text" id="gift_points" placeholder="輸入負數則為扣除"/></p>
                            <button class="btn sendbtn" onclick="gift();">
                                <p class="mb-0">確認&nbsp;</p>
                            </button>
                            <br>
                        </div>
                    </div>
                    <!-- 贈送結束 -->
                @endif
            </div>
            <div class="member-num">
                會員數：{{$data['allMember']}}
                @if($data['nameKey']==1)、已註冊數：{{$data['do_number']}}、未註冊數：{{$data['allMember']-$data['do_number']}}、註冊率：{{$data['do_percent']}}@endif
                @if($data['nameKey']==2)、已購買數：{{$data['do_number']}}、未購買數：{{$data['allMember']-$data['do_number']}}、購買率：{{$data['do_percent']}}@endif
            </div>
        </div>
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width:1600px;"> 
                <thead>
                    <tr>
                        <th style="width:20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=indexCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width:120px">會員註冊日期</th>
        
                        @if(config('control.control_platform')==1)
                            <th tyle="width:80px">會員類型</th>
                        @endif
                        <th style="width:140px">會員編號</th>
                        <th style="width:140px">姓名</th>
        
                        @if(config('control.control_VipDiscount')==1)
                        <th style="width:80px">會員等級</th>
                        @endif
        
                        <th style="width:60px">狀態</th>
                        <th>聯絡信箱</th>
                        <th style="width:140px">手機</th>
                        <!-- <th style="width:70px;cursor:pointer">完成<span class="bi bi-arrow-down"></th> -->
                        <!-- <th style="width:70px;cursor:pointer">退貨<span class="bi bi-arrow-down"></th> -->
                        <!-- <th style="width:70px;cursor:pointer">取消<span class="bi bi-arrow-down"></th> -->
                        <th style="width:130px;cursor:pointer">總消費金額<span class="bi bi-arrow-down"></th>
                        <!-- <th style="width:80px">匯出狀態</th> -->
                        @if ($data['nameKey'] == 1)
                            <th style="width:10%">註冊狀態</th>
                        @elseif ($data['nameKey'] == 2)
                            <th style="width:10%">購買狀態</th>
                        @endif
                        <th style="width:60px">刪除</th>
                    </tr> 
                </thead>
                <tbody>
                    @foreach($data['rowDataItem'] as $vo)
                        <tr class="item-info">
                            <td><input type="checkbox" class="indexCheckbox" alt="{{$vo['id']}}"></td>
                            <td>{{date('Y-m-d', $vo['createtime'])}}</td>
        
                            @if(config('control.control_platform')==1)
                                <td> @if($vo['user_type'] == 1)供應商@else一般會員@endif</td>
                            @endif
                            <td><a href="{{url('order/index/edit')}}?id={{$vo['id']}}">{{$vo['number'] ?? $vo['id']}}</a></td>
                            <td><a href="{{url('order/index/edit')}}?id={{$vo['id']}}">{{$vo['name']}}</a></td>
        
                            @if(config('control.control_VipDiscount')==1)
                                <td>@if($vo['vip_id']==0)無@else{{$vo['vip_name']}}@endif</td>
                            @endif
        
                            <td>{{$vo['status']}}</td>
                            <td><a href="mailto:{$vo.email}">{{$vo['email']}}</a></td>                    
                            <td>{{$vo['phone']}}</td>
                            <!-- <td>{{$vo['complete']}}</td> -->
                            <!-- <td>{{$vo['return']}}</td> -->
                            <!-- <td>{{$vo['cancel']}}</td> -->
                            <td>{{$vo['total']}}</td>
                            <!-- <td>{{$data['Export'][$vo['export']]}}</td> -->
                            @if ($data['nameKey'] == 1)
                                <td>{{$data['Registered'][$vo['reg']]}}</td>
                            @elseif ($data['nameKey'] == 2)
                                <td>{{$data['Buy'][$vo['buy']]}}</td>
                            @endif
                            <td><span class="bi bi-trash" onclick="delete_item('{{$vo['id']}}')"></span></td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <div class="text-center">
            <ul class="pagination">
                <li class=""><a href="javascript:change_page({{$data['page']-1}})">«</a></li>

                @foreach($data['pages'] as $vo)
                    <li class="<?php if($vo==$data['page']) echo 'active'; ?>">
                        <a href="javascript:change_page({{$vo}})">{{$vo}}</a>
                    </li> 
                @endforeach

                <li class=""><a href="javascript:change_page({{$data['page']+1}})">»</a></li>
            </ul>
        </div>
    </div>

    <div class="modal fade in main-modal" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel" aria-hidden="true">
        <div id="addMember" class="modal-dialog" role="document">
            <div class="modal-content">
                <button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel" style="display: inline-block;">新增會員</h5>
                
                </div>
                <div class="modal-body row" id="boxModel">
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">推薦者會員編號</label>
                        <input type="text" class="form-control " value=""  v-model="addModel.upline_user" placeholder="">
                    </div>
                    <div class="form-group col-sm-12 col-12"></div>

                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">會員帳號</label>
                        <input type="text" class="form-control " value=""  v-model="addModel.email" placeholder="請填入email">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">會員密碼</label>
                        <input type="text" class="form-control " value=""  v-model="addModel.pwd" placeholder="需包含英文數字">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">姓名</label>
                        <input type="text" class="form-control " value=""  v-model="addModel.name">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">生日</label>
                        <input type="date" class="form-control " name="birthday">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">手機</label>
                        <input type="text" class="form-control " value=""  v-model="addModel.phone">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">電話</label>
                        <input type="text" class="form-control " value=""  v-model="addModel.tele">
                    </div>
                    <div class="form-group col-sm-12 col-12">
                        <label class="col-form-label">聯絡地址</label>
                        <input type="text" class="form-control " value=""  v-model="addModel.F_S_NH_Address">
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn sendbtn" @click="doAdd()">新增</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section("ownJS")
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>  
        /*搜尋功能*/
        var searchForm_data = {
            nameKey: "{{$data['nameKey']}}",
            vipType: "{{$data['vipType']}}",
            userType: "{{$data['userType']}}",
        }
        var searchFormVM = new Vue({
            el: '#searchForm', 
            data: searchForm_data,
            watch: {
            },
            methods: {
            }
        }); 

        //抓取GET資料
        function get_theRequest(){
            var Request = new Object();  
            var url = location.search; 
            var theRequest = new Object();      
            if (url.indexOf("?") != -1) {       
                var str = url.substr(1);         
                strs = str.split("&");       
                for(var i = 0; i < strs.length; i++) {       
                    theRequest[strs[i].split("=")[0]]=decodeURI(strs[i].split("=")[1]);       
                }        
            }
            return theRequest;
        }
        function change_tag(tag){
            theRequest = get_theRequest();

            target_url = "{{url('order/Index/index')}}?status={{$data['urlstatus']}}&";
            theRequest['page'] = 1;
            theRequest['tag'] = tag;
            ob_keys = Object.keys(theRequest);
            for(var i = 0; i < ob_keys.length; i++) {       
                target_url += ( ob_keys[i] + '=' + theRequest[ob_keys[i]] + '&');
            } 
            location.href= target_url;
        }
        function change_page(page){
            total_pages = {{$data['total_pages']}};
            if(page<1 || page>total_pages){ return; }

            theRequest = get_theRequest();

            target_url = "{{url('order/Index/index')}}?status={{$data['urlstatus']}}&";
            theRequest['page'] = page;
            ob_keys = Object.keys(theRequest);
            for(var i = 0; i < ob_keys.length; i++) {       
                target_url += ( ob_keys[i] + '=' + theRequest[ob_keys[i]] + '&');
            } 
            location.href= target_url;
        }

        /*工具列區塊*/
        $(function () {    
            // 編輯
            $('.edit').click(function(){
                if($('.edit-item').is(':visible')==true){
                    $('.edit-item').hide();
                }
                else{
                    $('.edit-item').show();
                }
            })
            $('.gift').click(function(){
                if($('.gift-item').is(':visible')==true){
                    $('.gift-item').hide();
                }
                else{
                    $('.gift-item').show();
                }
            })
            $('.notification').click(function(){
                if($('.notification-item').is(':visible')==true){
                    $('.notification-item').hide();
                }
                else{
                    $('.notification-item').show();
                }
            })
        });

        /*推播訊息*/
        $('#notification_id').val(new Date());
        function notification(){
            var notification_tit = $('#notification_tit').val();
            if(notification_tit == ''){
                Vue.toasted.show('請輸推播標題',{duration:1500, className: ["toasted-primary", 'bg-danger']});
                return;
            }

            var notification_cont = $('#notification_cont').val();
            if(notification_cont == ''){
                Vue.toasted.show('請輸推播內容',{duration:1500, className: ["toasted-primary", 'bg-danger']});
                return;
            }

            var notification_url = $('#notification_url').val();
            var notification_id = $('#notification_id').val() ? $('#notification_id').val() : 'confirm-notification';

            $('.activityCheckboxAll').prop('checked', false);
            $.ajax({
                url     : "{{url('notification/send_notification')}}",
                dataType: 'json',
                headers: {
                    'X-CSRF-Token': '{{csrf_token()}}' 
                },
                type    : 'POST',
                data : {
                    'ids'               : JSON.stringify(getMultiId()),
                    'notification_id'   : notification_id,
                    'title'             : notification_tit, 
                    'msg'               : notification_cont, 
                    'open_url'          : notification_url
                },
                contentType:"application/x-www-form-urlencoded; charset=UTF-8",
                success: function(result){
                    bg_class = result.code ? 'bg-success' : 'bg-danger';
                    Vue.toasted.show(result.msg,{duration:1500, className: ["toasted-primary", bg_class]});
                    $('#notification_id').val(new Date());
                }
            });
        }

        /*贈送點數*/
        async function gift(){
            var number = $('#gift_member').val();
            var msg = $('#gift_msg').val();
            var point = $('#gift_points').val();

            if(number == ''){
                Vue.toasted.show('請輸入會員編號',{duration:1500, className: ["toasted-primary", 'bg-danger']});
                return;
            }
            if(msg == ''){
                Vue.toasted.show('請輸入說明',{duration:1500, className: ["toasted-primary", 'bg-danger']});
                return;
            }
            if(point == ''){
                Vue.toasted.show('請輸入點數',{duration:1500, className: ["toasted-primary", 'bg-danger']});
                return;
            }

            if (confirm("目標會員"+number+"確定調整") == true) {
                $('#block_block').show();
                var result = await $.ajax({
                    url     : "{{url('order/Index/gift')}}",
                    dataType: 'json',
                    headers: {
                        'X-CSRF-Token': '{{csrf_token()}}' 
                    },
                    type    : 'POST',
                    data : {number:number,point:point,msg:msg},
                    contentType:"application/x-www-form-urlencoded; charset=UTF-8",
                });
                $('#block_block').hide();
                bg_class = result.code ? 'bg-success' : 'bg-danger';
                Vue.toasted.show(result.msg,{duration:1500, className: ["toasted-primary", bg_class]});
            }
        }

        /*批次操作會員*/
        function multiOperate(Operate, value=null) {
            if(Operate=='Delete'){
                if(!confirm('確認刪除？')){
                    return;
                }
            }

            if(Operate=='vip_type'){
                if($("#vip_type").val()=="") return;
                vip_name = $("#vip_type option:selected").html();
                if(!confirm('確認修改成 '+vip_name+' ?')){
                    $("#vip_type").val("")
                    return;
                }
            }

            var form = document.createElement("form");
            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            if(Operate == 'Delete'){ /*刪除*/
                form.action = "{{url('order/Index/multiDelete')}}";
            }
            else{ /*VIP等級、狀態*/
                form.action = "{{url('order/Index/multiUpdate')}}";
                inputValue = document.createElement("input");
                inputValue.value = value!==null ? value : $("#"+Operate).val();
                inputValue.name = "value";
                form.appendChild(inputValue);

                inputColumn = document.createElement("input");
                inputColumn.value = Operate;
                inputColumn.name = "column";
                form.appendChild(inputColumn);
            }
            form.method = "post";
            form.className = 'd-none';
            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            if(multiId.value=="[]"){
                alert("請選擇對象");
                $("#vip_type").val("");
                return;
            }
            multiId.name = "id";
            form.appendChild(multiId);
            document.body.appendChild(form);

            $('#block_block').show();
            form.submit();

            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }

        /*刪除會員*/
        function delete_item(id){
            if(confirm('確認刪除？')){
                location.href = `{{url('order/Index/delete')}}?id=`+id
            }
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.indexCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        /*匯出客戶功能*/
        var Box = {id:0, content: "", title: "", caller: null}
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            watch: {
                content: function (val) {
                    editor.html(val);
                }
            },
            methods: {
                ajaxSubmit: function () {
                    editor.sync();
                    this.content = editor.html();
                    this.content = this.content.replace(/\n/g, '')
                    if(this.caller == 'new'){
                        this.createNews({
                            content: this.content,
                            title: this.title
                        });
                    }else{
                        this.updateNews({
                            id: this.id,
                            content: this.content,
                            title: this.title
                        });
                    }
                },
                updateCallerData: function () {
                    this.caller.content = this.content;
                    this.caller.title = this.title;
                    CloseAdd('.add-news');
                }
            }
        });
        /*匯入檔案*/
        function uploadFile(){
            event.preventDefault();
            if($("#file_excel").val() != ''){
                excelForm.submit();
            }else{
                alert('請選擇檔案');
            }
        }
        /*匯出檔案*/
        function newBlock(){
            alert('已匯出檔案');

            theRequest = get_theRequest();

            target_url = "{{url('order/Index/member_excel')}}?status={{$data['urlstatus']}}&";
            ob_keys = Object.keys(theRequest);

            let query, params = {};

            for(var i = 0; i < ob_keys.length; i++) {     
                if (theRequest[ob_keys[i]] && theRequest[ob_keys[i]] != 'undefined' && ob_keys[i] != '_token') {
                    params[ob_keys[i]] = theRequest[ob_keys[i]];
                }
            }

            query = new URLSearchParams(params).toString();

            location.href= target_url + query;
        }

        /*新增會員功能*/
        var addMember_data = { 
            addModel:{
                email:"",
                pwd:"",
                name:"",
                phone:"",
                tele:"",
                F_S_NH_Address:"",
            },
        }
        var addMemberVM = new Vue({
            el: '#addMember', 
            data: addMember_data,
            methods: {
                doAdd: function () {
                    post_data = Object.assign({}, this.addModel);
                    post_data.birthday = $('input[name="birthday"]').val();
                    $.ajax({
                        url: "{{url('/order/index/addMember')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        data: post_data,
                        datatype: 'json',
                        success: function (response) {
                            if (response.code==1) {
                                location.reload();
                            } else {
                                alert(response.msg);
                            }
                        }
                    });
                },
            }
        }); 
    </script>
@endsection