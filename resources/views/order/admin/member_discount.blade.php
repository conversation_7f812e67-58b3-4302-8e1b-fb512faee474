@extends('admin.Public.aside')

@section("title")B會員功能管理 - 會員優惠設定@endsection

@section("content")
  <div>
    <ul id="title" class="brand-menu">
      <li><a href="###">B會員功能管理 </a></li>
      <li><a href="###">會員優惠設定</a></li>
    </ul>
    @if(config('control.control_FirstBuyDiscount')==1)
    <div>
      <h3 class="main-title">會員首購優惠設定</h3>
      <table class="table col-lg-6  col-12 table-rwd table-mobile">
        <thead>
          <tr>
            <th style="width:60%">優惠方式</th>
            <th>優惠說明</th>
            <th style="width:160px">更新</th>
          </tr>  
        </thead>
        <tbody>
          <form action="{{url('order/Admin/update_vip_type')}}?id=0" method="post">
            @csrf
            <input type="hidden" name="id" value="0">
            <input type="hidden" name="vip_name" value="會員首購優惠">
            <tr>
              <td data-th="優惠方式">
                <input type="radio" name="type" id="type0_0" value="0" @if($data['fisrt_buy']['type'] == 0) checked @endif>
                <label for="type0_0" class="mr-2">打折</label>
                <input type="radio" name="type" id="type0_1" value="1" @if($data['fisrt_buy']['type'] == 1) checked @endif>
                <label for="type{{$data['fisrt_buy']['id']}}_1" class="mr-4">扣元</label>
                <input type="number" name="discount" class="discount" value="{{$data['fisrt_buy']['discount']}}" step="0.01">
                <br><span class="text-danger">{{Lang::get('如需打85折，請輸入0.85')}}</span>
              </td>
              <td data-th="優惠說明">
                <input type="text" name="note" value="{{$data['fisrt_buy']['note']}}" class="w-100">
              </td>
              <td><button class="btn clearbtn">更改</button></td>
            </tr>    
          </form>
        </tbody>
      </table>
    </div>
    <hr>
    @endif

    @if(config('control.control_VipDiscount')==1)
      <div>
        <h3 class="main-title">VIP等級設定</h3>
        <form action="{{url('order/Admin/add_vip_type')}}" method="post">
          @csrf
          <table class="table table-rwd table-mobile">
            <thead>
              <tr>
                <th style="width:20%">等級名稱：</th>
                <th style="width:25%">優惠方式：</th>
                <th style="width:15%">升等條件金額：</th>
                <th>等級說明：</th>
                <th style="width: 160px;">新增</th>
              </tr>
            </thead>
            <tr>
              <td data-th="等級名稱"><input type="text" name="vip_name"></td>
              <td data-th="優惠方式">
                <input type="radio" name="type" id="type_0" value="0" checked>
                <label for="type_0" class="mr-2">打折</label>
                <input type="radio" name="type" id="type_1" value="1">
                <label for="type_1" class="mr-4">扣元</label>
                <input type="number" name="discount" class="discount" step="0.01">
                <p class="text-danger remark">{{Lang::get('如需打85折，請輸入0.85')}}</p>
              </td>
              <td data-th="升等條件金額"><input type="number" name="rule">
                <p class="text-danger remark">如設定0元，則只要有消費過即符合</p>
              </td>
              <td data-th="等級說明"><input type="text" name="note" class="w-100"></td>
              <td><button class="btn sendbtn">新增</button></td>
            </tr>
          </table>
        </form>

        <table class="table table-rwd table-mobile">
          <thead>
            <tr >
              <th style="width:20%">等級名稱</th>
              <th style="width:25%">優惠方式 ({{Lang::get('如需打85折，請輸入0.85')}})</th>
              <th style="width:15%">升等條件金額</th>
              <th >等級說明</th>				
              <th style="width: 160px;">操作</th>
            </tr>  
          </thead>
          <tbody>
            @foreach($data['vip_type'] as $vo)
            <form action="{{url('order/Admin/update_vip_type')}}?id={{$vo['id']}}" method="post">
              @csrf
              <input name="id" type="hidden" value="{{$vo['id']}}">    
              <tr>
                <td data-th="等級名稱"><input type="text" name="vip_name" value="{{$vo['vip_name']}}"></td>
                <td data-th="優惠方式">
                  <input type="radio" name="type" id="type{{$vo['id']}}_0" value="0" @if($vo['type'] == 0)checked @endif>
                  <label for="type{{$vo['id']}}_0" class="mr-2">打折</label>
                  <input type="radio" name="type" id="type{{$vo['id']}}_1" value="1" @if($vo['type'] == 1)checked @endif>
                  <label for="type{{$vo['id']}}_1" class="mr-4">扣元</label>
                  <input type="number" name="discount" class="discount" value="{{$vo['discount']}}" step="0.01">
                </td>
                <td data-th="升等條件金額"><input type="number" name="rule" value="{{$vo['rule']}}"></td>
                <td data-th="等級說明"><input type="text" name="note" value="{{$vo['note']}}" class="w-100"></td>
                <td>
                  <button class="btn clearbtn">更改</button>
                  <a class="btn sendbtn" href="javascript:if(confirm('確定刪除?'))location.href='{{url('order/Admin/del_vip_type')}}?id={{$vo['id']}}'">刪除</a>
                </td>
              </tr>    
            </form>
            @endforeach
          </tbody>
        </table>
      </div>
    @endif
  </div>
@endsection

@section("ownJS")
@endsection
