### Laravel 基礎設定
APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:GRnq9wGRnrEph7CbD4p0E7l72HgcmjC7tMjZ14a5tS0=
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

### [config]
# 是否啟用報錯，註解掉或設為false即可關閉
APP_DEBUG=true
# 網站語言版本對應(lang id與語系關聯)(此設定也決定的有幾個分站)
LangIdMapping = '{"1":"A"}'
# 本站語言版，與subDeparment的設定需跟「訂單資料庫」中`lang`表的資料一致
LangId=1
# 本站分站代號，與langId的設定需跟「訂單資料庫」中`lang`表的資料一致
subDeparment=A
# 本站對應網址，請留意格式
APP_URL='http://shop.test'
# EDM網站網址，請留意格式
edm_url='https://shop-edm.sprlight.net'

### [db]
# 商品資料庫設定
DB_DATABASE=shop_test
DB_USERNAME=root
DB_PASSWORD=
# 訂單資料庫設定(可用以下字段修改個別連線設定如：DB_HOST_ADMIN, DB_PORT_ADMIN, DB_DATABASE_ADMIN, DB_USERNAME_ADMIN, DB_PASSWORD_ADMIN)
DB_DATABASE_ADMIN=shop_test-admin
# A分站資料庫設定(可用以下字段修改個別連線設定如：DB_HOST_A, DB_PORT_A, DB_DATABASE_A, DB_USERNAME_A, DB_PASSWORD_A)
DB_DATABASE_A=
# B分站資料庫設定(可用以下字段修改個別連線設定如：DB_HOST_B, DB_PORT_B, DB_DATABASE_B, DB_USERNAME_B, DB_PASSWORD_B)

### [mail]
MAIL_MAILER=smtp
MAIL_HOST=
MAIL_PORT='465'
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION='ssl'
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=

### [productinfo] #若不需要，註解掉設定即可
# productinfo.property1="額外欄位1："
# productinfo.property2="額外欄位2："
# productinfo.property3="額外欄位3："

### 如需使用，請取消註解並填入正確參數
### [social_media]
# goolge 社群登入(須在google cloud的「憑證>OAuth 2.0 用戶端 ID」中設定「已授權的重新導向 URI」包含「{APP_URL}、{APP_URL}/index/login/g_access_token」)
# social_media.Google_appId=
# FB 社群登入(需要「驗證通過」的「企業平台帳號」，傳訊光申請不過，請先【不要使用】)
# social_media.FB_appID=
# LINE 登入(需在line developers的「LINE Login」中設定「Callback URL」包含「{APP_URL}/index/linelogin/call_back、{APP_URL}/index/linelogin/open)
# social_media.client_id=
# social_media.client_secret=
# 掃碼付款，LINE LIFF APP ID(對應網址:{APP_URL}/index/consumption/create_pay)
# social_media.LIFF_ID=

### [shop]
# 幣別
shop.dollar=NT
shop.dollar_symbol=$
# google 後台驗證開關(0: 關閉, 1: 開啟)
shop.admin_google_auth=0
# google機器人驗證，如需使用，請取消註解並填入正確的值
# shop.google_recaptcha_sitekey=
# shop.google_recaptcha_seckey=
# 公司資料
shop.company_name=傳訊光科技股份有限公司
shop.service_tel=02-2738-6266
shop.service_email=<EMAIL>
shop.service_address=臺北市信義區基隆路２段189號16樓之8
# 出貨資料
# shop.sender_name=
# shop.sender_phone=
# shop.sender_cellphone=
# shop.sender_zipcode=
# shop.sender_address=

### 綠界金流相關設定，如需使用測試環境，保持註解即可；如需使用正式環境，請取消註解並填入正確的值
### [ecpay]
# ecpay.MerchantID=
# ecpay.PlatformID=
# 金流設定
# ecpay.HashKey=
# ecpay.HashIV=
# ecpay.CreditInstallment=
# 額外設定：虛擬ATM
# ecpay.ATM_ExpireDate=3
# 檢查N日前的未付款訂單將會被取消
# ecpayt.order_remove_day=1
# 物流設定
# ecpay.Logistic_MerchantID=
# ecpay.Logistic_HashKey=
# ecpay.Logistic_HashIV=
# 發票設定
# ecpay.HashKey_invoice=
# ecpay.HashIV_invoice=
# ecpay.invoice_days=3
# ecpay.invoice_notify=0

### 推播通知設定
### [notification]
# notification.PUBKEY=
# notification.PRIKEY=

### LinePay金流相關設定，如需使用測試環境，保持base_url註解即可；如需使用正式環境，請取消註解並填入正確的值
### [line_pay]
# line_pay.channel_id=
# line_pay.channel_secret=
# line_pay.base_url=

### 台新金流，正式或測試環境需自行設定
### [tspg]
# tspg.mid=
# tspg.s_mid=
# tspg.tid=

### 新竹物流設定，正式或測試環境需自行設定
### [hct]
# hct.company=
# hct.password=
# hct.IVector=
# hct.Var=

### 黑貓物流設定，正式或測試環境需自行設定
### [tcat]
tcat.is_test=true
tcat.customer_id=
tcat.customer_token=
tcat.customer_test_token=

### Laravel 進階設定
BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=database
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"