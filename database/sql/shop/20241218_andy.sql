-- 新增捐贈碼管理頁面
CREATE TABLE `admin_email_remind` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '對應管理者id',
  `email_type` char(32) NOT NULL DEFAULT '' COMMENT '通知信類型',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='管理者-接收通知信紀錄';
INSERT INTO `admin_email_remind` (`id`, `admin_id`, `email_type`) VALUES 
(NULL, '2', 'admin_order_new'),
(NULL, '2', 'admin_order_remit'),
(NULL, '2', 'admin_order_paid'),
(NULL, '2', 'admin_order_cancel'),
(NULL, '2', 'admin_register_special'),
(NULL, '2', 'admin_register_update'),
(NULL, '2', 'admin_contact_new'),
(NULL, '2', 'admin_qa_new'),
(NULL, '2', 'admin_askprice_new'),
(NULL, '2', 'admin_askprice_again'),
(NULL, '2', 'admin_find_product_new'),
(NULL, '2', 'admin_supplier_check'),
(NULL, '2', 'admin_product_register');
