INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES (NULL, '禮盒版型管理', '禮盒版型管理', '/admin/boxlayout/index', NULL, NULL, '16', '5', '0', 'boxlayout_indexx', '_parent');

CREATE TABLE `boxlayout` (
  `id` int(10) NOT NULL,
  `name` text NOT NULL COMMENT '版型名稱',
  `number` int(100) NOT NULL COMMENT '版型數量',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `online` tinyint(1) NOT NULL DEFAULT 1 COMMENT '狀態 0.停用 1.啟用',
  `sys_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '系統控制開關 0.關 1.開'
);

ALTER TABLE `boxlayout`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `boxlayout`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;