-- 報名資料添加手機欄位
ALTER TABLE `examinee_info` 
ADD `phone` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手機' AFTER `name`;

-- 常用欄位添加不可刪除的信箱
INSERT INTO `fields_set` (`id`, `distributor_id`, `title`, `type`, `required`, `special`, `limit`, `discription`, `options`, `order_id`, `online`) VALUES 
('3', '0', '信箱', 'text', '1', '0', '[^@ \\t\\r\\n]+@[^@ \\t\\r\\n]+\\.[^@ \\t\\r\\n]+', '', '[]', '-98', '1');

-- 調整功能選單路由
UPDATE `backstage_menu_second` SET `url` = '/admin/Prodesc/index' WHERE `backstage_menu_second`.`id` = 28;

-- 添加暫存訂單欄位
ALTER TABLE `temp_order_data` 
ADD `session_id` VARCHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER `cart_data`;
