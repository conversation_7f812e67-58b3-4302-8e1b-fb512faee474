CREATE TABLE `box_layout` (
  `id` INT(10) NOT NULL AUTO_INCREMENT,
  `cate_id` INT(10) NOT NULL COMMENT '所屬版型分類id',
  `image_path` TEXT NOT NULL COMMENT '版型背景圖片路徑',
  `removed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '刪除 0.否 1.是',
  PRIMARY KEY (`id`)
) COMMENT = "版型內容";

CREATE TABLE `box_layout_block` (
  `id` INT(10) NOT NULL AUTO_INCREMENT,
  `layout_id` INT(10) NOT NULL COMMENT '所屬版型id',
  `items` TEXT NOT NULL DEFAULT '[]' COMMENT '版型格子可放入內容物分類id',
  `width` DOUBLE(13, 10) NOT NULL COMMENT '寬度',
  `height` DOUBLE(13, 10) NOT NULL COMMENT '高度',
  `left` DOUBLE(13, 10) NOT NULL COMMENT '左邊距',
  `top` DOUBLE(13, 10) NOT NULL COMMENT '上邊距',
  `removed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '刪除 0.否 1.是',
  PRIMARY KEY (`id`)
) COMMENT = "版型格子內容";
