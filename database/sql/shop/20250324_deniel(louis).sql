-- 在 productinfo 表格中建立是否為禮盒欄位
ALTER TABLE `productinfo` ADD COLUMN `is_box` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '選擇是否為禮盒' AFTER `cost`;
ALTER TABLE `productinfo` ADD COLUMN `is_customized_box` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '選擇是否為禮盒' AFTER `is_box`;

-- 客製化禮盒資訊
CREATE TABLE `customized_box_info` (
  `id` INT(10) NOT NULL AUTO_INCREMENT,
  `productinfo` BIGINT(20) UNSIGNED NOT NULL COMMENT '禮盒ID',
  `min_purchase` INT(11) NOT NULL DEFAULT 1 COMMENT '最低購買數量',
  `layers` INT(11) NOT NULL DEFAULT 1 COMMENT '盒子層數',
  `layouts_allowed` TEXT NOT NULL DEFAULT '[]' COMMENT '版型允許的版型id',
  `removed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '刪除 0.否 1.是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客製化禮盒資訊';
