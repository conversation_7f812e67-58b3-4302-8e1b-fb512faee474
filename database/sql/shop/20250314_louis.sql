-- 備貨天數
ALTER TABLE `shipping_fee` ADD COLUMN `prepare_days` TINYINT(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '備貨天數' AFTER `free_rule`;

-- 排除特定星期
CREATE TABLE `shipping_exclude_weekday` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `weekday` TINYINT UNSIGNED NOT NULL COMMENT '排除星期(同PHP之date("w"))',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `weekday_UNIQUE` (`weekday`)
) COMMENT = '排除特定星期';

INSERT INTO `shipping_exclude_weekday` (`weekday`) VALUES (0);

-- 排除特定日期
CREATE TABLE `shipping_exclude_dates` (
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `date` DATE NOT NULL COMMENT '排除日期',
  `removed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  `created_at` TIMESTAMP NULL DEFAULT NULL,
  `updated_at` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) COMMENT = '排除特定日期';

-- 新增門店系統
INSERT INTO `backstage_menu` (`id`, `title`, `name`, `sort`) VALUES (12, '門店系統', 'M門店系統', 13);
INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `sort`, `class`, `backstage_menu_id`) VALUES (95, '門店管理', '門店管理', '/shop/store/index', 1, 'store_index', 12);
