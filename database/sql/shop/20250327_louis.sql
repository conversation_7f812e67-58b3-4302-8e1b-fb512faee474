-- 禮盒內容物分類
CREATE TABLE `box_content_cate` (
  `id` INT(10) NOT NULL AUTO_INCREMENT,
  `name` TEXT NOT NULL COMMENT '內容物類型名稱',
  `online` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '狀態 0.停用 1.啟用',
  `order` INT(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `removed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '刪除 0.否 1.是',
  PRIMARY KEY (`id`)
) COMMENT = "禮盒內容物分類";

-- 禮盒內容物
CREATE TABLE `box_content` (
  `id` INT(10) NOT NULL AUTO_INCREMENT,
  `cate_id` INT(10) NOT NULL COMMENT '內容物類型id',
  `productinfo_type` INT(10) NOT NULL COMMENT '品項id',
  `removed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '刪除 0.否 1.是',
  UNIQUE KEY `productinfo_type_UNIQUE` (`productinfo_type`),
  PRIMARY KEY (`id`)
) COMMENT = "禮盒內容物";

-- 定型化禮盒資訊
CREATE TABLE `standard_box_info` (
  `id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `productinfo` INT(10) UNSIGNED NOT NULL COMMENT '禮盒ID',
  `content` TEXT NOT NULL COMMENT 'json(內容物表ID、數量)',
  `removed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '刪除 0.否 1.是',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  PRIMARY KEY (`id`)
) COMMENT='定型化禮盒資訊';

-- SEO 相關設定
ALTER TABLE `seo` ADD COLUMN `meta_pixel_id` TEXT NULL AFTER `marketyahoo`;
ALTER TABLE `seo` ADD COLUMN `google_ads_conversion_id` TEXT NULL AFTER `meta_pixel_id`;
ALTER TABLE `seo` ADD COLUMN `google_ads_conversion_tag` TEXT NULL AFTER `google_ads_conversion_id`;
ALTER TABLE `seo` ADD COLUMN `ga_4` TEXT NULL AFTER `google_ads_conversion_tag`;
