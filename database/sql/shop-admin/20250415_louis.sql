CREATE TABLE `visitor_logs` (
  `id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `ip` VARCHAR(45) NOT NULL COMMENT 'IP',
  `product_id` INT(10) UNSIGNED NOT NULL COMMENT '商品ID，首頁為0',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  PRIMARY KEY (`id`)
) COMMENT='訪客紀錄';

CREATE TABLE `customer_report` (
  `id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `year` INT(4) NOT NULL COMMENT '年份',
  `month` INT(2) NOT NULL COMMENT '月份',
  `day` INT(2) NOT NULL COMMENT '日期',
  `product_id` INT(10) UNSIGNED NOT NULL COMMENT '商品ID，首頁為0',
  `amount` INT(10) UNSIGNED NOT NULL COMMENT '進站人數',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  PRIMARY KEY (`id`)
) COMMENT '瀏覽人數統計報表';
