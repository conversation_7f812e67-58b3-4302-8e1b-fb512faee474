ALTER TABLE `orderform`
ADD COLUMN `tcat_latest_status` INT (4) NOT NULL DEFAULT 0 COMMENT '黑貓宅配狀態' AFTER `tcat_file_no`;

CREATE TABLE
    `tcat_order_status` (
        `id` INT (4) UNSIGNED NOT NULL AUTO_INCREMENT,
        `status` INT (4) NOT NULL COMMENT '狀態代碼',
        `description` VARCHAR(50) NOT NULL COMMENT '狀態說明',
        PRIMARY KEY (`id`)
    ) COMMENT '瀏覽人數統計報表';

INSERT INTO
    `tcat_order_status` (`status`, `description`)
VALUES
    (100, '已集貨'),
    (111, '轉運中'),
    (151, '配送中'),
    (152, '空運中'),
    (153, '當配下車'),
    (154, '當配上車'),
    (155, '轉寄'),
    (159, '暫置營業所'),
    (161, '一般退貨'),
    (168, '假日暫置'),
    (183, '地址錯誤'),
    (184, '郵政信箱'),
    (185, '航班延誤'),
    (186, '航班取消'),
    (202, '轉交配送中'),
    (204, '委外人員配送中'),
    (205, '二迴配送'),
    (211, '不在家'),
    (212, '公司行號休息'),
    (213, '地址不明'),
    (214, '搬家'),
    (215, '拒收'),
    (216, '另約時間'),
    (301, '順利送達'),
    (302, 'BASE列管'),
    (303, '代收退貨'),
    (305, '退貨配完'),
    (208, '轉交超商配達'),
    (209, '超商取回'),
    (308, '超商通知取回'),
    (309, 'B2S退貨'),
    (420, '轉交超商配達刪除');